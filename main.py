from fastapi import FastAPI
import uvicorn
from agents.config import PREDICT_MAIN_HOST, PREDICT_MAIN_PORT
from agents.welding_agent import welding_agent
from routes import fastapidemo,user
from core.db import init_db
app = FastAPI()

init_db(app)

@app.get("/")
def read_root():
    return {"message": "Hello World"}

app.include_router(fastapidemo.router, prefix="/api")
app.include_router(user.router, prefix="/api")
app.include_router(welding_agent.router, prefix="/agents")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=PREDICT_MAIN_HOST,
        port=PREDICT_MAIN_PORT,
        reload=True
    )