2025-08-01 15:51:33,790 - INFO - 🚀 开始WebSocket完整功能测试...
2025-08-01 15:51:36,031 - INFO - ✅ 连接建立: PASS - 成功建立WebSocket连接并收到欢迎消息
2025-08-01 15:51:39,685 - INFO - ❌ 聊天消息: FAIL - 未收到预期的聊天回复: {'type': 'thinking', 'data': {'message': '正在分析您的需求...'}, 'timestamp': '2025-08-01T15:51:39.685269', 'session_id': 'dad23fb1bee94998a783aa4fea2d60a1', 'status': 'success'}
2025-08-01 15:51:58,727 - INFO - ❌ API调用: FAIL - API调用测试失败: 
2025-08-01 15:52:02,141 - INFO - ✅ 对话管理: PASS - 成功获取对话列表
2025-08-01 15:52:05,939 - INFO - ❌ 消息格式兼容性: FAIL - v2格式消息兼容性测试失败: {'type': 'thinking', 'data': {'message': '正在分析您的需求...'}, 'timestamp': '2025-08-01T15:52:05.939254', 'session_id': '037cb22fcc224dc98834d8b29518d3ef', 'status': 'success'}
2025-08-01 15:52:09,988 - INFO - ✅ 错误处理: PASS - 正确处理无效消息类型
2025-08-01 15:52:09,989 - INFO - 
=== WebSocket 功能测试报告 ===
测试时间: 2025-08-01 15:52:09
总测试数: 6
通过: 3 ✅
失败: 3 ❌
警告: 0 ⚠️
成功率: 50.0%

=== 详细测试结果 ===
✅ 连接建立: PASS
   详情: 成功建立WebSocket连接并收到欢迎消息
   时间: 2025-08-01T15:51:36.031302

❌ 聊天消息: FAIL
   详情: 未收到预期的聊天回复: {'type': 'thinking', 'data': {'message': '正在分析您的需求...'}, 'timestamp': '2025-08-01T15:51:39.685269', 'session_id': 'dad23fb1bee94998a783aa4fea2d60a1', 'status': 'success'}
   时间: 2025-08-01T15:51:39.685873

❌ API调用: FAIL
   详情: API调用测试失败: 
   时间: 2025-08-01T15:51:58.727660

✅ 对话管理: PASS
   详情: 成功获取对话列表
   时间: 2025-08-01T15:52:02.141712

❌ 消息格式兼容性: FAIL
   详情: v2格式消息兼容性测试失败: {'type': 'thinking', 'data': {'message': '正在分析您的需求...'}, 'timestamp': '2025-08-01T15:52:05.939254', 'session_id': '037cb22fcc224dc98834d8b29518d3ef', 'status': 'success'}
   时间: 2025-08-01T15:52:05.939764

✅ 错误处理: PASS
   详情: 正确处理无效消息类型
   时间: 2025-08-01T15:52:09.988873


2025-08-01 15:52:58,939 - INFO - 🚀 开始WebSocket完整功能测试...
2025-08-01 15:53:08,993 - INFO - ❌ 连接建立: FAIL - 连接失败: timed out during opening handshake
2025-08-01 15:53:19,993 - INFO - ❌ 聊天消息: FAIL - 聊天消息测试失败: timed out during opening handshake
2025-08-01 15:53:31,013 - INFO - ❌ API调用: FAIL - API调用测试失败: timed out during opening handshake
