#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的Web端测试脚本
基于智能体问答对话样例进行自动化测试
"""

import asyncio
import websockets
import json
import time
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebTestRunner:
    def __init__(self):
        self.websocket = None
        self.session_id = f"test_session_{int(time.time())}"
        self.user_id = "test_user_001"
        self.username = "测试用户"
        self.test_results = []
        
    async def connect_websocket(self):
        """连接WebSocket"""
        try:
            url = f"ws://localhost:8008/api/ws/chat?session_id={self.session_id}&user_id={self.user_id}&username={self.username}"
            logger.info(f"正在连接WebSocket: {url}")
            
            self.websocket = await websockets.connect(url)
            logger.info("✅ WebSocket连接成功")
            
            # 等待欢迎消息
            welcome_msg = await self.websocket.recv()
            welcome_data = json.loads(welcome_msg)
            logger.info(f"收到欢迎消息: {welcome_data.get('type')}")
            
            return True
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            return False
    
    async def send_message(self, message: str):
        """发送聊天消息"""
        try:
            message_data = {
                "type": "chat",
                "data": {
                    "message": message
                },
                "session_id": self.session_id,
                "user_id": self.user_id
            }
            
            await self.websocket.send(json.dumps(message_data, ensure_ascii=False))
            logger.info(f"发送消息: {message[:50]}...")
            
            # 等待回复
            response = await asyncio.wait_for(self.websocket.recv(), timeout=30)
            response_data = json.loads(response)
            
            if response_data.get('type') == 'chat_response':
                reply = response_data.get('data', {}).get('message', '')
                logger.info(f"收到回复: {reply[:100]}...")
                return reply
            else:
                logger.warning(f"收到非聊天回复: {response_data.get('type')}")
                return None
                
        except asyncio.TimeoutError:
            logger.error("等待回复超时")
            return None
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return None
    
    def load_test_conversations(self):
        """加载测试对话"""
        try:
            with open('agents/predict_algorith_agent/tests/智能体问答对话样例.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversations = []
            for item in data:
                for round_data in item['rounds']:
                    if round_data['role'] == 'user':
                        conversations.append(round_data['content'])
            
            logger.info(f"加载了 {len(conversations)} 个测试对话")
            return conversations
        except Exception as e:
            logger.error(f"加载测试数据失败: {e}")
            return []
    
    async def run_conversation_tests(self):
        """运行对话测试"""
        conversations = self.load_test_conversations()
        if not conversations:
            return False
        
        success_count = 0
        total_count = len(conversations)
        
        for i, message in enumerate(conversations, 1):
            logger.info(f"\n=== 测试对话 {i}/{total_count} ===")
            logger.info(f"用户输入: {message}")
            
            reply = await self.send_message(message)
            
            if reply:
                success_count += 1
                self.test_results.append({
                    "test_id": i,
                    "input": message,
                    "output": reply,
                    "status": "success",
                    "timestamp": time.time()
                })
            else:
                self.test_results.append({
                    "test_id": i,
                    "input": message,
                    "output": None,
                    "status": "failed",
                    "timestamp": time.time()
                })
            
            # 等待一段时间再发送下一条消息
            await asyncio.sleep(2)
        
        logger.info(f"\n=== 对话测试完成 ===")
        logger.info(f"总测试数: {total_count}")
        logger.info(f"成功数: {success_count}")
        logger.info(f"成功率: {success_count/total_count*100:.1f}%")
        
        return success_count == total_count
    
    async def test_api_functions(self):
        """测试API功能"""
        logger.info("\n=== 开始API功能测试 ===")
        
        api_tests = [
            {
                "action": "get_all_algorithms",
                "description": "查询全部算法名称"
            },
            {
                "action": "get_all_datasets", 
                "description": "查询全部数据集名称"
            }
        ]
        
        api_success_count = 0
        
        for test in api_tests:
            try:
                api_message = {
                    "type": "api_call",
                    "data": {
                        "action": test["action"],
                        "project_id": "285",
                        "parameters": {}
                    },
                    "session_id": self.session_id,
                    "user_id": self.user_id
                }
                
                await self.websocket.send(json.dumps(api_message, ensure_ascii=False))
                logger.info(f"发送API请求: {test['description']}")
                
                # 等待API回复
                response = await asyncio.wait_for(self.websocket.recv(), timeout=15)
                response_data = json.loads(response)
                
                if response_data.get('type') == 'api_response':
                    status = response_data.get('data', {}).get('status')
                    if status == 'success':
                        logger.info(f"✅ API测试成功: {test['description']}")
                        api_success_count += 1
                    else:
                        logger.warning(f"⚠️ API返回错误: {test['description']}")
                else:
                    logger.warning(f"⚠️ 收到非API回复: {response_data.get('type')}")
                
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ API测试失败: {test['description']}, error: {e}")
        
        logger.info(f"API测试完成: {api_success_count}/{len(api_tests)} 成功")
        return api_success_count == len(api_tests)
    
    async def close_connection(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("WebSocket连接已关闭")
    
    def generate_test_report(self):
        """生成测试报告"""
        report = {
            "test_summary": {
                "total_tests": len(self.test_results),
                "successful_tests": len([r for r in self.test_results if r["status"] == "success"]),
                "failed_tests": len([r for r in self.test_results if r["status"] == "failed"]),
                "success_rate": len([r for r in self.test_results if r["status"] == "success"]) / len(self.test_results) * 100 if self.test_results else 0
            },
            "test_details": self.test_results,
            "timestamp": time.time()
        }
        
        # 保存报告
        with open("web_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("测试报告已保存: web_test_report.json")
        return report

async def main():
    """主测试函数"""
    print("🚀 开始完整的Web端测试")
    print("=" * 60)
    
    tester = WebTestRunner()
    
    try:
        # 1. 连接WebSocket
        print("\n1️⃣ 连接WebSocket服务...")
        if not await tester.connect_websocket():
            print("❌ WebSocket连接失败，测试终止")
            return
        
        # 2. 运行对话测试
        print("\n2️⃣ 开始对话功能测试...")
        conversation_success = await tester.run_conversation_tests()
        
        # 3. 运行API测试
        print("\n3️⃣ 开始API功能测试...")
        api_success = await tester.test_api_functions()
        
        # 4. 生成测试报告
        print("\n4️⃣ 生成测试报告...")
        report = tester.generate_test_report()
        
        # 5. 输出最终结果
        print("\n" + "=" * 60)
        print("🎯 测试完成！")
        print(f"📊 对话测试: {'✅ 通过' if conversation_success else '❌ 失败'}")
        print(f"🔧 API测试: {'✅ 通过' if api_success else '❌ 失败'}")
        print(f"📈 总成功率: {report['test_summary']['success_rate']:.1f}%")
        print(f"📄 详细报告: web_test_report.json")
        
        if conversation_success and api_success:
            print("\n🎉 所有测试通过！智能体系统运行正常！")
        else:
            print("\n⚠️ 部分测试失败，请检查日志和报告")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
    finally:
        await tester.close_connection()

if __name__ == "__main__":
    asyncio.run(main())
