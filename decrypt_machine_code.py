#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解密机器码脚本
使用RSA私钥解密base64编码的机器码信息
"""

from rsa import PrivateKey, decrypt, sign
import base64

def rsa_decrypt_key(encrypted_key: str, private_key: str) -> str:
    """
    使用RSA私钥解密base64编码的机器码

    Args:
        encrypted_key (str): base64编码的加密机器码
        private_key (str): PEM格式的RSA私钥

    Returns:
        str: 解密后的原始机器码字符串
    """
    # 1. base64解码
    encrypted_bytes = base64.b64decode(encrypted_key)

    # 2. 加载RSA私钥
    priv_key = PrivateKey.load_pkcs1(private_key.encode('utf-8'))

    # 3. RSA解密
    decrypted_bytes = decrypt(encrypted_bytes, priv_key)

    # 4. 转换为字符串
    return decrypted_bytes.decode('utf-8')

def sign_cpu_with_private_key(cpu_str: str, private_key: str) -> str:
    """
    用私钥对CPU字符串做PKCS1+SHA1签名，返回base64字符串
    """
    priv_key = PrivateKey.load_pkcs1(private_key.encode('utf-8'))
    signature = sign(cpu_str.encode('utf-8'), priv_key, 'SHA-1')
    return base64.b64encode(signature).decode('utf-8')

def main():
    # 用户admin6的RSA私钥 - 假的key 测试使用
    private_key = """
    xxx
    """

    # 要解密的机器码（base64编码）
    encrypted_machine_code = "xx"

    try:
        print("开始解密机器码...")
        print(f"加密的机器码: {encrypted_machine_code[:50]}...")
        print()
        
        # 解密机器码
        decrypted_result = rsa_decrypt_key(encrypted_machine_code, private_key)
        
        print("解密成功！")
        print(f"解密结果: {decrypted_result}")
        print()
        
        # 解析机器码格式 (Mac|CPU|硬盘号)
        try:
            parts = decrypted_result.split('|')
            if len(parts) == 3:
                mac, cpu, disk = parts
                print("机器码信息解析:")
                print(f"  MAC地址: {mac}")
                print(f"  CPU信息: {cpu}")
                print(f"  硬盘号: {disk}")
                print()

                # 生成CPU签名（注册码）
                print("生成CPU签名（注册码）:")
                cpu_signature = sign_cpu_with_private_key(cpu, private_key)
                print(f"  CPU签名: {cpu_signature}")

            else:
                print(f"机器码格式不正确，期望3个部分，实际得到{len(parts)}个部分")
                print(f"各部分: {parts}")
        except Exception as e:
            print(f"解析机器码格式时出错: {e}")
            
    except Exception as e:
        print(f"解密失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
