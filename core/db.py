from tortoise.contrib.fastapi import register_tortoise
from fastapi import FastAP<PERSON>
from urllib.parse import quote

def init_db(app: FastAPI) -> None:
    #print("开始初始化数据库")
    username = ""
    ori_password = ""
    safe_password = quote(ori_password)
    database = "go_test"
    host = "*********:23306"
    db_url = f"mysql://{username}:{safe_password}@{host}/{database}?charset=utf8mb4"
    register_tortoise(
        app,
        db_url=db_url,
        modules={"models": ["models.user"]},
        generate_schemas=False,
        add_exception_handlers=True,
    )
    #print("数据库初始化完成")