-- Agent会话与任务状态表
CREATE TABLE `agent_session` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` varchar(64) NOT NULL COMMENT '用户唯一标识',
  `session_id` varchar(64) NOT NULL COMMENT '会话唯一标识',
  `task_id` varchar(128) DEFAULT NULL COMMENT '三方任务ID',
  `state` json NOT NULL COMMENT 'Agent内部状态(JSON序列化)',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标志,0-正常,1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_user_session` (`user_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Agent会话与任务状态表';

