# 控制是否输出deepseek大模型的思考过程
# True: 输出思考过程，False: 不输出
SHOW_DEEPSEEK_THOUGHT = False

# ==================== WebSocket欢迎消息配置 ====================
# 控制是否在WebSocket连接和新建对话时自动发送欢迎引导消息
# True: 发送欢迎消息，False: 不发送（由前端提供引导词）
ENABLE_WELCOME_MESSAGE = False

# ==================== 大模型配置管理 ====================
import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# 加载 .env 文件（如果存在）
env_file = os.path.join(os.path.dirname(__file__), "predict_algorith_agent", ".env")
if os.path.exists(env_file):
    load_dotenv(env_file)

# 大模型提供商配置
LLM_PROVIDERS = {
    "ds-int8": {
        "model_name": "ds-int8",
        "base_url": "https://devops.cm-iov.com:28033/oneapi/v1/",
        "api_key": "sk-bDYvEo0ZO4wYXBz50e1e09A3D9Ed409c875c11D337969467",
        "description": "DS-INT8 自定义大模型"
    },
    "qwen": {
        "model_name": "qwen-turbo-latest",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": "sk-b43e1b0d51e24f359b1d3c6342149577",
        "description": "阿里云通义千问"
    },
    "deepseek": {
        "model_name": "deepseek-chat",
        "base_url": "https://api.deepseek.com",
        "api_key": "***********************************",
        "description": "DeepSeek大模型"
    },
    "openai": {
        "model_name": "gpt-4o-mini",
        "base_url": "https://api.openai.com/v1",
        "api_key": "",  # 需要设置您的OpenAI API Key
        "description": "OpenAI GPT模型"
    }
}

# 当前使用的大模型提供商 (优先级: 系统环境变量 > .env文件 > 默认值)
CURRENT_LLM_PROVIDER = os.getenv("LLM_PROVIDER", "qwen")

# 大模型通用配置
LLM_CONFIG = {
    "temperature": float(os.getenv("LLM_TEMPERATURE", "0.7")),
    "max_tokens": int(os.getenv("LLM_MAX_TOKENS", "2000")),
    "timeout": int(os.getenv("LLM_TIMEOUT", "30")),
    "retry_times": int(os.getenv("LLM_RETRY_TIMES", "3")),
    "retry_delay": float(os.getenv("LLM_RETRY_DELAY", "1.0"))
}

def get_current_llm_config() -> Dict[str, Any]:
    """
    获取当前大模型配置

    Returns:
        Dict[str, Any]: 包含模型名称、base_url、api_key等配置信息

    Raises:
        ValueError: 当配置的提供商不存在时
    """
    if CURRENT_LLM_PROVIDER not in LLM_PROVIDERS:
        raise ValueError(f"未知的大模型提供商: {CURRENT_LLM_PROVIDER}")

    provider_config = LLM_PROVIDERS[CURRENT_LLM_PROVIDER].copy()
    provider_config.update(LLM_CONFIG)

    return provider_config

def set_llm_provider(provider_name: str) -> bool:
    """
    设置大模型提供商并持久化到.env文件

    Args:
        provider_name: 提供商名称 (ds-int8, qwen, deepseek, openai)

    Returns:
        bool: 设置是否成功
    """
    global CURRENT_LLM_PROVIDER

    if provider_name not in LLM_PROVIDERS:
        print(f"❌ 未知的大模型提供商: {provider_name}")
        print(f"💡 支持的提供商: {', '.join(LLM_PROVIDERS.keys())}")
        return False

    try:
        # 更新.env文件
        env_file = os.path.join(os.path.dirname(__file__), "predict_algorith_agent", ".env")

        if os.path.exists(env_file):
            # 读取现有内容
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 更新或添加LLM_PROVIDER配置
            updated = False
            for i, line in enumerate(lines):
                if line.startswith('LLM_PROVIDER='):
                    lines[i] = f'LLM_PROVIDER={provider_name}\n'
                    updated = True
                    break

            if not updated:
                # 如果没找到，添加到文件末尾
                lines.append(f'\nLLM_PROVIDER={provider_name}\n')

            # 写回文件
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
        else:
            # 如果.env文件不存在，创建一个简单的
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(f'LLM_PROVIDER={provider_name}\n')

        # 更新内存中的变量和环境变量
        CURRENT_LLM_PROVIDER = provider_name
        os.environ['LLM_PROVIDER'] = provider_name

        print(f"✅ 大模型提供商已设置为: {provider_name}")
        print(f"📁 配置已保存到: {env_file}")
        return True

    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def update_llm_api_key(provider_name: str, api_key: str) -> bool:
    """
    更新指定提供商的API Key

    Args:
        provider_name: 提供商名称
        api_key: 新的API Key

    Returns:
        bool: 更新是否成功
    """
    if provider_name not in LLM_PROVIDERS:
        print(f"❌ 未知的大模型提供商: {provider_name}")
        return False

    LLM_PROVIDERS[provider_name]["api_key"] = api_key
    print(f"✅ {provider_name} API Key已更新")
    return True

def get_llm_providers_info() -> Dict[str, str]:
    """
    获取所有大模型提供商信息

    Returns:
        Dict[str, str]: 提供商名称到描述的映射
    """
    return {name: config["description"] for name, config in LLM_PROVIDERS.items()}

# ==================== 算法平台API配置 ====================
# 替换原有的假API配置

# 算法平台基础URL
ALGORITHM_PLATFORM_BASE_URL = os.getenv(
    "ALGORITHM_PLATFORM_BASE_URL",
    "http://***********:8081"
)

# 算法平台8个接口端点配置
ALGORITHM_PLATFORM_ENDPOINTS = {
    # 1. 查询全部算法名称
    "get_all_algorithm": "/get_all_algorithm",

    # 2. 查询全部数据集名称
    "get_all_dataset": "/get_all_dataset",

    # 3. 训练新算法
    "algorithm_parameter": "/algorithm_parameter",

    # 4. 查询全部IOT名称
    "get_all_iot": "/get_all_iot",

    # 5. 启动监控分析服务
    "implement_parameter": "/implement_parameter",

    # 6. 查询算法训练信息
    "get_algorithm_log": "/get_algorithm_log",

    # 7. 查询全部服务名称
    "get_all_implement": "/get_all_implement",

    # 8. 查询服务预测结果
    "get_implement_log": "/get_implement_log"
}

# 算法平台请求配置
ALGORITHM_PLATFORM_TIMEOUT = int(os.getenv("ALGORITHM_PLATFORM_TIMEOUT", "30"))
ALGORITHM_PLATFORM_RETRY_TIMES = int(os.getenv("ALGORITHM_PLATFORM_RETRY_TIMES", "3"))
ALGORITHM_PLATFORM_RETRY_DELAY = float(os.getenv("ALGORITHM_PLATFORM_RETRY_DELAY", "1.0"))
ALGORITHM_PLATFORM_ENABLE_CACHE = True
ALGORITHM_PLATFORM_CACHE_TTL = 1800  # 缓存生存时间（30分钟）

def get_algorithm_platform_url(endpoint_key: str) -> str:
    """
    获取算法平台API的完整URL

    Args:
        endpoint_key: 端点键名，如 'get_all_algorithm'

    Returns:
        完整的API URL

    Raises:
        ValueError: 当端点键名不存在时
    """
    if endpoint_key not in ALGORITHM_PLATFORM_ENDPOINTS:
        raise ValueError(f"未知的端点键名: {endpoint_key}")

    return f"{ALGORITHM_PLATFORM_BASE_URL}{ALGORITHM_PLATFORM_ENDPOINTS[endpoint_key]}"

# 历史算法API配置 - 指向算法平台
HISTORY_ALGORITHM_API_BASE = ALGORITHM_PLATFORM_BASE_URL
HISTORY_ALGORITHM_API_KEY = None  # 历史算法API密钥，如果需要的话

# predict_main监听配置
PREDICT_MAIN_HOST = "0.0.0.0"
PREDICT_MAIN_PORT = 8008

# MySQL数据库连接配置 - 使用241服务器的正确配置
MYSQL_HOST = os.getenv("DB_HOST", "***********")
MYSQL_PORT = int(os.getenv("DB_PORT", "3306"))
MYSQL_USER = os.getenv("DB_USER", "root")
MYSQL_PASSWORD = os.getenv("DB_PASSWORD", "Spsm2021+")
MYSQL_DB = os.getenv("DB_NAME", "indusaio_agent")
MYSQL_CHARSET = "utf8mb4"

# 单机Redis数据库连接配置
REDIS_HOST = os.getenv("REDIS_HOST", "***********")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "Sygy@2025")
REDIS_DECODE_RESPONSES = True

# wkhtmltopdf 可执行文件路径（本地Windows默认，部署Linux时请修改）
WKHTMLTOPDF_PATH = r"D:\software\wkhtmltopdf\bin\wkhtmltopdf.exe"

# PDF报告输出目录（本地Windows默认，部署Linux时请修改）
REPORT_PDF_DIR = r"D:/tmp"

# ==================== 配置验证 ====================
def validate_algorithm_platform_config():
    """
    验证算法平台配置的有效性

    Returns:
        bool: 配置是否有效
    """
    try:
        import requests
        response = requests.get(f"{ALGORITHM_PLATFORM_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ 算法平台连接正常: {ALGORITHM_PLATFORM_BASE_URL}")
            return True
        else:
            print(f"⚠️ 算法平台响应异常: {ALGORITHM_PLATFORM_BASE_URL}, 状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 算法平台连接失败: {ALGORITHM_PLATFORM_BASE_URL}, 错误: {e}")
        return False

# 可选：在模块导入时自动验证（仅在直接运行时）
if __name__ == "__main__":
    validate_algorithm_platform_config()
