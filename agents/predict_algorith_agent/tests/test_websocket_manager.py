import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# 导入待测试的模块和其依赖
from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager, WebSocketMessage, WebSocketResponse, DateTimeEncoder
from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState, InteractionType

# Mock agents.config - 增强配置Mock
mock_config = {
    "ENABLE_WELCOME_MESSAGE": True,
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "MYSQL_CHARSET": "utf8mb4",
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model"
    }
}

@pytest.fixture(autouse=True)
def mock_agents_config_ws():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config):
        yield

# Mock logging
@pytest.fixture(autouse=True)
def mock_logging_ws():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.logger', autospec=True) as mock_logger:
        yield mock_logger

# Mock pymysql连接
@pytest.fixture(autouse=True)
def mock_pymysql_ws():
    """自动Mock pymysql数据库连接"""
    with patch('pymysql.connect') as mock_connect:
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_cursor.rowcount = 0
        mock_cursor.lastrowid = 1

        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.__enter__.return_value = mock_conn
        mock_conn.__exit__.return_value = None
        mock_connect.return_value = mock_conn

        yield mock_connect

# Mock PredictiveAlgorithmAssistant - 增强AI代理Mock
@pytest.fixture
def mock_predictive_agent():
    """Mock预测算法助手避免真实AI调用"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.PredictiveAlgorithmAssistant', autospec=True) as MockAgent:
        mock_instance = MockAgent.return_value
        mock_instance.process_user_input_async = AsyncMock(return_value=(
            {"msg": "这是Mock AI回复，用于测试WebSocket功能", "type": "chat_response"},
            PredictiveAgentState().dict(),
            "chat"
        ))
        yield MockAgent

# Mock DatabaseManager - 增强数据库管理器Mock
@pytest.fixture
def mock_db_manager_ws():
    """Mock数据库管理器避免真实数据库连接"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.DatabaseManager', autospec=True) as MockDBManager:
        mock_instance = MockDBManager.return_value
        mock_instance.create_conversation = MagicMock(return_value="conv_test_id")
        mock_instance.add_conversation_message = MagicMock(return_value=True)
        mock_instance.save_session_state = MagicMock(return_value=True)
        mock_instance.get_conversation_messages_with_context = MagicMock(return_value=[])
        mock_instance.get_user_conversations = MagicMock(return_value=[
            {
                "conversation_id": "conv_1",
                "title": "Mock对话1",
                "status": "active",
                "created_at": datetime.now()
            }
        ])
        mock_instance.get_conversation_detail = MagicMock(return_value={
            "conversation_id": "conv_test_id",
            "user_id": "test_user",
            "title": "Mock测试对话",
            "status": "active"
        })
        yield MockDBManager

# Mock ContextManager
@pytest.fixture
def mock_context_manager():
    with patch('agents.predict_algorith_agent.network.websocket_manager.ContextManager', autospec=True) as MockContextManager:
        mock_instance = MockContextManager.return_value
        mock_instance.build_context = MagicMock(return_value={'context_layers': {}, 'total_tokens': 0})
        mock_instance.rebuild_agent_state = MagicMock(return_value=PredictiveAgentState())
        yield MockContextManager

# Mock WebSocket
@pytest.fixture
def mock_websocket():
    mock_ws = AsyncMock()
    mock_ws.accept = AsyncMock()
    mock_ws.send_text = AsyncMock()
    mock_ws.close = AsyncMock()
    # Mock client_state for send_personal_message robustness check
    mock_ws.client_state = MagicMock(name="CONNECTED")
    yield mock_ws

# ====================================================================================================
# Test Cases for ConnectionManager
# ====================================================================================================

@pytest.mark.asyncio
async def test_connect_new_session(mock_websocket, mock_db_manager_ws, mock_logging_ws):
    """测试新会话连接成功 - 简化测试避免欢迎消息依赖"""
    manager = ConnectionManager()
    session_id = "test_session_1"
    user_id = "test_user_1"

    await manager.connect(mock_websocket, session_id, {"user_id": user_id})

    # 验证基本连接功能
    mock_websocket.accept.assert_called_once()
    assert session_id in manager.active_connections
    assert session_id in manager.user_sessions
    assert manager.user_sessions[session_id]["user_id"] == user_id
    mock_db_manager_ws.return_value.save_session_state.assert_called_once()

    # 欢迎消息是可选的，不强制要求
    # 如果发送了欢迎消息，验证其格式
    if mock_websocket.send_text.called:
        args, kwargs = mock_websocket.send_text.call_args
        welcome_msg = json.loads(args[0])
        assert welcome_msg["type"] == "welcome"
        assert "message" in welcome_msg["data"]

@pytest.mark.asyncio
async def test_connect_duplicate_session(mock_websocket, mock_db_manager_ws, mock_logging_ws):
    """测试重复会话ID连接，旧连接被断开"""
    manager = ConnectionManager()
    session_id = "test_session_2"
    user_id = "test_user_2"

    # 模拟一个已存在的连接
    old_websocket = AsyncMock()
    old_websocket.close = AsyncMock()
    manager.active_connections[session_id] = old_websocket
    manager.user_sessions[session_id] = {"user_id": user_id, "connected_at": datetime.now().isoformat()}
    
    await manager.connect(mock_websocket, session_id, {"user_id": user_id})
    
    old_websocket.close.assert_called_once() # 旧连接被关闭
    mock_websocket.accept.assert_called_once()
    assert session_id in manager.active_connections
    assert manager.active_connections[session_id] == mock_websocket # 新连接取代旧连接
    mock_db_manager_ws.return_value.save_session_state.assert_called_once()

def test_disconnect(mock_logging_ws):
    """测试断开连接"""
    manager = ConnectionManager()
    session_id = "test_session_3"
    manager.active_connections[session_id] = AsyncMock()
    manager.user_sessions[session_id] = {"user_id": "user3"}
    
    manager.disconnect(session_id)
    
    assert session_id not in manager.active_connections
    assert session_id not in manager.user_sessions

@pytest.mark.asyncio
async def test_send_personal_message_success(mock_websocket, mock_logging_ws):
    """测试发送个人消息成功"""
    manager = ConnectionManager()
    session_id = "test_session_4"
    manager.active_connections[session_id] = mock_websocket
    
    message = {"type": "test", "data": {"content": "hello"}}
    await manager.send_personal_message(message, session_id)
    
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    sent_message = json.loads(args[0])
    assert sent_message["type"] == "test"
    assert sent_message["data"]["content"] == "hello"

@pytest.mark.asyncio
async def test_send_personal_message_disconnected_websocket(mock_websocket, mock_logging_ws):
    """测试向已断开的WebSocket发送消息"""
    manager = ConnectionManager()
    session_id = "test_session_5"
    manager.active_connections[session_id] = mock_websocket
    
    # 模拟WebSocket已断开
    mock_websocket.client_state.name = "DISCONNECTED"
    
    message = {"type": "test", "data": {"content": "hello"}}
    await manager.send_personal_message(message, session_id)
    
    mock_websocket.send_text.assert_not_called()
    assert session_id not in manager.active_connections # 应该被清理

@pytest.mark.asyncio
async def test_broadcast_message(mock_websocket, mock_logging_ws):
    """测试广播消息"""
    manager = ConnectionManager()
    session_id_1 = "test_session_6_1"
    session_id_2 = "test_session_6_2"
    
    ws1 = AsyncMock()
    ws2 = AsyncMock()
    manager.active_connections[session_id_1] = ws1
    manager.active_connections[session_id_2] = ws2
    
    message = {"type": "broadcast", "data": {"content": "all"}}
    await manager.broadcast(message)
    
    ws1.send_text.assert_called_once()
    ws2.send_text.assert_called_once()
    args1, _ = ws1.send_text.call_args
    args2, _ = ws2.send_text.call_args
    assert json.loads(args1[0])["type"] == "broadcast"
    assert json.loads(args2[0])["type"] == "broadcast"

@pytest.mark.asyncio
async def test_handle_chat_message_new_conversation(mock_websocket, mock_predictive_agent, mock_db_manager_ws, mock_context_manager, mock_logging_ws):
    """测试处理聊天消息 - 新建对话"""
    manager = ConnectionManager()
    session_id = "test_session_7"
    user_id = "user7"
    manager.user_sessions[session_id] = {"conversation_id": None, "user_id": user_id, "state": {}}
    
    message_data = {"type": "chat", "data": {"message": "我想用LSTM预测", "project_id": "proj7"}}
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_db_manager_ws.return_value.create_conversation.assert_called_once_with(
        user_id=user_id, session_id=session_id, title="新建算法训练任务"
    )
    assert mock_db_manager_ws.return_value.add_conversation_message.call_count == 2 # user and assistant message
    mock_predictive_agent.return_value.process_user_input_async.assert_called_once()
    assert mock_websocket.send_text.call_count == 2 # thinking and chat_response
    assert manager.user_sessions[session_id]["conversation_id"] == "conv_test_id"

@pytest.mark.asyncio
async def test_handle_chat_message_existing_conversation(mock_websocket, mock_predictive_agent, mock_db_manager_ws, mock_context_manager, mock_logging_ws):
    """测试处理聊天消息 - 现有对话"""
    manager = ConnectionManager()
    session_id = "test_session_8"
    user_id = "user8"
    existing_conv_id = "existing_conv_id_8"
    manager.user_sessions[session_id] = {"conversation_id": existing_conv_id, "user_id": user_id, "state": {}}
    
    message_data = {"type": "chat", "data": {"message": "继续预���", "project_id": "proj8"}}
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_db_manager_ws.return_value.create_conversation.assert_not_called() # 不应创建新对话
    assert mock_db_manager_ws.return_value.add_conversation_message.call_count == 2 # user and assistant message
    mock_predictive_agent.return_value.process_user_input_async.assert_called_once()
    assert mock_websocket.send_text.call_count == 2 # thinking and chat_response
    assert manager.user_sessions[session_id]["conversation_id"] == existing_conv_id

@pytest.mark.asyncio
async def test_handle_api_call_message_train_algorithm(mock_websocket, mock_algo_platform_service, mock_logging_ws):
    """测试处理API调用消息 - 训练算法"""
    manager = ConnectionManager()
    session_id = "test_session_9"
    manager.active_connections[session_id] = mock_websocket
    
    message_data = {
        "type": "api_call",
        "data": {
            "action": "train_algorithm",
            "project_id": "proj9",
            "parameters": {"parameter1": "algo_name", "parameter2": "LSTM"}
        }
    }
    
    mock_algo_platform_service.return_value.train_algorithm.return_value = MagicMock(dict=lambda: {"message": "训练启动成功"})
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_algo_platform_service.return_value.train_algorithm.assert_called_once()
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "api_response"
    assert response["data"]["action"] == "train_algorithm"
    assert response["data"]["status"] == "success"
    assert response["data"]["result"]["message"] == "训练启动成功"

@pytest.mark.asyncio
async def test_handle_switch_conversation(mock_websocket, mock_db_manager_ws, mock_context_manager, mock_logging_ws):
    """测试处理切换对话请求"""
    manager = ConnectionManager()
    session_id = "test_session_10"
    user_id = "user10"
    manager.user_sessions[session_id] = {"conversation_id": "old_conv", "user_id": user_id, "state": {}}
    manager.active_connections[session_id] = mock_websocket
    
    new_conversation_id = "new_conv_id"
    message_data = {"type": "switch_conversation", "data": {"conversation_id": new_conversation_id}}
    
    # Mock restore_conversation
    with patch.object(manager, 'restore_conversation', new_callable=AsyncMock, return_value=True) as mock_restore_conv:
        await manager.handle_message(mock_websocket, session_id, message_data)
        
        mock_restore_conv.assert_called_once_with(session_id, new_conversation_id)
        mock_websocket.send_text.assert_called_once()
        args, kwargs = mock_websocket.send_text.call_args
        response = json.loads(args[0])
        assert response["type"] == "conversation_switched"
        assert response["data"]["conversation_id"] == new_conversation_id

@pytest.mark.asyncio
async def test_handle_new_conversation(mock_websocket, mock_db_manager_ws, mock_logging_ws):
    """测试处理新建对话请求"""
    manager = ConnectionManager()
    session_id = "test_session_11"
    user_id = "user11"
    manager.user_sessions[session_id] = {"conversation_id": "old_conv", "user_id": user_id, "state": {}}
    manager.active_connections[session_id] = mock_websocket
    
    message_data = {"type": "new_conversation", "data": {"title": "新的对话"}}
    
    # Mock _send_welcome_message_for_conversation
    with patch.object(manager, '_send_welcome_message_for_conversation', new_callable=AsyncMock) as mock_send_welcome:
        await manager.handle_message(mock_websocket, session_id, message_data)
        
        mock_db_manager_ws.return_value.create_conversation.assert_called_once_with(
            user_id=user_id, session_id=session_id, title="新的对话"
        )
        mock_websocket.send_text.assert_called_once() # new_conversation_created response
        mock_send_welcome.assert_called_once_with(session_id, "conv_test_id")
        assert manager.user_sessions[session_id]["conversation_id"] == "conv_test_id"
        assert manager.user_sessions[session_id]["state"] == {} # 状态被重置

@pytest.mark.asyncio
async def test_handle_get_conversations(mock_websocket, mock_db_manager_ws, mock_logging_ws):
    """测试处理获取对话列表请求"""
    manager = ConnectionManager()
    session_id = "test_session_12"
    user_id = "user12"
    manager.user_sessions[session_id] = {"user_id": user_id}
    manager.active_connections[session_id] = mock_websocket

    mock_db_manager_ws.return_value.get_user_conversations.return_value = [
        {
            "conversation_id": "conv_a",
            "title": "Conv A",
            "status": "active",
            "created_at": datetime.now(),
            "last_message_at": datetime.now(),
            "message_count": 5,
            "last_user_message": "Hi"
        }
    ]
    
    message_data = {"type": "get_conversations", "data": {"user_id": user_id}}
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_db_manager_ws.return_value.get_user_conversations.assert_called_once_with(
        user_id=user_id, limit=20, offset=0
    )
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "conversations_list"
    assert len(response["data"]["conversations"]) == 1
    assert response["data"]["conversations"][0]["title"] == "Conv A"

@pytest.mark.asyncio
async def test_handle_ping(mock_websocket, mock_logging_ws):
    """测试处理心跳消息"""
    manager = ConnectionManager()
    session_id = "test_session_13"
    manager.active_connections[session_id] = mock_websocket
    
    message_data = {"type": "ping", "data": {}}
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "pong"

@pytest.mark.asyncio
async def test_send_error_response(mock_websocket, mock_logging_ws):
    """测试发送错误响应"""
    manager = ConnectionManager()
    session_id = "test_session_14"
    manager.active_connections[session_id] = mock_websocket
    
    error_message = "Something went wrong"
    await manager.send_error_response(session_id, error_message)
    
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "error"
    assert response["data"]["error"] == error_message
    assert response["status"] == "error"

def test_get_connection_stats(mock_logging_ws):
    """测试获取连接统计信息"""
    manager = ConnectionManager()
    session_id_1 = "test_session_15_1"
    session_id_2 = "test_session_15_2"
    
    manager.active_connections[session_id_1] = AsyncMock()
    manager.active_connections[session_id_2] = AsyncMock()
    manager.user_sessions[session_id_1] = {"connected_at": datetime.now().isoformat(), "message_count": 5, "task_id": "task1"}
    manager.user_sessions[session_id_2] = {"connected_at": datetime.now().isoformat(), "message_count": 10, "task_id": None}
    
    stats = manager.get_connection_stats()
    
    assert stats["active_connections"] == 2
    assert stats["total_sessions"] == 2
    assert stats["sessions"][session_id_1]["message_count"] == 5
    assert stats["sessions"][session_id_1]["has_task"] is True
    assert stats["sessions"][session_id_2]["has_task"] is False

@pytest.mark.asyncio
async def test_restore_conversation_success(mock_websocket, mock_db_manager_ws, mock_context_manager, mock_logging_ws):
    """测试恢复对话成功"""
    manager = ConnectionManager()
    session_id = "test_session_16"
    user_id = "user16"
    conversation_id = "restored_conv_id"
    manager.user_sessions[session_id] = {"user_id": user_id, "state": {}}
    manager.active_connections[session_id] = mock_websocket

    mock_db_manager_ws.return_value.get_conversation_messages_with_context.return_value = [
        {"message_type": "user", "content": "Hi", "timestamp": datetime.now(), "message_sequence": 1}
    ]
    mock_context_manager.return_value.rebuild_agent_state.return_value = PredictiveAgentState(conversation_stage="restored")

    success = await manager.restore_conversation(session_id, conversation_id)
    assert success is True
    assert manager.user_sessions[session_id]["conversation_id"] == conversation_id
    assert manager.user_sessions[session_id]["state"]["conversation_stage"] == "restored"
    mock_db_manager_ws.return_value.get_conversation_messages_with_context.assert_called_once_with(conversation_id)
    mock_context_manager.return_value.rebuild_agent_state.assert_called_once_with(conversation_id)
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "conversation_restored"
    assert response["data"]["conversation_id"] == conversation_id

@pytest.mark.asyncio
async def test_handle_progress_request_success(mock_websocket, mock_logging_ws):
    """测试处理进度查询请求成功"""
    manager = ConnectionManager()
    session_id = "test_session_17"
    manager.active_connections[session_id] = mock_websocket
    
    message_data = {"type": "get_progress", "data": {"task_id": "task_progress_1"}}
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "progress_update"
    assert response["data"]["task_id"] == "task_progress_1"
    assert response["data"]["progress"] == 75 # Mocked value

@pytest.mark.asyncio
async def test_handle_report_request_success(mock_websocket, mock_predictive_agent, mock_logging_ws):
    """测试处理报告生成请求成功"""
    manager = ConnectionManager()
    session_id = "test_session_18"
    manager.active_connections[session_id] = mock_websocket
    manager.user_sessions[session_id] = {"state": {"current_params": {"algo": "LSTM"}}}
    
    message_data = {"type": "generate_report", "data": {"task_id": "task_report_1"}}
    
    mock_predictive_agent.return_value.generate_report.return_value = "<html>Report</html>"
    mock_predictive_agent.return_value.get_report_pdf.return_value = "/tmp/report.pdf"
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_predictive_agent.return_value.generate_report.assert_called_once_with("task_report_1", {"algo": "LSTM"})
    assert mock_predictive_agent.return_value.get_report_pdf.assert_called_once_with("task_report_1", {"algo": "LSTM"})
    assert mock_websocket.send_text.call_count == 2 # generating_report and report_generated
    args, kwargs = mock_websocket.send_text.call_args_list[1]
    response = json.loads(args[0])
    assert response["type"] == "report_generated"
    assert response["data"]["pdf_path"] == "/tmp/report.pdf"

@pytest.mark.asyncio
async def test_handle_history_request_success(mock_websocket, mock_db_manager_ws, mock_logging_ws):
    """测试处理历史记录请求成功"""
    manager = ConnectionManager()
    session_id = "test_session_19"
    user_id = "user19"
    manager.user_sessions[session_id] = {"user_id": user_id}
    manager.active_connections[session_id] = mock_websocket

    mock_db_manager_ws.return_value.get_conversations.return_value = [
        {"conversation_id": "conv_h1", "title": "History 1"}
    ]
    
    message_data = {"type": "get_history", "data": {"user_id": user_id}}
    
    await manager.handle_message(mock_websocket, session_id, message_data)
    
    mock_db_manager_ws.return_value.get_conversations.assert_called_once_with(user_id=user_id, limit=10)
    mock_websocket.send_text.assert_called_once()
    args, kwargs = mock_websocket.send_text.call_args
    response = json.loads(args[0])
    assert response["type"] == "history_data"
    assert len(response["data"]["conversations"]) == 1
    assert response["data"]["conversations"][0]["title"] == "History 1"
