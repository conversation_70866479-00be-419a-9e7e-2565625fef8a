#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数推荐服务测试 - 专注于提高代码覆盖率
测试services/parameter_recommendation_service.py中的主要类和方法
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import statistics

# 导入待测试的模块
from agents.predict_algorith_agent.services.parameter_recommendation_service import (
    ParameterRecommendationService,
    parameter_recommendation_service
)
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    ParameterRecommendationRequest,
    ParameterRecommendationResponse,
    ParameterRecommendation,
    MultiLLMRecommendationResponse,
    LLMProvider,
    DataScale,
    PerformanceObjective,
    ParameterOptimizationSuggestion,
    ParameterRecommendationHistory,
    ALGORITHM_PARAMETER_TEMPLATES
)

# Mock配置
mock_config_param = {
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model",
        "api_key": "mock_api_key"
    },
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db"
}

@pytest.fixture(autouse=True)
def mock_agents_config_param():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config_param):
        yield

@pytest.fixture(autouse=True)
def mock_logging_param():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.services.parameter_recommendation_service.logger'):
        yield

@pytest.fixture
def mock_llm_provider_manager():
    """Mock LLM提供者管理器"""
    with patch('agents.predict_algorith_agent.services.parameter_recommendation_service.llm_provider_manager') as mock_manager:
        mock_manager.get_parameter_recommendation = AsyncMock()
        mock_manager.get_multi_llm_recommendations = AsyncMock()
        mock_manager.get_available_providers = MagicMock(return_value=[LLMProvider.QWEN, LLMProvider.DEEPSEEK])
        yield mock_manager

class TestParameterRecommendationService:
    """测试ParameterRecommendationService类的主要方法"""
    
    def test_service_initialization(self, mock_llm_provider_manager):
        """测试服务初始化"""
        service = ParameterRecommendationService()
        
        # 验证初始化
        assert service is not None
        assert hasattr(service, 'llm_manager')
        assert hasattr(service, 'recommendation_history')
        assert isinstance(service.recommendation_history, list)

    @pytest.mark.asyncio
    async def test_get_parameter_recommendation_success(self, mock_llm_provider_manager):
        """测试获取参数推荐成功"""
        service = ParameterRecommendationService()
        
        # 配置Mock返回值
        mock_response = ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="learning_rate",
                    recommended_value=0.001,
                    confidence=0.9,
                    reasoning="适合中等规模数据",
                    impact_description="影响收敛速度"
                )
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=1.5
        )
        mock_llm_provider_manager.get_parameter_recommendation.return_value = mock_response
        
        # 调用方法
        result = await service.get_parameter_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            performance_objective=PerformanceObjective.ACCURACY
        )
        
        # 验证结果
        assert isinstance(result, ParameterRecommendationResponse)
        assert result.algorithm_type == "LSTM"
        assert len(result.recommended_parameters) == 1
        assert result.overall_confidence == 0.9
        
        # 验证LLM管理器被调用
        mock_llm_provider_manager.get_parameter_recommendation.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_parameter_recommendation_with_user_id(self, mock_llm_provider_manager):
        """测试带用户ID的参数推荐"""
        service = ParameterRecommendationService()
        
        # 配置Mock返回值
        mock_response = ParameterRecommendationResponse(
            algorithm_type="CNN",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="batch_size",
                    recommended_value=64,
                    confidence=0.85,
                    reasoning="适合图像处理",
                    impact_description="影响训练稳定性"
                )
            ],
            overall_confidence=0.85,
            llm_provider=LLMProvider.DEEPSEEK,
            model_name="deepseek-chat",
            generation_time=2.0
        )
        mock_llm_provider_manager.get_parameter_recommendation.return_value = mock_response
        
        # 调用方法
        result = await service.get_parameter_recommendation(
            algorithm_type="CNN",
            data_scale=DataScale.LARGE,
            user_id="test_user_123"
        )
        
        # 验证结果
        assert isinstance(result, ParameterRecommendationResponse)
        assert result.algorithm_type == "CNN"
        
        # 验证历史记录被保存（通过检查内部状态）
        # 注意：这里我们假设_save_recommendation_history方法会被调用
        # 实际测试中可能需要Mock这个方法

    @pytest.mark.asyncio
    async def test_get_multi_llm_recommendation_success(self, mock_llm_provider_manager):
        """测试获取多LLM推荐成功"""
        service = ParameterRecommendationService()
        
        # 配置Mock返回值
        mock_responses = [
            ParameterRecommendationResponse(
                algorithm_type="LSTM",
                recommended_parameters=[
                    ParameterRecommendation(
                        parameter_name="learning_rate",
                        recommended_value=0.001,
                        confidence=0.9,
                        reasoning="QWEN推荐",
                        impact_description="QWEN分析"
                    )
                ],
                overall_confidence=0.9,
                llm_provider=LLMProvider.QWEN,
                model_name="qwen-turbo",
                generation_time=1.5
            ),
            ParameterRecommendationResponse(
                algorithm_type="LSTM",
                recommended_parameters=[
                    ParameterRecommendation(
                        parameter_name="learning_rate",
                        recommended_value=0.002,
                        confidence=0.85,
                        reasoning="DEEPSEEK推荐",
                        impact_description="DEEPSEEK分析"
                    )
                ],
                overall_confidence=0.85,
                llm_provider=LLMProvider.DEEPSEEK,
                model_name="deepseek-chat",
                generation_time=2.0
            )
        ]
        mock_llm_provider_manager.get_multi_llm_recommendations.return_value = mock_responses
        
        # 调用方法
        result = await service.get_multi_llm_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            providers=[LLMProvider.QWEN, LLMProvider.DEEPSEEK]
        )
        
        # 验证结果
        assert isinstance(result, MultiLLMRecommendationResponse)
        assert len(result.recommendations) == 2
        assert result.algorithm_type == "LSTM"
        
        # 验证LLM管理器被调用
        mock_llm_provider_manager.get_multi_llm_recommendations.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_multi_llm_recommendation_no_providers(self, mock_llm_provider_manager):
        """测试没有可用提供者时的多LLM推荐"""
        service = ParameterRecommendationService()
        
        # 配置Mock返回空的提供者列表
        mock_llm_provider_manager.get_available_providers.return_value = []
        
        # 调用方法
        result = await service.get_multi_llm_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM
        )
        
        # 验证结果
        assert isinstance(result, MultiLLMRecommendationResponse)
        assert len(result.recommendations) == 0
        assert result.algorithm_type == "LSTM"

    @pytest.mark.asyncio
    async def test_service_error_handling(self, mock_llm_provider_manager):
        """测试服务错误处理"""
        service = ParameterRecommendationService()

        # Mock LLM管理器抛出异常
        mock_llm_provider_manager.get_parameter_recommendation.side_effect = Exception("Mock error")

        # 验证异常被正确抛出
        with pytest.raises(Exception, match="Mock error"):
            await service.get_parameter_recommendation(
                algorithm_type="LSTM",
                data_scale=DataScale.MEDIUM
            )

    def test_service_initialization_attributes(self, mock_llm_provider_manager):
        """测试服务初始化的属性"""
        service = ParameterRecommendationService()

        # 验证初始化属性
        assert hasattr(service, 'llm_manager')
        assert hasattr(service, 'recommendation_history')
        assert isinstance(service.recommendation_history, list)
        assert len(service.recommendation_history) == 0

    def test_validate_parameter_value_in_range(self, mock_llm_provider_manager):
        """测试参数值在范围内的验证"""
        service = ParameterRecommendationService()
        
        # 测试在范围内的值
        result = service._validate_parameter_value(0.001, {"min": 0.0001, "max": 0.1})

        # 验证结果
        assert result == 0.001  # 值应该保持不变

    def test_validate_parameter_value_out_of_range(self, mock_llm_provider_manager):
        """测试参数值超出范围的验证"""
        service = ParameterRecommendationService()
        
        # 测试超出最大值的情况
        result = service._validate_parameter_value(0.5, {"min": 0.0001, "max": 0.1})

        # 验证结果
        assert result == 0.1  # 值应该被限制为最大值

        # 测试低于最小值的情况
        result = service._validate_parameter_value(0.00001, {"min": 0.0001, "max": 0.1})

        # 验证结果
        assert result == 0.0001  # 值应该被限制为最小值

    def test_post_process_recommendation(self, mock_llm_provider_manager):
        """测试推荐结果后处理"""
        service = ParameterRecommendationService()
        
        # 创建原始推荐响应
        original_response = ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="learning_rate",
                    recommended_value=0.5,  # 超出合理范围
                    confidence=0.9,
                    reasoning="测试推荐",
                    impact_description="测试影响"
                )
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=1.5
        )
        
        # 创建请求
        request = ParameterRecommendationRequest(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM
        )
        
        # 调用后处理方法
        result = service._post_process_recommendation(original_response, request)
        
        # 验证结果
        assert isinstance(result, ParameterRecommendationResponse)
        assert result.algorithm_type == "LSTM"
        # 验证参数值被调整到合理范围内
        if len(result.recommended_parameters) > 0:
            lr_param = next((p for p in result.recommended_parameters if p.parameter_name == "learning_rate"), None)
            if lr_param:
                assert lr_param.recommended_value <= 0.1  # 应该被限制

    def test_get_optimization_suggestions(self, mock_llm_provider_manager):
        """测试获取优化建议"""
        service = ParameterRecommendationService()
        
        # 准备当前参数
        current_params = {
            "learning_rate": 0.01,
            "batch_size": 32,
            "epochs": 100
        }
        
        # 准备性能反馈
        performance_feedback = {
            "loss_trend": "increasing",
            "accuracy": 0.85
        }
        
        # 调用方法
        suggestions = service.get_optimization_suggestions(
            current_params=current_params,
            algorithm_type="LSTM",
            performance_feedback=performance_feedback
        )
        
        # 验证结果
        assert isinstance(suggestions, list)
        # 如果有LSTM模板，应该有建议
        if "LSTM" in ALGORITHM_PARAMETER_TEMPLATES:
            # 可能会有针对learning_rate的建议
            lr_suggestions = [s for s in suggestions if s.parameter_name == "learning_rate"]
            if lr_suggestions:
                assert len(lr_suggestions) > 0

    def test_generate_parameter_suggestion(self, mock_llm_provider_manager):
        """测试生成单个参数建议"""
        service = ParameterRecommendationService()
        
        # 准备性能反馈
        performance_feedback = {
            "loss_trend": "increasing"
        }
        
        # 调用方法（测试learning_rate参数）
        suggestion = service._generate_parameter_suggestion(
            param_name="learning_rate",
            current_value=0.01,
            template=None,  # 简化测试
            performance_feedback=performance_feedback
        )
        
        # 验证结果
        if suggestion:
            assert isinstance(suggestion, ParameterOptimizationSuggestion)
            assert suggestion.parameter_name == "learning_rate"
            assert suggestion.current_value == 0.01
            assert suggestion.suggested_value < 0.01  # 应该建议降低学习率

class TestParameterRecommendationServiceGlobal:
    """测试全局参数推荐服务实例"""
    
    def test_global_service_instance(self):
        """测试全局服务实例"""
        # 验证全局实例存在
        assert parameter_recommendation_service is not None
        assert isinstance(parameter_recommendation_service, ParameterRecommendationService)

class TestParameterRecommendationServiceIntegration:
    """测试参数推荐服务的集成功能"""

    @pytest.mark.asyncio
    async def test_full_recommendation_workflow(self, mock_llm_provider_manager):
        """测试完整的推荐工作流程"""
        service = ParameterRecommendationService()

        # 配置完整的Mock链
        mock_response = ParameterRecommendationResponse(
            algorithm_type="CNN",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="learning_rate",
                    recommended_value=0.001,
                    confidence=0.9,
                    reasoning="适合图像分类任务",
                    impact_description="控制训练收敛速度"
                ),
                ParameterRecommendation(
                    parameter_name="batch_size",
                    recommended_value=64,
                    confidence=0.85,
                    reasoning="平衡内存使用和训练效率",
                    impact_description="影响梯度估计质量"
                )
            ],
            overall_confidence=0.875,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=2.5
        )
        mock_llm_provider_manager.get_parameter_recommendation.return_value = mock_response

        # 执行完整的推荐流程
        result = await service.get_parameter_recommendation(
            algorithm_type="CNN",
            data_scale=DataScale.LARGE,
            data_features={"image_size": "224x224", "channels": 3},
            performance_objective=PerformanceObjective.ACCURACY,
            constraints={"memory_limit": "8GB"},
            current_params={"learning_rate": 0.01},
            user_preferences={"fast_training": True},
            context="图像分类任务，需要高精度",
            provider=LLMProvider.QWEN,
            user_id="test_user_456"
        )

        # 验证完整流程结果
        assert isinstance(result, ParameterRecommendationResponse)
        assert result.algorithm_type == "CNN"
        assert len(result.recommended_parameters) == 2
        assert result.overall_confidence > 0.8

        # 验证所有参数都被正确传递
        call_args = mock_llm_provider_manager.get_parameter_recommendation.call_args
        assert call_args is not None
        request_arg = call_args[0][0]  # 第一个参数应该是ParameterRecommendationRequest
        assert isinstance(request_arg, ParameterRecommendationRequest)
        assert request_arg.algorithm_type == "CNN"
        assert request_arg.data_scale == DataScale.LARGE

class TestParameterRecommendationServiceCoverage:
    """专门用于提升代码覆盖率的测试类"""

    def test_save_recommendation_history_method(self, mock_llm_provider_manager):
        """测试保存推荐历史的私有方法"""
        service = ParameterRecommendationService()

        # 创建测试数据
        request = ParameterRecommendationRequest(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM
        )
        response = ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[],
            overall_confidence=0.8,
            llm_provider=LLMProvider.QWEN,
            model_name="test-model",
            generation_time=1.0
        )

        # 调用私有方法
        initial_count = len(service.recommendation_history)
        service._save_recommendation_history(request, response, "test_user")

        # 验证历史记录被保存
        assert len(service.recommendation_history) == initial_count + 1
        assert service.recommendation_history[-1].user_id == "test_user"
        assert service.recommendation_history[-1].algorithm_type == "LSTM"
