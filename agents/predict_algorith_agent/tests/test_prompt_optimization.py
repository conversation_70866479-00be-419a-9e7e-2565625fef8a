#!/usr/bin/env python3
"""
提示词优化效果测试脚本
测试修改后的算法分类、参数提取、交互分类提示词的效果
"""

import sys
import os
import asyncio
import json
import pytest
from typing import Dict, List, Any, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '../../../..'))
sys.path.insert(0, project_root)

from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
from agents.predict_algorith_agent.models.predictive_models import AlgorithmType, InteractionType

class PromptOptimizationTester:
    """提示词优化效果测试器"""
    
    def __init__(self):
        self.assistant = PredictiveAlgorithmAssistant()
        self.test_results = {
            "algorithm_classification": [],
            "parameter_extraction": [],
            "interaction_classification": []
        }
        
    def create_algorithm_test_cases(self) -> List[Dict[str, Any]]:
        """创建算法分类测试用例"""
        return [
            # LSTM测试用例
            {
                "input": "我想用LSTM预测股票价格走势",
                "expected": "LSTM",
                "description": "明确LSTM + 时序预测"
            },
            {
                "input": "需要分析时间序列数据，用长短期记忆网络",
                "expected": "LSTM",
                "description": "时序数据 + LSTM全称"
            },
            {
                "input": "销售数据预测，按月份的历史数据",
                "expected": "LSTM",
                "description": "隐含时序预测场景"
            },
            
            # CNN测试用例
            {
                "input": "识别图片中的猫和狗",
                "expected": "CNN",
                "description": "图像分类任务"
            },
            {
                "input": "医学影像诊断，X光片分析",
                "expected": "CNN",
                "description": "医学图像处理"
            },
            {
                "input": "卷积神经网络处理照片",
                "expected": "CNN",
                "description": "明确CNN + 图像"
            },
            
            # RNN测试用例
            {
                "input": "文本情感分析，处理用户评论序列",
                "expected": "RNN",
                "description": "文本序列处理"
            },
            {
                "input": "语音识别，循环神经网络",
                "expected": "RNN",
                "description": "明确RNN + 语音"
            },
            
            # 线性回归测试用例
            {
                "input": "预测房价，有面积、位置等特征",
                "expected": "线性回归",
                "description": "简单回归预测"
            },
            {
                "input": "根据温度预测用电量，线性关系",
                "expected": "线性回归",
                "description": "明确线性关系"
            },
            
            # 逻辑回归测试用例
            {
                "input": "判断邮件是否为垃圾邮件",
                "expected": "逻辑回归",
                "description": "二分类问题"
            },
            {
                "input": "用户是否会购买产品，分类预测",
                "expected": "逻辑回归",
                "description": "分类预测任务"
            },
            
            # 随机森林测试用例
            {
                "input": "客户流失预测，数据复杂，特征很多",
                "expected": "随机森林",
                "description": "复杂多特征任务"
            },
            {
                "input": "信用评分，多个维度的数据",
                "expected": "随机森林",
                "description": "多维度复杂任务"
            },
            
            # SVM测试用例
            {
                "input": "文档分类，需要高精度",
                "expected": "SVM",
                "description": "高精度分类"
            },
            {
                "input": "支持向量机做分类",
                "expected": "SVM",
                "description": "明确SVM"
            },
            
            # 其他算法测试用例
            {
                "input": "用深度强化学习训练游戏AI",
                "expected": "其他",
                "description": "不支持的算法类型"
            },
            {
                "input": "聚类分析，K-means算法",
                "expected": "其他",
                "description": "聚类算法"
            },
            
            # 边界情况测试
            {
                "input": "我想做机器学习",
                "expected": "其他",
                "description": "模糊描述"
            },
            {
                "input": "图像和文本混合处理",
                "expected": "其他",
                "description": "混合任务"
            }
        ]
    
    def create_parameter_test_cases(self) -> List[Dict[str, Any]]:
        """创建参数提取测试用例"""
        return [
            # LSTM参数测试
            {
                "input": "LSTM模型，学习率0.001，批次大小32，隐藏层128，训练100轮",
                "expected_params": {
                    "learning_rate": 0.001,
                    "batch_size": 32,
                    "hidden_size": 128,
                    "epochs": 100
                },
                "expected_missing": [],
                "description": "完整LSTM参数"
            },
            {
                "input": "用LSTM预测，序列长度60，双向网络",
                "expected_params": {
                    "sequence_length": 60,
                    "bidirectional": True
                },
                "expected_missing": ["learning_rate", "batch_size", "epochs"],
                "description": "部分LSTM特定参数"
            },
            
            # CNN参数测试
            {
                "input": "CNN模型，32个卷积核，核大小3x3，池化大小2",
                "expected_params": {
                    "num_filters": 32,
                    "kernel_size": 3,
                    "pool_size": 2
                },
                "expected_missing": ["learning_rate", "batch_size", "epochs"],
                "description": "CNN特定参数"
            },
            
            # 混合参数测试
            {
                "input": "学习率0.01，Adam优化器，dropout 0.2，训练200轮",
                "expected_params": {
                    "learning_rate": 0.01,
                    "optimizer": "Adam",
                    "dropout": 0.2,
                    "epochs": 200
                },
                "expected_missing": ["batch_size"],
                "description": "通用参数组合"
            },
            
            # 边界情况
            {
                "input": "我想训练一个模型",
                "expected_params": {},
                "expected_missing": ["learning_rate", "batch_size", "epochs"],
                "description": "无具体参数"
            }
        ]
    
    def create_interaction_test_cases(self) -> List[Dict[str, Any]]:
        """创建交互分类测试用例"""
        return [
            # 技术咨询类
            {
                "input": "如何选择合适的学习率？",
                "expected": "一般咨询",
                "description": "技术问题咨询"
            },
            {
                "input": "什么是过拟合？怎么解决？",
                "expected": "一般咨询",
                "description": "概念解释请求"
            },
            {
                "input": "LSTM和CNN有什么区别？",
                "expected": "一般咨询",
                "description": "算法对比咨询"
            },
            
            # 确认类
            {
                "input": "确认这些参数",
                "expected": "确认",
                "description": "明确确认"
            },
            {
                "input": "好的，开始训练",
                "expected": "确认",
                "description": "同意操作"
            },
            {
                "input": "OK，可以",
                "expected": "确认",
                "description": "英文确认"
            },
            
            # 调整类
            {
                "input": "把学习率改成0.01",
                "expected": "调整",
                "description": "参数调整"
            },
            {
                "input": "调整批次大小为64",
                "expected": "调整",
                "description": "明确调整请求"
            },
            
            # 查询类
            {
                "input": "训练进度如何？",
                "expected": "查询进度",
                "description": "进度查询"
            },
            {
                "input": "查看结果",
                "expected": "查询进度",
                "description": "结果查看"
            },
            {
                "input": "生成报告",
                "expected": "查询进度",
                "description": "报告生成"
            },
            
            # 取消类
            {
                "input": "取消训练",
                "expected": "取消",
                "description": "明确取消"
            },
            {
                "input": "不要了",
                "expected": "取消",
                "description": "拒绝操作"
            }
        ]

    async def test_algorithm_classification(self) -> Dict[str, Any]:
        """测试算法分类效果"""
        print("🧪 开始测试算法分类...")
        test_cases = self.create_algorithm_test_cases()
        results = []
        correct_count = 0

        for i, case in enumerate(test_cases):
            print(f"  测试用例 {i+1}/{len(test_cases)}: {case['description']}")
            try:
                result = await self.assistant.classify_algorithm_async(case["input"])
                predicted = result["output"].algorithm_type.value if hasattr(result["output"].algorithm_type, 'value') else str(result["output"].algorithm_type)
                expected = case["expected"]

                is_correct = predicted == expected
                if is_correct:
                    correct_count += 1

                test_result = {
                    "input": case["input"],
                    "expected": expected,
                    "predicted": predicted,
                    "confidence": result["output"].confidence,
                    "reason": result["output"].reason,
                    "correct": is_correct,
                    "description": case["description"]
                }
                results.append(test_result)

                status = "✅" if is_correct else "❌"
                print(f"    {status} 预期: {expected}, 实际: {predicted}, 置信度: {result['output'].confidence:.2f}")

            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
                results.append({
                    "input": case["input"],
                    "expected": case["expected"],
                    "predicted": "ERROR",
                    "confidence": 0.0,
                    "reason": str(e),
                    "correct": False,
                    "description": case["description"]
                })

        accuracy = correct_count / len(test_cases) if test_cases else 0
        print(f"📊 算法分类准确率: {accuracy:.2%} ({correct_count}/{len(test_cases)})")

        return {
            "accuracy": accuracy,
            "correct_count": correct_count,
            "total_count": len(test_cases),
            "results": results
        }

    async def test_parameter_extraction(self) -> Dict[str, Any]:
        """测试参数提取效果"""
        print("🧪 开始测试参数提取...")
        test_cases = self.create_parameter_test_cases()
        results = []
        param_accuracy_scores = []
        missing_accuracy_scores = []

        for i, case in enumerate(test_cases):
            print(f"  测试用例 {i+1}/{len(test_cases)}: {case['description']}")
            try:
                result = await self.assistant.extract_parameters_async(case["input"])
                extracted = result["output"].extracted_params
                missing = result["output"].missing_params

                # 计算参数提取准确性
                expected_params = case["expected_params"]
                param_matches = 0
                param_total = len(expected_params)

                for key, expected_value in expected_params.items():
                    if key in extracted:
                        if str(extracted[key]).lower() == str(expected_value).lower():
                            param_matches += 1

                param_accuracy = param_matches / param_total if param_total > 0 else 1.0
                param_accuracy_scores.append(param_accuracy)

                # 计算缺失参数识别准确性
                expected_missing = set(case["expected_missing"])
                actual_missing = set(missing)
                missing_intersection = len(expected_missing.intersection(actual_missing))
                missing_union = len(expected_missing.union(actual_missing))
                missing_accuracy = missing_intersection / missing_union if missing_union > 0 else 1.0
                missing_accuracy_scores.append(missing_accuracy)

                test_result = {
                    "input": case["input"],
                    "expected_params": expected_params,
                    "extracted_params": extracted,
                    "expected_missing": list(expected_missing),
                    "actual_missing": missing,
                    "param_accuracy": param_accuracy,
                    "missing_accuracy": missing_accuracy,
                    "confidence": result["output"].confidence,
                    "notes": result["output"].notes,
                    "description": case["description"]
                }
                results.append(test_result)

                print(f"    📊 参数准确率: {param_accuracy:.2%}, 缺失识别: {missing_accuracy:.2%}")

            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
                results.append({
                    "input": case["input"],
                    "expected_params": case["expected_params"],
                    "extracted_params": {},
                    "expected_missing": case["expected_missing"],
                    "actual_missing": [],
                    "param_accuracy": 0.0,
                    "missing_accuracy": 0.0,
                    "confidence": 0.0,
                    "notes": str(e),
                    "description": case["description"]
                })
                param_accuracy_scores.append(0.0)
                missing_accuracy_scores.append(0.0)

        avg_param_accuracy = sum(param_accuracy_scores) / len(param_accuracy_scores) if param_accuracy_scores else 0
        avg_missing_accuracy = sum(missing_accuracy_scores) / len(missing_accuracy_scores) if missing_accuracy_scores else 0

        print(f"📊 参数提取平均准确率: {avg_param_accuracy:.2%}")
        print(f"📊 缺失参数识别准确率: {avg_missing_accuracy:.2%}")

        return {
            "param_accuracy": avg_param_accuracy,
            "missing_accuracy": avg_missing_accuracy,
            "total_count": len(test_cases),
            "results": results
        }

    async def test_interaction_classification(self) -> Dict[str, Any]:
        """测试交互分类效果"""
        print("🧪 开始测试交互分类...")
        test_cases = self.create_interaction_test_cases()
        results = []
        correct_count = 0

        for i, case in enumerate(test_cases):
            print(f"  测试用例 {i+1}/{len(test_cases)}: {case['description']}")
            try:
                result = await self.assistant.classify_interaction_async(case["input"])
                predicted = result["output"].interaction_type.value if hasattr(result["output"].interaction_type, 'value') else str(result["output"].interaction_type)
                expected = case["expected"]

                is_correct = predicted == expected
                if is_correct:
                    correct_count += 1

                test_result = {
                    "input": case["input"],
                    "expected": expected,
                    "predicted": predicted,
                    "confidence": result["output"].confidence,
                    "reason": result["output"].reason,
                    "correct": is_correct,
                    "description": case["description"]
                }
                results.append(test_result)

                status = "✅" if is_correct else "❌"
                print(f"    {status} 预期: {expected}, 实际: {predicted}, 置信度: {result['output'].confidence:.2f}")

            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
                results.append({
                    "input": case["input"],
                    "expected": case["expected"],
                    "predicted": "ERROR",
                    "confidence": 0.0,
                    "reason": str(e),
                    "correct": False,
                    "description": case["description"]
                })

        accuracy = correct_count / len(test_cases) if test_cases else 0
        print(f"📊 交互分类准确率: {accuracy:.2%} ({correct_count}/{len(test_cases)})")

        return {
            "accuracy": accuracy,
            "correct_count": correct_count,
            "total_count": len(test_cases),
            "results": results
        }

    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始运行提示词优化效果测试")
        print("=" * 60)

        start_time = datetime.now()

        # 运行算法分类测试
        algo_results = await self.test_algorithm_classification()
        print()

        # 运行参数提取测试
        param_results = await self.test_parameter_extraction()
        print()

        # 运行交互分类测试
        interaction_results = await self.test_interaction_classification()
        print()

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 汇总结果
        summary = {
            "test_time": start_time.isoformat(),
            "duration_seconds": duration,
            "algorithm_classification": algo_results,
            "parameter_extraction": param_results,
            "interaction_classification": interaction_results,
            "overall_summary": {
                "algorithm_accuracy": algo_results["accuracy"],
                "param_extraction_accuracy": param_results["param_accuracy"],
                "missing_param_accuracy": param_results["missing_accuracy"],
                "interaction_accuracy": interaction_results["accuracy"]
            }
        }

        # 打印总结
        print("📋 测试总结")
        print("=" * 60)
        print(f"⏱️  测试耗时: {duration:.2f}秒")
        print(f"🎯 算法分类准确率: {algo_results['accuracy']:.2%}")
        print(f"🔧 参数提取准确率: {param_results['param_accuracy']:.2%}")
        print(f"🔍 缺失参数识别率: {param_results['missing_accuracy']:.2%}")
        print(f"💬 交互分类准确率: {interaction_results['accuracy']:.2%}")

        # 保存结果到文件
        result_file = f"test_results_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
        result_path = os.path.join(os.path.dirname(__file__), result_file)
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        print(f"📄 详细结果已保存到: {result_path}")

        return summary

# pytest测试函数包装器
@pytest.mark.asyncio
async def test_algorithm_classification():
    """测试算法分类功能"""
    tester = PromptOptimizationTester()
    result = await tester.test_algorithm_classification()
    assert result["accuracy"] > 0.5, f"算法分类准确率过低: {result['accuracy']}"

@pytest.mark.asyncio
async def test_parameter_extraction():
    """测试参数提取功能"""
    tester = PromptOptimizationTester()
    result = await tester.test_parameter_extraction()
    assert result["param_accuracy"] > 0.3, f"参数提取准确率过低: {result['param_accuracy']}"

@pytest.mark.asyncio
async def test_interaction_classification():
    """测试交互分类功能"""
    tester = PromptOptimizationTester()
    result = await tester.test_interaction_classification()
    assert result["accuracy"] > 0.5, f"交互分类准确率过低: {result['accuracy']}"

async def main():
    """主函数"""
    tester = PromptOptimizationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
