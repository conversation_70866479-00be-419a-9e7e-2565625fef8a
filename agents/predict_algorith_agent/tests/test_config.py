#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from datetime import datetime

# 测试数据库配置
TEST_DB_CONFIG = {
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "MYSQL_CHARSET": "utf8mb4",
}

# 测试Redis配置
TEST_REDIS_CONFIG = {
    "REDIS_HOST": "localhost",
    "REDIS_PORT": 6379,
    "REDIS_DB": 0,
    "REDIS_PASSWORD": None,
    "REDIS_DECODE_RESPONSES": True,
}

# 测试API配置
TEST_API_CONFIG = {
    "ALGORITHM_API_BASE": "http://test-api.example.com",
    "TRAINING_API_BASE": "http://test-training.example.com", 
    "HISTORY_ALGORITHM_API_BASE": "http://test-history.example.com",
}

# 测试WebSocket配置
TEST_WEBSOCKET_CONFIG = {
    "WEBSOCKET_HOST": "localhost",
    "WEBSOCKET_PORT": 8008,
    "ENABLE_WELCOME_MESSAGE": True,
}

# 测试日志配置
TEST_LOG_CONFIG = {
    "LOG_LEVEL": "ERROR",  # 减少测试时的日志输出
    "ENABLE_FILE_LOGGING": False,
    "ENABLE_COLOR_LOGGING": False,
}

# 测试用的示例数据
SAMPLE_CONVERSATION = {
    "conversation_id": "test_conv_123",
    "user_id": "test_user_456", 
    "username": "测试用户",
    "title": "测试对话",
    "created_at": datetime.now(),
    "updated_at": datetime.now(),
    "is_active": True,
    "context_summary": "测试对话摘要",
    "total_tokens": 100
}

SAMPLE_MESSAGE = {
    "id": 1,
    "conversation_id": "test_conv_123",
    "message_type": "user",
    "content": "测试消息内容",
    "message_data": {"key": "value", "test": True},
    "interaction_type": "chat",
    "message_sequence": 1,
    "key_info": None,
    "token_count": 10,
    "timestamp": datetime.now()
}

SAMPLE_ALGORITHM = {
    "algorithm_id": "test_algo_123",
    "algorithm_name": "测试LSTM算法",
    "algorithm_type": "LSTM",
    "description": "用于测试的LSTM算法",
    "parameters": {
        "learning_rate": 0.001,
        "hidden_size": 128,
        "num_layers": 2,
        "dropout": 0.1,
        "batch_size": 32
    },
    "status": "active",
    "created_at": datetime.now(),
    "updated_at": datetime.now()
}

SAMPLE_PARAMETER_RECOMMENDATION = {
    "algorithm_type": "LSTM",
    "recommended_parameters": [
        {
            "parameter_name": "learning_rate",
            "recommended_value": 0.001,
            "confidence": 0.9,
            "reasoning": "基于中等规模数据集的推荐学习率",
            "impact_description": "控制模型收敛速度和稳定性"
        },
        {
            "parameter_name": "hidden_size", 
            "recommended_value": 128,
            "confidence": 0.85,
            "reasoning": "适合时序预测任务的隐藏层大小",
            "impact_description": "影响模型的表达能力"
        }
    ],
    "overall_confidence": 0.87,
    "llm_provider": "QWEN",
    "model_name": "qwen-turbo",
    "generation_time": 0.5,
    "request_id": "test_req_123"
}

SAMPLE_API_RESPONSES = {
    "get_all_algorithms": {
        "status": "success",
        "data": {
            "algorithms": [
                {
                    "id": "algo_1",
                    "name": "LSTM预测算法",
                    "type": "LSTM",
                    "description": "用于时序预测"
                },
                {
                    "id": "algo_2", 
                    "name": "CNN分类算法",
                    "type": "CNN",
                    "description": "用于图像分类"
                }
            ],
            "total": 2
        }
    },
    "train_algorithm": {
        "status": "success",
        "data": {
            "task_id": "train_task_123",
            "algorithm_id": "algo_1",
            "status": "started",
            "message": "训练任务已启动"
        }
    },
    "get_algorithm_log": {
        "status": "success", 
        "data": {
            "logs": [
                {
                    "timestamp": "2024-01-01 10:00:00",
                    "level": "INFO",
                    "message": "开始训练"
                },
                {
                    "timestamp": "2024-01-01 10:01:00", 
                    "level": "INFO",
                    "message": "训练完成"
                }
            ]
        }
    }
}

# 测试用的错误响应
SAMPLE_ERROR_RESPONSES = {
    "network_error": {
        "error": "NetworkError",
        "message": "网络连接失败"
    },
    "timeout_error": {
        "error": "TimeoutError", 
        "message": "请求超时"
    },
    "api_error": {
        "error": "APIError",
        "message": "API调用失败",
        "status_code": 500
    }
}

# 测试环境变量
TEST_ENV_VARS = {
    "PROJECT_ROOT": os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../..")),
    "PYTHONPATH": os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../..")),
    "TESTING": "true",
    "ENVIRONMENT": "test"
}

def get_test_config():
    """获取完整的测试配置"""
    config = {}
    config.update(TEST_DB_CONFIG)
    config.update(TEST_REDIS_CONFIG)
    config.update(TEST_API_CONFIG)
    config.update(TEST_WEBSOCKET_CONFIG)
    config.update(TEST_LOG_CONFIG)
    return config

def setup_test_environment():
    """设置测试环境"""
    for key, value in TEST_ENV_VARS.items():
        os.environ[key] = str(value)
