#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试大模型配置管理系统
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

def test_config_import():
    """测试配置导入"""
    print("🧪 测试配置导入")

    try:
        import agents.config as config
        print("✅ agents.config 导入成功")

        # 测试基本配置
        if hasattr(config, 'CURRENT_LLM_PROVIDER'):
            print(f"📋 当前提供商: {config.CURRENT_LLM_PROVIDER}")
        if hasattr(config, 'LLM_PROVIDERS'):
            print(f"🌟 可用提供商: {list(config.LLM_PROVIDERS.keys())}")

        # 测试配置函数
        if hasattr(config, 'get_current_llm_config'):
            current_config = config.get_current_llm_config()
            print(f"🎯 当前模型: {current_config['model_name']}")
            print(f"🏷️ 描述: {current_config['description']}")

        assert True

    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        assert False, f"配置导入失败: {e}"

def test_predictive_agent_import():
    """测试智能体导入"""
    print("\n🧪 测试智能体导入")

    try:
        from agents.predict_algorith_agent.core.predictive_agent import llm_instance
        print("✅ predictive_agent 导入成功")
        print(f"🤖 LLM实例类型: {type(llm_instance)}")

        assert True

    except Exception as e:
        print(f"❌ 智能体导入失败: {e}")
        assert False, f"智能体导入失败: {e}"

def main():
    """主测试函数"""
    print("🚀 大模型配置管理系统测试")
    print("=" * 50)
    
    # 测试配置导入
    config_ok = test_config_import()
    
    # 测试智能体导入
    agent_ok = test_predictive_agent_import()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"🔧 配置系统: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"🤖 智能体: {'✅ 正常' if agent_ok else '❌ 异常'}")
    
    if config_ok and agent_ok:
        print("\n🎉 大模型配置管理系统工作正常!")
        print("💡 现在可以使用以下方式管理配置:")
        print("   - 修改 agents/config.py 中的 LLM_PROVIDERS")
        print("   - 使用 agents/predict_algorith_agent/switch_llm_config.py")
        print("   - 使用 agents/predict_algorith_agent/utils/llm_config_manager.py")
    else:
        print("\n❌ 配置系统存在问题，需要修复")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
