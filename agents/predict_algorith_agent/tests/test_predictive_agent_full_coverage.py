#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PredictiveAlgorithmAssistant完整方法覆盖测试
专门针对core/predictive_agent.py中PredictiveAlgorithmAssistant类的所有方法进行形式化覆盖
目标：达到该类的100%方法覆盖率，所有测试通过
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# Mock配置
mock_config_full = {
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model",
        "api_key": "mock_api_key"
    },
    "ALGORITHM_PLATFORM_BASE_URL": "http://localhost:9999",
    "WKHTMLTOPDF_PATH": "/usr/local/bin/wkhtmltopdf",
    "REPORT_PDF_DIR": "/tmp/reports",
    "SHOW_DEEPSEEK_THOUGHT": False
}

@pytest.fixture(autouse=True)
def mock_all_dependencies():
    """自动Mock所有外部依赖"""
    with patch.dict('agents.config.__dict__', mock_config_full), \
         patch('agents.predict_algorith_agent.utils.logging_config.setup_logging'), \
         patch('pymysql.connect'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.llm_instance') as mock_llm, \
         patch('agents.predict_algorith_agent.core.predictive_agent.Agent') as mock_agent, \
         patch('agents.predict_algorith_agent.core.predictive_agent.Response') as mock_response, \
         patch('agents.predict_algorith_agent.core.predictive_agent.AgentMemoryManager'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.DatabaseManager'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.history_algorithm_service'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.HistoryAlgorithmAgent'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.fallback_manager'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.parameter_recommendation_service'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.requests'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.pdfkit'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.Template'):
        
        # 配置Mock Agent
        mock_agent_instance = MagicMock()
        mock_agent_instance.run_sync.return_value = MagicMock(output=MagicMock())
        mock_agent_instance.run.return_value = MagicMock(output=MagicMock())
        mock_agent.return_value = mock_agent_instance
        
        yield mock_llm, mock_agent, mock_response

class TestPredictiveAlgorithmAssistantFullCoverage:
    """PredictiveAlgorithmAssistant类完整方法覆盖测试 - 只测试实际存在的方法"""

    def test_init(self, mock_all_dependencies):
        """测试__init__方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        assert assistant is not None
        assert hasattr(assistant, 'algorithm_classifier')
        assert hasattr(assistant, 'parameter_extractor')
        assert hasattr(assistant, 'interaction_classifier')

    def test_classify_algorithm(self, mock_all_dependencies):
        """测试classify_algorithm方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.classify_algorithm("test input")
        assert result is not None
        assert "output" in result

    @pytest.mark.asyncio
    async def test_classify_algorithm_async(self, mock_all_dependencies):
        """测试classify_algorithm_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = await assistant.classify_algorithm_async("test input")
        assert result is not None
        assert "output" in result

    def test_extract_parameters(self, mock_all_dependencies):
        """测试extract_parameters方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.extract_parameters("test input")
        assert result is not None
        assert "output" in result

    @pytest.mark.asyncio
    async def test_extract_parameters_async(self, mock_all_dependencies):
        """测试extract_parameters_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = await assistant.extract_parameters_async("test input")
        assert result is not None
        assert "output" in result

    def test_classify_interaction(self, mock_all_dependencies):
        """测试classify_interaction方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.classify_interaction("test input")
        assert result is not None
        assert "output" in result

    @pytest.mark.asyncio
    async def test_classify_interaction_async(self, mock_all_dependencies):
        """测试classify_interaction_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = await assistant.classify_interaction_async("test input")
        assert result is not None
        assert "output" in result

    @pytest.mark.asyncio
    async def test_get_parameter_recommendation(self, mock_all_dependencies):
        """测试get_parameter_recommendation方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = await assistant.get_parameter_recommendation("LSTM")
        assert result is not None

    def test_create_algorithm_task(self, mock_all_dependencies):
        """测试create_algorithm_task方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.create_algorithm_task({"algorithm_type": "LSTM"})
        assert result is not None
        assert "task_id" in result

    def test_query_task_status(self, mock_all_dependencies):
        """测试query_task_status方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.query_task_status("test_task_id")
        assert result is not None
        assert "task_id" in result

    def test_get_task_result(self, mock_all_dependencies):
        """测试get_task_result方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.get_task_result("test_task_id")
        assert result is not None
        assert "task_id" in result

    def test_process_user_input(self, mock_all_dependencies):
        """测试process_user_input方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.process_user_input("test input")
        assert result is not None
        assert len(result) == 3  # 返回tuple (response, state, type)

    @pytest.mark.asyncio
    async def test_process_user_input_async(self, mock_all_dependencies):
        """测试process_user_input_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = await assistant.process_user_input_async("test input")
        assert result is not None
        assert len(result) == 3  # 返回tuple (response, state, type)

    def test_generate_report(self, mock_all_dependencies):
        """测试generate_report方法 - 修复Mock返回值"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        with patch('agents.predict_algorith_agent.core.predictive_agent.Template') as mock_template:
            mock_template_instance = MagicMock()
            mock_template_instance.render.return_value = "<html>Mock Report</html>"
            mock_template.return_value = mock_template_instance

            assistant = PredictiveAlgorithmAssistant()
            result = assistant.generate_report("test_task_id", {"algorithm_type": "LSTM"})
            assert result is not None
            assert isinstance(result, str)

    def test_call_llm_for_report(self, mock_all_dependencies):
        """测试call_llm_for_report方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.call_llm_for_report("test_task_id", {"algorithm_type": "LSTM"})
        assert result is not None
        assert isinstance(result, dict)

    def test_handle_new_conversation(self, mock_all_dependencies):
        """测试handle_new_conversation方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        result = assistant.handle_new_conversation("test input", state)
        assert result is not None
        assert len(result) == 3

    @pytest.mark.asyncio
    async def test_handle_new_conversation_async(self, mock_all_dependencies):
        """测试handle_new_conversation_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        result = await assistant.handle_new_conversation_async("test input", state)
        assert result is not None
        assert len(result) == 3

    @pytest.mark.asyncio
    async def test_call_result_analysis_model(self, mock_all_dependencies):
        """测试call_result_analysis_model方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        result = await assistant.call_result_analysis_model("test input", state)
        assert result is not None
        assert isinstance(result, str)

    @pytest.mark.asyncio
    async def test_handle_general_question(self, mock_all_dependencies):
        """测试handle_general_question方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        result = await assistant.handle_general_question("test input", state)
        assert result is not None
        assert len(result) == 3

    def test_is_algorithm_related_question(self, mock_all_dependencies):
        """测试is_algorithm_related_question方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()
        result = assistant.is_algorithm_related_question("LSTM算法")
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_handle_general_consultation_async(self, mock_all_dependencies):
        """测试handle_general_consultation_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        result = await assistant.handle_general_consultation_async("test input", state)
        assert result is not None
        assert len(result) == 3

    @pytest.mark.asyncio
    async def test_handle_algorithm_recommendation_async(self, mock_all_dependencies):
        """测试handle_algorithm_recommendation_async方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        result = await assistant.handle_algorithm_recommendation_async("test input", state)
        assert result is not None
        assert len(result) == 3

    # 删除不存在的方法测试

    # 删除所有不存在的方法测试，只保留实际存在的方法
    # 根据代码分析，以下方法不存在，已删除：
    # - handle_parameter_optimization_async (应该是handle_parameter_configuration_async)
    # - handle_training_monitoring_async
    # - handle_result_analysis_async (应该是call_result_analysis_model)
    # - handle_history_algorithm_query_async (应该是handle_history_algorithm_search)
    # - handle_confirmation, handle_adjustment, handle_cancel, handle_restart
    # - get_available_algorithms, get_available_datasets, train_algorithm_with_platform
    # - get_training_log, get_available_iot_devices, start_monitoring_service
    # - get_all_services, get_service_prediction
    # - format_* 系列方法
    # - validate_* 系列方法
    # - handle_api_error, get_fallback_response
    # - log_user_interaction, save_conversation_state, load_conversation_state

    # 添加一些实际存在的辅助方法测试
    def test_router_function(self, mock_all_dependencies):
        """测试router函数"""
        from agents.predict_algorith_agent.core.predictive_agent import router

        # 验证router是可调用的
        assert callable(router)

    def test_prompt_constants(self, mock_all_dependencies):
        """测试Prompt常量"""
        from agents.predict_algorith_agent.core.predictive_agent import (
            algorithm_classify_prompt,
            param_extract_prompt,
            interaction_classify_prompt
        )

        # 验证常量存在且不为空
        assert algorithm_classify_prompt is not None
        assert len(algorithm_classify_prompt) > 0
        assert "算法类型分类专家" in algorithm_classify_prompt

        assert param_extract_prompt is not None
        assert len(param_extract_prompt) > 0
        assert "参数提取专家" in param_extract_prompt

        assert interaction_classify_prompt is not None
        assert len(interaction_classify_prompt) > 0
        assert "交互意图分类" in interaction_classify_prompt
