#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket连接测试脚本 - 使用Mock避免真实WebSocket连接
基于智能体问答对话样例进行完整测试
"""

import asyncio
import json
import time
import logging
from typing import List, Dict, Any
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(levelname)s - %(message)s')  # 减少日志输出
logger = logging.getLogger(__name__)

class MockWebSocketTester:
    """Mock WebSocket测试器 - 避免真实网络连接"""
    def __init__(self, url: str = "ws://localhost:8008/api/ws/chat"):
        self.url = url
        self.websocket = None
        self.session_id = "test_session_" + str(int(time.time()))
        self.user_id = "test_user"
        self.username = "测试用户"
        self.connected = False

    async def connect(self):
        """Mock连接WebSocket"""
        try:
            full_url = f"{self.url}?session_id={self.session_id}&user_id={self.user_id}&username={self.username}"
            logger.info(f"Mock连接: {full_url}")

            # 创建Mock WebSocket对象
            self.websocket = AsyncMock()
            self.websocket.send = AsyncMock()
            self.websocket.recv = AsyncMock()
            self.websocket.close = AsyncMock()

            self.connected = True
            logger.info("Mock WebSocket连接成功")
            return True
        except Exception as e:
            logger.error(f"Mock WebSocket连接失败: {e}")
            return False
    
    async def send_message(self, message_type: str, data: Dict[str, Any]):
        """Mock发送消息"""
        if not self.connected or not self.websocket:
            logger.error("Mock WebSocket未连接")
            return False
            
        try:
            message = {
                "type": message_type,
                "data": data,
                "session_id": self.session_id,
                "user_id": self.user_id
            }
            # Mock发送消息
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
            logger.info(f"Mock发送消息: {message_type}")
            return True
        except Exception as e:
            logger.error(f"Mock发送消息失败: {e}")
            return False

    async def receive_message(self, timeout: int = 10):
        """Mock接收消息"""
        if not self.connected or not self.websocket:
            logger.error("Mock WebSocket未连接")
            return None

        try:
            # Mock响应数据
            mock_response = {
                "type": "chat_response",
                "data": {
                    "message": "这是一个Mock响应，用于测试WebSocket功能",
                    "response_type": "general_consultation"
                },
                "session_id": self.session_id,
                "timestamp": time.time()
            }

            # 模拟网络延迟
            await asyncio.sleep(0.1)

            logger.info(f"Mock收到消息: {mock_response.get('type', 'unknown')}")
            return mock_response
        except Exception as e:
            logger.error(f"Mock接收消息失败: {e}")
            return None
    
    async def test_chat_message(self, message: str):
        """Mock测试聊天消息"""
        logger.info(f"Mock测试聊天消息: {message}")

        # Mock发送消息
        success = await self.send_message("chat", {"message": message})
        if not success:
            return False

        # Mock接收响应
        response = await self.receive_message()
        if response:
            logger.info(f"Mock AI回复: {response.get('data', {}).get('message', '无回复')}")
            return True
        return False

    async def close(self):
        """Mock关闭连接"""
        if self.websocket:
            await self.websocket.close()
            self.connected = False
            logger.info("Mock WebSocket连接已关闭")

@pytest.mark.asyncio
async def test_server_connection():
    """测试WebSocket服务器连接 - Mock模式"""

    # 简化的测试用例
    test_conversations = [
        "你能提供哪些帮助？",
        "我想训练一个算法",
        "我有水泵电流数据，想预测故障"
    ]

    tester = MockWebSocketTester()

    # Mock连接WebSocket
    connection_success = await tester.connect()
    assert connection_success, "Mock WebSocket连接应该成功"

    # Mock等待欢迎消息
    welcome_msg = await tester.receive_message()
    assert welcome_msg is not None, "应该收到Mock欢迎消息"
    logger.info("收到Mock欢迎消息")

    # 逐个测试对话
    success_count = 0
    total_count = len(test_conversations)

    for i, message in enumerate(test_conversations, 1):
        logger.info(f"\n=== Mock测试 {i}/{total_count} ===")

        success = await tester.test_chat_message(message)
        if success:
            success_count += 1

        # 减少等待时间
        await asyncio.sleep(0.1)

    # Mock关闭连接
    await tester.close()

    # 验证测试结果
    assert success_count == total_count, f"所有Mock测试都应该成功，实际成功: {success_count}/{total_count}"

    # 输出Mock测试结果
    logger.info(f"\n=== Mock测试完成 ===")
    logger.info(f"总测试数: {total_count}")
    logger.info(f"成功数: {success_count}")
    logger.info(f"成功率: {success_count/total_count*100:.1f}%")

    return success_count, total_count

# 如果作为脚本运行，执行Mock测试
if __name__ == "__main__":
    print("智能体WebSocket测试工具 (Mock模式)")
    print("=" * 50)

    # 运行Mock测试
    loop = asyncio.get_event_loop()

    print("\n1. 测试Mock WebSocket连接...")
    try:
        loop.run_until_complete(test_server_connection())
        print("✅ Mock WebSocket测试通过")

        print("\n🎉 所有Mock测试通过！")
        print("💡 注意: 这是Mock测试，实际部署时需要:")
        print("1. 启动后端服务 (python predict_main.py)")
        print("2. 确保端口8008可用")
        print("3. 检查防火墙设置")

    except Exception as e:
        print(f"❌ Mock测试失败: {e}")
        print("请检查测试代码配置")
