#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 导入测试配置
from agents.predict_algorith_agent.tests.test_config import get_test_config, SAMPLE_PARAMETER_RECOMMENDATION

# 导入待测试的模块
from agents.predict_algorith_agent.services.parameter_recommendation_service import ParameterRecommendationService
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    ParameterRecommendationRequest, ParameterRecommendationResponse, 
    ParameterRecommendation, DataScale, PerformanceObjective, LLMProvider,
    ALGORITHM_PARAMETER_TEMPLATES
)

@pytest.fixture(autouse=True)
def mock_agents_config():
    """Mock agents.config配置"""
    test_config = get_test_config()
    with patch.dict('agents.config.__dict__', test_config):
        yield

@pytest.fixture(autouse=True)
def mock_logging_config():
    """Mock日志配置"""
    with patch('agents.predict_algorith_agent.services.parameter_recommendation_service.logger', autospec=True):
        yield

@pytest.fixture
def mock_pydantic_ai_agent():
    """Mock PydanticAI Agent"""
    mock_agent = MagicMock()
    mock_run_result = MagicMock()
    
    # 创建Mock的参数推荐响应
    mock_response = ParameterRecommendationResponse(
        algorithm_type="LSTM",
        recommended_parameters=[
            ParameterRecommendation(
                parameter_name="learning_rate",
                recommended_value=0.001,
                confidence=0.9,
                reasoning="基于数据规模推荐",
                impact_description="影响模型收敛速度"
            )
        ],
        overall_confidence=0.85,
        llm_provider=LLMProvider.QWEN,
        model_name="qwen-turbo",
        generation_time=0.5
    )
    
    mock_run_result.output = mock_response
    mock_agent.run = AsyncMock(return_value=mock_run_result)
    
    with patch('pydantic_ai.Agent', return_value=mock_agent):
        yield mock_agent

class TestParameterRecommendationService:
    """参数推荐服务测试类"""
    
    @pytest.mark.asyncio
    async def test_get_parameter_recommendation_success(self, mock_pydantic_ai_agent):
        """测试获取参数推荐成功"""
        service = ParameterRecommendationService()
        
        response = await service.get_parameter_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            data_features={"sequence_length": 100, "feature_count": 10},
            performance_objective=PerformanceObjective.ACCURACY,
            context="时序预测任务",
            provider=LLMProvider.QWEN,
            user_id="test_user"
        )
        
        assert response is not None
        assert response.algorithm_type == "LSTM"
        assert len(response.recommended_parameters) > 0
        assert response.overall_confidence > 0
        mock_pydantic_ai_agent.run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_parameter_recommendation_with_post_processing(self, mock_pydantic_ai_agent):
        """测试获取参数推荐并进行后处理"""
        service = ParameterRecommendationService()
        
        # 设置Mock返回值，包含需要调整的参数
        mock_response = ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="learning_rate",
                    recommended_value=2.0,  # 超出合理范围
                    confidence=0.9,
                    reasoning="测试推荐",
                    impact_description="测试影响"
                )
            ],
            overall_confidence=0.85,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=0.5
        )
        
        mock_pydantic_ai_agent.run.return_value.output = mock_response
        
        response = await service.get_parameter_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM
        )
        
        # 验证learning_rate被调整到合理范围内
        lr_param = next((p for p in response.recommended_parameters if p.parameter_name == "learning_rate"), None)
        assert lr_param is not None
        assert lr_param.recommended_value <= 1.0  # 应该被调整
        assert "已调整到合理范围内" in lr_param.reasoning
    
    @pytest.mark.asyncio
    async def test_get_multi_llm_recommendation_success(self, mock_pydantic_ai_agent):
        """测试多LLM参数推荐成功"""
        service = ParameterRecommendationService()
        
        responses = await service.get_multi_llm_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            providers=[LLMProvider.QWEN, LLMProvider.OPENAI],
            user_id="test_user"
        )
        
        assert len(responses) == 2
        for response in responses:
            assert response.algorithm_type == "LSTM"
            assert len(response.recommended_parameters) > 0
        
        # 验证调用了多次
        assert mock_pydantic_ai_agent.run.call_count == 2
    
    @pytest.mark.asyncio
    async def test_get_multi_llm_recommendation_no_providers(self, mock_pydantic_ai_agent):
        """测试多LLM推荐无提供商"""
        service = ParameterRecommendationService()
        
        responses = await service.get_multi_llm_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            providers=[],
            user_id="test_user"
        )
        
        assert len(responses) == 1  # 应该使用默认提供商
        mock_pydantic_ai_agent.run.assert_called_once()
    
    def test_get_fallback_recommendation_no_template(self):
        """测试获取回退推荐，无算法模板"""
        service = ParameterRecommendationService()
        
        request = ParameterRecommendationRequest(
            algorithm_type="UNKNOWN_ALGORITHM",
            data_scale=DataScale.MEDIUM
        )
        
        response = service.get_fallback_recommendation(request)
        
        assert response is not None
        assert response.algorithm_type == "UNKNOWN_ALGORITHM"
        assert len(response.recommended_parameters) == 0
        assert response.overall_confidence == 0.0
    
    def test_get_fallback_recommendation_with_template(self):
        """测试获取回退推荐，有算法模板"""
        service = ParameterRecommendationService()
        
        request = ParameterRecommendationRequest(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM
        )
        
        response = service.get_fallback_recommendation(request)
        
        assert response is not None
        assert response.algorithm_type == "LSTM"
        assert len(response.recommended_parameters) > 0
        assert response.overall_confidence > 0
    
    def test_get_optimization_suggestions_no_feedback(self):
        """测试获取优化建议，无性能反馈"""
        service = ParameterRecommendationService()
        
        current_params = {"learning_rate": 0.01}
        algorithm_type = "LSTM"
        
        suggestions = service.get_optimization_suggestions(current_params, algorithm_type)
        
        # 没有性能反馈时，应该返回空建议
        assert len(suggestions) == 0
    
    def test_get_optimization_suggestions_with_feedback(self):
        """测试获取优化建议，有性能反馈"""
        service = ParameterRecommendationService()
        
        current_params = {"learning_rate": 0.01}
        algorithm_type = "LSTM"
        performance_feedback = {
            "accuracy": 0.7,
            "loss": 0.5,
            "training_time": 100,
            "convergence_issues": True
        }
        
        suggestions = service.get_optimization_suggestions(
            current_params, 
            algorithm_type, 
            performance_feedback
        )
        
        # 有性能反馈时，应该有建议
        assert len(suggestions) > 0  # 可能有也可能没有，取决于具体实现
    
    def test_post_process_recommendation_no_template(self):
        """测试后处理推荐结果，无算法模板"""
        service = ParameterRecommendationService()
        
        request = ParameterRecommendationRequest(
            algorithm_type="UNKNOWN",
            data_scale=DataScale.MEDIUM
        )
        
        response = ParameterRecommendationResponse(
            algorithm_type="UNKNOWN",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="param1",
                    recommended_value=10,
                    confidence=0.8,
                    reasoning="test",
                    impact_description="test"
                )
            ],
            overall_confidence=0.8,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=0.5
        )
        
        processed_response = service._post_process_recommendation(response, request)
        
        # 无模板时应该直接返回原始响应
        assert processed_response == response
    
    def test_validate_parameter_value_in_range(self):
        """测试参数值验证在范围内"""
        service = ParameterRecommendationService()

        # 假设LSTM模板存在
        if "LSTM" in ALGORITHM_PARAMETER_TEMPLATES:
            template = ALGORITHM_PARAMETER_TEMPLATES["LSTM"]
            if "learning_rate" in template.parameter_ranges:
                param_range = template.parameter_ranges["learning_rate"]
                min_val, max_val = param_range["min"], param_range["max"]
                test_value = (min_val + max_val) / 2  # 中间值

                validated_value = service._validate_parameter_value(
                    test_value, param_range
                )

                assert validated_value == test_value
    
    def test_validate_parameter_value_out_of_range(self):
        """测试参数值验证超出范围"""
        service = ParameterRecommendationService()

        # 假设LSTM模板存在
        if "LSTM" in ALGORITHM_PARAMETER_TEMPLATES:
            template = ALGORITHM_PARAMETER_TEMPLATES["LSTM"]
            if "learning_rate" in template.parameter_ranges:
                param_range = template.parameter_ranges["learning_rate"]
                max_val = param_range["max"]
                test_value = max_val + 1.0  # 超出最大值

                validated_value = service._validate_parameter_value(
                    test_value, param_range
                )

                assert validated_value == max_val  # 应该被调整到最大值
    
    def test_generate_parameter_suggestion_no_feedback(self):
        """测试生成参数建议，无性能反馈"""
        service = ParameterRecommendationService()
        
        suggestion = service._generate_parameter_suggestion(
            "learning_rate", 0.01, "LSTM"
        )
        
        # 无性能反馈时应该返回None
        assert suggestion is None
    
    def test_service_initialization(self):
        """测试服务初始化"""
        service = ParameterRecommendationService()
        
        assert service is not None
        # 验证服务的基本属性
        assert hasattr(service, 'get_parameter_recommendation')
        assert hasattr(service, 'get_multi_llm_recommendation')
        assert hasattr(service, 'get_fallback_recommendation')
