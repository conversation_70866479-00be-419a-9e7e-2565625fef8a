#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime
import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 导入测试配置
from agents.predict_algorith_agent.tests.test_config import get_test_config, SAMPLE_CONVERSATION, SAMPLE_MESSAGE

# 导入待测试的模块
from agents.predict_algorith_agent.database.database_manager import DatabaseManager, DateTimeEncoder

@pytest.fixture(autouse=True)
def mock_agents_config():
    """Mock agents.config配置"""
    test_config = get_test_config()
    with patch.dict('agents.config.__dict__', test_config):
        yield

@pytest.fixture(autouse=True)
def mock_logging_config():
    """Mock日志配置"""
    with patch('agents.predict_algorith_agent.utils.logging_config.setup_logging'):
        yield

@pytest.fixture
def mock_database_connection():
    """Mock数据库连接"""
    mock_cursor = MagicMock()
    mock_conn = MagicMock()

    # 配置cursor的默认返回值
    mock_cursor.fetchone.return_value = None
    mock_cursor.fetchall.return_value = []
    mock_cursor.rowcount = 0
    mock_cursor.lastrowid = 1

    # 配置connection
    mock_conn.cursor.return_value = mock_cursor
    mock_conn.commit = MagicMock()  # 添加commit方法
    mock_conn.rollback = MagicMock()  # 添加rollback方法
    mock_conn.__enter__.return_value = mock_conn
    mock_conn.__exit__.return_value = None

    with patch('pymysql.connect', return_value=mock_conn):
        yield mock_conn, mock_cursor

class TestDatabaseManager:
    """数据库管理器测试类"""
    
    def test_datetime_encoder(self):
        """测试DateTimeEncoder"""
        dt_obj = datetime(2023, 1, 1, 12, 30, 0)
        encoded = json.dumps({"time": dt_obj}, cls=DateTimeEncoder)
        assert encoded == '{"time": "2023-01-01T12:30:00"}'
    
    def test_generate_id(self):
        """测试ID生成器"""
        db_manager = DatabaseManager()
        _id = db_manager.generate_id()
        assert isinstance(_id, str)
        assert len(_id) == 16
        
        _id_with_prefix = db_manager.generate_id("test_")
        assert _id_with_prefix.startswith("test_")
        assert len(_id_with_prefix) == 21
    
    def test_generate_session_id(self):
        """测试Session ID生成器"""
        db_manager = DatabaseManager()
        session_id = db_manager.generate_session_id()
        assert isinstance(session_id, str)
        assert len(session_id) == 32
    
    def test_create_conversation_success(self, mock_database_connection):
        """测试创建对话成功"""
        mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.lastrowid = 123
        
        conversation_id = db_manager.create_conversation(
            user_id="user123",
            session_id="session456", 
            title="测试对话",
            conversation_type="training"
        )
        
        assert conversation_id.startswith("conv_")
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    def test_get_conversations_by_user_success(self, mock_database_connection):
        """测试获取用户对话列表成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()

        # 设置Mock返回值 - 注意get_conversations_by_user返回的是一个元组(conversations, total)
        mock_cursor.fetchone.side_effect = [{"total": 1}, None]
        mock_cursor.fetchall.return_value = [SAMPLE_CONVERSATION]

        conversations, total = db_manager.get_conversations_by_user("user123")

        assert len(conversations) == 1
        assert total == 1
        assert conversations[0]["conversation_id"] == SAMPLE_CONVERSATION["conversation_id"]
        mock_cursor.execute.assert_called()
    
    def test_get_conversation_detail_success(self, mock_database_connection):
        """测试获取对话详情成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.fetchone.return_value = SAMPLE_CONVERSATION
        
        conversation = db_manager.get_conversation_detail("conv_123")
        
        assert conversation is not None
        assert conversation["conversation_id"] == "test_conv_123"
        mock_cursor.execute.assert_called()
    
    def test_update_conversation_success(self, mock_database_connection):
        """测试更新对话成功"""
        mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.rowcount = 1
        
        result = db_manager.update_conversation(
            conversation_id="conv_123",
            title="新标题",
            status="completed"
        )
        
        assert result is True
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    def test_delete_conversation_success(self, mock_database_connection):
        """测试删除对话成功"""
        mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.rowcount = 1
        
        result = db_manager.delete_conversation("conv_123")
        
        assert result is True
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    def test_add_conversation_message_success(self, mock_database_connection):
        """测试添加对话消息成功"""
        mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.lastrowid = 1
        
        message_id = db_manager.add_conversation_message(
            conversation_id="conv_123",
            message_type="user",
            content="测试消息",
            message_data={"key": "value"},
            interaction_type="chat"
        )
        
        assert message_id == 1
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    def test_get_conversation_messages_success(self, mock_database_connection):
        """测试获取对话消息成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.fetchall.return_value = [SAMPLE_MESSAGE]
        
        messages = db_manager.get_conversation_messages("conv_123")
        
        assert len(messages) == 1
        assert messages[0]["content"] == "测试消息内容"
        mock_cursor.execute.assert_called()
    
    def test_save_session_state_success(self, mock_database_connection):
        """测试保存会话状态成功"""
        mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()

        result = db_manager.save_session_state(
            user_id="user123",
            session_id="session456",
            conversation_id="conv_123",  # 添加缺少的参数
            task_id="task_789",
            state_data={"key": "value"}
        )

        assert result is True
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    def test_load_session_state_success(self, mock_database_connection):
        """测试加载会话状态成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.fetchone.return_value = {
            "task_id": "task_789",
            "state_data": '{"key": "value"}'
        }
        
        task_id, state_data = db_manager.load_session_state("user123", "session456")
        
        assert task_id == "task_789"
        assert state_data == {"key": "value"}
        mock_cursor.execute.assert_called()
    
    def test_get_user_conversations_success(self, mock_database_connection):
        """测试获取用户对话成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()
        
        # 设置Mock返回值
        mock_cursor.fetchall.return_value = [SAMPLE_CONVERSATION]
        
        conversations = db_manager.get_user_conversations("user123")
        
        assert len(conversations) == 1
        assert conversations[0]["user_id"] == "test_user_456"
        mock_cursor.execute.assert_called()
    
    def test_create_context_summary_success(self, mock_database_connection):
        """测试创建上下文摘要成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()

        # 设置Mock返回值
        mock_cursor.lastrowid = 1

        summary_id = db_manager.create_context_summary(
            conversation_id="conv_123",
            summary_content="测试摘要",
            key_points={"key": "value"},  # 修正参数名
            message_count=5  # 添加缺少的参数
        )

        assert summary_id == "1"  # 返回的是字符串
        mock_cursor.execute.assert_called()
    
    def test_get_latest_context_summary_success(self, mock_database_connection):
        """测试获取最新上下文摘要成功"""
        _mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()

        # 设置Mock返回值 - 包含key_points字段
        mock_cursor.fetchone.return_value = {
            "summary_content": "测试摘要",
            "key_points": '{"key": "value"}',  # JSON字符串格式
            "token_count": 100,
            "summary_time": datetime.now()
        }

        summary = db_manager.get_latest_context_summary("conv_123")

        assert summary is not None
        assert summary["summary_content"] == "测试摘要"
        assert summary["key_points"] == {"key": "value"}  # 应该被解析为字典
        mock_cursor.execute.assert_called()
    
    def test_update_conversation_context_tokens_success(self, mock_database_connection):
        """测试更新对话上下文令牌数成功"""
        mock_conn, mock_cursor = mock_database_connection
        db_manager = DatabaseManager()

        # 设置Mock返回值
        mock_cursor.rowcount = 1

        # 这个方法可能不存在，我们测试一个存在的方法
        result = db_manager.update_conversation(
            conversation_id="conv_123",
            context_tokens=200
        )

        assert result is True
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
