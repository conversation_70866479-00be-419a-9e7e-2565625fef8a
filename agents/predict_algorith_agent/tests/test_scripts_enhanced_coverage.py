#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scripts模块增强测试覆盖率
专门测试scripts目录下覆盖率不足的脚本模块
"""

import subprocess
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

import pytest

# 导入待测试的模块
from agents.predict_algorith_agent.scripts.complete_self_test import CompleteSelfTester
from agents.predict_algorith_agent.scripts.start_server import (
    setup_environment, print_startup_info, main as start_server_main
)


class TestCompleteSelfTesterEnhanced:
    """CompleteSelfTester增强测试"""
    
    def test_complete_self_tester_initialization(self):
        """测试CompleteSelfTester初始化"""
        tester = CompleteSelfTester()
        assert tester is not None
        assert 'timestamp' in tester.test_results
        assert 'config_tests' in tester.test_results
        assert 'database_tests' in tester.test_results
        assert 'backend_tests' in tester.test_results
        assert 'websocket_tests' in tester.test_results
        assert tester.server_port == 8008
        assert len(tester.test_users) == 3
        
    def test_test_config_loading_success(self):
        """测试配置加载成功情况"""
        tester = CompleteSelfTester()
        
        with patch('agents.predict_algorith_agent.scripts.complete_self_test.get_config') as mock_get_config:
            mock_config = Mock()
            mock_config.environment = Mock()
            mock_config.environment.value = 'testing'
            mock_get_config.return_value = mock_config
            
            result = tester.test_config_loading()
            assert result['config_import_success'] is True
            assert result['config_access_success'] is True
            
    def test_test_config_loading_failure(self):
        """测试配置加载失败情况"""
        tester = CompleteSelfTester()
        
        with patch('agents.predict_algorith_agent.scripts.complete_self_test.get_config', side_effect=ImportError("Config not found")):
            result = tester.test_config_loading()
            assert result['config_import_success'] is False
            assert 'errors' in result
            
    def test_test_database_connection_success(self):
        """测试数据库连接成功情况"""
        tester = CompleteSelfTester()
        
        with patch('agents.predict_algorith_agent.scripts.complete_self_test.DatabaseManager') as mock_db_manager:
            mock_instance = Mock()
            mock_instance.test_connection.return_value = True
            mock_db_manager.return_value = mock_instance
            
            result = tester.test_database_connection()
            assert result['connection_success'] is True
            
    def test_test_database_connection_failure(self):
        """测试数据库连接失败情况"""
        tester = CompleteSelfTester()
        
        with patch('agents.predict_algorith_agent.scripts.complete_self_test.DatabaseManager', side_effect=Exception("DB Error")):
            result = tester.test_database_connection()
            assert result['connection_success'] is False
            assert 'errors' in result
            
    def test_start_backend_server_success(self):
        """测试后端服务器启动成功"""
        tester = CompleteSelfTester()
        
        with patch('subprocess.Popen') as mock_popen:
            mock_process = Mock()
            mock_process.poll.return_value = None  # 进程运行中
            mock_popen.return_value = mock_process
            
            with patch('time.sleep'):  # 跳过等待
                with patch('requests.get') as mock_get:
                    mock_response = Mock()
                    mock_response.status_code = 200
                    mock_get.return_value = mock_response
                    
                    result = tester.start_backend_server()
                    assert result['server_started'] is True
                    
    def test_start_backend_server_failure(self):
        """测试后端服务器启动失败"""
        tester = CompleteSelfTester()
        
        with patch('subprocess.Popen', side_effect=Exception("Server start failed")):
            result = tester.start_backend_server()
            assert result['server_started'] is False
            assert 'errors' in result
            
    def test_stop_backend_server(self):
        """测试停止后端服务器"""
        tester = CompleteSelfTester()
        
        # 模拟有运行中的服务器进程
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.wait = Mock()
        tester.server_process = mock_process
        
        tester.stop_backend_server()
        mock_process.terminate.assert_called_once()
        mock_process.wait.assert_called_once()
        
    def test_stop_backend_server_with_timeout(self):
        """测试停止后端服务器超时情况"""
        tester = CompleteSelfTester()
        
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.wait.side_effect = subprocess.TimeoutExpired('cmd', 10)
        mock_process.kill = Mock()
        tester.server_process = mock_process
        
        tester.stop_backend_server()
        mock_process.terminate.assert_called_once()
        mock_process.kill.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_websocket_functionality_success(self):
        """测试WebSocket功能成功情况"""
        tester = CompleteSelfTester()
        
        # Mock websocket连接
        mock_websocket = AsyncMock()
        mock_websocket.recv.side_effect = [
            '{"type": "welcome", "message": "Welcome"}',
            '{"type": "response", "message": "Hello back"}'
        ]
        
        with patch('websockets.connect', return_value=mock_websocket):
            result = await tester.test_websocket_functionality()
            assert result['basic_connection'] is True
            
    @pytest.mark.asyncio
    async def test_websocket_functionality_connection_failure(self):
        """测试WebSocket连接失败情况"""
        tester = CompleteSelfTester()
        
        with patch('websockets.connect', side_effect=Exception("Connection failed")):
            result = await tester.test_websocket_functionality()
            assert result['basic_connection'] is False
            assert 'errors' in result
            
    def test_generate_summary_all_success(self):
        """测试生成总结 - 所有测试成功"""
        tester = CompleteSelfTester()
        tester.test_results = {
            'config_tests': {'config_import_success': True},
            'database_tests': {'connection_success': True},
            'backend_tests': {'server_started': True},
            'websocket_tests': {'basic_connection': True, 'message_exchange': True}
        }
        
        tester.generate_summary()
        summary = tester.test_results['summary']
        assert summary['overall_success'] is True
        assert summary['ready_for_deployment'] is True
        
    def test_generate_summary_partial_failure(self):
        """测试生成总结 - 部分测试失败"""
        tester = CompleteSelfTester()
        tester.test_results = {
            'config_tests': {'config_import_success': True},
            'database_tests': {'connection_success': False},
            'backend_tests': {'server_started': True},
            'websocket_tests': {'basic_connection': True, 'message_exchange': False}
        }
        
        tester.generate_summary()
        summary = tester.test_results['summary']
        assert summary['overall_success'] is False
        assert summary['ready_for_deployment'] is False
        
    def test_generate_report_success(self):
        """测试生成报告 - 成功情况"""
        tester = CompleteSelfTester()
        tester.test_results = {
            'summary': {
                'overall_success': True,
                'config_fixed': True,
                'database_working': True,
                'backend_working': True,
                'websocket_working': True,
                'ready_for_deployment': True
            },
            'config_tests': {},
            'database_tests': {},
            'backend_tests': {},
            'websocket_tests': {}
        }
        
        report = tester.generate_report()
        assert "恭喜！所有测试通过" in report
        assert "可以部署到生产环境" in report
        
    def test_generate_report_failure(self):
        """测试生成报告 - 失败情况"""
        tester = CompleteSelfTester()
        tester.test_results = {
            'summary': {
                'overall_success': False,
                'config_fixed': False,
                'database_working': True,
                'backend_working': False,
                'websocket_working': True,
                'ready_for_deployment': False
            },
            'config_tests': {},
            'database_tests': {},
            'backend_tests': {},
            'websocket_tests': {}
        }
        
        report = tester.generate_report()
        assert "部分测试未通过" in report
        assert "修复配置文件问题" in report
        assert "修复后端服务启动问题" in report


class TestStartServerEnhanced:
    """StartServer增强测试"""
    
    def test_setup_environment_development(self):
        """测试开发环境设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            env_example = project_root / '.env.example'
            env_example.write_text('TEST_VAR=development')
            
            setup_environment('development', project_root)
            
            target_env = project_root / '.env'
            assert target_env.exists()
            
    def test_setup_environment_production(self):
        """测试生产环境设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            env_prod = project_root / '.env.production'
            env_prod.write_text('TEST_VAR=production')
            
            setup_environment('production', project_root)
            
            target_env = project_root / '.env'
            assert target_env.exists()
            
    def test_setup_environment_missing_file(self):
        """测试环境文件不存在的情况"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            
            with patch('builtins.print') as mock_print:
                setup_environment('development', project_root)
                # 应该打印警告信息
                assert any("警告" in str(call) for call in mock_print.call_args_list)
                
    def test_print_startup_info(self):
        """测试打印启动信息"""
        with patch('builtins.print') as mock_print:
            print_startup_info('development', 'localhost', 8008, 'INFO')
            
            # 验证打印了必要的信息
            printed_text = ' '.join(str(call) for call in mock_print.call_args_list)
            assert 'development' in printed_text
            assert 'localhost' in printed_text
            assert '8008' in printed_text
            assert 'INFO' in printed_text
            
    def test_start_server_main_check_only(self):
        """测试启动服务器主函数 - 仅检查模式"""
        test_args = ['start_server.py', '--check-only']
        
        with patch('sys.argv', test_args):
            with patch('builtins.print') as mock_print:
                with pytest.raises(SystemExit) as exc_info:
                    start_server_main()
                
                # 检查模式应该正常退出
                assert exc_info.value.code == 0
                
    def test_start_server_main_missing_uvicorn(self):
        """测试启动服务器主函数 - 缺少uvicorn"""
        test_args = ['start_server.py', '--env', 'testing']
        
        with patch('sys.argv', test_args):
            with patch('importlib.import_module', side_effect=ImportError("No module named 'uvicorn'")):
                with patch('builtins.print') as mock_print:
                    with pytest.raises(SystemExit) as exc_info:
                        start_server_main()
                    
                    # 应该因为缺少依赖而退出
                    assert exc_info.value.code == 1


class TestScriptsIntegration:
    """Scripts模块集成测试"""
    
    def test_scripts_import_all(self):
        """测试所有scripts模块导入"""
        # 测试主要脚本模块可以导入
        import agents.predict_algorith_agent.scripts.complete_self_test as self_test
        import agents.predict_algorith_agent.scripts.start_server as start_server
        
        assert self_test is not None
        assert start_server is not None
        
        # 验证主要类和函数存在
        assert hasattr(self_test, 'CompleteSelfTester')
        assert hasattr(start_server, 'setup_environment')
        assert hasattr(start_server, 'print_startup_info')
        
    @pytest.mark.asyncio
    async def test_complete_self_test_integration(self):
        """测试完整自测集成"""
        tester = CompleteSelfTester()
        
        # Mock所有外部依赖
        with patch.object(tester, 'test_config_loading', return_value={'config_import_success': True}):
            with patch.object(tester, 'test_database_connection', return_value={'connection_success': True}):
                with patch.object(tester, 'start_backend_server', return_value={'server_started': False}):
                    with patch.object(tester, 'stop_backend_server'):
                        
                        result = await tester.run_complete_test()
                        
                        assert 'timestamp' in result
                        assert 'summary' in result
                        assert isinstance(result['config_tests'], dict)
                        assert isinstance(result['database_tests'], dict)
                        
    def test_environment_setup_integration(self):
        """测试环境设置集成"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)
            
            # 创建测试环境文件
            env_files = ['.env.example', '.env.production']
            for env_file in env_files:
                (project_root / env_file).write_text(f'TEST_VAR={env_file}')
            
            # 测试不同环境设置
            for env_type in ['development', 'production', 'testing']:
                setup_environment(env_type, project_root)
                
                target_env = project_root / '.env'
                if env_type in ['development', 'testing']:
                    assert target_env.exists()
                elif env_type == 'production':
                    assert target_env.exists()
                    content = target_env.read_text()
                    assert 'production' in content
