#!/usr/bin/env python3
"""
功能测试脚本 - 测试修改后的提示词在实际场景中的表现
通过模拟大模型调用来验证提示词优化效果
"""

import json
import re
from datetime import datetime

def simulate_llm_response(prompt: str, user_input: str) -> str:
    """
    模拟大模型响应，基于提示词和用户输入生成预期的响应
    这里我们基于规则来模拟，验证提示词的逻辑是否正确
    """
    
    # 算法分类模拟
    if "算法类型分类专家" in prompt and "algorithm_type" in prompt:
        # 基于用户输入进行算法分类
        user_lower = user_input.lower()
        
        if "lstm" in user_lower or "长短期记忆" in user_input or ("时序" in user_input and "预测" in user_input):
            return '{"algorithm_type": "LSTM", "confidence": 0.9, "reason": "用户提到LSTM或时序预测相关内容"}'
        elif "cnn" in user_lower or "卷积" in user_input or "图像" in user_input or "图片" in user_input:
            return '{"algorithm_type": "CNN", "confidence": 0.9, "reason": "用户提到CNN或图像处理相关内容"}'
        elif "rnn" in user_lower or "循环神经网络" in user_input or ("文本" in user_input and "序列" in user_input):
            return '{"algorithm_type": "RNN", "confidence": 0.85, "reason": "用户提到RNN或文本序列处理"}'
        elif "线性回归" in user_input or ("预测" in user_input and "特征" in user_input and "简单" in user_input):
            return '{"algorithm_type": "线性回归", "confidence": 0.8, "reason": "简单的回归预测任务"}'
        elif "逻辑回归" in user_input or ("分类" in user_input and ("邮件" in user_input or "二分类" in user_input)):
            return '{"algorithm_type": "逻辑回归", "confidence": 0.85, "reason": "二分类问题"}'
        elif "随机森林" in user_input or ("复杂" in user_input and "特征" in user_input):
            return '{"algorithm_type": "随机森林", "confidence": 0.9, "reason": "复杂的多特征任务"}'
        elif "svm" in user_lower or "支持向量机" in user_input or "高精度" in user_input:
            return '{"algorithm_type": "SVM", "confidence": 0.8, "reason": "高精度分类任务"}'
        else:
            return '{"algorithm_type": "其他", "confidence": 0.7, "reason": "不在支持的算法类型中"}'
    
    # 参数提取模拟
    elif "参数提取专家" in prompt and "extracted_params" in prompt:
        extracted_params = {}
        missing_params = []
        
        # 提取学习率
        lr_match = re.search(r'学习率\s*([0-9.]+)', user_input)
        if lr_match:
            extracted_params["learning_rate"] = float(lr_match.group(1))
        else:
            missing_params.append("learning_rate")
        
        # 提取批次大小
        batch_match = re.search(r'批次大小\s*([0-9]+)', user_input)
        if batch_match:
            extracted_params["batch_size"] = int(batch_match.group(1))
        elif "32" in user_input or "64" in user_input or "128" in user_input:
            # 简单的数字匹配
            numbers = re.findall(r'\b(32|64|128)\b', user_input)
            if numbers:
                extracted_params["batch_size"] = int(numbers[0])
        else:
            missing_params.append("batch_size")
        
        # 提取训练轮数
        epoch_match = re.search(r'训练\s*([0-9]+)\s*轮', user_input)
        if epoch_match:
            extracted_params["epochs"] = int(epoch_match.group(1))
        elif "100" in user_input or "200" in user_input:
            numbers = re.findall(r'\b(100|200|500)\b', user_input)
            if numbers:
                extracted_params["epochs"] = int(numbers[0])
        else:
            missing_params.append("epochs")
        
        # 提取隐藏层大小
        hidden_match = re.search(r'隐藏层\s*([0-9]+)', user_input)
        if hidden_match:
            extracted_params["hidden_size"] = int(hidden_match.group(1))
        
        # 提取优化器
        if "adam" in user_input.lower():
            extracted_params["optimizer"] = "Adam"
        elif "sgd" in user_input.lower():
            extracted_params["optimizer"] = "SGD"
        
        # 提取dropout
        dropout_match = re.search(r'dropout\s*([0-9.]+)', user_input)
        if dropout_match:
            extracted_params["dropout"] = float(dropout_match.group(1))
        
        confidence = 0.8 if extracted_params else 0.3
        notes = f"提取到{len(extracted_params)}个参数" if extracted_params else "未提取到具体参数"
        
        return json.dumps({
            "extracted_params": extracted_params,
            "missing_params": missing_params,
            "confidence": confidence,
            "notes": notes
        }, ensure_ascii=False)
    
    # 交互分类模拟
    elif "交互意图分类专家" in prompt and "interaction_type" in prompt:
        user_lower = user_input.lower()
        
        # 技术咨询
        if any(word in user_input for word in ["如何", "怎么", "什么", "为什么", "请问"]):
            return '{"interaction_type": "一般咨询", "confidence": 0.95, "reason": "包含疑问词，属于技术咨询"}'
        
        # 确认操作
        elif any(word in user_input for word in ["确认", "好的", "是的", "ok", "可以", "同意", "开始训练"]):
            return '{"interaction_type": "确认", "confidence": 0.9, "reason": "用户确认操作"}'
        
        # 参数调整
        elif any(word in user_input for word in ["修改", "调整", "改变", "更新", "换成", "改成"]):
            return '{"interaction_type": "调整", "confidence": 0.9, "reason": "用户要求调整参数"}'
        
        # 查询状态
        elif any(word in user_input for word in ["进度", "状态", "结果", "报告", "查看", "怎么样了"]):
            return '{"interaction_type": "查询进度", "confidence": 0.85, "reason": "用户查询状态或结果"}'
        
        # 取消操作
        elif any(word in user_input for word in ["取消", "停止", "不要", "算了"]):
            return '{"interaction_type": "取消", "confidence": 0.9, "reason": "用户取消操作"}'
        
        else:
            return '{"interaction_type": "一般咨询", "confidence": 0.6, "reason": "默认归类为一般咨询"}'
    
    return '{"error": "未知的提示词类型"}'

def load_prompts():
    """加载当前的提示词"""
    try:
        with open('core/predictive_agent.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        prompts = {}
        
        # 提取算法分类提示词
        algo_start = content.find('algorithm_classify_prompt = """') + len('algorithm_classify_prompt = """')
        algo_end = content.find('"""', algo_start)
        prompts['algorithm'] = content[algo_start:algo_end]
        
        # 提取参数提取提示词
        param_start = content.find('param_extract_prompt = """') + len('param_extract_prompt = """')
        param_end = content.find('"""', param_start)
        prompts['parameter'] = content[param_start:param_end]
        
        # 提取交互分类提示词
        inter_start = content.find('interaction_classify_prompt = """') + len('interaction_classify_prompt = """')
        inter_end = content.find('"""', inter_start)
        prompts['interaction'] = content[inter_start:inter_end]
        
        return prompts
    except Exception as e:
        print(f"❌ 加载提示词失败: {e}")
        return {}

def test_algorithm_classification():
    """测试算法分类功能"""
    print("🧪 测试算法分类功能")
    print("=" * 50)
    
    prompts = load_prompts()
    if 'algorithm' not in prompts:
        print("❌ 未找到算法分类提示词")
        return
    
    test_cases = [
        {"input": "我想用LSTM预测股票价格", "expected": "LSTM"},
        {"input": "识别图片中的猫和狗", "expected": "CNN"},
        {"input": "文本情感分析处理", "expected": "RNN"},
        {"input": "预测房价，有面积位置特征", "expected": "线性回归"},
        {"input": "判断邮件是否垃圾邮件", "expected": "逻辑回归"},
        {"input": "客户流失预测，数据复杂特征很多", "expected": "随机森林"},
        {"input": "文档分类需要高精度", "expected": "SVM"},
        {"input": "深度强化学习训练游戏AI", "expected": "其他"}
    ]
    
    correct_count = 0
    for i, case in enumerate(test_cases):
        response = simulate_llm_response(prompts['algorithm'], case['input'])
        try:
            result = json.loads(response)
            predicted = result.get('algorithm_type', 'ERROR')
            expected = case['expected']
            
            is_correct = predicted == expected
            if is_correct:
                correct_count += 1
            
            status = "✅" if is_correct else "❌"
            print(f"  {i+1}. {status} 输入: {case['input'][:30]}...")
            print(f"     预期: {expected}, 实际: {predicted}, 置信度: {result.get('confidence', 0)}")
            
        except Exception as e:
            print(f"  {i+1}. ❌ 解析失败: {e}")
    
    accuracy = correct_count / len(test_cases)
    print(f"\n📊 算法分类准确率: {accuracy:.2%} ({correct_count}/{len(test_cases)})")
    return accuracy

def test_parameter_extraction():
    """测试参数提取功能"""
    print("\n🧪 测试参数提取功能")
    print("=" * 50)
    
    prompts = load_prompts()
    if 'parameter' not in prompts:
        print("❌ 未找到参数提取提示词")
        return
    
    test_cases = [
        {
            "input": "LSTM模型，学习率0.001，批次大小32，训练100轮",
            "expected_params": ["learning_rate", "batch_size", "epochs"]
        },
        {
            "input": "CNN模型，隐藏层128，Adam优化器",
            "expected_params": ["hidden_size", "optimizer"]
        },
        {
            "input": "学习率0.01，dropout 0.2",
            "expected_params": ["learning_rate", "dropout"]
        },
        {
            "input": "我想训练一个模型",
            "expected_params": []
        }
    ]
    
    total_score = 0
    for i, case in enumerate(test_cases):
        response = simulate_llm_response(prompts['parameter'], case['input'])
        try:
            result = json.loads(response)
            extracted = result.get('extracted_params', {})
            expected_params = case['expected_params']
            
            # 计算参数提取准确性
            extracted_keys = set(extracted.keys())
            expected_keys = set(expected_params)
            
            if expected_keys:
                intersection = len(extracted_keys.intersection(expected_keys))
                union = len(extracted_keys.union(expected_keys))
                score = intersection / union if union > 0 else 0
            else:
                score = 1.0 if not extracted_keys else 0.5
            
            total_score += score
            
            print(f"  {i+1}. 输入: {case['input'][:40]}...")
            print(f"     提取参数: {list(extracted.keys())}")
            print(f"     预期参数: {expected_params}")
            print(f"     准确率: {score:.2%}")
            
        except Exception as e:
            print(f"  {i+1}. ❌ 解析失败: {e}")
    
    avg_accuracy = total_score / len(test_cases)
    print(f"\n📊 参数提取平均准确率: {avg_accuracy:.2%}")
    return avg_accuracy

def test_interaction_classification():
    """测试交互分类功能"""
    print("\n🧪 测试交互分类功能")
    print("=" * 50)
    
    prompts = load_prompts()
    if 'interaction' not in prompts:
        print("❌ 未找到交互分类提示词")
        return
    
    test_cases = [
        {"input": "如何选择学习率？", "expected": "一般咨询"},
        {"input": "确认这些参数", "expected": "确认"},
        {"input": "把学习率改成0.01", "expected": "调整"},
        {"input": "训练进度如何？", "expected": "查询进度"},
        {"input": "取消训练", "expected": "取消"},
        {"input": "好的，开始训练", "expected": "确认"},
        {"input": "查看结果", "expected": "查询进度"},
        {"input": "什么是过拟合？", "expected": "一般咨询"}
    ]
    
    correct_count = 0
    for i, case in enumerate(test_cases):
        response = simulate_llm_response(prompts['interaction'], case['input'])
        try:
            result = json.loads(response)
            predicted = result.get('interaction_type', 'ERROR')
            expected = case['expected']
            
            is_correct = predicted == expected
            if is_correct:
                correct_count += 1
            
            status = "✅" if is_correct else "❌"
            print(f"  {i+1}. {status} 输入: {case['input']}")
            print(f"     预期: {expected}, 实际: {predicted}, 置信度: {result.get('confidence', 0)}")
            
        except Exception as e:
            print(f"  {i+1}. ❌ 解析失败: {e}")
    
    accuracy = correct_count / len(test_cases)
    print(f"\n📊 交互分类准确率: {accuracy:.2%} ({correct_count}/{len(test_cases)})")
    return accuracy

def main():
    """主函数"""
    print("🚀 提示词优化功能测试")
    print("=" * 60)
    
    start_time = datetime.now()
    
    # 运行各项测试
    algo_accuracy = test_algorithm_classification()
    param_accuracy = test_parameter_extraction()
    interaction_accuracy = test_interaction_classification()
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 生成测试报告
    print("\n📋 功能测试总结")
    print("=" * 60)
    print(f"⏱️  测试耗时: {duration:.2f}秒")
    print(f"🎯 算法分类准确率: {algo_accuracy:.2%}")
    print(f"🔧 参数提取准确率: {param_accuracy:.2%}")
    print(f"💬 交互分类准确率: {interaction_accuracy:.2%}")
    
    # 评估改进效果
    print("\n📈 改进效果评估:")
    if algo_accuracy >= 0.9:
        print("✅ 算法分类: 优秀 (≥90%)")
    elif algo_accuracy >= 0.8:
        print("🟡 算法分类: 良好 (80-89%)")
    else:
        print("❌ 算法分类: 需要改进 (<80%)")
    
    if param_accuracy >= 0.8:
        print("✅ 参数提取: 优秀 (≥80%)")
    elif param_accuracy >= 0.7:
        print("🟡 参数提取: 良好 (70-79%)")
    else:
        print("❌ 参数提取: 需要改进 (<70%)")
    
    if interaction_accuracy >= 0.9:
        print("✅ 交互分类: 优秀 (≥90%)")
    elif interaction_accuracy >= 0.8:
        print("🟡 交互分类: 良好 (80-89%)")
    else:
        print("❌ 交互分类: 需要改进 (<80%)")
    
    # 保存测试结果
    result = {
        "test_time": start_time.isoformat(),
        "duration_seconds": duration,
        "algorithm_accuracy": algo_accuracy,
        "parameter_accuracy": param_accuracy,
        "interaction_accuracy": interaction_accuracy,
        "overall_score": (algo_accuracy + param_accuracy + interaction_accuracy) / 3
    }
    
    result_file = f"functional_test_results_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 测试结果已保存到: {result_file}")

if __name__ == "__main__":
    main()
