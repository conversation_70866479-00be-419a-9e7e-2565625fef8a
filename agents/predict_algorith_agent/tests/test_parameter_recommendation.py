#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数推荐功能
验证多LLM参数推荐和交互功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

import asyncio
import json
import pytest
from agents.predict_algorith_agent.services.parameter_recommendation_service import parameter_recommendation_service
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    DataScale, PerformanceObjective, LLMProvider
)
from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

@pytest.mark.asyncio
async def test_single_llm_recommendation():
    """测试单个LLM的参数推荐"""
    print("=== 测试单个LLM参数推荐 ===\n")
    
    # 测试LSTM参数推荐
    print("1. 测试LSTM算法参数推荐")
    try:
        recommendation = await parameter_recommendation_service.get_parameter_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            data_features={
                "sequence_length": 60,
                "feature_count": 10,
                "data_type": "时序传感器数据"
            },
            performance_objective=PerformanceObjective.BALANCED,
            context="用于设备预测性维护的LSTM模型",
            provider=LLMProvider.QWEN,
            user_id="test_user_001"
        )
        
        print(f"算法类型: {recommendation.algorithm_type}")
        print(f"LLM提供商: {recommendation.llm_provider.value}")
        print(f"模型名称: {recommendation.model_name}")
        print(f"生成耗时: {recommendation.generation_time:.2f}s")
        print(f"整体置信度: {recommendation.overall_confidence}")
        print(f"推荐参数数量: {len(recommendation.recommended_parameters)}")
        
        print("\n推荐参数详情:")
        for param in recommendation.recommended_parameters:
            print(f"  - {param.parameter_name}: {param.recommended_value}")
            print(f"    置信度: {param.confidence}")
            print(f"    推荐理由: {param.reasoning}")
            print(f"    影响描述: {param.impact_description}")
            print()
        
        if recommendation.optimization_suggestions:
            print("优化建议:")
            for suggestion in recommendation.optimization_suggestions:
                print(f"  - {suggestion}")
        
    except Exception as e:
        print(f"LSTM参数推荐测试失败: {e}")
    
    print("-" * 80)
    
    # 测试CNN参数推荐
    print("2. 测试CNN算法参数推荐")
    try:
        recommendation = await parameter_recommendation_service.get_parameter_recommendation(
            algorithm_type="CNN",
            data_scale=DataScale.LARGE,
            data_features={
                "image_size": [224, 224, 3],
                "num_classes": 10,
                "data_type": "图像分类数据"
            },
            performance_objective=PerformanceObjective.ACCURACY,
            context="用于图像分类的CNN模型",
            provider=LLMProvider.QWEN,
            user_id="test_user_001"
        )
        
        print(f"算法类型: {recommendation.algorithm_type}")
        print(f"推荐参数数量: {len(recommendation.recommended_parameters)}")
        print(f"整体置信度: {recommendation.overall_confidence}")
        
        print("\n关键参数推荐:")
        for param in recommendation.recommended_parameters[:5]:  # 只显示前5个
            print(f"  - {param.parameter_name}: {param.recommended_value}")
            print(f"    推荐理由: {param.reasoning[:100]}...")
        
    except Exception as e:
        print(f"CNN参数推荐测试失败: {e}")

@pytest.mark.asyncio
async def test_multi_llm_recommendation():
    """测试多LLM参数推荐对比"""
    print("\n=== 测试多LLM参数推荐对比 ===\n")
    
    try:
        multi_recommendation = await parameter_recommendation_service.get_multi_llm_recommendation(
            algorithm_type="LSTM",
            data_scale=DataScale.MEDIUM,
            data_features={
                "sequence_length": 60,
                "feature_count": 5,
                "data_type": "时序数据"
            },
            performance_objective=PerformanceObjective.BALANCED,
            context="多LLM对比测试",
            user_id="test_user_002"
        )
        
        print(f"算法类型: {multi_recommendation.algorithm_type}")
        print(f"参与对比的模型数量: {len(multi_recommendation.recommendations)}")
        
        print("\n各模型推荐结果:")
        for rec in multi_recommendation.recommendations:
            print(f"  {rec.llm_provider.value} ({rec.model_name}):")
            print(f"    置信度: {rec.overall_confidence}")
            print(f"    生成耗时: {rec.generation_time:.2f}s")
            print(f"    参数数量: {len(rec.recommended_parameters)}")
        
        print(f"\n一致性参数 ({len(multi_recommendation.consensus_parameters)} 个):")
        for param_name, value in multi_recommendation.consensus_parameters.items():
            print(f"  - {param_name}: {value}")
        
        print(f"\n分歧参数 ({len(multi_recommendation.divergent_parameters)} 个):")
        for param_name, values in multi_recommendation.divergent_parameters.items():
            print(f"  - {param_name}: {values}")
        
        print(f"\n最终推荐参数:")
        for param_name, value in multi_recommendation.final_recommendation.items():
            print(f"  - {param_name}: {value}")
        
        print(f"\n模型对比分析:")
        for model_name, comparison in multi_recommendation.model_comparison.items():
            print(f"  {model_name}:")
            print(f"    置信度: {comparison['confidence']}")
            print(f"    生成时间: {comparison['generation_time']:.2f}s")
            print(f"    参数数量: {comparison['param_count']}")
        
    except Exception as e:
        print(f"多LLM参数推荐测试失败: {e}")

@pytest.mark.asyncio
async def test_agent_integration():
    """测试Agent集成的参数推荐功能"""
    print("\n=== 测试Agent集成参数推荐 ===\n")

    # 创建智能体实例
    agent = PredictiveAlgorithmAssistant()

    # 测试单LLM推荐
    print("1. 测试Agent单LLM参数推荐")
    try:
        result = await agent.get_parameter_recommendation(
            algorithm_type="LSTM",
            data_features={
                "data_size": 10000,
                "feature_dim": 8,
                "sequence_length": 50
            },
            performance_objective="balanced",
            provider="qwen",
            user_id="test_user_003"
        )
        
        if result["success"]:
            recommendation = result["recommendation"]
            print(f"推荐成功!")
            print(f"算法类型: {recommendation.algorithm_type}")
            print(f"参数数量: {len(recommendation.recommended_parameters)}")
            print(f"整体置信度: {recommendation.overall_confidence}")
        else:
            print(f"推荐失败: {result['error']}")
    
    except Exception as e:
        print(f"Agent单LLM推荐测试失败: {e}")
    
    print("-" * 60)
    
    # 测试多LLM推荐
    print("2. 测试Agent多LLM参数推荐")
    try:
        result = await predict_algorith_agent.get_multi_llm_parameter_recommendation(
            algorithm_type="CNN",
            data_features={
                "image_size": [128, 128, 3],
                "num_classes": 5,
                "dataset_size": 50000
            },
            performance_objective="accuracy",
            user_id="test_user_003"
        )
        
        if result["success"]:
            multi_rec = result["multi_recommendation"]
            print(f"多LLM推荐成功!")
            print(f"参与模型数: {len(multi_rec.recommendations)}")
            print(f"一致性参数: {len(multi_rec.consensus_parameters)} 个")
            print(f"分歧参数: {len(multi_rec.divergent_parameters)} 个")
        else:
            print(f"多LLM推荐失败: {result['error']}")
    
    except Exception as e:
        print(f"Agent多LLM推荐测试失败: {e}")

def test_parameter_templates():
    """测试参数模板功能"""
    print("\n=== 测试参数模板功能 ===\n")

    try:
        from agents.predict_algorith_agent.models.parameter_recommendation_models import ALGORITHM_PARAMETER_TEMPLATES
    except ImportError:
        print("ALGORITHM_PARAMETER_TEMPLATES 不存在，跳过测试")
        return
    
    print("可用的算法参数模板:")
    for algo_type, template in ALGORITHM_PARAMETER_TEMPLATES.items():
        print(f"\n{algo_type}:")
        print(f"  必需参数: {template.required_parameters}")
        print(f"  可选参数: {template.optional_parameters}")
        print(f"  默认值数量: {len(template.parameter_defaults)}")
        print(f"  参数范围数量: {len(template.parameter_ranges)}")
        print(f"  调优优先级: {template.tuning_priorities}")

def test_llm_provider_manager():
    """测试LLM提供商管理器"""
    print("\n=== 测试LLM提供商管理器 ===\n")

    try:
        from agents.predict_algorith_agent.utils.llm_provider_manager import LLMProviderManager
        llm_provider_manager = LLMProviderManager()
    except ImportError:
        print("LLMProviderManager 不存在，跳过测试")
        return
    
    available_providers = llm_provider_manager.get_available_providers()
    print(f"可用的LLM提供商: {[p.value for p in available_providers]}")
    
    for provider in available_providers:
        config = llm_provider_manager.providers[provider]
        print(f"\n{provider.value}:")
        print(f"  模型名称: {config.model_name}")
        print(f"  API地址: {config.base_url}")
        print(f"  最大tokens: {config.max_tokens}")
        print(f"  温度参数: {config.temperature}")

async def main():
    """主测试函数"""
    print("参数推荐功能测试开始")
    print("=" * 80)
    
    # 测试参数模板
    test_parameter_templates()
    
    # 测试LLM提供商管理器
    test_llm_provider_manager()
    
    # 测试单LLM推荐
    await test_single_llm_recommendation()
    
    # 测试多LLM推荐
    await test_multi_llm_recommendation()
    
    # 测试Agent集成
    await test_agent_integration()
    
    print("\n" + "=" * 80)
    print("参数推荐功能测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
