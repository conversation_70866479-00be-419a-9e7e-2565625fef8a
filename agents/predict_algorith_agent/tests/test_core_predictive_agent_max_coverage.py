#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心预测智能体最大覆盖率测试 - 专门用于提升覆盖率到60%以上
针对core/predictive_agent.py中的PredictiveAlgorithmAssistant类进行全面测试
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pytest

# Mock配置
mock_config = {
    "LLM_CONFIG": {"provider": "mock", "model": "mock", "api_key": "mock"},
    "ALGORITHM_PLATFORM_BASE_URL": "http://mock.com",
    "ALGORITHM_PLATFORM_TIMEOUT": 5,
    "ALGORITHM_PLATFORM_RETRY_TIMES": 1,
    "ALGORITHM_PLATFORM_RETRY_DELAY": 0.1,
    "WKHTMLTOPDF_PATH": "/usr/bin/wkhtmltopdf",
    "REPORT_PDF_DIR": "/tmp",
    "SHOW_DEEPSEEK_THOUGHT": False,
    "get_current_llm_config": lambda: {"provider": "mock", "model": "mock"},
    "get_algorithm_platform_url": lambda x: f"http://mock.com/{x}"
}

@pytest.fixture(autouse=True)
def mock_all_external_dependencies():
    """自动Mock所有外部依赖"""
    with patch.dict('agents.config.__dict__', mock_config), \
         patch('agents.predict_algorith_agent.core.predictive_agent.logger'), \
         patch('pydantic_ai.models.openai.OpenAIModel'), \
         patch('pydantic_ai.providers.openai.OpenAIProvider'), \
         patch('pydantic_ai.Agent'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.HistoryAlgorithmAgent'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.parameter_recommendation_service'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.history_algorithm_service'), \
         patch('agents.predict_algorith_agent.services.algorithm_platform_service.AlgorithmPlatformService'), \
         patch('agents.predict_algorith_agent.database.database_manager.DatabaseManager'), \
         patch('agents.predict_algorith_agent.utils.predict_memory_manager.AgentMemoryManager'), \
         patch('jinja2.Template'), \
         patch('pdfkit.from_string'), \
         patch('requests.get'), \
         patch('requests.post'):
        yield

class TestPredictiveAlgorithmAssistantMaxCoverage:
    """预测算法智能体最大覆盖率测试"""
    
    def test_predictive_algorithm_assistant_creation(self):
        """测试预测算法智能体创建"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        
        # 测试类创建
        assistant = PredictiveAlgorithmAssistant()
        assert assistant is not None
        
        # 测试基础属性
        assert hasattr(assistant, 'algorithm_classifier')
        assert hasattr(assistant, 'parameter_extractor')
        assert hasattr(assistant, 'interaction_classifier')
        assert hasattr(assistant, 'parameter_adjuster')
        assert hasattr(assistant, 'history_algorithm_agent')
        assert hasattr(assistant, 'parameter_recommendation_service')
        assert hasattr(assistant, 'algorithm_platform_service')
        assert hasattr(assistant, 'state')
    
    def test_classify_algorithm_method(self):
        """测试算法分类方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import AlgorithmClassification, AlgorithmType
        
        assistant = PredictiveAlgorithmAssistant()
        
        # Mock算法分类器
        mock_result = MagicMock()
        mock_result.output = AlgorithmClassification(
            algorithm_type=AlgorithmType.LSTM,
            confidence=0.9,
            reason="测试分类"
        )
        
        with patch.object(assistant.algorithm_classifier, 'run_sync', return_value=mock_result):
            result = assistant.classify_algorithm("我想用LSTM做时序预测")
            
            # 验证返回值
            assert isinstance(result, dict)
            assert 'output' in result
            assert result['output'].algorithm_type == AlgorithmType.LSTM
            assert result['output'].confidence == 0.9
    
    def test_extract_parameters_method(self):
        """测试参数提取方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import (
            PredictiveParameterExtraction
        )

        assistant = PredictiveAlgorithmAssistant()

        # Mock参数提取器
        mock_result = MagicMock()
        mock_result.output = PredictiveParameterExtraction(
            extracted_params={
                "input_dim": 10,
                "hidden_dim": 128,
                "num_layers": 2,
                "output_dim": 1,
                "batch_size": 32,
                "learning_rate": 0.001,
                "epochs": 100
            },
            missing_params=["dropout_rate"],
            confidence=0.8,
            notes="测试提取"
        )
        
        with patch.object(assistant.parameter_extractor, 'run_sync', return_value=mock_result):
            result = assistant.extract_parameters("学习率0.001，训练100轮")
            
            # 验证返回值
            assert isinstance(result, dict)
            assert 'output' in result
            assert result['output'].extracted_params.learning_rate == 0.001
            assert result['output'].extracted_params.epochs == 100
            assert "hidden_size" in result['output'].missing_params
    
    def test_classify_interaction_method(self):
        """测试交互分类方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import (
            PredictiveInteractionClassification, InteractionType
        )
        
        assistant = PredictiveAlgorithmAssistant()
        
        # Mock交互分类器
        mock_result = MagicMock()
        mock_result.output = PredictiveInteractionClassification(
            interaction_type=InteractionType.CONFIRM,
            confidence=0.95,
            reason="用户确认参数"
        )
        
        with patch.object(assistant.interaction_classifier, 'run_sync', return_value=mock_result):
            result = assistant.classify_interaction("确认，开始训练")
            
            # 验证返回值
            assert isinstance(result, dict)
            assert 'output' in result
            assert result['output'].interaction_type == InteractionType.CONFIRM
            assert result['output'].confidence == 0.95
    
    def test_create_algorithm_task_method(self):
        """测试创建算法任务方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant

        assistant = PredictiveAlgorithmAssistant()

        # 测试参数（转换为dict）
        params = {
            "input_dim": 10,
            "hidden_dim": 128,
            "num_layers": 2,
            "output_dim": 1,
            "batch_size": 32,
            "learning_rate": 0.001,
            "epochs": 100
        }

        # 测试创建任务
        result = assistant.create_algorithm_task(params)
        
        # 验证返回值（Mock实现）
        assert isinstance(result, dict)
        assert "mock" in result
        assert result["mock"] is True
        assert "task_id" in result
        assert "params" in result
    
    def test_get_task_result_method(self):
        """测试获取任务结果方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        
        assistant = PredictiveAlgorithmAssistant()
        
        # 测试获取任务结果
        result = assistant.get_task_result("test_task_123")
        
        # 验证返回值（Mock实现）
        assert isinstance(result, dict)
        assert "mock" in result
        assert result["mock"] is True
        assert result["task_id"] == "test_task_123"
        assert "result" in result
        assert "message" in result
    
    def test_process_user_input_new_conversation(self):
        """测试处理用户输入 - 新对话场景"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        
        assistant = PredictiveAlgorithmAssistant()
        
        # Mock新对话处理方法
        with patch.object(assistant, 'handle_new_conversation', 
                         return_value=({"msg": "欢迎"}, {"is_new_conversation": False}, "welcome")):
            
            result, state, response = assistant.process_user_input("你好")
            
            # 验证返回值
            assert isinstance(result, dict)
            assert isinstance(state, dict)
            assert isinstance(response, str)
            assert result["msg"] == "欢迎"
            assert response == "welcome"
    
    def test_process_user_input_confirmation_flow(self):
        """测试处理用户输入 - 确认流程"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import (
            InteractionType, PredictiveInteractionClassification
        )
        
        assistant = PredictiveAlgorithmAssistant()
        
        # 创建等待确认的状态
        state_dict = {
            "is_new_conversation": False,
            "has_shown_welcome": True,
            "awaiting_confirmation": True,
            "current_params": {"learning_rate": 0.001, "epochs": 100}
        }
        
        # Mock交互分类结果
        mock_interaction = MagicMock()
        mock_interaction.output = PredictiveInteractionClassification(
            interaction_type=InteractionType.CONFIRM,
            confidence=0.95,
            reason="用户确认"
        )
        
        with patch.object(assistant, 'classify_interaction', return_value=mock_interaction), \
             patch.object(assistant, 'create_algorithm_task', 
                         return_value={"task_id": "task_123", "status": "created"}):
            
            result, state, response = assistant.process_user_input("确认", state_dict)
            
            # 验证返回值
            assert isinstance(result, dict)
            assert "msg" in result
            assert "task_id" in result
            assert result["task_id"] == "task_123"
            assert response == "created"
    
    def test_process_user_input_parameter_extraction(self):
        """测试处理用户输入 - 参数提取流程"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import (
            AlgorithmClassification, AlgorithmType, PredictiveParameterExtraction
        )

        assistant = PredictiveAlgorithmAssistant()

        # 创建新对话状态
        state_dict = {
            "is_new_conversation": False,
            "has_shown_welcome": True,
            "awaiting_confirmation": False
        }

        # Mock算法分类结果
        mock_classification = MagicMock()
        mock_classification.output = AlgorithmClassification(
            algorithm_type=AlgorithmType.LSTM,
            confidence=0.9,
            reason="LSTM算法"
        )

        # Mock参数提取结果
        mock_extraction = MagicMock()
        mock_extraction.output = PredictiveParameterExtraction(
            extracted_params={
                "input_dim": 10,
                "hidden_dim": 128,
                "learning_rate": 0.001,
                "epochs": 100
            },
            missing_params=["batch_size", "num_layers"],
            confidence=0.8,
            notes="提取了部分参数"
        )
        
        with patch.object(assistant, 'classify_algorithm', return_value=mock_classification), \
             patch.object(assistant, 'extract_parameters', return_value=mock_extraction):
            
            result, state, response = assistant.process_user_input("用LSTM做预测，学习率0.001", state_dict)
            
            # 验证返回值
            assert isinstance(result, dict)
            assert isinstance(state, dict)
            assert isinstance(response, str)
            # 应该返回需要补充参数的消息
            assert "missing" in result or "params" in result
    
    def test_handle_new_conversation_method(self):
        """测试处理新对话方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState
        
        assistant = PredictiveAlgorithmAssistant()
        state = PredictiveAgentState()
        
        # 测试新对话处理
        result, new_state, response = assistant.handle_new_conversation("你好", state)
        
        # 验证返回值
        assert isinstance(result, dict)
        assert isinstance(new_state, dict)
        assert isinstance(response, str)
        assert "msg" in result
        # 状态应该更新为已显示欢迎信息
        assert new_state.get("has_shown_welcome") is True
    
    def test_generate_report_method(self):
        """测试生成报告方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        
        assistant = PredictiveAlgorithmAssistant()
        
        # 测试数据
        analysis_data = {
            "algorithm_type": "LSTM",
            "accuracy": 0.95,
            "analysis": "模型表现良好",
            "parameters": {"learning_rate": 0.001, "epochs": 100}
        }
        
        # Mock模板和PDF生成
        with patch('jinja2.Template') as mock_template, \
             patch('pdfkit.from_string') as mock_pdf, \
             patch('os.path.exists', return_value=True):
            
            mock_template_instance = MagicMock()
            mock_template_instance.render.return_value = "<html>测试报告</html>"
            mock_template.return_value = mock_template_instance
            mock_pdf.return_value = True
            
            result = assistant.generate_report("task_123", analysis_data)
            
            # 验证返回值（generate_report返回字符串）
            assert isinstance(result, str)
            assert len(result) > 0
    
    @pytest.mark.asyncio
    async def test_process_user_input_async_method(self):
        """测试异步处理用户输入方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        
        assistant = PredictiveAlgorithmAssistant()
        
        # Mock异步新对话处理
        with patch.object(assistant, 'handle_new_conversation_async', 
                         return_value=({"msg": "异步欢迎"}, {"is_new_conversation": False}, "async_welcome")):
            
            result, state, response = await assistant.process_user_input_async("你好", project_id="project_123")
            
            # 验证返回值
            assert isinstance(result, dict)
            assert isinstance(state, dict)
            assert isinstance(response, str)
            assert result["msg"] == "异步欢迎"
            assert response == "async_welcome"
    
    @pytest.mark.asyncio
    async def test_classify_interaction_async_method(self):
        """测试异步交互分类方法"""
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        from agents.predict_algorith_agent.models.predictive_models import (
            PredictiveInteractionClassification, InteractionType
        )
        
        assistant = PredictiveAlgorithmAssistant()
        
        # Mock异步交互分类器
        mock_result = AsyncMock()
        mock_result.output = PredictiveInteractionClassification(
            interaction_type=InteractionType.ADJUST,
            confidence=0.85,
            reason="用户要调整参数"
        )
        
        # 直接Mock异步方法
        with patch.object(assistant, 'classify_interaction_async', return_value={"output": mock_result.output}):
            result = await assistant.classify_interaction_async("我想调整学习率")
            
            # 验证返回值
            assert isinstance(result, dict)
            assert 'output' in result
            assert result['output'].interaction_type == InteractionType.ADJUST
            assert result['output'].confidence == 0.85
