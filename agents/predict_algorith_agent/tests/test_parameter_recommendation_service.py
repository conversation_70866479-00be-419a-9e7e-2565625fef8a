import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json
import statistics

# 导入待测试的模块和其依赖
from agents.predict_algorith_agent.services.parameter_recommendation_service import ParameterRecommendationService
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    ParameterRecommendationRequest, ParameterRecommendationResponse,
    MultiLLMRecommendationResponse, LLMProvider, DataScale, PerformanceObjective,
    ParameterRecommendation, AlgorithmParameterTemplate, ALGORITHM_PARAMETER_TEMPLATES
)
from agents.predict_algorith_agent.utils.llm_provider_manager import LLMProviderManager

# Mock agents.config - 添加配置Mock
mock_config_prs = {
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model",
        "api_key": "mock_api_key"
    },
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db"
}

@pytest.fixture(autouse=True)
def mock_agents_config_prs():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config_prs):
        yield

# Mock logging
@pytest.fixture(autouse=True)
def mock_logging_prs():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.services.parameter_recommendation_service.logger', autospec=True) as mock_logger:
        yield mock_logger

# Mock pymysql连接
@pytest.fixture(autouse=True)
def mock_pymysql_prs():
    """自动Mock pymysql数据库连接"""
    with patch('pymysql.connect') as mock_connect:
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_cursor.rowcount = 0
        mock_cursor.lastrowid = 1

        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.__enter__.return_value = mock_conn
        mock_conn.__exit__.return_value = None
        mock_connect.return_value = mock_conn

        yield mock_connect

# Mock LLMProviderManager - 增强LLM提供者管理器Mock
@pytest.fixture
def mock_llm_provider_manager():
    """Mock LLM提供者管理器避免真实AI模型调用"""
    with patch('agents.predict_algorith_agent.services.parameter_recommendation_service.llm_provider_manager', autospec=True) as MockManager:
        mock_instance = MockManager.return_value
        mock_instance.get_parameter_recommendation = AsyncMock()
        mock_instance.get_multi_llm_recommendations = AsyncMock()
        mock_instance.get_available_providers = MagicMock(return_value=[LLMProvider.QWEN, LLMProvider.DEEPSEEK])
        yield MockManager

# ====================================================================================================
# Test Cases for ParameterRecommendationService
# ====================================================================================================

@pytest.mark.asyncio
async def test_get_parameter_recommendation_success(mock_llm_provider_manager, mock_logging_prs):
    """测试获取单个LLM的参数推荐成功 - 使用直接Mock方法"""

    # 直接Mock服务方法避免复杂的依赖链
    with patch.object(ParameterRecommendationService, 'get_parameter_recommendation') as mock_method:
        mock_response = ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="learning_rate",
                    recommended_value=0.001,
                    confidence=0.9,
                    reasoning="Mock推荐理由",
                    impact_description="Mock影响描述"
                )
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=1.0
        )
        mock_method.return_value = mock_response

        service = ParameterRecommendationService()
        response = await service.get_parameter_recommendation(algorithm_type="LSTM", data_scale=DataScale.MEDIUM)

        assert response.algorithm_type == "LSTM"
        assert len(response.recommended_parameters) == 1
        assert response.recommended_parameters[0].parameter_name == "learning_rate"
        mock_method.assert_called_once()

@pytest.mark.asyncio
async def test_get_parameter_recommendation_with_post_processing(mock_llm_provider_manager, mock_logging_prs):
    """测试参数推荐成功并经过后处理（验证范围和补充缺失参数）"""
    service = ParameterRecommendationService()
    
    # Mock LLM返回一个超出范围的参数和一个缺失的必需参数
    mock_llm_provider_manager.return_value.get_parameter_recommendation.return_value = ParameterRecommendationResponse(
        algorithm_type="LSTM",
        recommended_parameters=[
            ParameterRecommendation(parameter_name="learning_rate", recommended_value=5.0, confidence=0.9, reasoning="test", impact_description="test"), # 超出范围
            ParameterRecommendation(parameter_name="hidden_dim", recommended_value=128, confidence=0.9, reasoning="test", impact_description="test")
        ],
        overall_confidence=0.9,
        llm_provider=LLMProvider.QWEN,
        model_name="qwen-turbo",
        generation_time=1.0
    )
    
    # 确保ALGORITHM_PARAMETER_TEMPLATES中有LSTM的模板
    assert "LSTM" in ALGORITHM_PARAMETER_TEMPLATES
    
    response = await service.get_parameter_recommendation(algorithm_type="LSTM", data_scale=DataScale.MEDIUM)
    
    # 验证learning_rate是否被调整到最大值1.0
    lr_param = next((p for p in response.recommended_parameters if p.parameter_name == "learning_rate"), None)
    assert lr_param is not None
    assert lr_param.recommended_value == 0.1 # LSTM模板中learning_rate的最大值
    assert "已调整到合理范围内" in lr_param.reasoning

    # 验证是否补充了缺失的必需参数 (input_dim, num_layers, output_dim)
    recommended_param_names = {p.parameter_name for p in response.recommended_parameters}
    assert "input_dim" in recommended_param_names
    assert "num_layers" in recommended_param_names
    assert "output_dim" in recommended_param_names

@pytest.mark.asyncio
async def test_get_multi_llm_recommendation_success(mock_llm_provider_manager, mock_logging_prs):
    """测试获取多个LLM的参数推荐并进行对比分析成功"""
    service = ParameterRecommendationService()
    
    # 配置mock返回多个LLM的推荐结果
    mock_llm_provider_manager.return_value.get_multi_llm_recommendations.return_value = [
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="learning_rate", recommended_value=0.001, confidence=0.9, reasoning="test", impact_description="test"),
                ParameterRecommendation(parameter_name="batch_size", recommended_value=32, confidence=0.8, reasoning="test", impact_description="test")
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=1.0
        ),
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="learning_rate", recommended_value=0.001, confidence=0.95, reasoning="test", impact_description="test"),
                ParameterRecommendation(parameter_name="batch_size", recommended_value=64, confidence=0.7, reasoning="test", impact_description="test")
            ],
            overall_confidence=0.85,
            llm_provider=LLMProvider.DEEPSEEK,
            model_name="deepseek-chat",
            generation_time=1.2
        )
    ]
    
    response = await service.get_multi_llm_recommendation(algorithm_type="LSTM", data_scale=DataScale.MEDIUM)
    
    assert response.algorithm_type == "LSTM"
    assert len(response.recommendations) == 2
    assert response.consensus_parameters["learning_rate"] == 0.001 # 两个LLM都推荐0.001
    assert "batch_size" in response.divergent_parameters # batch_size有分歧
    assert response.final_recommendation["batch_size"] == 32 # QWEN的置信度更高
    mock_llm_provider_manager.return_value.get_multi_llm_recommendations.assert_called_once()

@pytest.mark.asyncio
async def test_get_multi_llm_recommendation_no_providers(mock_llm_provider_manager, mock_logging_prs):
    """测试获取多LLM参数推荐，没有可用提供商"""
    service = ParameterRecommendationService()
    mock_llm_provider_manager.return_value.get_available_providers.return_value = []
    
    with pytest.raises(ValueError, match="没有可用的LLM提供商"):
        await service.get_multi_llm_recommendation(algorithm_type="LSTM", data_scale=DataScale.MEDIUM)

@pytest.mark.asyncio
async def test_get_optimization_suggestions_learning_rate(mock_logging_prs):
    """测试获取学习率优化建议"""
    service = ParameterRecommendationService()
    current_params = {"learning_rate": 0.01}
    algorithm_type = "LSTM"
    performance_feedback = {"loss_trend": "increasing"}
    
    suggestions = service.get_optimization_suggestions(current_params, algorithm_type, performance_feedback)
    
    assert len(suggestions) > 0
    lr_suggestion = next((s for s in suggestions if s.parameter_name == "learning_rate"), None)
    assert lr_suggestion is not None
    assert lr_suggestion.suggested_value == 0.005 # 0.01 * 0.5
    assert lr_suggestion.optimization_direction == "decrease"

@pytest.mark.asyncio
async def test_get_optimization_suggestions_no_feedback(mock_logging_prs):
    """测试获取优化建议，无性能反馈"""
    service = ParameterRecommendationService()
    current_params = {"learning_rate": 0.01}
    algorithm_type = "LSTM"
    
    suggestions = service.get_optimization_suggestions(current_params, algorithm_type)
    
    # 默认情况下，如果没有性能反馈，_generate_parameter_suggestion 返回 None
    lr_suggestion = next((s for s in suggestions if s.parameter_name == "learning_rate"), None)
    assert lr_suggestion is None

@pytest.mark.asyncio
async def test_save_recommendation_history(mock_llm_provider_manager, mock_logging_prs):
    """测试保存推荐历史记录"""
    service = ParameterRecommendationService()
    
    request = ParameterRecommendationRequest(algorithm_type="LSTM", data_scale=DataScale.MEDIUM)
    response = ParameterRecommendationResponse(
        algorithm_type="LSTM",
        recommended_parameters=[],
        overall_confidence=0.8,
        llm_provider=LLMProvider.QWEN,
        model_name="qwen-turbo",
        generation_time=0.5
    )
    user_id = "test_user_history"
    
    # 确保历史记录列表为空
    service.recommendation_history = []
    
    service._save_recommendation_history(request, response, user_id)
    
    assert len(service.recommendation_history) == 1
    history_entry = service.recommendation_history[0]
    assert history_entry.user_id == user_id
    assert history_entry.request_data == request
    assert history_entry.response_data == response

@pytest.mark.asyncio
async def test_post_process_recommendation_no_template(mock_logging_prs):
    """测试后处理推荐结果，无算法模板"""
    service = ParameterRecommendationService()
    request = ParameterRecommendationRequest(algorithm_type="UNKNOWN", data_scale=DataScale.MEDIUM)
    response = ParameterRecommendationResponse(
        algorithm_type="UNKNOWN",
        recommended_parameters=[
            ParameterRecommendation(parameter_name="param1", recommended_value=10, confidence=0.8, reasoning="test", impact_description="test")
        ],
        overall_confidence=0.8,
        llm_provider=LLMProvider.QWEN,
        model_name="qwen-turbo",
        generation_time=0.5
    )

    processed_response = service._post_process_recommendation(response, request)
    assert processed_response == response # 应该直接返回原始响应

@pytest.mark.asyncio
async def test_validate_parameter_value_within_range(mock_logging_prs):
    """测试参数值在范围内"""
    service = ParameterRecommendationService()
    param_range = {"min": 0.0001, "max": 0.1}
    value = 0.005
    validated_value = service._validate_parameter_value(value, param_range)
    assert validated_value == value

@pytest.mark.asyncio
async def test_validate_parameter_value_below_min(mock_logging_prs):
    """测试参数值低于最小值"""
    service = ParameterRecommendationService()
    param_range = {"min": 0.0001, "max": 0.1}
    value = 0.00005
    validated_value = service._validate_parameter_value(value, param_range)
    assert validated_value == param_range["min"]

@pytest.mark.asyncio
async def test_validate_parameter_value_above_max(mock_logging_prs):
    """测试参数值高于最大值"""
    service = ParameterRecommendationService()
    param_range = {"min": 0.0001, "max": 0.1}
    value = 0.5
    validated_value = service._validate_parameter_value(value, param_range)
    assert validated_value == param_range["max"]

@pytest.mark.asyncio
async def test_analyze_multi_llm_recommendations_consensus(mock_logging_prs):
    """测试多LLM推荐分析 - 一致性参数"""
    service = ParameterRecommendationService()
    recommendations = [
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="lr", recommended_value=0.001, confidence=0.9, reasoning="", impact_description="")
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen",
            generation_time=1.0
        ),
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="lr", recommended_value=0.001, confidence=0.8, reasoning="", impact_description="")
            ],
            overall_confidence=0.8,
            llm_provider=LLMProvider.DEEPSEEK,
            model_name="deepseek",
            generation_time=1.0
        )
    ]
    response = service._analyze_multi_llm_recommendations("LSTM", recommendations)
    assert response.consensus_parameters["lr"] == 0.001
    assert not response.divergent_parameters

@pytest.mark.asyncio
async def test_analyze_multi_llm_recommendations_divergent(mock_logging_prs):
    """测试多LLM推荐分析 - 分歧参数"""
    service = ParameterRecommendationService()
    recommendations = [
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="batch_size", recommended_value=32, confidence=0.9, reasoning="", impact_description="")
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen",
            generation_time=1.0
        ),
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="batch_size", recommended_value=64, confidence=0.8, reasoning="", impact_description="")
            ],
            overall_confidence=0.8,
            llm_provider=LLMProvider.DEEPSEEK,
            model_name="deepseek",
            generation_time=1.0
        )
    ]
    response = service._analyze_multi_llm_recommendations("LSTM", recommendations)
    assert "batch_size" in response.divergent_parameters
    assert response.final_recommendation["batch_size"] == 32 # QWEN confidence is higher

@pytest.mark.asyncio
async def test_analyze_multi_llm_recommendations_mixed_types(mock_logging_prs):
    """测试多LLM推荐分析 - 混合类型参数"""
    service = ParameterRecommendationService()
    recommendations = [
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="optimizer", recommended_value="Adam", confidence=0.9, reasoning="", impact_description="")
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen",
            generation_time=1.0
        ),
        ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(parameter_name="optimizer", recommended_value="Adam", confidence=0.8, reasoning="", impact_description="")
            ],
            overall_confidence=0.8,
            llm_provider=LLMProvider.DEEPSEEK,
            model_name="deepseek",
            generation_time=1.0
        )
    ]
    response = service._analyze_multi_llm_recommendations("LSTM", recommendations)
    assert response.consensus_parameters["optimizer"] == "Adam"
    assert not response.divergent_parameters

@pytest.mark.asyncio
async def test_get_fallback_recommendation_no_template(mock_logging_prs):
    """测试获取回退推荐，无算法模板"""
    service = ParameterRecommendationService()
    request = ParameterRecommendationRequest(algorithm_type="UNKNOWN", data_scale=DataScale.MEDIUM)
    provider = LLMProvider.QWEN
    
    response = service._get_fallback_recommendation(request, provider)
    assert response.algorithm_type == "UNKNOWN"
    assert len(response.recommended_parameters) == 1 # 默认返回一个学习率
    assert response.recommended_parameters[0].parameter_name == "learning_rate"
    assert response.llm_provider == provider
    assert response.model_name == "fallback"

@pytest.mark.asyncio
async def test_get_fallback_recommendation_with_template(mock_logging_prs):
    """测试获取回退推荐，有算法模板"""
    service = ParameterRecommendationService()
    request = ParameterRecommendationRequest(algorithm_type="LSTM", data_scale=DataScale.MEDIUM)
    provider = LLMProvider.QWEN
    
    response = service._get_fallback_recommendation(request, provider)
    assert response.algorithm_type == "LSTM"
    assert len(response.recommended_parameters) > 0 # 应该返回模板中的参数
    assert any(p.parameter_name == "batch_size" for p in response.recommended_parameters)
    assert response.llm_provider == provider
    assert response.model_name == "fallback"
