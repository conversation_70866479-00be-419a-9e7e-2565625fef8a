### 预测性算法智能体HTTP测试文件
### 基础URL配置
@baseUrl = http://localhost:8008
@contentType = application/json

### ===========================================
### 1. 基础连通性测试
### ===========================================

### 1.1 测试根路径
GET {{baseUrl}}/

### 1.2 测试健康检查
GET {{baseUrl}}/health

### ===========================================
### 2. 新建对话引导测试
### ===========================================

### 2.1 场景：新用户首次进入（空输入）
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "",
    "user_id": "new_user_001",
    "session_id": "new_session_001"
}

### 2.2 场景：新用户问候
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "你好",
    "user_id": "new_user_002",
    "session_id": "new_session_002"
}

### 2.3 场景：选择预设问题1
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "1",
    "user_id": "new_user_003",
    "session_id": "new_session_003"
}

### 2.4 场景：选择预设问题2
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "2",
    "user_id": "new_user_004",
    "session_id": "new_session_004"
}

### 2.5 场景：直接询问功能
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "你能提供哪些帮助？",
    "user_id": "new_user_005",
    "session_id": "new_session_005"
}

### ===========================================
### 3. 预测性算法主流程测试
### ===========================================

### 3.1 场景一：参数齐全，直接发起任务
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "我想用LSTM做设备预测性维护，输入维度10，隐藏层128，2层，输出1，batch 32，学习率0.001，训练100轮。",
    "user_id": "user001",
    "session_id": "sess001"
}

### 3.2 场景二：参数不全，需补充
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "我要用LSTM做预测，输入维度10，隐藏层128。",
    "user_id": "user002",
    "session_id": "sess002"
}

### 3.3 场景三：补充参数
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "2层，输出1，batch 32，学习率0.001，训练100轮。",
    "user_id": "user002",
    "session_id": "sess002"
}

### 3.4 场景四：参数确认
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "确认",
    "user_id": "user002",
    "session_id": "sess002"
}

### 3.5 场景五：查询任务进度
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "查询任务进度",
    "user_id": "user001",
    "session_id": "sess001"
}

### 3.6 场景六：查询任务结果
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "查询结果",
    "user_id": "user001",
    "session_id": "sess001"
}

### 3.7 场景七：生成分析报告
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "生成报告",
    "user_id": "user001",
    "session_id": "sess001"
}

### 3.8 场景八：下载PDF报告 (注意：此端点需要在独立的predict_main.py服务中运行)
# GET {{baseUrl}}/api/predict_algorith/report/pdf?task_id=mock_task_123456

### ===========================================
### 4. 错误处理测试
### ===========================================

### 4.1 测试缺少必填字段
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "我想用LSTM做预测"
}

### 4.2 测试空请求
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{}