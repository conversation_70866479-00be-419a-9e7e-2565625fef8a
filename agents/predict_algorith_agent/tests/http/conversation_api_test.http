### 对话管理API测试文件
### 基础URL配置
@baseUrl = http://localhost:8008
@contentType = application/json

### ===========================================
### 1. 基础连通性测试
### ===========================================

### 1.1 测试健康检查
GET {{baseUrl}}/health

### ===========================================
### 2. 对话管理功能测试
### ===========================================

### 2.1 创建新对话 - 训练类型
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "test_user_001",
    "title": "LSTM算法训练任务",
    "conversation_type": "training"
}

### 2.2 创建新对话 - 查询类型
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "test_user_001",
    "title": "算法性能查询",
    "conversation_type": "query"
}

### 2.3 创建新对话 - 默认参数
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "test_user_002"
}

### 2.4 获取用户对话列表 - 第一页
GET {{baseUrl}}/api/conversations?user_id=test_user_001&page=1&size=10

### 2.5 获取用户对话列表 - 分页测试
GET {{baseUrl}}/api/conversations?user_id=test_user_001&page=1&size=5

### 2.6 获取对话详情 (需要先创建对话获取conversation_id)
# 请将下面的conversation_id替换为实际创建的对话ID
GET {{baseUrl}}/api/conversations/conv_example_id

### 2.7 更新对话状态
# 请将下面的conversation_id替换为实际创建的对话ID
POST {{baseUrl}}/api/conversations/conv_example_id/update-status
Content-Type: {{contentType}}

{
    "status": "completed",
    "progress": 100.0,
    "algorithm_type": "LSTM",
    "task_summary": "LSTM算法训练已完成，准确率达到95%"
}

### 2.8 删除对话
# 请将下面的conversation_id替换为实际创建的对话ID
POST {{baseUrl}}/api/conversations/conv_example_id/delete

### ===========================================
### 3. 集成测试 - 对话与算法接口结合
### ===========================================

### 3.1 创建对话并开始算法训练流程
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "integration_user",
    "title": "集成测试 - CNN图像分类",
    "conversation_type": "training"
}

### 3.2 在对话中进行算法交互 (需要使用上一步返回的conversation_id)
# 请将conversation_id替换为实际值
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "",
    "user_id": "integration_user",
    "session_id": "integration_session",
    "conversation_id": "conv_example_id"
}

### 3.3 继续对话 - 选择预设问题1
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "1",
    "user_id": "integration_user",
    "session_id": "integration_session",
    "conversation_id": "conv_example_id"
}

### 3.4 继续对话 - 描述具体任务
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "我想用CNN做图像分类，输入尺寸224x224，3个通道，10个类别，batch size 32，学习率0.001，训练50轮",
    "user_id": "integration_user",
    "session_id": "integration_session",
    "conversation_id": "conv_example_id"
}

### 3.5 参数确认
POST {{baseUrl}}/api/predict_algorith
Content-Type: {{contentType}}

{
    "question": "确认",
    "user_id": "integration_user",
    "session_id": "integration_session",
    "conversation_id": "conv_example_id"
}

### 3.6 查看对话详情和消息记录
GET {{baseUrl}}/api/conversations/conv_example_id

### ===========================================
### 4. 错误处理测试
### ===========================================

### 4.1 创建对话 - 缺少必填字段
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "title": "缺少用户ID的对话"
}

### 4.2 获取不存在的对话详情
GET {{baseUrl}}/api/conversations/non_existent_id

### 4.3 删除不存在的对话
POST {{baseUrl}}/api/conversations/non_existent_id/delete

### 4.4 更新不存在对话的状态
POST {{baseUrl}}/api/conversations/non_existent_id/update-status
Content-Type: {{contentType}}

{
    "status": "completed"
}

### ===========================================
### 5. 性能测试
### ===========================================

### 5.1 批量创建对话
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "perf_test_user",
    "title": "性能测试对话 1"
}

### 5.2 批量创建对话
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "perf_test_user",
    "title": "性能测试对话 2"
}

### 5.3 批量创建对话
POST {{baseUrl}}/api/conversations
Content-Type: {{contentType}}

{
    "user_id": "perf_test_user",
    "title": "性能测试对话 3"
}

### 5.4 获取大量对话列表
GET {{baseUrl}}/api/conversations?user_id=perf_test_user&page=1&size=50

### ===========================================
### 6. 数据清理
### ===========================================

### 6.1 获取测试用户的所有对话
GET {{baseUrl}}/api/conversations?user_id=test_user_001&page=1&size=100

### 6.2 获取集成测试用户的所有对话
GET {{baseUrl}}/api/conversations?user_id=integration_user&page=1&size=100

### 6.3 获取性能测试用户的所有对话
GET {{baseUrl}}/api/conversations?user_id=perf_test_user&page=1&size=100

# 注意：删除对话请手动执行，避免误删重要数据
