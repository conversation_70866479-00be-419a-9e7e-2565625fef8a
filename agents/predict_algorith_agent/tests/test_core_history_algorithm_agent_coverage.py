#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史算法智能体测试 - 专注于提高代码覆盖率
测试core/history_algorithm_agent.py中的主要类和方法
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# 导入待测试的模块
from agents.predict_algorith_agent.core.history_algorithm_agent import (
    HistoryAlgorithmAgent,
    UserRequirementAnalysis
)
from agents.predict_algorith_agent.services.history_algorithm_service import (
    HistoryAlgorithmService,
    HistoryAlgorithm,
    HistoryAlgorithmListResponse
)

# Mock配置
mock_config_history = {
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model",
        "api_key": "mock_api_key"
    },
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "ALGORITHM_PLATFORM_BASE_URL": "http://localhost:9999"
}

@pytest.fixture(autouse=True)
def mock_agents_config_history():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config_history):
        yield

@pytest.fixture(autouse=True)
def mock_logging_history():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.utils.logging_config.setup_logging'):
        yield

@pytest.fixture(autouse=True)
def mock_pymysql_history():
    """自动Mock pymysql数据库连接"""
    with patch('pymysql.connect') as mock_connect:
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_cursor.rowcount = 0
        mock_cursor.lastrowid = 1
        
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.__enter__.return_value = mock_conn
        mock_conn.__exit__.return_value = None
        mock_connect.return_value = mock_conn
        
        yield mock_connect

@pytest.fixture
def mock_llm_model():
    """Mock LLM模型"""
    with patch('pydantic_ai.models.openai.OpenAIModel') as MockModel:
        mock_instance = MagicMock()
        MockModel.return_value = mock_instance
        yield mock_instance

@pytest.fixture
def mock_history_service():
    """Mock历史算法服务"""
    mock_service = MagicMock(spec=HistoryAlgorithmService)
    mock_service.get_all_algorithms = AsyncMock()
    mock_service.search_algorithms = AsyncMock()
    return mock_service

@pytest.fixture
def mock_pydantic_ai_agent_history():
    """Mock PydanticAI Agent"""
    with patch('pydantic_ai.Agent') as MockAgent:
        mock_instance = AsyncMock()
        mock_instance.run_sync = MagicMock()
        mock_instance.run = AsyncMock()
        MockAgent.return_value = mock_instance
        yield MockAgent, mock_instance

class TestHistoryAlgorithmAgent:
    """测试HistoryAlgorithmAgent类的主要方法"""
    
    def test_agent_initialization(self, mock_llm_model, mock_history_service, mock_pydantic_ai_agent_history):
        """测试历史算法智能体初始化"""
        MockAgent, mock_instance = mock_pydantic_ai_agent_history
        
        agent = HistoryAlgorithmAgent(mock_llm_model, mock_history_service)
        
        # 验证初始化
        assert agent is not None
        assert agent.history_service == mock_history_service
        assert hasattr(agent, 'requirement_analyzer')
        
        # 验证Agent被正确创建
        MockAgent.assert_called()

    @pytest.mark.asyncio
    async def test_process_user_request_success(self, mock_llm_model, mock_history_service, mock_pydantic_ai_agent_history):
        """测试处理用户请求成功"""
        MockAgent, mock_instance = mock_pydantic_ai_agent_history
        
        # 配置Mock返回值
        mock_instance.run.return_value = MagicMock(
            output=UserRequirementAnalysis(
                data_type="时序数据",
                task_type="预测",
                domain="工业",
                performance_requirements=["高精度"],
                constraints=["实时性"],
                keywords=["LSTM", "时序预测"]
            )
        )
        
        # 配置历史算法服务返回值
        mock_algorithms = [
            HistoryAlgorithm(
                name="水泵电流预警算法",
                project_id="285",
                algorithm_type="LSTM",
                description="用于水泵电流异常预警",
                accuracy=0.95,
                created_date="2024-07-23"
            )
        ]
        mock_history_service.search_algorithms.return_value = HistoryAlgorithmListResponse(
            algorithms=mock_algorithms,
            total_count=1,
            msg="查询成功"
        )
        
        agent = HistoryAlgorithmAgent(mock_llm_model, mock_history_service)
        result = await agent.process_user_request("我需要一个LSTM算法来预测水泵电流")
        
        # 验证结果
        assert result is not None
        assert "history_algorithms" in result
        assert len(result["history_algorithms"]) == 1
        assert result["total_count"] == 1
        
        # 验证方法调用
        mock_instance.run.assert_called()
        mock_history_service.search_algorithms.assert_called()

    @pytest.mark.asyncio
    async def test_process_user_request_no_algorithms_found(self, mock_llm_model, mock_history_service, mock_pydantic_ai_agent_history):
        """测试处理用户请求但未找到算法"""
        MockAgent, mock_instance = mock_pydantic_ai_agent_history
        
        # 配置Mock返回值
        mock_instance.run.return_value = MagicMock(
            output=UserRequirementAnalysis(
                data_type="图像数据",
                task_type="分类",
                domain="医疗",
                performance_requirements=["高准确率"],
                constraints=[],
                keywords=["CNN", "图像分类"]
            )
        )
        
        # 配置历史算法服务返回空结果
        mock_history_service.search_algorithms.return_value = HistoryAlgorithmListResponse(
            algorithms=[],
            total_count=0,
            msg="未找到匹配的算法"
        )
        
        agent = HistoryAlgorithmAgent(mock_llm_model, mock_history_service)
        result = await agent.process_user_request("我需要一个CNN算法来分类医疗图像")
        
        # 验证结果
        assert result is not None
        assert "history_algorithms" in result
        assert len(result["history_algorithms"]) == 0
        assert result["total_count"] == 0
        
        # 验证方法调用
        mock_instance.run.assert_called()
        mock_history_service.search_algorithms.assert_called()

    @pytest.mark.asyncio
    async def test_process_user_request_service_error(self, mock_llm_model, mock_history_service, mock_pydantic_ai_agent_history):
        """测试处理用户请求时服务出错"""
        MockAgent, mock_instance = mock_pydantic_ai_agent_history
        
        # 配置Mock返回值
        mock_instance.run.return_value = MagicMock(
            output=UserRequirementAnalysis(
                data_type="文本数据",
                task_type="分类",
                domain="金融",
                performance_requirements=["高效率"],
                constraints=["低延迟"],
                keywords=["RNN", "文本分类"]
            )
        )
        
        # 配置历史算法服务抛出异常
        mock_history_service.search_algorithms.side_effect = Exception("服务连接失败")
        
        agent = HistoryAlgorithmAgent(mock_llm_model, mock_history_service)
        
        # 测试异常处理
        with pytest.raises(Exception, match="服务连接失败"):
            await agent.process_user_request("我需要一个RNN算法来分类金融文本")
        
        # 验证方法调用
        mock_instance.run.assert_called()
        mock_history_service.search_algorithms.assert_called()

    @pytest.mark.asyncio
    async def test_analyze_user_requirement(self, mock_llm_model, mock_history_service, mock_pydantic_ai_agent_history):
        """测试分析用户需求方法"""
        MockAgent, mock_instance = mock_pydantic_ai_agent_history
        
        # 配置Mock返回值
        mock_analysis = UserRequirementAnalysis(
            data_type="传感器数据",
            task_type="异常检测",
            domain="制造业",
            performance_requirements=["实时性", "高准确率"],
            constraints=["资源限制"],
            keywords=["异常检测", "传感器", "制造"]
        )
        mock_instance.run.return_value = MagicMock(output=mock_analysis)
        
        agent = HistoryAlgorithmAgent(mock_llm_model, mock_history_service)
        
        # 由于这是私有方法，我们通过process_user_request间接测试
        # 或者直接测试如果方法是公开的
        user_input = "我需要检测制造设备传感器数据的异常"
        
        # 这里我们假设有一个公开的分析方法，如果没有，可以通过其他方式测试
        # result = await agent.analyze_user_requirement(user_input)
        
        # 通过process_user_request间接测试
        mock_history_service.search_algorithms.return_value = HistoryAlgorithmListResponse(
            algorithms=[], total_count=0, msg="测试"
        )
        
        await agent.process_user_request(user_input)
        
        # 验证LLM被调用
        mock_instance.run.assert_called()

class TestUserRequirementAnalysis:
    """测试UserRequirementAnalysis数据模型"""
    
    def test_user_requirement_analysis_creation(self):
        """测试用户需求分析对象创建"""
        analysis = UserRequirementAnalysis(
            data_type="时序数据",
            task_type="预测",
            domain="能源",
            performance_requirements=["高精度", "低延迟"],
            constraints=["内存限制"],
            keywords=["LSTM", "时序", "预测"]
        )
        
        # 验证对象创建
        assert analysis.data_type == "时序数据"
        assert analysis.task_type == "预测"
        assert analysis.domain == "能源"
        assert len(analysis.performance_requirements) == 2
        assert len(analysis.constraints) == 1
        assert len(analysis.keywords) == 3

    def test_user_requirement_analysis_optional_fields(self):
        """测试用户需求分析对象的可选字段"""
        analysis = UserRequirementAnalysis(
            data_type="图像数据",
            task_type="分类",
            domain="医疗"
        )
        
        # 验证可选字段的默认值
        assert analysis.data_type == "图像数据"
        assert analysis.task_type == "分类"
        assert analysis.domain == "医疗"
        assert analysis.performance_requirements == []
        assert analysis.constraints == []
        assert analysis.keywords == []

class TestHistoryAlgorithmAgentIntegration:
    """测试历史算法智能体的集成功能"""
    
    @pytest.mark.asyncio
    async def test_full_workflow(self, mock_llm_model, mock_history_service, mock_pydantic_ai_agent_history):
        """测试完整的工作流程"""
        MockAgent, mock_instance = mock_pydantic_ai_agent_history
        
        # 配置完整的Mock链
        mock_instance.run.return_value = MagicMock(
            output=UserRequirementAnalysis(
                data_type="多维时序数据",
                task_type="预测和分类",
                domain="智能制造",
                performance_requirements=["高精度", "实时性"],
                constraints=["边缘计算"],
                keywords=["LSTM", "CNN", "混合模型"]
            )
        )
        
        mock_algorithms = [
            HistoryAlgorithm(
                name="智能制造预测算法v1",
                project_id="300",
                algorithm_type="LSTM+CNN",
                description="混合模型用于智能制造预测",
                accuracy=0.92,
                created_date="2024-08-01"
            ),
            HistoryAlgorithm(
                name="边缘计算优化算法",
                project_id="301",
                algorithm_type="轻量级LSTM",
                description="适用于边缘计算的轻量级模型",
                accuracy=0.88,
                created_date="2024-08-05"
            )
        ]
        
        mock_history_service.search_algorithms.return_value = HistoryAlgorithmListResponse(
            algorithms=mock_algorithms,
            total_count=2,
            msg="找到2个匹配的算法"
        )
        
        agent = HistoryAlgorithmAgent(mock_llm_model, mock_history_service)
        result = await agent.process_user_request(
            "我需要一个适用于边缘计算的智能制造预测算法，要求高精度和实时性"
        )
        
        # 验证完整流程结果
        assert result is not None
        assert result["total_count"] == 2
        assert len(result["history_algorithms"]) == 2
        
        # 验证算法信息
        algorithms = result["history_algorithms"]
        assert algorithms[0]["name"] == "智能制造预测算法v1"
        assert algorithms[1]["name"] == "边缘计算优化算法"
        
        # 验证所有Mock方法都被调用
        mock_instance.run.assert_called()
        mock_history_service.search_algorithms.assert_called()
