#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史算法服务测试 - 专注于提高代码覆盖率
测试services/history_algorithm_service.py中的主要类和方法
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# Mock配置
mock_config_history_service = {
    "ALGORITHM_PLATFORM_BASE_URL": "http://localhost:9999",
    "ALGORITHM_PLATFORM_TIMEOUT": 5,
    "ALGORITHM_PLATFORM_RETRY_TIMES": 2,
    "ALGORITHM_PLATFORM_RETRY_DELAY": 0.1,
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model",
        "api_key": "mock_api_key"
    }
}

@pytest.fixture(autouse=True)
def mock_agents_config_history_service():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config_history_service):
        yield

@pytest.fixture(autouse=True)
def mock_logging_history_service():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.services.history_algorithm_service.logger'):
        yield

class TestHistoryAlgorithmServiceImports:
    """测试历史算法服务导入"""
    
    def test_history_algorithm_service_import(self):
        """测试历史算法服务导入"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithm,
                HistoryAlgorithmListResponse
            )
            assert HistoryAlgorithmService is not None
            assert HistoryAlgorithm is not None
            assert HistoryAlgorithmListResponse is not None
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

class TestHistoryAlgorithm:
    """测试HistoryAlgorithm数据模型"""
    
    def test_history_algorithm_creation(self):
        """测试历史算法对象创建"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import HistoryAlgorithm
            
            algorithm = HistoryAlgorithm(
                name="测试算法",
                project_id="123",
                algorithm_type="LSTM",
                description="测试描述",
                accuracy=0.95,
                created_date="2024-01-01"
            )
            
            assert algorithm.name == "测试算法"
            assert algorithm.project_id == "123"
            assert algorithm.algorithm_type == "LSTM"
            assert algorithm.description == "测试描述"
            assert algorithm.accuracy == 0.95
            assert algorithm.created_date == "2024-01-01"
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    def test_history_algorithm_optional_fields(self):
        """测试历史算法对象的可选字段"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import HistoryAlgorithm
            
            algorithm = HistoryAlgorithm(
                name="简单算法",
                project_id="456"
            )
            
            assert algorithm.name == "简单算法"
            assert algorithm.project_id == "456"
            # 验证可选字段的默认值
            assert algorithm.algorithm_type == ""
            assert algorithm.description == ""
            assert algorithm.accuracy == 0.0
            assert algorithm.created_date == ""
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

class TestHistoryAlgorithmListResponse:
    """测试HistoryAlgorithmListResponse数据模型"""
    
    def test_history_algorithm_list_response_creation(self):
        """测试历史算法列表响应创建"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmListResponse,
                HistoryAlgorithm
            )
            
            algorithms = [
                HistoryAlgorithm(name="算法1", project_id="1"),
                HistoryAlgorithm(name="算法2", project_id="2")
            ]
            
            response = HistoryAlgorithmListResponse(
                algorithms=algorithms,
                total_count=2,
                msg="查询成功"
            )
            
            assert len(response.algorithms) == 2
            assert response.total_count == 2
            assert response.msg == "查询成功"
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    def test_history_algorithm_list_response_empty(self):
        """测试空的历史算法列表响应"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmListResponse
            )
            
            response = HistoryAlgorithmListResponse(
                algorithms=[],
                total_count=0,
                msg="未找到算法"
            )
            
            assert len(response.algorithms) == 0
            assert response.total_count == 0
            assert response.msg == "未找到算法"
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

class TestHistoryAlgorithmService:
    """测试HistoryAlgorithmService类的主要方法"""
    
    def test_service_creation(self):
        """测试服务创建"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import HistoryAlgorithmService
            
            service = HistoryAlgorithmService()
            assert service is not None
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_get_all_algorithms_success(self):
        """测试获取所有算法成功"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse,
                HistoryAlgorithm
            )
            
            # Mock算法平台服务
            with patch('agents.predict_algorith_agent.services.history_algorithm_service.AlgorithmPlatformService') as MockService:
                mock_service_instance = AsyncMock()
                mock_response = MagicMock()
                mock_response.algorithm_name = [
                    ["算法1", "项目1"],
                    ["算法2", "项目2"]
                ]
                mock_response.msg = "success"
                mock_service_instance.get_all_algorithms.return_value = mock_response
                MockService.return_value.__aenter__.return_value = mock_service_instance
                MockService.return_value.__aexit__.return_value = None
                
                service = HistoryAlgorithmService()
                result = await service.get_all_algorithms()
                
                assert isinstance(result, HistoryAlgorithmListResponse)
                assert len(result.algorithms) == 2
                assert result.total_count == 2
                assert result.algorithms[0].name == "算法1"
                assert result.algorithms[0].project_id == "项目1"
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_get_all_algorithms_empty(self):
        """测试获取所有算法为空"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse
            )
            
            # Mock算法平台服务返回空结果
            with patch('agents.predict_algorith_agent.services.history_algorithm_service.AlgorithmPlatformService') as MockService:
                mock_service_instance = AsyncMock()
                mock_response = MagicMock()
                mock_response.algorithm_name = []
                mock_response.msg = "success"
                mock_service_instance.get_all_algorithms.return_value = mock_response
                MockService.return_value.__aenter__.return_value = mock_service_instance
                MockService.return_value.__aexit__.return_value = None
                
                service = HistoryAlgorithmService()
                result = await service.get_all_algorithms()
                
                assert isinstance(result, HistoryAlgorithmListResponse)
                assert len(result.algorithms) == 0
                assert result.total_count == 0
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_search_algorithms_by_keyword(self):
        """测试按关键词搜索算法"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse
            )
            
            # Mock获取所有算法
            with patch.object(HistoryAlgorithmService, 'get_all_algorithms') as mock_get_all:
                from agents.predict_algorith_agent.services.history_algorithm_service import HistoryAlgorithm
                
                mock_algorithms = [
                    HistoryAlgorithm(name="LSTM预测算法", project_id="1", algorithm_type="LSTM"),
                    HistoryAlgorithm(name="CNN图像分类", project_id="2", algorithm_type="CNN"),
                    HistoryAlgorithm(name="LSTM时序分析", project_id="3", algorithm_type="LSTM")
                ]
                
                mock_get_all.return_value = HistoryAlgorithmListResponse(
                    algorithms=mock_algorithms,
                    total_count=3,
                    msg="success"
                )
                
                service = HistoryAlgorithmService()
                result = await service.search_algorithms(keywords=["LSTM"])
                
                assert isinstance(result, HistoryAlgorithmListResponse)
                assert len(result.algorithms) == 2  # 应该找到2个LSTM算法
                assert all("LSTM" in alg.name or alg.algorithm_type == "LSTM" for alg in result.algorithms)
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_search_algorithms_by_type(self):
        """测试按算法类型搜索"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse
            )
            
            # Mock获取所有算法
            with patch.object(HistoryAlgorithmService, 'get_all_algorithms') as mock_get_all:
                from agents.predict_algorith_agent.services.history_algorithm_service import HistoryAlgorithm
                
                mock_algorithms = [
                    HistoryAlgorithm(name="算法1", project_id="1", algorithm_type="LSTM"),
                    HistoryAlgorithm(name="算法2", project_id="2", algorithm_type="CNN"),
                    HistoryAlgorithm(name="算法3", project_id="3", algorithm_type="LSTM")
                ]
                
                mock_get_all.return_value = HistoryAlgorithmListResponse(
                    algorithms=mock_algorithms,
                    total_count=3,
                    msg="success"
                )
                
                service = HistoryAlgorithmService()
                result = await service.search_algorithms(algorithm_types=["CNN"])
                
                assert isinstance(result, HistoryAlgorithmListResponse)
                assert len(result.algorithms) == 1  # 应该找到1个CNN算法
                assert result.algorithms[0].algorithm_type == "CNN"
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_search_algorithms_no_results(self):
        """测试搜索算法无结果"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse
            )
            
            # Mock获取所有算法
            with patch.object(HistoryAlgorithmService, 'get_all_algorithms') as mock_get_all:
                from agents.predict_algorith_agent.services.history_algorithm_service import HistoryAlgorithm
                
                mock_algorithms = [
                    HistoryAlgorithm(name="LSTM算法", project_id="1", algorithm_type="LSTM"),
                    HistoryAlgorithm(name="CNN算法", project_id="2", algorithm_type="CNN")
                ]
                
                mock_get_all.return_value = HistoryAlgorithmListResponse(
                    algorithms=mock_algorithms,
                    total_count=2,
                    msg="success"
                )
                
                service = HistoryAlgorithmService()
                result = await service.search_algorithms(keywords=["不存在的关键词"])
                
                assert isinstance(result, HistoryAlgorithmListResponse)
                assert len(result.algorithms) == 0
                assert result.total_count == 0
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_get_algorithm_by_name(self):
        """测试按名称获取算法"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService
            )
            
            # Mock获取所有算法
            with patch.object(HistoryAlgorithmService, 'get_all_algorithms') as mock_get_all:
                from agents.predict_algorith_agent.services.history_algorithm_service import (
                    HistoryAlgorithm,
                    HistoryAlgorithmListResponse
                )
                
                target_algorithm = HistoryAlgorithm(
                    name="目标算法",
                    project_id="123",
                    algorithm_type="LSTM",
                    description="目标算法描述"
                )
                
                mock_algorithms = [
                    HistoryAlgorithm(name="其他算法", project_id="1"),
                    target_algorithm,
                    HistoryAlgorithm(name="另一个算法", project_id="2")
                ]
                
                mock_get_all.return_value = HistoryAlgorithmListResponse(
                    algorithms=mock_algorithms,
                    total_count=3,
                    msg="success"
                )
                
                service = HistoryAlgorithmService()
                result = await service.get_algorithm_by_name("目标算法")
                
                assert result is not None
                assert result.name == "目标算法"
                assert result.project_id == "123"
                assert result.algorithm_type == "LSTM"
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_get_algorithm_by_name_not_found(self):
        """测试按名称获取算法未找到"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService
            )
            
            # Mock获取所有算法
            with patch.object(HistoryAlgorithmService, 'get_all_algorithms') as mock_get_all:
                from agents.predict_algorith_agent.services.history_algorithm_service import (
                    HistoryAlgorithm,
                    HistoryAlgorithmListResponse
                )
                
                mock_algorithms = [
                    HistoryAlgorithm(name="算法1", project_id="1"),
                    HistoryAlgorithm(name="算法2", project_id="2")
                ]
                
                mock_get_all.return_value = HistoryAlgorithmListResponse(
                    algorithms=mock_algorithms,
                    total_count=2,
                    msg="success"
                )
                
                service = HistoryAlgorithmService()
                result = await service.get_algorithm_by_name("不存在的算法")
                
                assert result is None
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

class TestHistoryAlgorithmServiceErrorHandling:
    """测试历史算法服务的错误处理"""
    
    @pytest.mark.asyncio
    async def test_get_all_algorithms_service_error(self):
        """测试获取所有算法时服务错误"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse
            )
            
            # Mock算法平台服务抛出异常
            with patch('agents.predict_algorith_agent.services.history_algorithm_service.AlgorithmPlatformService') as MockService:
                mock_service_instance = AsyncMock()
                mock_service_instance.get_all_algorithms.side_effect = Exception("服务连接失败")
                MockService.return_value.__aenter__.return_value = mock_service_instance
                MockService.return_value.__aexit__.return_value = None
                
                service = HistoryAlgorithmService()
                
                # 应该返回空结果而不是抛出异常
                result = await service.get_all_algorithms()
                
                assert isinstance(result, HistoryAlgorithmListResponse)
                assert len(result.algorithms) == 0
                assert result.total_count == 0
                assert "错误" in result.msg or "失败" in result.msg
                
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_search_algorithms_with_invalid_parameters(self):
        """测试使用无效参数搜索算法"""
        try:
            from agents.predict_algorith_agent.services.history_algorithm_service import (
                HistoryAlgorithmService,
                HistoryAlgorithmListResponse
            )
            
            service = HistoryAlgorithmService()
            
            # 测试空参数
            result = await service.search_algorithms(keywords=[], algorithm_types=[])
            assert isinstance(result, HistoryAlgorithmListResponse)
            
            # 测试None参数
            result = await service.search_algorithms(keywords=None, algorithm_types=None)
            assert isinstance(result, HistoryAlgorithmListResponse)
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")
