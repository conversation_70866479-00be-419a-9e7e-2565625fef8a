#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法平台服务最大覆盖率测试 - 专门用于提升覆盖率到60%以上
针对services/algorithm_platform_service.py中的AlgorithmPlatformService类进行全面测试
"""

import pytest
from unittest.mock import AsyncMock, patch
import aiohttp
import asyncio

# Mock配置
mock_config = {
    "ALGORITHM_PLATFORM_BASE_URL": "http://mock-platform.com",
    "ALGORITHM_PLATFORM_TIMEOUT": 5,
    "ALGORITHM_PLATFORM_RETRY_TIMES": 3,
    "ALGORITHM_PLATFORM_RETRY_DELAY": 1,
    "get_algorithm_platform_url": lambda endpoint: f"http://mock-platform.com/{endpoint}"
}

@pytest.fixture(autouse=True)
def mock_all_external_dependencies():
    """自动Mock所有外部依赖"""
    with patch.dict('agents.config.__dict__', mock_config), \
         patch('agents.predict_algorith_agent.services.algorithm_platform_service.logger'), \
         patch('aiohttp.ClientSession'):
        yield

class TestAlgorithmPlatformServiceMaxCoverage:
    """算法平台服务最大覆盖率测试"""
    
    def test_algorithm_platform_service_creation(self):
        """测试算法平台服务创建"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        
        # 测试服务创建
        service = AlgorithmPlatformService()
        assert service is not None
        assert hasattr(service, '_session')
        assert hasattr(service, '_base_url')
        assert hasattr(service, '_timeout')
        assert hasattr(service, '_retry_times')
        assert hasattr(service, '_retry_delay')
    
    @pytest.mark.asyncio
    async def test_context_manager_enter_exit(self):
        """测试上下文管理器进入和退出"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        
        service = AlgorithmPlatformService()
        
        # Mock aiohttp.ClientSession
        mock_session = AsyncMock()
        
        with patch('aiohttp.ClientSession', return_value=mock_session):
            # 测试进入上下文
            async with service:
                assert service._session == mock_session
            
            # 验证会话已关闭
            mock_session.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_get_success(self):
        """测试_make_request方法 - GET请求成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"msg": "success", "data": []}
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        with patch.object(service, '_session', mock_session):
            result = await service._make_request("GET", "test_endpoint")
            
            # 验证结果
            assert result["msg"] == "success"
            assert "data" in result
            mock_session.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_post_success(self):
        """测试_make_request方法 - POST请求成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"msg": "success", "task_id": "123"}
        
        mock_session = AsyncMock()
        mock_session.post.return_value.__aenter__.return_value = mock_response
        
        test_data = {"algorithm": "LSTM", "params": {"lr": 0.001}}
        
        with patch.object(service, '_session', mock_session):
            result = await service._make_request("POST", "train", data=test_data)
            
            # 验证结果
            assert result["msg"] == "success"
            assert result["task_id"] == "123"
            mock_session.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_with_params(self):
        """测试_make_request方法 - 带参数请求"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"msg": "success", "results": []}
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        test_params = {"project_id": "proj123", "limit": 10}
        
        with patch.object(service, '_session', mock_session):
            result = await service._make_request("GET", "algorithms", params=test_params)
            
            # 验证结果
            assert result["msg"] == "success"
            mock_session.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_network_error(self):
        """测试_make_request方法 - 网络错误"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import (
            AlgorithmPlatformService, NetworkError
        )
        
        service = AlgorithmPlatformService()
        
        # Mock网络错误
        mock_session = AsyncMock()
        mock_session.get.side_effect = aiohttp.ClientError("网络连接失败")
        
        with patch.object(service, '_session', mock_session):
            with pytest.raises(NetworkError):
                await service._make_request("GET", "test_endpoint")
    
    @pytest.mark.asyncio
    async def test_make_request_timeout_error(self):
        """测试_make_request方法 - 超时错误"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import (
            AlgorithmPlatformService, TimeoutError
        )
        
        service = AlgorithmPlatformService()
        
        # Mock超时错误
        mock_session = AsyncMock()
        mock_session.get.side_effect = asyncio.TimeoutError("请求超时")
        
        with patch.object(service, '_session', mock_session):
            with pytest.raises(TimeoutError):
                await service._make_request("GET", "test_endpoint")
    
    @pytest.mark.asyncio
    async def test_make_request_api_error(self):
        """测试_make_request方法 - API错误"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import (
            AlgorithmPlatformService, APIError
        )
        
        service = AlgorithmPlatformService()
        
        # Mock API错误响应
        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text.return_value = "内部服务器错误"
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        with patch.object(service, '_session', mock_session):
            with pytest.raises(APIError):
                await service._make_request("GET", "test_endpoint")
    
    @pytest.mark.asyncio
    async def test_get_all_algorithms_success(self):
        """测试获取所有算法 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import GetAllAlgorithmResponse
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "algorithm_name": [["LSTM", "project1"], ["CNN", "project2"]]
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.get_all_algorithms()
            
            # 验证结果
            assert isinstance(result, GetAllAlgorithmResponse)
            assert result.msg == "success"
            assert len(result.algorithm_name) == 2
            assert result.algorithm_name[0] == ["LSTM", "project1"]
    
    @pytest.mark.asyncio
    async def test_get_all_datasets_success(self):
        """测试获取所有数据集 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import GetAllDatasetResponse
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "dataset_name": [["dataset1", "project1"], ["dataset2", "project2"]]
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.get_all_datasets()
            
            # 验证结果
            assert isinstance(result, GetAllDatasetResponse)
            assert result.msg == "success"
            assert len(result.dataset_name) == 2
    
    @pytest.mark.asyncio
    async def test_get_all_iot_success(self):
        """测试获取所有IoT设备 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import GetAllIOTResponse
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "iot_name": [["device1", "project1"], ["device2", "project2"]]
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.get_all_iot("project123")
            
            # 验证结果
            assert isinstance(result, GetAllIOTResponse)
            assert result.msg == "success"
            assert len(result.iot_name) == 2
    
    @pytest.mark.asyncio
    async def test_train_algorithm_success(self):
        """测试训练算法 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import (
            AlgorithmTrainingRequest, AlgorithmTrainingResponse
        )
        
        service = AlgorithmPlatformService()
        
        # 创建训练请求
        request = AlgorithmTrainingRequest(
            parameter1="LSTM",
            parameter2="时序预测",
            parameter3="timeseries_data",
            parameter4="10,100",
            parameter5="128",
            parameter6="2",
            parameter7="0.2",
            parameter8="false",
            parameter9="1",
            parameter10="0.001",
            parameter11="0.8",
            parameter12="Adam",
            parameter13="MSE",
            parameter14="target",
            parameter15="temperature,pressure",
            parameter16="sensor_field",
            project_number="project123"
        )
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "task_id": "task_456",
            "status": "started"
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.train_algorithm(request)
            
            # 验证结果
            assert isinstance(result, AlgorithmTrainingResponse)
            assert result.msg == "success"
            assert result.task_id == "task_456"
            assert result.status == "started"
    
    @pytest.mark.asyncio
    async def test_start_monitoring_service_success(self):
        """测试启动监控服务 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import (
            MonitoringServiceRequest, MonitoringServiceResponse
        )
        
        service = AlgorithmPlatformService()
        
        # 创建监控请求
        request = MonitoringServiceRequest(
            db_ip="*************",
            db_name="sensor_data",
            list_name="temperature,pressure",
            implement_name="predictive_maintenance",
            project_name="project123"
        )
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "service_id": "service_789",
            "status": "running"
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.start_monitoring_service(request)
            
            # 验证结果
            assert isinstance(result, MonitoringServiceResponse)
            assert result.msg == "success"
            assert result.service_id == "service_789"
            assert result.status == "running"
    
    @pytest.mark.asyncio
    async def test_get_algorithm_log_success(self):
        """测试获取算法日志 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import GetAlgorithmLogResponse
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "logs": ["训练开始", "第1轮训练完成", "训练结束"],
            "task_id": "task_456"
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.get_algorithm_log("task_456", "project123")
            
            # 验证结果
            assert isinstance(result, GetAlgorithmLogResponse)
            assert result.msg == "success"
            assert len(result.logs) == 3
            assert result.task_id == "task_456"
    
    @pytest.mark.asyncio
    async def test_get_all_implement_success(self):
        """测试获取所有实现 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import GetAllImplementResponse
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "implement_info": [
                {
                    "algorithm_name": "LSTM",
                    "field_value": "temperature",
                    "status": "running",
                    "tablename": "sensor_data"
                },
                {
                    "algorithm_name": "RandomForest",
                    "field_value": "pressure",
                    "status": "stopped",
                    "tablename": "sensor_data2"
                }
            ]
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.get_all_services("project123")
            
            # 验证结果
            assert isinstance(result, GetAllImplementResponse)
            assert result.msg == "success"
            assert len(result.implement_info) == 2
            assert result.implement_info[0].algorithm_name == "LSTM"
            assert result.implement_info[1].algorithm_name == "RandomForest"
    
    @pytest.mark.asyncio
    async def test_get_service_prediction_success(self):
        """测试获取服务预测结果 - 成功"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
        from agents.predict_algorith_agent.models.algorithm_platform_models import GetImplementLogResponse
        
        service = AlgorithmPlatformService()
        
        # Mock成功响应数据
        mock_data = {
            "msg": "success",
            "prediction": {"result": 0.85, "confidence": 0.92},
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        with patch.object(service, '_make_request', return_value=mock_data):
            result = await service.get_service_prediction("predictive_service", "project123")
            
            # 验证结果
            assert isinstance(result, GetImplementLogResponse)
            assert result.msg == "success"
    
    @pytest.mark.asyncio
    async def test_create_algorithm_platform_service_function(self):
        """测试创建算法平台服务函数"""
        from agents.predict_algorith_agent.services.algorithm_platform_service import create_algorithm_platform_service
        
        # 测试创建服务
        service = await create_algorithm_platform_service()
        
        # 验证服务创建成功
        assert service is not None
        assert hasattr(service, '_make_request')
        assert hasattr(service, 'get_all_algorithms')
    
    def test_error_classes_creation(self):
        """测试错误类创建"""
        from agents.predict_algorith_agent.models.algorithm_platform_models import (
            AlgorithmPlatformError, NetworkError, TimeoutError, APIError
        )
        
        # 测试基础错误
        base_error = AlgorithmPlatformError("基础错误")
        assert str(base_error) == "基础错误"
        
        # 测试网络错误
        network_error = NetworkError("网络错误")
        assert isinstance(network_error, AlgorithmPlatformError)
        assert str(network_error) == "网络错误"
        
        # 测试超时错误
        timeout_error = TimeoutError("超时错误")
        assert isinstance(timeout_error, AlgorithmPlatformError)
        assert str(timeout_error) == "超时错误"
        
        # 测试API错误
        api_error = APIError("API错误", status_code=500)
        assert isinstance(api_error, AlgorithmPlatformError)
        assert str(api_error) == "API错误"
        assert api_error.status_code == 500
