#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import aiohttp
import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 导入测试配置
from agents.predict_algorith_agent.tests.test_config import get_test_config, SAMPLE_API_RESPONSES, SAMPLE_ERROR_RESPONSES

# 导入待测试的模块
from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
from agents.predict_algorith_agent.models.algorithm_platform_models import (
    GetAllAlgorithmResponse, AlgorithmTrainingRequest, AlgorithmTrainingResponse,
    GetAllDatasetResponse, GetAllIOTResponse, MonitoringServiceRequest,
    MonitoringServiceResponse, GetAlgorithmLogResponse, GetAllImplementResponse,
    GetImplementLogResponse, AlgorithmPlatformError, NetworkError, TimeoutError, APIError
)

@pytest.fixture(autouse=True)
def mock_agents_config():
    """Mock agents.config配置"""
    test_config = get_test_config()
    test_config.update({
        "ALGORITHM_PLATFORM_BASE_URL": "http://mock-platform.com",
        "ALGORITHM_PLATFORM_TIMEOUT": 5,
        "ALGORITHM_PLATFORM_RETRY_TIMES": 1,
        "ALGORITHM_PLATFORM_RETRY_DELAY": 0.1,
        "get_algorithm_platform_url": MagicMock(side_effect=lambda endpoint: f"http://mock-platform.com/{endpoint}")
    })
    with patch.dict('agents.config.__dict__', test_config):
        yield

@pytest.fixture(autouse=True)
def mock_logging_config():
    """Mock日志配置"""
    with patch('agents.predict_algorith_agent.services.algorithm_platform_service.logger', autospec=True):
        yield

@pytest.fixture
def mock_http_session():
    """Mock HTTP会话"""
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.json = AsyncMock(return_value=SAMPLE_API_RESPONSES["get_all_algorithms"])
    mock_response.text = json.dumps(SAMPLE_API_RESPONSES["get_all_algorithms"])
    
    mock_session = AsyncMock()
    mock_session.request = AsyncMock(return_value=mock_response)
    mock_session.close = AsyncMock()
    mock_session.closed = False
    
    with patch('aiohttp.ClientSession') as MockSession:
        MockSession.return_value.__aenter__.return_value = mock_session
        MockSession.return_value.__aexit__.return_value = None
        yield mock_session, mock_response

class TestAlgorithmPlatformService:
    """算法平台服务测试类"""
    
    @pytest.mark.asyncio
    async def test_get_all_algorithms_success(self, mock_http_session):
        """测试获取所有算法成功"""
        mock_session, _mock_response = mock_http_session
        service = AlgorithmPlatformService()
        
        result = await service.get_all_algorithms()
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_algorithms_api_error(self, mock_http_session):
        """测试获取所有算法API错误"""
        _mock_session, mock_response = mock_http_session
        mock_response.status = 500
        mock_response.json = AsyncMock(side_effect=Exception("JSON decode error"))
        
        service = AlgorithmPlatformService()
        
        with pytest.raises(APIError):
            await service.get_all_algorithms()
    
    @pytest.mark.asyncio
    async def test_train_algorithm_success(self, mock_http_session):
        """测试训练算法成功"""
        _mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value=SAMPLE_API_RESPONSES["train_algorithm"])
        
        service = AlgorithmPlatformService()
        
        request = AlgorithmTrainingRequest(
            algorithm_name="测试算法",
            dataset_name="测试数据集",
            parameters={"learning_rate": 0.001}
        )
        
        result = await service.train_algorithm(request)
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_monitoring_service_success(self, mock_http_session):
        """测试启动监控服务成功"""
        mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value={"status": "success", "data": {"service_id": "monitor_123"}})
        
        service = AlgorithmPlatformService()
        
        request = MonitoringServiceRequest(
            algorithm_name="测试算法",
            service_name="监控服务",
            data_source_config={"url": "http://test.com/data"}
        )
        
        result = await service.start_monitoring_service(request)
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_algorithm_log_success(self, mock_http_session):
        """测试获取算法日志成功"""
        mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value=SAMPLE_API_RESPONSES["get_algorithm_log"])
        
        service = AlgorithmPlatformService()
        
        result = await service.get_algorithm_log("测试算法", "280")
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_services_success(self, mock_http_session):
        """测试获取所有服务成功"""
        mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value={"status": "success", "data": {"services": []}})
        
        service = AlgorithmPlatformService()
        
        result = await service.get_all_services("280")
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_service_prediction_success(self, mock_http_session):
        """测试获取服务预测成功"""
        mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value={"status": "success", "data": {"prediction": 0.85}})
        
        service = AlgorithmPlatformService()
        
        result = await service.get_service_prediction("预测服务", "255")
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_timeout_error(self, mock_http_session):
        """测试请求超时错误"""
        mock_session, mock_response = mock_http_session
        mock_session.request = AsyncMock(side_effect=aiohttp.ServerTimeoutError())
        
        service = AlgorithmPlatformService()
        
        with pytest.raises(TimeoutError):
            await service.get_all_algorithms()
    
    @pytest.mark.asyncio
    async def test_make_request_network_error(self, mock_http_session):
        """测试网络错误"""
        mock_session, _mock_response = mock_http_session
        mock_session.request = AsyncMock(side_effect=aiohttp.ClientError())
        
        service = AlgorithmPlatformService()
        
        with pytest.raises(NetworkError):
            await service.get_all_algorithms()
    
    @pytest.mark.asyncio
    async def test_make_request_json_decode_error(self, mock_http_session):
        """测试JSON解码错误"""
        _mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(side_effect=json.JSONDecodeError("Invalid JSON", "", 0))
        mock_response.text = "Invalid JSON response"
        
        service = AlgorithmPlatformService()
        
        with pytest.raises(APIError):
            await service.get_all_algorithms()
    
    @pytest.mark.asyncio
    async def test_context_manager_aenter_aexit(self):
        """测试上下文管理器的进入和退出"""
        with patch('aiohttp.ClientSession') as MockSession:
            mock_session = AsyncMock()
            mock_session.closed = False
            MockSession.return_value.__aenter__.return_value = mock_session
            MockSession.return_value.__aexit__.return_value = None
            
            service = AlgorithmPlatformService()
            
            async with service:
                assert service.session is not None
            
            mock_session.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_datasets_success(self, mock_http_session):
        """测试获取所有数据集成功"""
        mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value={"status": "success", "data": {"datasets": []}})
        
        service = AlgorithmPlatformService()
        
        result = await service.get_all_datasets()
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_all_iot_success(self, mock_http_session):
        """测试获取所有IOT设备成功"""
        mock_session, mock_response = mock_http_session
        mock_response.json = AsyncMock(return_value={"status": "success", "data": {"iot_devices": []}})
        
        service = AlgorithmPlatformService()
        
        result = await service.get_all_iot("285")
        
        assert result is not None
        assert "data" in result
        mock_session.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_service_initialization(self):
        """测试服务初始化"""
        service = AlgorithmPlatformService()
        
        assert service.session is None
        assert service.base_url == "http://mock-platform.com"
        assert service.timeout == 5
        assert service.retry_times == 1
        assert service.retry_delay == 0.1
    
    @pytest.mark.asyncio
    async def test_service_with_custom_config(self):
        """测试使用自定义配置的服务"""
        custom_config = {
            "base_url": "http://custom.com",
            "timeout": 10,
            "retry_times": 3,
            "retry_delay": 0.5
        }
        
        service = AlgorithmPlatformService(custom_config)
        
        assert service.base_url == "http://custom.com"
        assert service.timeout == 10
        assert service.retry_times == 3
        assert service.retry_delay == 0.5
