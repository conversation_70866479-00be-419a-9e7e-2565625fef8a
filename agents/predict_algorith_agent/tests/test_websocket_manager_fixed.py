#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 导入测试配置
from agents.predict_algorith_agent.tests.test_config import get_test_config, SAMPLE_CONVERSATION, SAMPLE_MESSAGE

# 导入待测试的模块
from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager, WebSocketMessage, WebSocketResponse, DateTimeEncoder
from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState, InteractionType

@pytest.fixture(autouse=True)
def mock_agents_config():
    """Mock agents.config配置"""
    test_config = get_test_config()
    with patch.dict('agents.config.__dict__', test_config):
        yield

@pytest.fixture(autouse=True)
def mock_logging_config():
    """Mock日志配置"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.logger', autospec=True):
        yield

@pytest.fixture
def mock_websocket():
    """Mock WebSocket连接"""
    mock_ws = AsyncMock()
    mock_ws.accept = AsyncMock()
    mock_ws.send_text = AsyncMock()
    mock_ws.close = AsyncMock()
    mock_ws.client_state = MagicMock()
    mock_ws.client_state.name = "CONNECTED"
    return mock_ws

@pytest.fixture
def mock_database_manager():
    """Mock数据库管理器"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.DatabaseManager') as MockDatabaseManager:
        mock_db = MockDatabaseManager.return_value
        mock_db.create_conversation.return_value = "conv_123"
        mock_db.get_conversation_detail.return_value = SAMPLE_CONVERSATION
        mock_db.get_conversations_by_user.return_value = [SAMPLE_CONVERSATION]
        mock_db.add_conversation_message.return_value = 1
        mock_db.get_conversation_messages.return_value = [SAMPLE_MESSAGE]
        mock_db.save_session_state.return_value = True
        mock_db.load_session_state.return_value = ("task_123", {"key": "value"})
        yield mock_db

@pytest.fixture
def mock_predictive_agent():
    """Mock预测智能体"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.PredictiveAlgorithmAssistant') as MockAgent:
        mock_agent = MockAgent.return_value
        mock_agent.process_user_input_async.return_value = (
            {"msg": "测试回复", "type": "response"},
            PredictiveAgentState(),
            "response"
        )
        mock_agent.get_report_pdf.return_value = b"PDF content"
        yield mock_agent

@pytest.fixture
def mock_context_manager():
    """Mock上下文管理器"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.ContextManager') as MockContextManager:
        mock_context = MockContextManager.return_value
        mock_context.build_context.return_value = {'context_layers': {}, 'total_tokens': 0}
        mock_context.rebuild_agent_state.return_value = PredictiveAgentState()
        yield mock_context

class TestConnectionManager:
    """连接管理器测试类"""
    
    def test_connection_manager_initialization(self):
        """测试连接管理器初始化"""
        manager = ConnectionManager()
        
        assert manager.active_connections == {}
        assert manager.user_sessions == {}
        assert manager.session_states == {}
    
    @pytest.mark.asyncio
    async def test_connect_new_session(self, mock_websocket, mock_database_manager, mock_context_manager):
        """测试连接新会话"""
        manager = ConnectionManager()
        session_id = "test_session_1"
        user_info = {
            "user_id": "test_user_1",
            "username": "测试用户"
        }

        await manager.connect(mock_websocket, session_id, user_info)

        assert session_id in manager.active_connections
        assert session_id in manager.user_sessions
        mock_websocket.accept.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_disconnect_session(self, mock_websocket):
        """测试断开会话连接"""
        manager = ConnectionManager()
        session_id = "test_session_2"
        
        # 先连接
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {"user_id": "test_user"}
        
        await manager.disconnect(session_id)
        
        assert session_id not in manager.active_connections
        assert session_id not in manager.session_states
    
    @pytest.mark.asyncio
    async def test_send_personal_message(self, mock_websocket):
        """测试发送个人消息"""
        manager = ConnectionManager()
        session_id = "test_session_3"
        manager.active_connections[session_id] = mock_websocket
        
        message = {"type": "test", "data": {"content": "hello"}}
        await manager.send_personal_message(message, session_id)
        
        mock_websocket.send_text.assert_called_once()
        args, _kwargs = mock_websocket.send_text.call_args
        sent_data = json.loads(args[0])
        assert sent_data["type"] == "test"
        assert sent_data["data"]["content"] == "hello"
    
    @pytest.mark.asyncio
    async def test_send_personal_message_disconnected_websocket(self, mock_websocket):
        """测试向已断开的WebSocket发送消息"""
        manager = ConnectionManager()
        session_id = "test_session_4"
        manager.active_connections[session_id] = mock_websocket
        
        # 模拟WebSocket已断开
        mock_websocket.client_state.name = "DISCONNECTED"
        
        message = {"type": "test", "data": {"content": "hello"}}
        await manager.send_personal_message(message, session_id)
        
        mock_websocket.send_text.assert_not_called()
        assert session_id not in manager.active_connections  # 应该被清理
    
    @pytest.mark.asyncio
    async def test_handle_chat_message_new_conversation(self, mock_websocket, mock_database_manager, mock_predictive_agent, mock_context_manager):
        """测试处理聊天消息，新对话"""
        manager = ConnectionManager()
        session_id = "test_session_5"
        user_id = "test_user_5"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": user_id,
            "username": "测试用户",
            "current_conversation_id": None
        }
        
        message = WebSocketMessage(
            type="chat_message",
            data={"message": "你好", "conversation_id": None}
        )
        
        await manager.handle_chat_message(session_id, message)
        
        # 验证创建了新对话
        mock_database_manager.create_conversation.assert_called_once()
        mock_predictive_agent.process_user_input_async.assert_called_once()
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_chat_message_existing_conversation(self, mock_websocket, mock_database_manager, mock_predictive_agent, mock_context_manager):
        """测试处理聊天消息，现有对话"""
        manager = ConnectionManager()
        session_id = "test_session_6"
        user_id = "test_user_6"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": user_id,
            "username": "测试用户",
            "current_conversation_id": "conv_123"
        }
        
        message = WebSocketMessage(
            type="chat_message",
            data={"message": "继续对话", "conversation_id": "conv_123"}
        )
        
        await manager.handle_chat_message(session_id, message)
        
        # 验证没有创建新对话
        mock_database_manager.create_conversation.assert_not_called()
        mock_predictive_agent.process_user_input_async.assert_called_once()
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_switch_conversation(self, mock_websocket, mock_database_manager, mock_context_manager):
        """测试处理切换对话"""
        manager = ConnectionManager()
        session_id = "test_session_7"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": "test_user_7",
            "username": "测试用户",
            "current_conversation_id": "old_conv"
        }
        
        message = WebSocketMessage(
            type="switch_conversation",
            data={"conversation_id": "new_conv"}
        )
        
        await manager.handle_switch_conversation(session_id, message)
        
        # 验证对话ID已切换
        assert manager.session_states[session_id]["current_conversation_id"] == "new_conv"
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_new_conversation(self, mock_websocket, mock_database_manager):
        """测试处理新建对话"""
        manager = ConnectionManager()
        session_id = "test_session_8"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": "test_user_8",
            "username": "测试用户"
        }
        
        message = WebSocketMessage(
            type="new_conversation",
            data={"title": "新对话"}
        )
        
        await manager.handle_new_conversation(session_id, message)
        
        mock_database_manager.create_conversation.assert_called_once()
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_get_conversations(self, mock_websocket, mock_database_manager):
        """测试处理获取对话列表"""
        manager = ConnectionManager()
        session_id = "test_session_9"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": "test_user_9",
            "username": "测试用户"
        }
        
        message = WebSocketMessage(
            type="get_conversations",
            data={}
        )
        
        await manager.handle_get_conversations(session_id, message)
        
        mock_database_manager.get_conversations_by_user.assert_called_once()
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_report_request_success(self, mock_websocket, mock_predictive_agent):
        """测试处理报告请求成功"""
        manager = ConnectionManager()
        session_id = "test_session_10"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": "test_user_10",
            "username": "测试用户"
        }
        
        message = WebSocketMessage(
            type="report_request",
            data={"task_id": "task_123", "report_type": "analysis"}
        )
        
        await manager.handle_report_request(session_id, message)
        
        mock_predictive_agent.get_report_pdf.assert_called_once()
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_handle_history_request_success(self, mock_websocket, mock_database_manager):
        """测试处理历史记录请求成功"""
        manager = ConnectionManager()
        session_id = "test_session_11"
        
        # 设置会话状态
        manager.active_connections[session_id] = mock_websocket
        manager.session_states[session_id] = {
            "user_id": "test_user_11",
            "username": "测试用户",
            "current_conversation_id": "conv_123"
        }
        
        message = WebSocketMessage(
            type="history_request",
            data={"conversation_id": "conv_123"}
        )
        
        await manager.handle_history_request(session_id, message)
        
        mock_database_manager.get_conversation_messages.assert_called_once()
        mock_websocket.send_text.assert_called()
    
    @pytest.mark.asyncio
    async def test_send_error_response(self, mock_websocket):
        """测试发送错误响应"""
        manager = ConnectionManager()
        session_id = "test_session_12"
        manager.active_connections[session_id] = mock_websocket
        
        error_message = "Something went wrong"
        await manager.send_error_response(session_id, error_message)
        
        mock_websocket.send_text.assert_called_once()
        args, _kwargs = mock_websocket.send_text.call_args
        response = json.loads(args[0])
        assert response["type"] == "error"
        assert response["data"]["error"] == error_message
        assert response["status"] == "error"
    
    def test_datetime_encoder(self):
        """测试DateTime编码器"""
        dt_obj = datetime(2023, 1, 1, 12, 30, 0)
        encoded = json.dumps({"time": dt_obj}, cls=DateTimeEncoder)
        assert encoded == '{"time": "2023-01-01T12:30:00"}'
