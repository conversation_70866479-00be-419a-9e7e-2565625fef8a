#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法平台API测试
测试与算法平台的API交互功能
"""

import asyncio
import pytest
import pytest_asyncio
import sys
import os
from unittest.mock import AsyncMock, patch

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
from agents.predict_algorith_agent.models.algorithm_platform_models import (
    AlgorithmPlatformConfig,
    AlgorithmTrainingRequest,
    MonitoringServiceRequest,
    AlgorithmPlatformError,
    NetworkError,
    TimeoutError,
    APIError
)


class TestAlgorithmPlatformService:
    """算法平台服务测试类"""
    
    @pytest_asyncio.fixture
    async def service(self):
        """创建测试服务实例"""
        service = AlgorithmPlatformService()
        yield service
        await service.close()
    
    @pytest.mark.asyncio
    async def test_get_all_algorithms(self, service):
        """测试查询全部算法名称"""
        try:
            response = await service.get_all_algorithms()
            print(f"算法列表: {response}")
            assert response.msg == "获取成功"
            assert isinstance(response.algorithm_name, list)
        except Exception as e:
            print(f"查询算法列表失败: {e}")
            # 在实际测试中，这里可能需要mock数据
    
    @pytest.mark.asyncio
    async def test_get_all_datasets(self, service):
        """测试查询全部数据集名称"""
        try:
            response = await service.get_all_datasets()
            print(f"数据集列表: {response}")
            assert response.msg == "获取成功"
            assert isinstance(response.dataset_name, list)
        except Exception as e:
            print(f"查询数据集列表失败: {e}")
    
    @pytest.mark.asyncio
    async def test_get_all_iot(self, service):
        """测试查询全部IOT名称"""
        try:
            project_id = "285"  # 使用文档中的示例项目ID
            response = await service.get_all_iot(project_id)
            print(f"IOT列表: {response}")
            assert response.msg == "获取成功"
            assert isinstance(response.iot_info, list)
        except Exception as e:
            print(f"查询IOT列表失败: {e}")
    
    @pytest.mark.asyncio
    async def test_get_all_services(self, service):
        """测试查询全部服务名称"""
        try:
            project_id = "280"  # 使用文档中的示例项目ID
            response = await service.get_all_services(project_id)
            print(f"服务列表: {response}")
            assert response.msg == "获取成功"
            assert isinstance(response.implement_info, list)
        except Exception as e:
            print(f"查询服务列表失败: {e}")
    
    @pytest.mark.asyncio
    async def test_get_algorithm_log(self, service):
        """测试查询算法训练信息"""
        try:
            algorithm_name = "水泵电流预警算法0612"
            project_id = "280"
            response = await service.get_algorithm_log(algorithm_name, project_id)
            print(f"算法训练信息: {response}")
            assert response.msg == "获取成功"
            assert hasattr(response.algorithm_log, 'Algorithm_parameter')
        except Exception as e:
            print(f"查询算法训练信息失败: {e}")
    
    @pytest.mark.asyncio
    async def test_get_service_prediction(self, service):
        """测试查询服务预测结果"""
        try:
            service_name = "电流预警"
            project_id = "255"
            response = await service.get_service_prediction(service_name, project_id)
            print(f"预测结果: {response}")
            assert response.msg == "获取成功"
            assert hasattr(response.implement_info, 'probability')
        except Exception as e:
            print(f"查询预测结果失败: {e}")
    
    @pytest.mark.asyncio
    async def test_train_algorithm(self, service):
        """测试训练新算法（谨慎使用，会实际启动训练）"""
        # 注意：这个测试会实际启动算法训练，请谨慎使用
        training_request = AlgorithmTrainingRequest(
            parameter1="测试算法API调用",
            parameter2="风险预警",
            parameter3="水泵数据集0612",
            parameter4="5",
            parameter5="64",
            parameter6="3",
            parameter7="0.2",
            parameter8="YES",
            parameter9="10",
            parameter10="0.01",
            parameter11="1",
            parameter12="Adam",
            parameter13="MSE",
            parameter14="4",
            parameter15="5",
            parameter16="P11111124112047702",
            project_number="280"
        )
        
        try:
            # 注释掉实际调用，避免意外启动训练
            # response = await service.train_algorithm(training_request)
            # print(f"训练启动结果: {response}")
            # assert response.message == "启动成功"
            print("训练算法测试已跳过（避免实际启动训练）")
        except Exception as e:
            print(f"启动算法训练失败: {e}")
    
    @pytest.mark.asyncio
    async def test_start_monitoring_service(self, service):
        """测试启动监控分析服务（谨慎使用，会实际启动服务）"""
        # 注意：这个测试会实际启动监控服务，请谨慎使用
        monitoring_request = MonitoringServiceRequest(
            db_ip="***********",
            db_name="data_1815320358166839298",
            list_name="P11111124112047702",
            implement_name="API测试服务",
            project_name="280"
        )
        
        try:
            # 注释掉实际调用，避免意外启动服务
            # response = await service.start_monitoring_service(monitoring_request)
            # print(f"监控服务启动结果: {response}")
            # assert response.msg == "启动成功"
            print("监控服务测试已跳过（避免实际启动服务）")
        except Exception as e:
            print(f"启动监控服务失败: {e}")


class TestAlgorithmPlatformServiceMock:
    """使用Mock数据的算法平台服务测试"""
    
    @pytest.mark.asyncio
    async def test_mock_responses(self):
        """测试Mock响应"""
        with patch('aiohttp.ClientSession.request') as mock_request:
            # Mock算法列表响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {
                "algorithm_name": [
                    ["测试算法1", "280"],
                    ["测试算法2", "285"]
                ],
                "msg": "获取成功"
            }
            mock_request.return_value.__aenter__.return_value = mock_response

            service = AlgorithmPlatformService()
            response = await service.get_all_algorithms()

            assert response.msg == "获取成功"
            assert len(response.algorithm_name) == 2
            assert response.algorithm_name[0][0] == "测试算法1"

            await service.close()


async def run_basic_tests():
    """运行基础测试"""
    print("开始算法平台API基础测试...")
    
    config = AlgorithmPlatformConfig(
        base_url="http://10.0.42.241:8081",
        timeout=15,
        retry_times=2
    )
    
    async with AlgorithmPlatformService(config) as service:
        # 测试查询接口（相对安全）
        test_cases = [
            ("查询算法列表", service.get_all_algorithms),
            ("查询数据集列表", service.get_all_datasets),
            ("查询IOT列表", lambda: service.get_all_iot("285")),
            ("查询服务列表", lambda: service.get_all_services("280")),
            ("查询算法训练信息", lambda: service.get_algorithm_log("水泵电流预警算法0612", "280")),
            ("查询预测结果", lambda: service.get_service_prediction("电流预警", "255"))
        ]
        
        results = {}
        for test_name, test_func in test_cases:
            try:
                print(f"\n正在测试: {test_name}")
                result = await test_func()
                results[test_name] = {"status": "成功", "data": result}
                print(f"✅ {test_name} - 成功")
            except Exception as e:
                results[test_name] = {"status": "失败", "error": str(e)}
                print(f"❌ {test_name} - 失败: {e}")
        
        # 输出测试总结
        print("\n" + "="*50)
        print("测试总结:")
        print("="*50)
        for test_name, result in results.items():
            status = result["status"]
            print(f"{test_name}: {status}")
            if status == "成功" and "data" in result:
                print(f"  响应消息: {result['data'].msg}")
        
        return results


if __name__ == "__main__":
    # 运行基础测试
    asyncio.run(run_basic_tests())
