#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RSA密钥对的脚本
验证私钥是否有效，并测试加密解密过程
"""

import rsa
import base64

def test_rsa_key():
    # 用户admin6的RSA私钥
    private_key_pem = """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

    try:
        print("测试RSA密钥...")
        
        # 1. 加载私钥
        private_key = rsa.PrivateKey.load_pkcs1(private_key_pem.encode('utf-8'))
        print("✓ 私钥加载成功")

        # 2. 获取公钥 - 使用rsa库的方法
        # 从私钥的参数构造公钥
        public_key = rsa.PublicKey(private_key.n, private_key.e)
        print("✓ 公钥提取成功")

        # 3. 测试加密解密
        test_message = "Hello|World|Test"
        print(f"原始消息: {test_message}")

        # 用公钥加密
        encrypted = rsa.encrypt(test_message.encode('utf-8'), public_key)
        print("✓ 公钥加密成功")

        # 用私钥解密
        decrypted = rsa.decrypt(encrypted, private_key)
        decrypted_message = decrypted.decode('utf-8')
        print(f"解密消息: {decrypted_message}")

        if test_message == decrypted_message:
            print("✓ 加密解密测试成功！")
        else:
            print("✗ 加密解密测试失败！")

        # 4. 输出公钥信息
        public_key_pem = public_key.save_pkcs1().decode('utf-8')
        print("\n对应的公钥:")
        print(public_key_pem)

        # 5. 测试用公钥加密一个示例机器码
        sample_machine_code = "AA:BB:CC:DD:EE:FF|Intel Core i7|WD1234567890"
        encrypted_sample = rsa.encrypt(sample_machine_code.encode('utf-8'), public_key)
        encrypted_sample_b64 = base64.b64encode(encrypted_sample).decode('utf-8')
        print(f"\n示例机器码: {sample_machine_code}")
        print(f"加密后(base64): {encrypted_sample_b64}")

        # 6. 解密验证
        decrypted_sample = rsa.decrypt(encrypted_sample, private_key).decode('utf-8')
        print(f"解密验证: {decrypted_sample}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_rsa_key()
