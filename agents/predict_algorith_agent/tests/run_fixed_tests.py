#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并返回结果"""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"{'='*60}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False, "", "命令执行超时"
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False, "", str(e)

def main():
    """主函数"""
    print("🚀 开始运行修复后的测试")
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent.parent.parent
    os.chdir(project_root)
    print(f"当前工作目录: {os.getcwd()}")
    
    # 测试结果统计
    test_results = {}
    
    # 1. 运行修复后的数据库管理器测试
    success, _stdout, stderr = run_command(
        "python -m pytest agents/predict_algorith_agent/tests/test_database_manager_fixed.py -v",
        "运行修复后的数据库管理器测试"
    )
    test_results["database_manager_fixed"] = success
    
    # 2. 运行修复后的算法平台服务测试
    success, _stdout, stderr = run_command(
        "python -m pytest agents/predict_algorith_agent/tests/test_algorithm_platform_service_fixed.py -v",
        "运行修复后的算法平台服务测试"
    )
    test_results["algorithm_platform_service_fixed"] = success
    
    # 3. 运行修复后的参数推荐服务测试
    success, _stdout, stderr = run_command(
        "python -m pytest agents/predict_algorith_agent/tests/test_parameter_recommendation_service_fixed.py -v",
        "运行修复后的参数推荐服务测试"
    )
    test_results["parameter_recommendation_service_fixed"] = success
    
    # 4. 运行修复后的WebSocket管理器测试
    success, _stdout, stderr = run_command(
        "python -m pytest agents/predict_algorith_agent/tests/test_websocket_manager_fixed.py -v",
        "运行修复后的WebSocket管理器测试"
    )
    test_results["websocket_manager_fixed"] = success
    
    # 5. 运行所有修复后的测试
    success, _stdout, stderr = run_command(
        "python -m pytest agents/predict_algorith_agent/tests/test_*_fixed.py -v --tb=short",
        "运行所有修复后的测试"
    )
    test_results["all_fixed_tests"] = success
    
    # 6. 生成测试覆盖率报告
    success, _stdout, stderr = run_command(
        "python -m pytest agents/predict_algorith_agent/tests/test_*_fixed.py --cov=agents/predict_algorith_agent --cov-report=term-missing",
        "生成测试覆盖率报告"
    )
    test_results["coverage_report"] = success
    
    # 输出测试结果总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print(f"{'='*60}")
    
    passed_count = sum(1 for result in test_results.values() if result)
    total_count = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed_count}/{total_count} 测试通过")
    success_rate = (passed_count / total_count) * 100
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 测试修复效果良好!")
    elif success_rate >= 60:
        print("⚠️ 测试修复有一定效果，但仍需改进")
    else:
        print("❌ 测试修复效果不佳，需要进一步调试")
    
    # 提供改进建议
    print(f"\n{'='*60}")
    print("💡 改进建议")
    print(f"{'='*60}")
    
    if not test_results.get("database_manager_fixed"):
        print("- 数据库管理器测试失败，检查Mock配置和数据库连接")
    
    if not test_results.get("algorithm_platform_service_fixed"):
        print("- 算法平台服务测试失败，检查HTTP客户端Mock和异步配置")
    
    if not test_results.get("parameter_recommendation_service_fixed"):
        print("- 参数推荐服务测试失败，检查PydanticAI Mock和模型配置")
    
    if not test_results.get("websocket_manager_fixed"):
        print("- WebSocket管理器测试失败，检查WebSocket Mock和异步处理")
    
    print("\n建议下一步行动:")
    print("1. 针对失败的测试进行详细调试")
    print("2. 检查依赖包版本兼容性")
    print("3. 完善Mock对象的配置")
    print("4. 添加更多的集成测试")
    print("5. 建立CI/CD测试流水线")
    
    return success_rate >= 60

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
