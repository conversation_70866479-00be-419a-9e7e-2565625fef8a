#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法分类功能的修复
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

from agents.predict_algorith_agent.models.predictive_models import AlgorithmType, AlgorithmClassification
from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
import json

def test_algorithm_classification():
    """测试算法分类功能"""
    print("=== 测试算法分类功能修复 ===\n")
    
    # 测试用例
    test_cases = [
        "我想用LSTM做设备预测性维护",
        "使用CNN进行图像识别",
        "用随机森林做分类任务",
        "线性回归预测房价",
        "SVM支持向量机分类"
    ]
    
    # 创建智能体实例
    agent = PredictiveAlgorithmAssistant()

    for i, test_input in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {test_input}")
        try:
            result = agent.classify_algorithm(test_input)
            print(f"分类结果: {result['output'].algorithm_type}")
            print(f"置信度: {result['output'].confidence}")
            print(f"理由: {result['output'].reason}")
            print("-" * 50)
        except Exception as e:
            print(f"错误: {e}")
            print("-" * 50)

def test_algorithm_type_enum():
    """测试算法类型枚举"""
    print("=== 测试算法类型枚举 ===\n")
    
    print("支持的算法类型:")
    for algo_type in AlgorithmType:
        print(f"- {algo_type.name}: {algo_type.value}")
    print()

if __name__ == "__main__":
    test_algorithm_type_enum()
    test_algorithm_classification()
