#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket管理器最大覆盖率测试 - 专门用于提升覆盖率到60%以上
针对network/websocket_manager.py中的WebSocket相关功能进行全面测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List

# Mock配置
mock_config = {
    "WEBSOCKET_TIMEOUT": 30,
    "WEBSOCKET_HEARTBEAT_INTERVAL": 10,
    "MAX_CONNECTIONS_PER_USER": 5
}

@pytest.fixture(autouse=True)
def mock_all_external_dependencies():
    """自动Mock所有外部依赖"""
    with patch.dict('agents.config.__dict__', mock_config), \
         patch('agents.predict_algorith_agent.network.websocket_manager.logger'), \
         patch('agents.predict_algorith_agent.database.database_manager.DatabaseManager'), \
         patch('agents.predict_algorith_agent.core.predictive_agent.PredictiveAlgorithmAssistant'):
        yield

class TestWebSocketManagerMaxCoverage:
    """WebSocket管理器最大覆盖率测试"""
    
    def test_date_time_encoder_creation(self):
        """测试DateTimeEncoder创建"""
        from agents.predict_algorith_agent.network.websocket_manager import DateTimeEncoder
        
        # 测试编码器创建
        encoder = DateTimeEncoder()
        assert encoder is not None
        assert hasattr(encoder, 'default')
    
    def test_date_time_encoder_datetime_encoding(self):
        """测试DateTimeEncoder日期时间编码"""
        from agents.predict_algorith_agent.network.websocket_manager import DateTimeEncoder
        
        encoder = DateTimeEncoder()
        test_datetime = datetime(2024, 1, 1, 12, 0, 0)
        
        # 测试日期时间编码
        result = encoder.default(test_datetime)
        assert isinstance(result, str)
        assert "2024-01-01" in result
        assert "12:00:00" in result
    
    def test_date_time_encoder_non_datetime_encoding(self):
        """测试DateTimeEncoder非日期时间对象编码"""
        from agents.predict_algorith_agent.network.websocket_manager import DateTimeEncoder
        
        encoder = DateTimeEncoder()
        
        # 测试非日期时间对象应该抛出TypeError
        with pytest.raises(TypeError):
            encoder.default("not a datetime")
    
    def test_websocket_connection_manager_creation(self):
        """测试WebSocket连接管理器创建"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            # 测试管理器创建
            manager = WebSocketConnectionManager()
            assert manager is not None
            assert hasattr(manager, 'active_connections')
            assert hasattr(manager, 'user_sessions')
            assert isinstance(manager.active_connections, dict)
            assert isinstance(manager.user_sessions, dict)
        except ImportError:
            # 如果类不存在，跳过测试
            pytest.skip("WebSocketConnectionManager类不存在")
    
    @pytest.mark.asyncio
    async def test_websocket_connection_manager_connect(self):
        """测试WebSocket连接管理器连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # Mock WebSocket
            mock_websocket = AsyncMock()
            mock_websocket.accept = AsyncMock()
            
            # 测试连接
            session_id = "test_session_123"
            session_data = {"user_id": "user123", "username": "testuser"}
            
            await manager.connect(mock_websocket, session_id, session_data)
            
            # 验证连接状态
            assert session_id in manager.active_connections
            assert session_id in manager.user_sessions
            assert manager.user_sessions[session_id]["user_id"] == "user123"
            mock_websocket.accept.assert_called_once()
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_disconnect(self):
        """测试WebSocket连接管理器断开连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 添加测试连接
            session_id = "test_session_123"
            mock_websocket = MagicMock()
            manager.active_connections[session_id] = mock_websocket
            manager.user_sessions[session_id] = {"user_id": "user123"}
            
            # 测试断开连接
            manager.disconnect(session_id)
            
            # 验证连接已清理
            assert session_id not in manager.active_connections
            assert session_id not in manager.user_sessions
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    @pytest.mark.asyncio
    async def test_websocket_connection_manager_send_message(self):
        """测试WebSocket连接管理器发送消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # Mock WebSocket
            mock_websocket = AsyncMock()
            mock_websocket.send_text = AsyncMock()
            
            # 添加连接
            session_id = "test_session_123"
            manager.active_connections[session_id] = mock_websocket
            
            # 测试发送消息
            test_message = {"type": "test", "content": "测试消息"}
            await manager.send_message(session_id, test_message)
            
            # 验证消息发送
            mock_websocket.send_text.assert_called_once()
            call_args = mock_websocket.send_text.call_args[0][0]
            sent_data = json.loads(call_args)
            assert sent_data["type"] == "test"
            assert sent_data["content"] == "测试消息"
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    @pytest.mark.asyncio
    async def test_websocket_connection_manager_send_message_connection_error(self):
        """测试WebSocket连接管理器发送消息 - 连接错误"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # Mock WebSocket发送失败
            mock_websocket = AsyncMock()
            mock_websocket.send_text.side_effect = Exception("连接已断开")
            
            # 添加连接
            session_id = "test_session_123"
            manager.active_connections[session_id] = mock_websocket
            
            # 测试发送消息（应该处理异常）
            test_message = {"type": "test", "content": "测试消息"}
            await manager.send_message(session_id, test_message)
            
            # 验证连接被清理
            assert session_id not in manager.active_connections
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    @pytest.mark.asyncio
    async def test_websocket_connection_manager_broadcast(self):
        """测试WebSocket连接管理器广播"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # Mock多个WebSocket连接
            mock_ws1 = AsyncMock()
            mock_ws2 = AsyncMock()
            mock_ws1.send_text = AsyncMock()
            mock_ws2.send_text = AsyncMock()
            
            # 添加连接
            manager.active_connections["session1"] = mock_ws1
            manager.active_connections["session2"] = mock_ws2
            
            # 测试广播
            test_message = {"type": "broadcast", "content": "广播消息"}
            await manager.broadcast(test_message)
            
            # 验证所有连接都收到消息
            mock_ws1.send_text.assert_called_once()
            mock_ws2.send_text.assert_called_once()
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_get_session_info(self):
        """测试WebSocket连接管理器获取会话信息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 添加测试会话
            session_id = "test_session_123"
            session_data = {
                "user_id": "user123",
                "username": "testuser",
                "conversation_id": "conv123",
                "connected_at": datetime.now().isoformat()
            }
            manager.user_sessions[session_id] = session_data
            
            # 测试获取会话信息
            info = manager.get_session_info(session_id)
            assert info is not None
            assert info["user_id"] == "user123"
            assert info["username"] == "testuser"
            assert info["conversation_id"] == "conv123"
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_get_session_info_not_found(self):
        """测试WebSocket连接管理器获取会话信息 - 未找到"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 测试获取不存在的会话信息
            info = manager.get_session_info("nonexistent_session")
            assert info is None
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_update_session_state(self):
        """测试WebSocket连接管理器更新会话状态"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 添加测试会话
            session_id = "test_session_123"
            manager.user_sessions[session_id] = {
                "user_id": "user123",
                "state": {"test": "old_value"}
            }
            
            # 测试更新状态
            new_state = {"test": "new_value", "new_key": "new_data"}
            manager.update_session_state(session_id, new_state)
            
            # 验证状态更新
            assert manager.user_sessions[session_id]["state"]["test"] == "new_value"
            assert manager.user_sessions[session_id]["state"]["new_key"] == "new_data"
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_get_user_sessions(self):
        """测试WebSocket连接管理器获取用户会话"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 添加测试会话
            user_id = "user123"
            manager.user_sessions["session1"] = {"user_id": user_id, "data": "test1"}
            manager.user_sessions["session2"] = {"user_id": user_id, "data": "test2"}
            manager.user_sessions["session3"] = {"user_id": "other_user", "data": "test3"}
            
            # 测试获取用户会话
            user_sessions = manager.get_user_sessions(user_id)
            
            # 验证结果
            assert len(user_sessions) == 2
            assert "session1" in user_sessions
            assert "session2" in user_sessions
            assert "session3" not in user_sessions
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_get_connection_count(self):
        """测试WebSocket连接管理器获取连接数"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 添加测试连接
            manager.active_connections["session1"] = MagicMock()
            manager.active_connections["session2"] = MagicMock()
            manager.active_connections["session3"] = MagicMock()
            
            # 测试获取连接数
            count = manager.get_connection_count()
            assert count == 3
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    @pytest.mark.asyncio
    async def test_websocket_connection_manager_send_to_user(self):
        """测试WebSocket连接管理器发送消息给特定用户"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # Mock WebSocket连接
            mock_ws1 = AsyncMock()
            mock_ws2 = AsyncMock()
            mock_ws1.send_text = AsyncMock()
            mock_ws2.send_text = AsyncMock()
            
            # 添加用户会话
            user_id = "user123"
            manager.active_connections["session1"] = mock_ws1
            manager.active_connections["session2"] = mock_ws2
            manager.user_sessions["session1"] = {"user_id": user_id}
            manager.user_sessions["session2"] = {"user_id": user_id}
            
            # 测试发送消息给用户
            test_message = {"type": "user_message", "content": "用户消息"}
            await manager.send_to_user(user_id, test_message)
            
            # 验证用户的所有会话都收到消息
            mock_ws1.send_text.assert_called_once()
            mock_ws2.send_text.assert_called_once()
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_connection_manager_is_user_connected(self):
        """测试WebSocket连接管理器检查用户是否连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # 添加用户会话
            user_id = "user123"
            manager.user_sessions["session1"] = {"user_id": user_id}
            
            # 测试用户连接状态
            assert manager.is_user_connected(user_id) is True
            assert manager.is_user_connected("nonexistent_user") is False
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    @pytest.mark.asyncio
    async def test_websocket_connection_manager_cleanup_inactive_connections(self):
        """测试WebSocket连接管理器清理非活跃连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import WebSocketConnectionManager
            
            manager = WebSocketConnectionManager()
            
            # Mock WebSocket连接 - 一个正常，一个已断开
            mock_ws_active = AsyncMock()
            mock_ws_inactive = AsyncMock()
            mock_ws_active.send_text = AsyncMock()
            mock_ws_inactive.send_text.side_effect = Exception("连接已断开")
            
            # 添加连接
            manager.active_connections["active_session"] = mock_ws_active
            manager.active_connections["inactive_session"] = mock_ws_inactive
            manager.user_sessions["active_session"] = {"user_id": "user1"}
            manager.user_sessions["inactive_session"] = {"user_id": "user2"}
            
            # 测试清理非活跃连接
            await manager.cleanup_inactive_connections()
            
            # 验证非活跃连接被清理
            assert "active_session" in manager.active_connections
            assert "inactive_session" not in manager.active_connections
            assert "inactive_session" not in manager.user_sessions
        except ImportError:
            pytest.skip("WebSocketConnectionManager类不存在")
    
    def test_websocket_routes_import(self):
        """测试WebSocket路由导入"""
        import agents.predict_algorith_agent.network.websocket_manager as ws_module
        
        # 测试模块导入成功
        assert ws_module is not None
        assert hasattr(ws_module, '__file__')
        
        # 测试DateTimeEncoder存在
        assert hasattr(ws_module, 'DateTimeEncoder')
    
    def test_websocket_module_constants(self):
        """测试WebSocket模块常量"""
        import agents.predict_algorith_agent.network.websocket_manager as ws_module
        
        # 测试模块级别的常量和函数
        assert hasattr(ws_module, 'DateTimeEncoder')
        
        # 如果有其他常量，也可以在这里测试
        # 例如：assert hasattr(ws_module, 'DEFAULT_TIMEOUT')
    
    def test_json_serialization_with_datetime(self):
        """测试包含日期时间的JSON序列化"""
        from agents.predict_algorith_agent.network.websocket_manager import DateTimeEncoder
        
        # 测试包含日期时间的数据序列化
        test_data = {
            "message": "测试消息",
            "timestamp": datetime(2024, 1, 1, 12, 0, 0),
            "user_id": "user123"
        }
        
        # 使用自定义编码器序列化
        json_str = json.dumps(test_data, cls=DateTimeEncoder, ensure_ascii=False)
        
        # 验证序列化结果
        assert isinstance(json_str, str)
        assert "测试消息" in json_str
        assert "2024-01-01" in json_str
        assert "user123" in json_str
        
        # 验证可以反序列化
        parsed_data = json.loads(json_str)
        assert parsed_data["message"] == "测试消息"
        assert parsed_data["user_id"] == "user123"
