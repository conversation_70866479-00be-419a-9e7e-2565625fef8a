#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pytest
import sys
import os
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime
import json

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 测试配置
TEST_CONFIG = {
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "MYSQL_CHARSET": "utf8mb4",
    "REDIS_HOST": "localhost",
    "REDIS_PORT": 6379,
    "REDIS_DB": 0,
    "REDIS_PASSWORD": None,
    "ENABLE_WELCOME_MESSAGE": True,
    "LOG_LEVEL": "ERROR",  # 减少测试时的日志输出
    "ALGORITHM_API_BASE": "http://test-api.example.com",
    "TRAINING_API_BASE": "http://test-training.example.com",
    "HISTORY_ALGORITHM_API_BASE": "http://test-history.example.com",
}

@pytest.fixture(autouse=True)
def mock_agents_config():
    """自动Mock agents.config模块"""
    with patch.dict('agents.config.__dict__', TEST_CONFIG):
        yield

@pytest.fixture(autouse=True)
def mock_logging():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.utils.logging_config.setup_logging'):
        yield

@pytest.fixture
def mock_database_connection():
    """Mock数据库连接"""
    mock_connection = MagicMock()
    mock_cursor = MagicMock()
    
    # 配置cursor的返回值
    mock_cursor.fetchone.return_value = None
    mock_cursor.fetchall.return_value = []
    mock_cursor.rowcount = 0
    mock_cursor.lastrowid = 1
    
    # 配置connection
    mock_connection.cursor.return_value = mock_cursor
    mock_connection.__enter__.return_value = mock_connection
    mock_connection.__exit__.return_value = None
    
    with patch('pymysql.connect', return_value=mock_connection):
        yield mock_connection, mock_cursor

@pytest.fixture
def mock_redis_connection():
    """Mock Redis连接"""
    mock_redis = MagicMock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    
    with patch('redis.Redis', return_value=mock_redis):
        yield mock_redis

@pytest.fixture
def mock_websocket():
    """Mock WebSocket连接"""
    mock_ws = AsyncMock()
    mock_ws.accept = AsyncMock()
    mock_ws.send_text = AsyncMock()
    mock_ws.close = AsyncMock()
    mock_ws.client_state = MagicMock()
    mock_ws.client_state.name = "CONNECTED"
    return mock_ws

@pytest.fixture
def mock_http_client():
    """Mock HTTP客户端"""
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {"status": "success", "data": {}}
    mock_response.text = '{"status": "success", "data": {}}'
    
    mock_client = AsyncMock()
    mock_client.get.return_value = mock_response
    mock_client.post.return_value = mock_response
    mock_client.put.return_value = mock_response
    mock_client.delete.return_value = mock_response
    
    with patch('aiohttp.ClientSession', return_value=mock_client):
        yield mock_client

@pytest.fixture
def mock_pydantic_ai_agent():
    """Mock PydanticAI Agent"""
    mock_agent = MagicMock()
    mock_run_result = MagicMock()
    mock_run_result.output = MagicMock()
    mock_agent.run.return_value = mock_run_result
    
    with patch('pydantic_ai.Agent', return_value=mock_agent):
        yield mock_agent

@pytest.fixture
def sample_conversation_data():
    """示例对话数据"""
    return {
        "conversation_id": "test_conv_123",
        "user_id": "test_user_456",
        "title": "测试对话",
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "is_active": True
    }

@pytest.fixture
def sample_message_data():
    """示例消息数据"""
    return {
        "id": 1,
        "conversation_id": "test_conv_123",
        "message_type": "user",
        "content": "测试消息",
        "message_data": {"key": "value"},
        "interaction_type": "chat",
        "message_sequence": 1,
        "key_info": None,
        "token_count": 10,
        "timestamp": datetime.now()
    }

@pytest.fixture
def sample_algorithm_data():
    """示例算法数据"""
    return {
        "algorithm_id": "test_algo_123",
        "algorithm_name": "测试算法",
        "algorithm_type": "LSTM",
        "description": "测试用的LSTM算法",
        "parameters": {
            "learning_rate": 0.001,
            "hidden_size": 128,
            "num_layers": 2
        },
        "status": "active"
    }

@pytest.fixture
def sample_parameter_recommendation():
    """示例参数推荐数据"""
    return {
        "algorithm_type": "LSTM",
        "recommended_parameters": [
            {
                "parameter_name": "learning_rate",
                "recommended_value": 0.001,
                "confidence": 0.9,
                "reasoning": "基于数据规模推荐",
                "impact_description": "影响模型收敛速度"
            }
        ],
        "overall_confidence": 0.85,
        "llm_provider": "QWEN",
        "model_name": "qwen-turbo",
        "generation_time": 0.5
    }

# 测试环境清理
@pytest.fixture(autouse=True)
def cleanup_test_environment():
    """测试环境清理"""
    yield

# 异步测试支持
@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    import asyncio
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
