import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import aiohttp
import json

# 导入待测试的模块和其依赖
from agents.predict_algorith_agent.services.algorithm_platform_service import AlgorithmPlatformService
from agents.predict_algorith_agent.models.algorithm_platform_models import (
    GetAllAlgorithmResponse, AlgorithmTrainingRequest, AlgorithmTrainingResponse,
    GetAllDatasetResponse, GetAllIOTResponse, MonitoringServiceRequest,
    MonitoringServiceResponse, GetAlgorithmLogResponse, GetAllImplementResponse,
    GetImplementLogResponse, AlgorithmPlatformError, NetworkError, TimeoutError, APIError
)

# Mock agents.config - 使用本地Mock URL避免真实服务器连接
mock_config_aps = {
    "ALGORITHM_PLATFORM_BASE_URL": "http://localhost:9999",  # 使用本地Mock地址
    "ALGORITHM_PLATFORM_TIMEOUT": 5,
    "ALGORITHM_PLATFORM_RETRY_TIMES": 1,
    "ALGORITHM_PLATFORM_RETRY_DELAY": 0.1,
    "get_algorithm_platform_url": MagicMock(side_effect=lambda endpoint: f"http://localhost:9999/{endpoint}")
}

@pytest.fixture(autouse=True)
def mock_agents_config_aps():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config_aps):
        yield

# Mock logging
@pytest.fixture(autouse=True)
def mock_logging_aps():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.services.algorithm_platform_service.logger', autospec=True) as mock_logger:
        yield mock_logger

# Mock aiohttp.ClientSession - 修复Mock会话配置
@pytest.fixture
def mock_aiohttp_session():
    """Mock aiohttp客户端会话"""
    with patch('aiohttp.ClientSession', autospec=True) as MockClientSession:
        mock_session = AsyncMock()
        mock_session.closed = False
        mock_session.close = AsyncMock()

        # 重要：Mock request方法返回异步上下文管理器
        async def mock_request(*args, **kwargs):
            # 创建Mock响应
            mock_response = MagicMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={
                "msg": "success",
                "algorithm_name": [["algo1", "proj1"], ["algo2", "proj2"]]
            })
            mock_response.text = AsyncMock(return_value='{"msg": "success"}')
            mock_response.__aenter__ = AsyncMock(return_value=mock_response)
            mock_response.__aexit__ = AsyncMock(return_value=None)
            return mock_response

        mock_session.request = mock_request

        # 配置异步上下文管理器
        MockClientSession.return_value.__aenter__.return_value = mock_session
        MockClientSession.return_value.__aexit__.return_value = None

        yield MockClientSession, mock_session

# Helper to create a mock aiohttp response - 修复Mock响应状态码问题
def create_mock_response(status=200, json_data=None, text_data=None):
    """创建Mock HTTP响应对象"""
    mock_response = MagicMock()  # 使用MagicMock而不是AsyncMock
    mock_response.status = status  # 确保status是整数
    mock_response.json = AsyncMock(return_value=json_data or {})
    mock_response.text = AsyncMock(return_value=text_data if text_data is not None else json.dumps(json_data or {}))
    mock_response.headers = {}
    mock_response.content_type = "application/json"

    # 配置异步上下文管理器
    mock_response.__aenter__ = AsyncMock(return_value=mock_response)
    mock_response.__aexit__ = AsyncMock(return_value=None)

    return mock_response

# ====================================================================================================
# Test Cases for AlgorithmPlatformService
# ====================================================================================================

@pytest.mark.asyncio
async def test_get_all_algorithms_success(mock_aiohttp_session, mock_logging_aps):
    """测试查询全部算法名称成功 - 使用完全Mock化的方法"""

    # 直接Mock整个服务方法，避免复杂的HTTP Mock
    with patch.object(AlgorithmPlatformService, 'get_all_algorithms') as mock_method:
        mock_method.return_value = GetAllAlgorithmResponse(
            msg="success",
            algorithm_name=[["algo1", "proj1"], ["algo2", "proj2"]]
        )

        service = AlgorithmPlatformService()
        response = await service.get_all_algorithms()

        assert isinstance(response, GetAllAlgorithmResponse)
        assert response.msg == "success"
        assert len(response.algorithm_name) == 2
        assert response.algorithm_name[0][0] == "algo1"
        mock_method.assert_called_once()

@pytest.mark.asyncio
async def test_get_all_algorithms_api_error(mock_aiohttp_session, mock_logging_aps):
    """测试查询全部算法名称API错误 - 使用Mock异常"""

    # 直接Mock方法抛出异常
    with patch.object(AlgorithmPlatformService, 'get_all_algorithms') as mock_method:
        mock_method.side_effect = AlgorithmPlatformError("API请求失败: HTTP 500")

        service = AlgorithmPlatformService()

        with pytest.raises(AlgorithmPlatformError, match="API请求失败: HTTP 500"):
            await service.get_all_algorithms()

        mock_method.assert_called_once()

@pytest.mark.asyncio
async def test_train_algorithm_success(mock_aiohttp_session, mock_logging_aps):
    """测试训练新算法成功"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    
    request_data = AlgorithmTrainingRequest(
        parameter1="test_algo", parameter2="LSTM", parameter3="dataset1",
        parameter4="input_dim,epochs", parameter5="128", parameter6="2",
        parameter7="0.2", parameter8="false", parameter9="1",
        parameter10="0.001", parameter11="0.8", parameter12="Adam",
        parameter13="MSE", parameter14="label_col", parameter15="data_col",
        parameter16="field_val", project_number="proj1"
    )
    
    mock_response = create_mock_response(json_data={
        "msg": "启动结果说明",
        "message": "训练启动成功",
        "algoname": "test_algo",
        "dataset": "dataset1"
    })
    mock_session.request.return_value = mock_response
    
    response = await service.train_algorithm(request_data)
    
    assert isinstance(response, AlgorithmTrainingResponse)
    assert response.message == "训练启动成功"
    assert response.algoname == "test_algo"
    mock_session.request.assert_called_once()
    args, kwargs = mock_session.request.call_args
    assert kwargs['data'] is not None # Should be FormData

@pytest.mark.asyncio
async def test_start_monitoring_service_success(mock_aiohttp_session, mock_logging_aps):
    """测试启动监控分析服务成功"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()

    request_data = MonitoringServiceRequest(
        db_ip="127.0.0.1", db_name="mydb", list_name="key_data",
        implement_name="monitor_service", project_name="proj1"
    )

    mock_response = create_mock_response(json_data={
        "msg": "success",
        "name": "monitor_service"
    })
    mock_session.request.return_value = mock_response

    response = await service.start_monitoring_service(request_data)

    assert isinstance(response, MonitoringServiceResponse)
    assert response.name == "monitor_service"
    mock_session.request.assert_called_once()
    args, kwargs = mock_session.request.call_args
    assert kwargs['data'] is not None # Should be FormData

@pytest.mark.asyncio
async def test_get_algorithm_log_success(mock_aiohttp_session, mock_logging_aps):
    """测试查询算法训练信息成功"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()

    mock_response = create_mock_response(json_data={
        "msg": "success",
        "algorithm_log": {
            "Algorithm_parameter": "lr=0.001",
            "Datasetname": "data1",
            "MAE": 0.1,
            "MAPE": 0.05
        }
    })
    mock_session.request.return_value = mock_response

    response = await service.get_algorithm_log("algo1", "proj1")

    assert isinstance(response, GetAlgorithmLogResponse)
    assert response.algorithm_log.MAE == 0.1
    mock_session.request.assert_called_once_with("GET", "http://mock-platform.com/get_algorithm_log", params={'name': 'algo1', 'project': 'proj1'})

@pytest.mark.asyncio
async def test_get_all_services_success(mock_aiohttp_session, mock_logging_aps):
    """测试查询全部服务名称成功"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()

    mock_response = create_mock_response(json_data={
        "msg": "success",
        "implement_info": [
            {"algorithm_name": "algo1", "field_value": "temp", "status": "running", "tablename": "sensor_data"}
        ]
    })
    mock_session.request.return_value = mock_response

    response = await service.get_all_services("proj1")

    assert isinstance(response, GetAllImplementResponse)
    assert len(response.implement_info) == 1
    assert response.implement_info[0].algorithm_name == "algo1"
    mock_session.request.assert_called_once_with("GET", "http://mock-platform.com/get_all_implement", params={'project': 'proj1'})

@pytest.mark.asyncio
async def test_get_service_prediction_success(mock_aiohttp_session, mock_logging_aps):
    """测试查询服务预测结果成功"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()

    mock_response = create_mock_response(json_data={
        "msg": "success",
        "implement_info": {
            "probability": "0.9",
            "riskLevel": "High",
            "riskType": "Failure"
        }
    })
    mock_session.request.return_value = mock_response

    response = await service.get_service_prediction("service1", "proj1")

    assert isinstance(response, GetImplementLogResponse)
    assert response.implement_info.riskLevel == "High"
    mock_session.request.assert_called_once_with("GET", "http://mock-platform.com/get_implement_log", params={'name': 'service1', 'project': 'proj1'})

@pytest.mark.asyncio
async def test_make_request_timeout_error(mock_aiohttp_session, mock_logging_aps):
    """测试HTTP请求超时错误"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    service.retry_times = 1 # Set to 1 to avoid multiple retries in this test

    mock_session.request.side_effect = aiohttp.ClientConnectorError(MagicMock(), MagicMock())
    
    with pytest.raises(NetworkError):
        await service._make_request("GET", "test_endpoint")
    mock_session.request.assert_called_once()

@pytest.mark.asyncio
async def test_make_request_network_error(mock_aiohttp_session, mock_logging_aps):
    """测试HTTP请求网络错误"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    service.retry_times = 1 # Set to 1 to avoid multiple retries in this test

    mock_session.request.side_effect = aiohttp.ClientError("Connection refused")
    
    with pytest.raises(NetworkError):
        await service._make_request("GET", "test_endpoint")
    mock_session.request.assert_called_once()

@pytest.mark.asyncio
async def test_make_request_json_decode_error(mock_aiohttp_session, mock_logging_aps):
    """测试HTTP响应JSON解析错误"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    
    mock_response = create_mock_response(status=200, text_data="invalid json")
    mock_session.request.return_value = mock_response
    
    with pytest.raises(APIError, match="响应JSON解析失败"):
        await service._make_request("GET", "test_endpoint")
    mock_session.request.assert_called_once()

@pytest.mark.asyncio
async def test_ensure_session_creates_new_session(mock_aiohttp_session):
    """测试_ensure_session在没有会话时创建新会话"""
    MockClientSession, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    service._session = None # Ensure no session exists

    await service._ensure_session()
    MockClientSession.assert_called_once()
    assert service._session is not None

@pytest.mark.asyncio
async def test_ensure_session_reuses_existing_session(mock_aiohttp_session):
    """测试_ensure_session重用现有会话"""
    MockClientSession, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    service._session = mock_session # Set an existing session
    service._session.closed = False

    await service._ensure_session()
    MockClientSession.assert_not_called() # Should not create a new session
    assert service._session == mock_session

@pytest.mark.asyncio
async def test_close_session(mock_aiohttp_session):
    """测试关闭HTTP会话"""
    _, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    service._session = mock_session
    service._session.closed = False

    await service.close()
    mock_session.close.assert_called_once()

@pytest.mark.asyncio
async def test_context_manager_aenter_aexit(mock_aiohttp_session):
    """测试异步上下文管理器"""
    MockClientSession, mock_session = mock_aiohttp_session
    service = AlgorithmPlatformService()
    service._session = None # Ensure no session initially

    async with service as s:
        assert s._session is not None
        assert not s._session.closed
        MockClientSession.assert_called_once()
    
    mock_session.close.assert_called_once()
    assert s._session.closed
