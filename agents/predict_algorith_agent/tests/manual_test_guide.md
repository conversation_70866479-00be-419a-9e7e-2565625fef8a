# 手动测试指南 - 智能体问答对话验证

## 🎯 测试目标
基于 `智能体问答对话样例.json` 完整验证智能体功能

## 🚀 测试准备

### 1. 确认服务器状态
- ✅ 后端服务器运行在：`http://localhost:8008`
- ✅ WebSocket端点：`ws://localhost:8008/api/ws/chat`
- ✅ 前端界面：`file:///D:/project/jiaoda/twinbuilder-agents/agents/predict_algorith_agent/static/html/chat_interface_v3.html`

### 2. 打开浏览器测试界面
1. 打开浏览器（Chrome/Edge）
2. 访问前端界面文件
3. 点击"快速测试"按钮登录
4. 点击"连接"按钮建立WebSocket连接

## 📋 测试场景清单

### 场景1：系统初始化 ✅
**操作：** 建立连接后等待系统响应
**预期：** "请问有什么能帮您，我猜您想问：(1)我想训练一个算法，我要怎么做?(2)你能提供哪些帮助？"
**验证点：**
- [ ] 欢迎消息显示正确
- [ ] 引导选项清晰
- [ ] 界面响应及时

### 场景2：功能咨询 🔄
**操作：** 输入 "你能提供哪些帮助？"
**预期：** 详细介绍智能助手功能（算法训练流程、监控进程、技术问题解答）
**验证点：**
- [ ] 功能介绍完整
- [ ] 表述专业准确
- [ ] 格式清晰易读

### 场景3：算法训练需求 🔄
**操作：** 输入 "我想训练一个算法"
**预期：** 引导描述数据来源、分析目标、数据结构、预处理需求、数据特征
**验证点：**
- [ ] 引导问题全面
- [ ] 逻辑顺序合理
- [ ] 专业术语准确

### 场景4：数据描述 🔄
**操作：** 输入 "我有水泵电流数据，想预测故障，数据是每秒采样，存在少量空值"
**预期：** 识别历史算法，提供直接调用或新建算法选项
**验证点：**
- [ ] 数据理解准确
- [ ] 算法推荐合理
- [ ] 选项提供清晰

### 场景5：算法创建 🔄
**操作：** 输入 "确认新建算法"
**预期：** 提示上传数据集，支持.csv、.xlsx格式
**验证点：**
- [ ] 上传提示清晰
- [ ] 格式限制正确
- [ ] 界面功能可用

### 场景6：参数推荐 🔄
**操作：** 输入 "已上传数据集，等待参数推荐"
**预期：** 返回参数建议并解释含义
**验证点：**
- [ ] 参数推荐合理
- [ ] 解释专业准确
- [ ] 操作指引清晰

### 场景7：训练进度 🔄
**操作：** 输入 "训练进度如何？"
**预期：** 显示进度百分比、损失函数值、实时状态
**验证点：**
- [ ] 进度信息实时
- [ ] 状态栏同步
- [ ] 数据格式正确

### 场景8：测试集效果 🔄
**操作：** 输入 "测试集效果怎么样？"
**预期：** 展示测试结果、预测效果图、拟合趋势
**验证点：**
- [ ] 结果展示完整
- [ ] 图表显示正确
- [ ] 分析专业准确

### 场景9：异常处理 🔄
**操作：** 输入 "为什么训练异常？"
**预期：** 分析日志、识别原因、提供解决方案
**验证点：**
- [ ] 错误诊断准确
- [ ] 解决方案可行
- [ ] 用户体验友好

### 场景10：重新训练 🔄
**操作：** 输入 "重新训练"
**预期：** 启动新训练、显示参数、更新状态
**验证点：**
- [ ] 重训练流程完整
- [ ] 参数更新正确
- [ ] 状态同步及时

## 🔍 边界情况测试

### 安全性测试 🔄
**操作：** 输入 "union select 1,2,3"
**预期：** "输入内容不符合规范，请重新输入"
**验证点：**
- [ ] SQL注入防护有效
- [ ] 错误提示友好
- [ ] 系统稳定性保持

### 格式验证测试 🔄
**操作：** 输入 "上传了.txt格式的数据集"
**预期：** "不支持该格式，请上传.csv或.xlsx文件"
**验证点：**
- [ ] 格式验证准确
- [ ] 错误提示清晰
- [ ] 支持格式明确

### 技术解释测试 🔄
**操作：** 输入 "学习率0.001是什么意思？"
**预期：** 详细解释学习率概念、取值范围、影响因素
**验证点：**
- [ ] 解释专业准确
- [ ] 通俗易懂
- [ ] 实用性强

## 📊 测试记录表

| 场景 | 用户输入 | 智能体响应 | 状态 | 备注 |
|------|----------|------------|------|------|
| 1 | 系统初始化 | 等待验证 | 🔄 | |
| 2 | 你能提供哪些帮助？ | 等待验证 | 🔄 | |
| 3 | 我想训练一个算法 | 等待验证 | 🔄 | |
| 4 | 水泵电流数据描述 | 等待验证 | 🔄 | |
| 5 | 确认新建算法 | 等待验证 | 🔄 | |
| 6 | 已上传数据集 | 等待验证 | 🔄 | |
| 7 | 训练进度如何？ | 等待验证 | 🔄 | |
| 8 | 测试集效果怎么样？ | 等待验证 | 🔄 | |
| 9 | 为什么训练异常？ | 等待验证 | 🔄 | |
| 10 | 重新训练 | 等待验证 | 🔄 | |

## 🎯 测试完成标准

### 功能性测试
- [ ] 所有对话场景响应正确
- [ ] 技术解释专业准确
- [ ] 操作指引清晰明确
- [ ] 异常处理友好有效

### 性能测试
- [ ] 响应时间 < 5秒
- [ ] WebSocket连接稳定
- [ ] 界面交互流畅
- [ ] 内存使用合理

### 用户体验测试
- [ ] 界面美观易用
- [ ] 交互逻辑清晰
- [ ] 错误提示友好
- [ ] 功能完整可用

## 📝 问题记录

### 发现的问题
1. **模块导入警告：** `No module named 'core.response'`
2. **待验证项：** 前端界面与后端集成完整性

### 改进建议
1. **优化响应时间：** 考虑缓存机制
2. **增强错误处理：** 更友好的用户提示
3. **完善日志记录：** 详细的行为跟踪
4. **界面优化：** 提升用户体验

## 🚀 下一步行动
1. 通过浏览器完成所有场景测试
2. 记录实际响应与预期对比
3. 验证API功能按钮效果
4. 完善测试报告和修复建议
