#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket管理器测试 - 专注于提高代码覆盖率
测试network/websocket_manager.py中的主要类和方法
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# Mock配置
mock_config_websocket = {
    "ENABLE_WELCOME_MESSAGE": True,
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model"
    }
}

@pytest.fixture(autouse=True)
def mock_agents_config_websocket():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config_websocket):
        yield

@pytest.fixture(autouse=True)
def mock_logging_websocket():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.logger'):
        yield

@pytest.fixture(autouse=True)
def mock_pymysql_websocket():
    """自动Mock pymysql数据库连接"""
    with patch('pymysql.connect') as mock_connect:
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_cursor.rowcount = 0
        mock_cursor.lastrowid = 1
        
        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.__enter__.return_value = mock_conn
        mock_conn.__exit__.return_value = None
        mock_connect.return_value = mock_conn
        
        yield mock_connect

@pytest.fixture
def mock_websocket():
    """Mock WebSocket连接"""
    websocket = AsyncMock()
    websocket.accept = AsyncMock()
    websocket.send_text = AsyncMock()
    websocket.receive_text = AsyncMock()
    websocket.close = AsyncMock()
    return websocket

@pytest.fixture
def mock_predictive_agent():
    """Mock预测算法助手"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.PredictiveAlgorithmAssistant') as MockAgent:
        mock_instance = MockAgent.return_value
        mock_instance.process_user_input_async = AsyncMock(return_value=(
            {"msg": "Mock AI回复", "type": "chat_response"}, 
            {"current_stage": "welcome", "progress": 0.0}, 
            "chat"
        ))
        yield MockAgent

@pytest.fixture
def mock_db_manager():
    """Mock数据库管理器"""
    with patch('agents.predict_algorith_agent.network.websocket_manager.DatabaseManager') as MockDBManager:
        mock_instance = MockDBManager.return_value
        mock_instance.create_conversation = MagicMock(return_value="conv_test_id")
        mock_instance.add_conversation_message = MagicMock(return_value=True)
        mock_instance.save_session_state = MagicMock(return_value=True)
        mock_instance.get_conversation_messages_with_context = MagicMock(return_value=[])
        mock_instance.get_user_conversations = MagicMock(return_value=[])
        mock_instance.get_conversation_detail = MagicMock(return_value={
            "conversation_id": "conv_test_id",
            "user_id": "test_user",
            "title": "测试对话",
            "status": "active"
        })
        yield MockDBManager

class TestConnectionManagerImports:
    """测试连接管理器导入"""
    
    def test_connection_manager_import(self):
        """测试连接管理器导入"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            assert ConnectionManager is not None
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

class TestConnectionManager:
    """测试ConnectionManager类的主要方法"""
    
    def test_connection_manager_creation(self, mock_predictive_agent, mock_db_manager):
        """测试连接管理器创建"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            assert manager is not None
            assert hasattr(manager, 'active_connections')
            assert hasattr(manager, 'user_sessions')
            assert isinstance(manager.active_connections, dict)
            assert isinstance(manager.user_sessions, dict)
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_connect_new_session(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试新会话连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 验证连接状态
            assert session_id in manager.active_connections
            assert session_id in manager.user_sessions
            assert manager.user_sessions[session_id]["user_id"] == "test_user_1"
            
            # 验证WebSocket操作
            mock_websocket.accept.assert_called_once()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_connect_existing_session(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试已存在会话的连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 第一次连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 第二次连接同一会话
            mock_websocket2 = AsyncMock()
            mock_websocket2.accept = AsyncMock()
            mock_websocket2.send_text = AsyncMock()
            
            await manager.connect(mock_websocket2, session_id, user_info)
            
            # 验证新连接替换了旧连接
            assert session_id in manager.active_connections
            assert manager.active_connections[session_id] == mock_websocket2
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_disconnect_session(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试会话断开连接"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            assert session_id in manager.active_connections
            
            # 断开连接
            manager.disconnect(session_id)
            
            # 验证连接已移除
            assert session_id not in manager.active_connections
            assert session_id not in manager.user_sessions
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_send_personal_message(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试发送个人消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 发送消息
            message = {"type": "test", "data": "测试消息"}
            await manager.send_personal_message(json.dumps(message), session_id)
            
            # 验证消息被发送
            mock_websocket.send_text.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_send_personal_message_to_nonexistent_session(self, mock_predictive_agent, mock_db_manager):
        """测试向不存在的会话发送消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            
            # 向不存在的会话发送消息（应该不会抛出异常）
            message = {"type": "test", "data": "测试消息"}
            await manager.send_personal_message(json.dumps(message), "nonexistent_session")
            
            # 测试通过，没有异常抛出
            assert True
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_broadcast_message(self, mock_predictive_agent, mock_db_manager):
        """测试广播消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            
            # 创建多个连接
            mock_websocket1 = AsyncMock()
            mock_websocket1.accept = AsyncMock()
            mock_websocket1.send_text = AsyncMock()
            
            mock_websocket2 = AsyncMock()
            mock_websocket2.accept = AsyncMock()
            mock_websocket2.send_text = AsyncMock()
            
            await manager.connect(mock_websocket1, "session1", {"user_id": "user1"})
            await manager.connect(mock_websocket2, "session2", {"user_id": "user2"})
            
            # 广播消息
            message = {"type": "broadcast", "data": "广播消息"}
            await manager.broadcast(json.dumps(message))
            
            # 验证所有连接都收到消息
            mock_websocket1.send_text.assert_called()
            mock_websocket2.send_text.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_handle_message_chat(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试处理聊天消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 处理聊天消息
            message_data = {
                "type": "chat",
                "data": {"message": "你好"},
                "session_id": session_id,
                "user_id": "test_user_1"
            }
            
            await manager.handle_message(mock_websocket, json.dumps(message_data), session_id)
            
            # 验证AI助手被调用
            mock_predictive_agent.return_value.process_user_input_async.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_handle_message_get_conversations(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试处理获取对话列表消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 处理获取对话列表消息
            message_data = {
                "type": "get_conversations",
                "data": {},
                "session_id": session_id,
                "user_id": "test_user_1"
            }
            
            await manager.handle_message(mock_websocket, json.dumps(message_data), session_id)
            
            # 验证数据库管理器被调用
            mock_db_manager.return_value.get_user_conversations.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_handle_message_get_conversation_detail(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试处理获取对话详情消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 处理获取对话详情消息
            message_data = {
                "type": "get_conversation_detail",
                "data": {"conversation_id": "conv_123"},
                "session_id": session_id,
                "user_id": "test_user_1"
            }
            
            await manager.handle_message(mock_websocket, json.dumps(message_data), session_id)
            
            # 验证数据库管理器被调用
            mock_db_manager.return_value.get_conversation_detail.assert_called_with("conv_123")
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_handle_message_invalid_json(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试处理无效JSON消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 处理无效JSON消息
            invalid_message = "这不是有效的JSON"
            
            await manager.handle_message(mock_websocket, invalid_message, session_id)
            
            # 验证错误响应被发送
            mock_websocket.send_text.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_handle_message_unknown_type(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试处理未知类型消息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 处理未知类型消息
            message_data = {
                "type": "unknown_type",
                "data": {"message": "未知消息"},
                "session_id": session_id,
                "user_id": "test_user_1"
            }
            
            await manager.handle_message(mock_websocket, json.dumps(message_data), session_id)
            
            # 验证错误响应被发送
            mock_websocket.send_text.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    def test_get_active_connections_count(self, mock_predictive_agent, mock_db_manager):
        """测试获取活跃连接数"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            
            # 初始连接数应该为0
            assert len(manager.active_connections) == 0
            
            # 添加一些连接
            manager.active_connections["session1"] = MagicMock()
            manager.active_connections["session2"] = MagicMock()
            
            # 验证连接数
            assert len(manager.active_connections) == 2
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    def test_get_user_sessions_info(self, mock_predictive_agent, mock_db_manager):
        """测试获取用户会话信息"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            
            # 添加一些用户会话
            manager.user_sessions["session1"] = {
                "user_id": "user1",
                "username": "用户1",
                "connected_at": datetime.now()
            }
            manager.user_sessions["session2"] = {
                "user_id": "user2", 
                "username": "用户2",
                "connected_at": datetime.now()
            }
            
            # 验证会话信息
            assert len(manager.user_sessions) == 2
            assert "user1" == manager.user_sessions["session1"]["user_id"]
            assert "用户2" == manager.user_sessions["session2"]["username"]
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

class TestConnectionManagerErrorHandling:
    """测试连接管理器的错误处理"""
    
    @pytest.mark.asyncio
    async def test_handle_websocket_disconnect_error(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试WebSocket断开连接错误处理"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 模拟WebSocket发送消息时出错
            mock_websocket.send_text.side_effect = Exception("连接已断开")
            
            # 尝试发送消息
            message = {"type": "test", "data": "测试消息"}
            await manager.send_personal_message(json.dumps(message), session_id)
            
            # 验证错误被处理，连接被清理
            # 这里我们主要验证没有未处理的异常抛出
            assert True
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")

    @pytest.mark.asyncio
    async def test_handle_ai_processing_error(self, mock_websocket, mock_predictive_agent, mock_db_manager):
        """测试AI处理错误"""
        try:
            from agents.predict_algorith_agent.network.websocket_manager import ConnectionManager
            
            manager = ConnectionManager()
            session_id = "test_session_1"
            user_info = {"user_id": "test_user_1", "username": "测试用户"}
            
            # 配置AI助手抛出异常
            mock_predictive_agent.return_value.process_user_input_async.side_effect = Exception("AI处理失败")
            
            # 先连接
            await manager.connect(mock_websocket, session_id, user_info)
            
            # 处理聊天消息
            message_data = {
                "type": "chat",
                "data": {"message": "你好"},
                "session_id": session_id,
                "user_id": "test_user_1"
            }
            
            await manager.handle_message(mock_websocket, json.dumps(message_data), session_id)
            
            # 验证错误响应被发送
            mock_websocket.send_text.assert_called()
            
        except ImportError as e:
            pytest.skip(f"导入失败: {e}")
