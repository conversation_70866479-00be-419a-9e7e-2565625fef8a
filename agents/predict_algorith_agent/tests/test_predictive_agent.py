import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# 导入待测试的模块和其依赖
from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant, router
from agents.predict_algorith_agent.models.predictive_models import (
    PredictiveTaskType, AlgorithmType, InteractionType,
    PredictiveAgentState, AlgorithmClassification, PredictiveParameterExtraction,
    PredictiveInteractionClassification, PredictiveConfirmationRequest
)
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    ParameterRecommendationResponse, ParameterRecommendation, LLMProvider,
    DataScale, PerformanceObjective
)
from agents.predict_algorith_agent.services.fallback_responses import FallbackResponseManager
from agents.predict_algorith_agent.utils.logging_config import get_agent_logger, should_log_debug, should_log_performance, log_performance

# Mock 外部依赖 - 使用本地Mock地址避免真实服务器连接
# Mock agents.config
mock_config = {
    "SHOW_DEEPSEEK_THOUGHT": False,
    "ALGORITHM_PLATFORM_BASE_URL": "http://localhost:9999",  # 使用本地Mock地址
    "WKHTMLTOPDF_PATH": "/usr/local/bin/wkhtmltopdf",
    "REPORT_PDF_DIR": "/tmp/reports",
    "MYSQL_HOST": "localhost",
    "MYSQL_PORT": 3306,
    "MYSQL_USER": "test_user",
    "MYSQL_PASSWORD": "test_password",
    "MYSQL_DB": "test_db",
    "MYSQL_CHARSET": "utf8mb4",
    "LLM_CONFIG": {
        "provider": "mock_provider",
        "model": "mock_model",
        "api_key": "mock_api_key"
    },
    "get_current_llm_config": MagicMock(return_value={
        "model_name": "mock-llm",
        "base_url": "http://localhost:9998",  # 使用本地Mock地址
        "api_key": "mock-key",
        "description": "Mock LLM"
    })
}

# 使用 patch.dict 模拟 agents.config 模块
@pytest.fixture(autouse=True)
def mock_agents_config():
    """自动Mock agents.config配置"""
    with patch.dict('agents.config.__dict__', mock_config):
        yield

# Mock pydantic_ai.Agent - 增强Mock AI Agent
@pytest.fixture
def mock_pydantic_ai_agent():
    """Mock PydanticAI Agent避免真实AI模型调用"""
    with patch('pydantic_ai.Agent', autospec=True) as MockAgent:
        # Mock run_sync and run methods
        mock_instance = MockAgent.return_value
        mock_instance.run_sync = MagicMock()
        mock_instance.run = AsyncMock()

        # 配置默认返回值
        mock_instance.run_sync.return_value = MagicMock(data={'classification': 'general_consultation'})
        mock_instance.run.return_value = MagicMock(data={'classification': 'general_consultation'})

        yield MockAgent

# Mock DatabaseManager - 增强数据库Mock
@pytest.fixture
def mock_db_manager():
    """Mock数据库管理器避免真实数据库连接"""
    with patch('agents.predict_algorith_agent.database.database_manager.DatabaseManager', autospec=True) as MockDBManager:
        mock_instance = MockDBManager.return_value
        mock_instance.create_conversation = MagicMock(return_value="conv_test_id")
        mock_instance.add_conversation_message = MagicMock(return_value=True)
        mock_instance.get_conversation_detail = MagicMock(return_value={
            "conversation_id": "conv_test_id",
            "user_id": "user123",
            "title": "Test Conversation",
            "conversation_type": "training",
            "status": "active",
            "current_stage": "welcome",
            "progress": 0.0,
            "algorithm_type": None,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "completed_at": None
        })
        mock_instance.get_conversation_messages_with_context = MagicMock(return_value=[])
        mock_instance.save_session_state = MagicMock(return_value=True)
        mock_instance.update_conversation = MagicMock(return_value=True)
        mock_instance.get_connection = MagicMock()

        # Mock数据库连接上下文管理器
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.__enter__.return_value = mock_conn
        mock_conn.__exit__.return_value = None
        mock_instance.get_connection.return_value = mock_conn

        yield MockDBManager

# Mock parameter_recommendation_service - 增强参数推荐服务Mock
@pytest.fixture
def mock_param_rec_service():
    """Mock参数推荐服务避免真实AI模型调用"""
    with patch('agents.predict_algorith_agent.services.parameter_recommendation_service.parameter_recommendation_service', autospec=True) as MockService:
        mock_instance = MockService.return_value
        mock_instance.get_parameter_recommendation = AsyncMock(return_value=ParameterRecommendationResponse(
            algorithm_type="LSTM",
            recommended_parameters=[
                ParameterRecommendation(
                    parameter_name="learning_rate",
                    recommended_value=0.001,
                    confidence=0.9,
                    reasoning="Mock推荐理由",
                    impact_description="Mock影响描述"
                )
            ],
            overall_confidence=0.9,
            llm_provider=LLMProvider.QWEN,
            model_name="qwen-turbo",
            generation_time=1.0
        ))
        mock_instance.get_multi_llm_recommendation = AsyncMock(return_value={
            "recommendations": [],
            "consensus": {},
            "comparison": "Mock多LLM对比结果"
        })
        yield MockService

# Mock history_algorithm_agent - 历史算法代理Mock
@pytest.fixture
def mock_history_agent():
    """Mock历史算法代理避免真实API调用"""
    with patch('agents.predict_algorith_agent.core.history_algorithm_agent.HistoryAlgorithmAgent', autospec=True) as MockAgent:
        mock_instance = MockAgent.return_value
        mock_instance.process_user_request = AsyncMock(return_value={
            "history_algorithms": [
                {"name": "Mock算法1", "type": "LSTM", "accuracy": 0.95},
                {"name": "Mock算法2", "type": "CNN", "accuracy": 0.92}
            ],
            "total_count": 2
        })
        yield MockAgent

# Mock fallback_manager - 兜底响应管理器Mock
@pytest.fixture
def mock_fallback_manager():
    """Mock兜底响应管理器"""
    with patch('agents.predict_algorith_agent.services.fallback_responses.fallback_manager', autospec=True) as MockManager:
        mock_instance = MockManager.return_value
        mock_instance.get_fallback_response = MagicMock(return_value={
            "msg": "这是一个Mock兜底回答，用于测试。",
            "type": "fallback",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        })
        yield MockManager

# Mock pymysql连接 - 数据库连接Mock
@pytest.fixture(autouse=True)
def mock_pymysql():
    """自动Mock pymysql数据库连接"""
    with patch('pymysql.connect') as mock_connect:
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = None
        mock_cursor.fetchall.return_value = []
        mock_cursor.rowcount = 0
        mock_cursor.lastrowid = 1

        mock_conn = MagicMock()
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.__enter__.return_value = mock_conn
        mock_conn.__exit__.return_value = None
        mock_connect.return_value = mock_conn

        yield mock_connect

# Mock logging配置 - 日志配置Mock
@pytest.fixture(autouse=True)
def mock_logging():
    """自动Mock日志配置"""
    with patch('agents.predict_algorith_agent.utils.logging_config.setup_logging'):
        yield

# Mock AlgorithmPlatformService
@pytest.fixture
def mock_algo_platform_service():
    with patch('agents.predict_algorith_agent.services.algorithm_platform_service.AlgorithmPlatformService', autospec=True) as MockService:
        mock_instance = MockService.return_value
        mock_instance.get_all_algorithms = AsyncMock(return_value=MagicMock(algorithm_name=[["algo1", "proj1"]]))
        yield MockService

# Mock logging_config functions
@pytest.fixture(autouse=True)
def mock_logging_config():
    with patch('agents.predict_algorith_agent.utils.logging_config.get_agent_logger', return_value=MagicMock()) as mock_get_logger, \
         patch('agents.predict_algorith_agent.utils.logging_config.should_log_debug', return_value=True) as mock_should_log_debug, \
         patch('agents.predict_algorith_agent.utils.logging_config.should_log_performance', return_value=False) as mock_should_log_performance, \
         patch('agents.predict_algorith_agent.utils.logging_config.log_performance', return_value=MagicMock()) as mock_log_performance:
        yield mock_get_logger, mock_should_log_debug, mock_should_log_performance, mock_log_performance

# ====================================================================================================
# Test Cases for PredictiveAlgorithmAssistant
# ====================================================================================================

@pytest.mark.asyncio
async def test_classify_algorithm_async_success(mock_pydantic_ai_agent, mock_logging_config):
    """测试异步算法分类成功"""
    assistant = PredictiveAlgorithmAssistant()
    
    # 配置mock返回值
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=AlgorithmClassification(algorithm_type=AlgorithmType.LSTM, confidence=0.9, reason="用户明确提到LSTM")
    )
    
    user_input = "我想用LSTM预测股票价格"
    result = await assistant.classify_algorithm_async(user_input)
    
    assert result["output"].algorithm_type == AlgorithmType.LSTM
    assert result["output"].confidence == 0.9
    mock_pydantic_ai_agent.return_value.run.assert_called_once_with(user_input)

@pytest.mark.asyncio
async def test_classify_algorithm_async_failure(mock_pydantic_ai_agent, mock_logging_config):
    """测试异步算法分类失败，返回默认值"""
    assistant = PredictiveAlgorithmAssistant()
    
    # 配置mock抛出异常
    mock_pydantic_ai_agent.return_value.run.side_effect = Exception("LLM API Error")
    
    user_input = "我想用LSTM预测股票价格"
    result = await assistant.classify_algorithm_async(user_input)
    
    assert result["output"].algorithm_type == AlgorithmType.LSTM # 默认值
    assert result["output"].confidence == 0.5 # 默认值
    assert "LLM调用失败" in result["output"].reason
    mock_pydantic_ai_agent.return_value.run.assert_called_once_with(user_input)

@pytest.mark.asyncio
async def test_extract_parameters_async_success(mock_pydantic_ai_agent, mock_logging_config):
    """测试异步参数提取成功"""
    assistant = PredictiveAlgorithmAssistant()
    
    # 配置mock返回值
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveParameterExtraction(
            extracted_params={"learning_rate": 0.001, "batch_size": 32},
            missing_params=["epochs"],
            confidence=0.8,
            notes="提取成功"
        )
    )
    
    user_input = "学习率0.001，批次大小32"
    result = await assistant.extract_parameters_async(user_input)
    
    assert result["output"].extracted_params["learning_rate"] == 0.001
    assert "epochs" in result["output"].missing_params
    mock_pydantic_ai_agent.return_value.run.assert_called_once_with(user_input)

@pytest.mark.asyncio
async def test_extract_parameters_async_failure(mock_pydantic_ai_agent, mock_logging_config):
    """测试异步参数提取失败，返回默认值"""
    assistant = PredictiveAlgorithmAssistant()
    
    # 配置mock抛出异常
    mock_pydantic_ai_agent.return_value.run.side_effect = Exception("LLM API Error")
    
    user_input = "学习率0.001，批次��小32"
    result = await assistant.extract_parameters_async(user_input)
    
    assert result["output"].extracted_params == {}
    assert "learning_rate" in result["output"].missing_params
    assert "LLM调用失败" in result["output"].reasoning
    mock_pydantic_ai_agent.return_value.run.assert_called_once_with(user_input)

@pytest.mark.asyncio
async def test_classify_interaction_async_success(mock_pydantic_ai_agent, mock_logging_config):
    """测试异步交互分类成功"""
    assistant = PredictiveAlgorithmAssistant()
    
    # 配置mock返回值
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.CONFIRM, confidence=0.95, reason="用户明确确认")
    )
    
    user_input = "好的，确认"
    result = await assistant.classify_interaction_async(user_input)
    
    assert result["output"].interaction_type == InteractionType.CONFIRM
    assert result["output"].confidence == 0.95
    mock_pydantic_ai_agent.return_value.run.assert_called_once_with(user_input)

@pytest.mark.asyncio
async def test_classify_interaction_async_failure(mock_pydantic_ai_agent, mock_logging_config):
    """测试异步交互分类失败，返回默认值"""
    assistant = PredictiveAlgorithmAssistant()
    
    # 配置mock抛出异常
    mock_pydantic_ai_agent.return_value.run.side_effect = Exception("LLM API Error")
    
    user_input = "好的，确认"
    result = await assistant.classify_interaction_async(user_input)
    
    assert result["output"].interaction_type == InteractionType.GENERAL # 默认值
    assert result["output"].confidence == 0.5 # 默认值
    assert "LLM调用失败" in result["output"].reason
    mock_pydantic_ai_agent.return_value.run.assert_called_once_with(user_input)

@pytest.mark.asyncio
async def test_process_user_input_async_new_conversation_welcome(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试新对话，用户输入为空，返回欢迎引导"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False)
    
    user_input = ""
    result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
    
    assert "您好！欢迎使用算法生成智能体！" in result["msg"]
    assert result["type"] == "welcome_guide"
    assert new_state["has_shown_welcome"] is True
    assert new_state["conversation_stage"] == "welcome"
    assert action == "welcome"

@pytest.mark.asyncio
async def test_process_user_input_async_new_conversation_preset_1(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试新对话，用户选择预设问题1"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False)
    
    user_input = "1"
    result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
    
    assert "很好！我来为您介绍算法训练的流程" in result["msg"]
    assert result["type"] == "algorithm_guide"
    assert new_state["is_new_conversation"] is False
    assert new_state["conversation_stage"] == "guided"
    assert action == "guided"

@pytest.mark.asyncio
async def test_process_user_input_async_new_conversation_data_prep_question(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试新对话，用户提出数据准备问题"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False)
    
    user_input = "我的数据格式需要怎么准备？"
    
    # Mock is_data_preparation_question
    with patch.object(assistant, 'is_data_preparation_question', return_value=True):
        # Mock call_parameter_recommendation_model
        with patch.object(assistant, 'call_parameter_recommendation_model', new_callable=AsyncMock) as mock_call_param_rec:
            mock_call_param_rec.return_value = "数据准备建议"
            result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
            
            assert "数据准备建议" in result["msg"]
            assert result["type"] == "data_preparation_guide"
            assert action == "data_guide"
            mock_call_param_rec.assert_called_once()

@pytest.mark.asyncio
async def test_process_user_input_async_new_conversation_general_question(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试新对话，用户提出一般性咨询问题"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False)
    
    user_input = "什么是机器学习？"
    
    # Mock is_data_preparation_question
    with patch.object(assistant, 'is_data_preparation_question', return_value=False):
        # Mock is_general_question
        with patch.object(assistant, 'is_general_question', return_value=True):
            # Mock handle_general_question
            with patch.object(assistant, 'handle_general_question', new_callable=AsyncMock) as mock_handle_general:
                mock_handle_general.return_value = {"msg": "机器学习是一种人工智能分支。", "type": "consultation"}, state.dict(), "consultation"
                result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
                
                assert "机器学习是一种人工智能分支。" in result["msg"]
                assert result["type"] == "consultation"
                assert action == "consultation"
                mock_handle_general.assert_called_once()

@pytest.mark.asyncio
async def test_process_user_input_async_new_conversation_history_search(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试新对话，进行历史算法检索"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False, current_project_id="proj1")
    
    user_input = "我需要一个预测模型"
    
    # Mock is_data_preparation_question and is_general_question
    with patch.object(assistant, 'is_data_preparation_question', return_value=False), \
         patch.object(assistant, 'is_general_question', return_value=False):
        
        # Mock handle_history_algorithm_search
        mock_history_agent.return_value.process_user_request.return_value = {
            "history_algorithms": [
                {"algorithm_name": "LSTM预测", "algorithm_type": "LSTM", "use_case": "时序预测", "performance_metrics": {"accuracy": 0.9}, "usage_count": 10, "description": "LSTM模型"}
            ],
            "total_count": 1
        }
        
        result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
        
        assert "历史算法列表" in result["msg"]
        assert result["type"] == "algorithm_list"
        assert new_state["awaiting_algorithm_choice"] is True
        assert action == "algorithm_choice"
        mock_history_agent.return_value.process_user_request.assert_called_once_with(user_input, "proj1")

@pytest.mark.asyncio
async def test_process_user_input_async_awaiting_confirmation_confirm(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试等待参数确认阶段，用户确认"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        awaiting_confirmation=True,
        current_params={"learning_rate": 0.001},
        task_id=None
    )
    
    user_input = "确认"
    
    # Mock classify_interaction_async
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.CONFIRM, confidence=0.99, reason="确认")
    )
    # Mock create_algorithm_task
    with patch.object(assistant, 'create_algorithm_task', return_value={"task_id": "new_task_123", "message": "任务已创建"}):
        result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
        
        assert "任务已创建" in result["msg"]
        assert result["task_id"] == "new_task_123"
        assert new_state["task_id"] == "new_task_123"
        assert new_state["awaiting_confirmation"] is False
        assert action == "created"

@pytest.mark.asyncio
async def test_process_user_input_async_awaiting_confirmation_adjust(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试等待参数确认阶段，用户调整参数"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        awaiting_confirmation=True,
        current_params={"learning_rate": 0.001},
        missing_params=["epochs"],
        task_id=None
    )
    
    user_input = "把学习率改成0.005"
    
    # Mock classify_interaction_async
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.ADJUST, confidence=0.9, reason="调整参数")
    )
    # Mock extract_parameters_async
    with patch.object(assistant, 'extract_parameters_async', new_callable=AsyncMock) as mock_extract_params:
        mock_extract_params.return_value = {
            "output": PredictiveParameterExtraction(
                extracted_params={"learning_rate": 0.005},
                missing_params=["epochs"],
                confidence=0.9,
                notes="提取到新学习率"
            )
        }
        result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
        
        assert "请补充或调整参数" in result["msg"]
        assert new_state["current_params"]["learning_rate"] == 0.005
        assert new_state["awaiting_confirmation"] is True
        assert action == "adjust"

@pytest.mark.asyncio
async def test_process_user_input_async_task_id_query_status(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试任务已创建阶段，用户查询进度"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        task_id="existing_task_id",
        task_status="running"
    )
    
    user_input = "训练进度怎么样了？"
    
    # Mock classify_interaction_async
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.QUERY_STATUS, confidence=0.9, reason="查询进度")
    )
    # Mock query_task_status
    with patch.object(assistant, 'query_task_status', return_value={"status": "running", "progress": 50}):
        result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
        
        assert "任务进度" in result["msg"]
        assert result["status"] == "running"
        assert new_state["task_status"] == "running"
        assert action == "status"

@pytest.mark.asyncio
async def test_process_user_input_async_task_id_general_consultation(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试任务已创建阶段，用户进行一般性咨询"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        task_id="existing_task_id",
        task_status="running"
    )
    
    user_input = "什么是过拟合？"
    
    # Mock classify_interaction_async
    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.GENERAL, confidence=0.9, reason="一般咨询")
    )
    # Mock handle_general_consultation_async
    with patch.object(assistant, 'handle_general_consultation_async', new_callable=AsyncMock) as mock_handle_general:
        mock_handle_general.return_value = {"msg": "过拟合是模型过度学习训练数据。", "type": "consultation"}, state.dict(), "consultation"
        result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
        
        assert "过拟合是模型过度学习训练数据。" in result["msg"]
        assert result["type"] == "consultation"
        assert action == "consultation"
        mock_handle_general.assert_called_once()

@pytest.mark.asyncio
async def test_process_user_input_async_initial_task_description(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试首次任务描述，进行算法分类和参数提取"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=False, history_search_completed=True) # 模拟历史搜索已完成
    
    user_input = "我想用CNN识别图片中的猫狗，学习率0.001"
    
    # Mock classify_interaction_async (return non-general)
    mock_pydantic_ai_agent.return_value.run.side_effect = [
        MagicMock(output=PredictiveInteractionClassification(interaction_type=InteractionType.ALGORITHM_RECOMMENDATION, confidence=0.9, reason="算法推荐")), # For initial check
        MagicMock(output=AlgorithmClassification(algorithm_type=AlgorithmType.CNN, confidence=0.9, reason="用户提到CNN和图片识别")), # For classify_algorithm_async
        MagicMock(output=PredictiveParameterExtraction(
            extracted_params={"learning_rate": 0.001},
            missing_params=["epochs"],
            confidence=0.8,
            notes="提取到学习率"
        )) # For extract_parameters_async
    ]
    
    # Mock is_data_preparation_question and is_general_question
    with patch.object(assistant, 'is_data_preparation_question', return_value=False), \
         patch.object(assistant, 'is_general_question', return_value=False):
        
        # Mock handle_algorithm_recommendation_async to let it pass through
        with patch.object(assistant, 'handle_algorithm_recommendation_async', new_callable=AsyncMock) as mock_handle_algo_rec:
            mock_handle_algo_rec.return_value = {"msg": "处理算法推荐", "type": "algo_rec"}, state.dict(), "algo_rec"
            
            result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
            
            # The first interaction classification should lead to handle_algorithm_recommendation_async
            # If it's not a general question, it should proceed to algorithm classification and parameter extraction
            # This test case needs to be carefully designed to reflect the flow.
            # Let's adjust the mock for classify_interaction_async to return a non-general type that doesn't trigger a specific handler
            # and then let the flow proceed to algorithm classification and parameter extraction.
            
            # Re-mock classify_interaction_async for this specific flow
            mock_pydantic_ai_agent.return_value.run.side_effect = [
                MagicMock(output=PredictiveInteractionClassification(interaction_type=InteractionType.UNKNOWN, confidence=0.6, reason="未知交互")), # For initial check
                MagicMock(output=AlgorithmClassification(algorithm_type=AlgorithmType.CNN, confidence=0.9, reason="用户提到CNN和图片识别")), # For classify_algorithm_async
                MagicMock(output=PredictiveParameterExtraction(
                    extracted_params={"learning_rate": 0.001},
                    missing_params=["epochs"],
                    confidence=0.8,
                    notes="提取到���习率"
                )) # For extract_parameters_async
            ]
            
            result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
            
            assert "请补充参数" in result["msg"]
            assert new_state["algorithm_type"] == AlgorithmType.CNN
            assert new_state["current_params"]["learning_rate"] == 0.001
            assert "epochs" in new_state["missing_params"]
            assert new_state["awaiting_confirmation"] is True
            assert action == "need_params"

@pytest.mark.asyncio
async def test_process_user_input_async_fallback_on_llm_error(mock_db_manager, mock_pydantic_ai_agent, mock_fallback_manager, mock_logging_config):
    """测试LLM调用失败时触发兜底回答"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    
    user_input = "这是一个会导致LLM错误的输入"
    
    # Mock classify_algorithm_async to raise an error
    with patch.object(assistant, 'classify_algorithm_async', side_effect=ValueError("Received empty model response")):
        result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
        
        assert "这是一个兜底回答。" in result["msg"]
        assert result["type"] == "fallback"
        assert action == "fallback_response"
        mock_fallback_manager.return_value.get_fallback_response.assert_called_once()

@pytest.mark.asyncio
async def test_generate_report_with_llm_analysis(mock_pydantic_ai_agent, mock_logging_config):
    """测试生成报告，调用LLM进行智能分析"""
    assistant = PredictiveAlgorithmAssistant()
    task_id = "report_task_123"
    params = {"algorithm_type": "LSTM", "learning_rate": 0.001}
    
    # Mock call_llm_for_report
    with patch.object(assistant, 'call_llm_for_report', return_value={
        "analysis_result": "模型性能良好。",
        "suggestions": "建议调整学习率。"
    }) as mock_call_llm_for_report:
        # Mock pdfkit.from_string
        with patch('pdfkit.from_string', MagicMock()), \
             patch('os.makedirs', MagicMock()):
            
            html_report = assistant.generate_report(task_id, params)
            
            assert "模型性能良好。" in html_report
            assert "建议调整学习率。" in html_report
            mock_call_llm_for_report.assert_called_once_with(task_id, params)

@pytest.mark.asyncio
async def test_get_report_pdf_success(mock_logging_config):
    """测试生成PDF报告成功"""
    assistant = PredictiveAlgorithmAssistant()
    task_id = "pdf_task_123"
    params = {"algorithm_type": "LSTM", "learning_rate": 0.001}
    
    # Mock generate_report
    with patch.object(assistant, 'generate_report', return_value="<html>Mock Report</html>") as mock_generate_report:
        # Mock pdfkit.from_string
        with patch('pdfkit.from_string', MagicMock()) as mock_pdfkit_from_string, \
             patch('os.makedirs', MagicMock()):
            
            pdf_path = assistant.get_report_pdf(task_id, params)
            
            assert pdf_path.endswith(f"report_{task_id}.pdf")
            mock_generate_report.assert_called_once_with(task_id, params, None, None)
            mock_pdfkit_from_string.assert_called_once()

@pytest.mark.asyncio
async def test_get_parameter_recommendation_success(mock_param_rec_service, mock_logging_config):
    """测试获取参数推荐成功"""
    assistant = PredictiveAlgorithmAssistant()
    
    algorithm_type = "LSTM"
    result = await assistant.get_parameter_recommendation(algorithm_type)
    
    assert result["success"] is True
    assert result["recommendation"].algorithm_type == "LSTM"
    mock_param_rec_service.get_parameter_recommendation.assert_called_once()

@pytest.mark.asyncio
async def test_get_multi_llm_parameter_recommendation_success(mock_param_rec_service, mock_logging_config):
    """测试获取多LLM参数推荐成功"""
    assistant = PredictiveAlgorithmAssistant()
    
    algorithm_type = "LSTM"
    result = await assistant.get_multi_llm_parameter_recommendation(algorithm_type)
    
    assert result["success"] is True
    mock_param_rec_service.get_multi_llm_recommendation.assert_called_once()

@pytest.mark.asyncio
async def test_handle_general_consultation_async_success(mock_pydantic_ai_agent, mock_logging_config):
    """测试处理一般性咨询成功"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    user_input = "什么是机器学习？"

    # Mock the internal LLM call within handle_general_consultation_async
    with patch('agents.predict_algorith_agent.core.predictive_agent.Agent', autospec=True) as MockConsultationAgent:
        mock_consultation_instance = MockConsultationAgent.return_value
        mock_consultation_instance.run_sync.return_value = MagicMock(
            data=MagicMock(answer="机器学习是一种人工智能分支。")
        )
        
        result, new_state, action = await assistant.handle_general_consultation_async(user_input, state)
        
        assert "机器学习是一种人工智能分支。" in result["msg"]
        assert result["type"] == "consultation"
        assert action == "consultation"
        mock_consultation_instance.run_sync.assert_called_once()

@pytest.mark.asyncio
async def test_handle_general_question_not_algorithm_related(mock_logging_config):
    """测试处理非算法相关的一般性问题，返回范围指导"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    user_input = "北京今天天气怎么样？"

    with patch.object(assistant, 'is_algorithm_related_question', return_value=False):
        result, new_state, action = await assistant.handle_general_question(user_input, state)
        assert "算法智能体专业服务范围" in result["msg"]
        assert result["type"] == "scope_guidance"
        assert action == "scope_guidance"

@pytest.mark.asyncio
async def test_handle_general_question_algorithm_related_success(mock_pydantic_ai_agent, mock_logging_config):
    """测试处理算法相关的一般性问题，成功调用咨询"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    user_input = "如何选择合适的学习率？"

    with patch.object(assistant, 'is_algorithm_related_question', return_value=True):
        with patch.object(assistant, 'handle_general_consultation_async', new_callable=AsyncMock) as mock_handle_consultation:
            mock_handle_consultation.return_value = {"msg": "选择学习率的建议。", "type": "consultation"}, state.dict(), "consultation"
            result, new_state, action = await assistant.handle_general_question(user_input, state)
            assert "选择学习率的建议。" in result["msg"]
            assert result["type"] == "consultation"
            assert action == "consultation"
            mock_handle_consultation.assert_called_once()

@pytest.mark.asyncio
async def test_handle_history_algorithm_search_no_project_id(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试历史算法检索，缺少项目ID"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False, current_project_id=None)
    user_input = "查找历史算法"

    result, new_state, action = await assistant.handle_history_algorithm_search(user_input, state)
    assert "项目ID缺失" in result["msg"]
    assert result["type"] == "project_id_missing"
    assert action == "project_id_missing"
    mock_history_agent.return_value.process_user_request.assert_not_called()

@pytest.mark.asyncio
async def test_handle_history_algorithm_search_api_error(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试历史算法检索，API调用失败"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False, current_project_id="proj1")
    user_input = "查找历史算法"

    mock_history_agent.return_value.process_user_request.return_value = {
        "error": "无法连接到算法平台",
        "history_algorithms": []
    }

    result, new_state, action = await assistant.handle_history_algorithm_search(user_input, state)
    assert "算法平台连接失败" in result["msg"]
    assert result["type"] == "api_error"
    assert new_state["history_search_completed"] is True
    assert action == "api_error"
    mock_history_agent.return_value.process_user_request.assert_called_once_with(user_input, "proj1")

@pytest.mark.asyncio
async def test_handle_history_algorithm_search_found_algorithms(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试历史算法检索，找到算法并返回列表"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False, current_project_id="proj1")
    user_input = "查找历史算法"

    mock_history_agent.return_value.process_user_request.return_value = {
        "history_algorithms": [
            {"algorithm_name": "LSTM预测", "algorithm_type": "LSTM", "use_case": "时序预测", "performance_metrics": {"accuracy": 0.9}, "usage_count": 10, "description": "LSTM模型"}
        ],
        "total_count": 1
    }

    result, new_state, action = await assistant.handle_history_algorithm_search(user_input, state)
    assert "历史算法列表" in result["msg"]
    assert result["type"] == "algorithm_list"
    assert new_state["awaiting_algorithm_choice"] is True
    assert new_state["history_search_completed"] is True
    assert action == "algorithm_choice"
    assert len(new_state["recommended_algorithms"]) == 1
    mock_history_agent.return_value.process_user_request.assert_called_once_with(user_input, "proj1")

@pytest.mark.asyncio
async def test_handle_history_algorithm_search_no_algorithms_found(mock_db_manager, mock_pydantic_ai_agent, mock_history_agent, mock_logging_config):
    """测试历史算法检索，未找到算法"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(is_new_conversation=True, has_shown_welcome=False, current_project_id="proj1")
    user_input = "查找历史算法"

    mock_history_agent.return_value.process_user_request.return_value = {
        "history_algorithms": [],
        "total_count": 0
    }

    result, new_state, action = await assistant.process_user_input_async(user_input, state.dict())
    assert "暂无可用的历史算法" in result["msg"]
    assert result["type"] == "create_new_algorithm"
    assert new_state["history_search_completed"] is True
    assert action == "create_new"
    assert new_state["conversation_stage"] == "task_processing"
    mock_history_agent.return_value.process_user_request.assert_called_once_with(user_input, "proj1")

@pytest.mark.asyncio
async def test_handle_data_preparation_question_param_rec_stage(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理数据准备问题，处于参数推荐阶段"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(awaiting_confirmation=True, missing_params=["param1"])
    user_input = "数据需要怎么处理？"

    with patch.object(assistant, 'determine_current_stage', return_value="parameter_recommendation"), \
         patch.object(assistant, 'call_parameter_recommendation_model', new_callable=AsyncMock, return_value="参数推荐模型回复"):
        result, new_state, action = await assistant.handle_data_preparation_question(user_input, state)
        assert "参数推荐模型回复" in result["msg"]
        assert result["type"] == "data_preparation_guide"
        assert action == "data_guide"
        assistant.call_parameter_recommendation_model.assert_called_once_with(user_input, state)

@pytest.mark.asyncio
async def test_handle_data_preparation_question_training_stage(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理数据准备问题，处于训练阶段"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(task_id="task1", task_status="running")
    user_input = "训练数据有什么要求？"

    with patch.object(assistant, 'determine_current_stage', return_value="training"), \
         patch.object(assistant, 'call_general_model', new_callable=AsyncMock, return_value="通用大模型回复"):
        result, new_state, action = await assistant.handle_data_preparation_question(user_input, state)
        assert "通用大模型回复" in result["msg"]
        assert result["type"] == "data_preparation_guide"
        assert action == "data_guide"
        assistant.call_general_model.assert_called_once_with(user_input, state)

@pytest.mark.asyncio
async def test_determine_current_stage_awaiting_confirmation(mock_logging_config):
    """测试判断当前阶段 - 等待确认"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(awaiting_confirmation=True, missing_params=["param1"])
    stage = assistant.determine_current_stage(state)
    assert stage == "parameter_recommendation"

@pytest.mark.asyncio
async def test_determine_current_stage_training(mock_logging_config):
    """测试判断当前阶段 - 训练中"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(task_status="running")
    stage = assistant.determine_current_stage(state)
    assert stage == "training"

@pytest.mark.asyncio
async def test_determine_current_stage_result_analysis(mock_logging_config):
    """测试判断当前阶段 - 结果分析"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(task_status="completed", report="some report content")
    stage = assistant.determine_current_stage(state)
    assert stage == "result_analysis"

@pytest.mark.asyncio
async def test_call_parameter_recommendation_model(mock_pydantic_ai_agent, mock_logging_config):
    """测试调用参数推荐微调模型"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(algorithm_type=AlgorithmType.LSTM)
    user_input = "推荐LSTM参数"

    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveParameterExtraction(
            extracted_params={"learning_rate": 0.001},
            missing_params=[],
            confidence=0.9,
            notes="推荐成功"
        )
    )
    
    response = await assistant.call_parameter_recommendation_model(user_input, state)
    assert "专业参数推荐" in response
    assert "learning_rate: 0.001" in response
    mock_pydantic_ai_agent.return_value.run.assert_called_once()

@pytest.mark.asyncio
async def test_call_general_model(mock_pydantic_ai_agent, mock_logging_config):
    """测试调用通用大模型"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(task_status="running")
    user_input = "训练过程如何监控？"

    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.GENERAL, confidence=0.9, reason="通用咨询")
    )
    
    response = await assistant.call_general_model(user_input, state)
    assert "智能训练指导" in response
    assert "训练监控要点" in response
    mock_pydantic_ai_agent.return_value.run.assert_called_once()

@pytest.mark.asyncio
async def test_call_result_analysis_model(mock_pydantic_ai_agent, mock_logging_config):
    """测试调用结果分析微调模型"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(task_id="task1", task_status="completed", last_api_result={"result": {"accuracy": 0.95}})
    user_input = "分析一下训练结果"

    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=AlgorithmClassification(algorithm_type=AlgorithmType.LSTM, confidence=0.9, reason="结果分析")
    )
    
    response = await assistant.call_result_analysis_model(user_input, state)
    assert "专业结果分析" in response
    assert "性能指标分析" in response
    assert "当前模型表现：0.95" in response
    mock_pydantic_ai_agent.return_value.run.assert_called_once()

@pytest.mark.asyncio
async def test_handle_training_completed_success(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理训练完成，进入确认流程"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(task_id="task_completed_123", task_status="completed", awaiting_training_confirmation=False)

    # Mock the internal LLM call for generating the confirmation message
    with patch('agents.predict_algorith_agent.core.predictive_agent.Agent', autospec=True) as MockConfirmationAgent:
        mock_confirmation_instance = MockConfirmationAgent.return_value
        mock_confirmation_instance.run.return_value = MagicMock(
            output=PredictiveConfirmationRequest(
                current_params={"param": "value"},
                formatted_params="param: value",
                confirmation_message="训练已完成，请确认结果。"
            )
        )
        
        result, new_state, action = await assistant.handle_training_completed(state)
        
        assert "训练已完成，请确认结果。" in result["msg"]
        assert new_state["awaiting_training_confirmation"] is True
        assert action == "awaiting_training_confirmation"
        mock_confirmation_instance.run.assert_called_once()

@pytest.mark.asyncio
async def test_handle_training_confirmation_confirm(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理训练结果确认，用户确认"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        task_id="task_completed_123",
        task_status="completed",
        awaiting_training_confirmation=True,
        training_result={"accuracy": 0.95}
    )
    user_input = "确认"

    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.CONFIRM, confidence=0.99, reason="确认")
    )

    result, new_state, action = await assistant.handle_training_confirmation(user_input, state)
    assert "感谢您的确认" in result["msg"]
    assert new_state["awaiting_training_confirmation"] is False
    assert action == "training_confirmed"

@pytest.mark.asyncio
async def test_handle_training_confirmation_adjust(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理训练结果确认，用户要求调整"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        task_id="task_completed_123",
        task_status="completed",
        awaiting_training_confirmation=True,
        training_result={"accuracy": 0.95}
    )
    user_input = "结果不太好，需要调整参数"

    mock_pydantic_ai_agent.return_value.run.return_value = MagicMock(
        output=PredictiveInteractionClassification(interaction_type=InteractionType.ADJUST, confidence=0.9, reason="调整")
    )
    
    # Mock handle_parameter_configuration_async
    with patch.object(assistant, 'handle_parameter_configuration_async', new_callable=AsyncMock) as mock_handle_param_config:
        mock_handle_param_config.return_value = {"msg": "进入参数配置流程", "type": "param_config"}, state.dict(), "param_config"
        result, new_state, action = await assistant.handle_training_confirmation(user_input, state)
        assert "进入参数配置流程" in result["msg"]
        assert action == "param_config"
        mock_handle_param_config.assert_called_once()

@pytest.mark.asyncio
async def test_handle_algorithm_choice_select_existing(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理算法选择，用户选择现有算法"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        awaiting_algorithm_choice=True,
        recommended_algorithms=[
            {"algorithm_id": "algo_001", "algorithm_name": "LSTM预测", "parameters": {"lr": 0.01}}
        ]
    )
    user_input = "1" # 选择第一个算法

    result, new_state, action = await assistant.handle_algorithm_choice(user_input, state)
    assert "您已选择算法：LSTM预测" in result["msg"]
    assert new_state["selected_algorithm_id"] == "algo_001"
    assert new_state["awaiting_algorithm_choice"] is False
    assert new_state["awaiting_confirmation"] is True
    assert new_state["current_params"]["lr"] == 0.01
    assert action == "algorithm_selected"

@pytest.mark.asyncio
async def test_handle_algorithm_choice_create_new(mock_db_manager, mock_pydantic_ai_agent, mock_logging_config):
    """测试处理算法选择，用户选择创建新算法"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(
        awaiting_algorithm_choice=True,
        recommended_algorithms=[
            {"algorithm_id": "algo_001", "algorithm_name": "LSTM预测", "parameters": {"lr": 0.01}}
        ]
    )
    user_input = "新建"

    result, new_state, action = await assistant.handle_algorithm_choice(user_input, state)
    assert "好的，让我们开始创建新的算法。" in result["msg"]
    assert new_state["awaiting_algorithm_choice"] is False
    assert new_state["conversation_stage"] == "task_processing"
    assert action == "create_new_algorithm_flow"

@pytest.mark.asyncio
async def test_handle_algorithm_recommendation_async_success(mock_param_rec_service, mock_logging_config):
    """测试处理算法推荐请求成功"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    user_input = "推荐一个时序预测算法"

    mock_param_rec_service.get_parameter_recommendation.return_value = ParameterRecommendationResponse(
        algorithm_type="LSTM",
        recommended_parameters=[
            ParameterRecommendation(parameter_name="learning_rate", recommended_value=0.001, confidence=0.9, reasoning="test", impact_description="test")
        ],
        overall_confidence=0.9,
        llm_provider=LLMProvider.QWEN,
        model_name="qwen-turbo",
        generation_time=1.0
    )

    result, new_state, action = await assistant.handle_algorithm_recommendation_async(user_input, state)
    assert "为您推荐以下算法和参数" in result["msg"]
    assert new_state["algorithm_type"] == AlgorithmType.LSTM
    assert new_state["awaiting_confirmation"] is True
    assert action == "algorithm_recommendation_response"
    mock_param_rec_service.get_parameter_recommendation.assert_called_once()

@pytest.mark.asyncio
async def test_handle_parameter_configuration_async_success(mock_param_rec_service, mock_logging_config):
    """测试处理参数配置请求成功"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState(algorithm_type=AlgorithmType.LSTM)
    user_input = "配置LSTM参数"

    mock_param_rec_service.get_parameter_recommendation.return_value = ParameterRecommendationResponse(
        algorithm_type="LSTM",
        recommended_parameters=[
            ParameterRecommendation(parameter_name="learning_rate", recommended_value=0.001, confidence=0.9, reasoning="test", impact_description="test")
        ],
        overall_confidence=0.9,
        llm_provider=LLMProvider.QWEN,
        model_name="qwen-turbo",
        generation_time=1.0
    )

    result, new_state, action = await assistant.handle_parameter_configuration_async(user_input, state)
    assert "为您推荐以下参数配置" in result["msg"]
    assert new_state["current_params"]["learning_rate"] == 0.001
    assert new_state["awaiting_confirmation"] is True
    assert action == "parameter_configuration_response"
    mock_param_rec_service.get_parameter_recommendation.assert_called_once()

@pytest.mark.asyncio
async def test_handle_technical_question_async_success(mock_pydantic_ai_agent, mock_logging_config):
    """测试处理技术问题成功"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    user_input = "什么是过拟合？"

    with patch('agents.predict_algorith_agent.core.predictive_agent.Agent', autospec=True) as MockConsultationAgent:
        mock_consultation_instance = MockConsultationAgent.return_value
        mock_consultation_instance.run.return_value = MagicMock(
            output=MagicMock(answer="过拟合是模型过度学习训练数据。")
        )
        
        result, new_state, action = await assistant.handle_technical_question_async(user_input, state)
        
        assert "过拟合是模型过度学习训练数据。" in result["msg"]
        assert result["type"] == "consultation"
        assert action == "technical_question_response"
        mock_consultation_instance.run.assert_called_once()

@pytest.mark.asyncio
async def test_handle_general_consultation_async_fallback(mock_pydantic_ai_agent, mock_logging_config):
    """测试处理一般性咨询失败，触发兜底回答"""
    assistant = PredictiveAlgorithmAssistant()
    state = PredictiveAgentState()
    user_input = "一个复杂的问题"

    with patch('agents.predict_algorith_agent.core.predictive_agent.Agent', autospec=True) as MockConsultationAgent:
        mock_consultation_instance = MockConsultationAgent.return_value
        mock_consultation_instance.run.side_effect = Exception("LLM Error")
        
        result, new_state, action = await assistant.handle_general_consultation_async(user_input, state)
        
        assert "我在调用专业模型时遇到了一些困难" in result["msg"]
        assert result["type"] == "consultation"
        assert action == "consultation"
        mock_consultation_instance.run.assert_called_once()

@pytest.mark.asyncio
async def test_is_data_preparation_question_true(mock_logging_config):
    """测试判断是否是数据准备问题 - True"""
    assistant = PredictiveAlgorithmAssistant()
    assert assistant.is_data_preparation_question("我的数据格式需要怎么准备？") is True
    assert assistant.is_data_preparation_question("如何进行数据预处理？") is True

@pytest.mark.asyncio
async def test_is_data_preparation_question_false(mock_logging_config):
    """测试判断是否是数据准备问题 - False"""
    assistant = PredictiveAlgorithmAssistant()
    assert assistant.is_data_preparation_question("我想用LSTM预测股票。") is False
    assert assistant.is_data_preparation_question("什么是机器学习？") is False

@pytest.mark.asyncio
async def test_is_general_question_true(mock_logging_config):
    """测试判断是否是一般性问题 - True"""
    assistant = PredictiveAlgorithmAssistant()
    assert assistant.is_general_question("什么是深度学习？") is True
    assert assistant.is_general_question("如何选择学习率？") is True
    assert assistant.is_general_question("你能提供哪些帮助？") is True

@pytest.mark.asyncio
async def test_is_general_question_false(mock_logging_config):
    """测试判断是否是一般性问题 - False"""
    assistant = PredictiveAlgorithmAssistant()
    assert assistant.is_general_question("我想预测销售额。") is False
    assert assistant.is_general_question("生成报告。") is False

@pytest.mark.asyncio
async def test_is_algorithm_related_question_true(mock_logging_config):
    """测试判断是否是算法相关问题 - True"""
    assistant = PredictiveAlgorithmAssistant()
    assert assistant.is_algorithm_related_question("LSTM和CNN有什么区别？") is True
    assert assistant.is_algorithm_related_question("如何优化模型参数？") is True

@pytest.mark.asyncio
async def test_is_algorithm_related_question_false(mock_logging_config):
    """测试判断是否是算法相关问题 - False"""
    assistant = PredictiveAlgorithmAssistant()
    assert assistant.is_algorithm_related_question("今天天气怎么样？") is False
    assert assistant.is_algorithm_related_question("帮我订餐。") is False
