import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime
import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..'))
sys.path.insert(0, project_root)

# 导入测试配置
from agents.predict_algorith_agent.tests.test_config import get_test_config, SAMPLE_CONVERSATION, SAMPLE_MESSAGE

# 导入待测试的模块
from agents.predict_algorith_agent.database.database_manager import DatabaseManager, DateTimeEncoder

@pytest.fixture(autouse=True)
def mock_agents_config_db():
    """Mock agents.config配置"""
    test_config = get_test_config()
    with patch.dict('agents.config.__dict__', test_config):
        yield

@pytest.fixture(autouse=True)
def mock_logging_config():
    """Mock日志配置"""
    with patch('agents.predict_algorith_agent.utils.logging_config.setup_logging'):
        yield

# Mock pymysql.connect - 增强Mock数据库连接
@pytest.fixture
def mock_pymysql_connect():
    """Mock数据库连接 - 完全模拟数据库操作"""
    mock_cursor = MagicMock()
    mock_conn = MagicMock()

    # 配置cursor的默认返回值
    mock_cursor.fetchone.return_value = None
    mock_cursor.fetchall.return_value = []
    mock_cursor.rowcount = 0
    mock_cursor.lastrowid = 1
    mock_cursor.execute.return_value = None
    mock_cursor.close.return_value = None

    # 配置connection
    mock_conn.cursor.return_value = mock_cursor
    mock_conn.commit.return_value = None
    mock_conn.rollback.return_value = None
    mock_conn.close.return_value = None
    mock_conn.__enter__.return_value = mock_conn
    mock_conn.__exit__.return_value = None

    with patch('pymysql.connect', return_value=mock_conn) as mock_connect:
        yield mock_connect, mock_conn, mock_cursor

# ====================================================================================================
# Test Cases for DatabaseManager
# ====================================================================================================

def test_datetime_encoder():
    """测试DateTimeEncoder是否正确处理datetime对象"""
    dt_obj = datetime(2023, 1, 1, 12, 30, 0)
    encoded = json.dumps({"time": dt_obj}, cls=DateTimeEncoder)
    assert encoded == '{"time": "2023-01-01T12:30:00"}'

def test_generate_id():
    """测试ID生成器"""
    db_manager = DatabaseManager()
    _id = db_manager.generate_id()
    assert isinstance(_id, str)
    assert len(_id) == 16 # uuid.hex[:16]

    _id_with_prefix = db_manager.generate_id("test_")
    assert _id_with_prefix.startswith("test_")
    assert len(_id_with_prefix) == 21 # prefix + uuid.hex[:16]

def test_generate_session_id():
    """测试Session ID生成器"""
    db_manager = DatabaseManager()
    session_id = db_manager.generate_session_id()
    assert isinstance(session_id, str)
    assert len(session_id) == 32 # full uuid.hex

def test_create_conversation_success(mock_pymysql_connect):
    """测试创建对话成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    user_id = "user123"
    session_id = "session456"
    title = "Test Conversation"
    conversation_type = "training"
    
    conv_id = db_manager.create_conversation(user_id, session_id, title, conversation_type)
    
    mock_conn.cursor.assert_called_once()
    mock_cursor.execute.assert_called_once()
    args, kwargs = mock_cursor.execute.call_args
    assert "INSERT INTO conversations" in args[0]
    assert args[1][1] == user_id
    assert args[1][2] == session_id
    assert args[1][3] == title
    assert args[1][4] == conversation_type
    assert conv_id.startswith("conv_")

def test_get_conversations_by_user_success(mock_pymysql_connect):
    """测试获取用户对话列表成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    mock_cursor.fetchone.side_effect = [{"total": 1}, None] # For count query
    mock_cursor.fetchall.return_value = [
        {
            "conversation_id": "conv_1",
            "title": "Test 1",
            "conversation_type": "training",
            "status": "active",
            "current_stage": "welcome",
            "progress": 0.0,
            "algorithm_type": None,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
    ]
    
    conversations, total = db_manager.get_conversations_by_user("user123", page=1, size=10)
    
    assert total == 1
    assert len(conversations) == 1
    assert conversations[0]["title"] == "Test 1"
    assert mock_cursor.execute.call_count == 2 # count and list query

def test_get_conversation_detail_success(mock_pymysql_connect):
    """测试获取对话详情成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    mock_cursor.fetchone.return_value = {
        "conversation_id": "conv_1",
        "title": "Test Detail",
        "user_id": "user123",
        "conversation_type": "training",
        "status": "active",
        "current_stage": "welcome",
        "progress": 0.0,
        "algorithm_type": "LSTM",
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "completed_at": None,
        "task_summary": None
    }
    
    detail = db_manager.get_conversation_detail("conv_1")
    
    assert detail["title"] == "Test Detail"
    assert detail["algorithm_type"] == "LSTM"
    mock_cursor.execute.assert_called_once()

def test_update_conversation_success(mock_pymysql_connect):
    """测试更新对话成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    success = db_manager.update_conversation("conv_1", status="completed", progress=100.0)
    
    assert success is True
    mock_cursor.execute.assert_called_once()
    args, kwargs = mock_cursor.execute.call_args
    assert "UPDATE conversations SET status = %s, progress = %s" in args[0]
    assert args[1][0] == "completed"
    assert args[1][1] == 100.0

def test_delete_conversation_success(mock_pymysql_connect):
    """测试删除对话成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    success = db_manager.delete_conversation("conv_1")
    
    assert success is True
    mock_cursor.execute.assert_called_once()
    args, kwargs = mock_cursor.execute.call_args
    assert "DELETE FROM conversations WHERE conversation_id = %s" in args[0]
    assert args[1][0] == "conv_1"

def test_add_conversation_message_success(mock_pymysql_connect):
    """测试添加对话消息成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    # Mock get_next_message_sequence
    with patch.object(db_manager, 'get_next_message_sequence', return_value=1):
        # Mock update_conversation_last_activity
        with patch.object(db_manager, 'update_conversation_last_activity', return_value=True):
            success = db_manager.add_conversation_message(
                conversation_id="conv_1",
                message_type="user",
                content="Hello",
                message_data={"key": "value"},
                interaction_type="chat",
                user_id="user123"
            )
            
            assert success is True
            mock_cursor.execute.assert_called_once()
            args, kwargs = mock_cursor.execute.call_args
            assert "INSERT INTO conversation_messages" in args[0]
            assert args[1][2] == "Hello"
            assert json.loads(args[1][3]) == {"key": "value"}
            db_manager.get_next_message_sequence.assert_called_once_with("conv_1")
            db_manager.update_conversation_last_activity.assert_called_once_with("conv_1")

def test_get_conversation_messages_success(mock_pymysql_connect):
    """测试获取对话消息列表成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    mock_cursor.fetchall.return_value = [
        {
            "id": 1,
            "conversation_id": "conv_1",
            "message_type": "user",
            "content": "Hello",
            "message_data": '{"key": "value"}',
            "interaction_type": "chat",
            "message_sequence": 1,
            "key_info": None,
            "token_count": 10,
            "timestamp": datetime.now()
        }
    ]
    
    messages = db_manager.get_conversation_messages("conv_1")
    
    assert len(messages) == 1
    assert messages[0]["content"] == "Hello"
    assert messages[0]["message_data"] == {"key": "value"}
    mock_cursor.execute.assert_called_once()

def test_save_session_state_success(mock_pymysql_connect):
    """测试保存会话状态成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    user_id = "user123"
    session_id = "session456"
    conversation_id = "conv_1"
    task_id = "task_1"
    state_data = {"param": "value"}
    
    success = db_manager.save_session_state(user_id, session_id, conversation_id, task_id, state_data)
    
    assert success is True
    mock_cursor.execute.assert_called_once()
    mock_conn.commit.assert_called_once()
    args, kwargs = mock_cursor.execute.call_args
    assert "INSERT INTO session_states" in args[0]
    assert args[1][0] == user_id
    assert json.loads(args[1][4]) == state_data

def test_load_session_state_success(mock_pymysql_connect):
    """测试加载会话状态成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()
    
    mock_cursor.fetchone.return_value = {"task_id": "task_1", "state_data": '{"param": "value"}'}
    
    task_id, state_data = db_manager.load_session_state("user123", "session456")
    
    assert task_id == "task_1"
    assert state_data == {"param": "value"}
    mock_cursor.execute.assert_called_once()

def test_get_user_conversations_success(mock_pymysql_connect):
    """测试获取用户历史对话列表成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()

    mock_cursor.fetchall.return_value = [
        {
            "conversation_id": "conv_1",
            "title": "Test Conv",
            "status": "active",
            "current_stage": "welcome",
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "last_message_at": datetime.now(),
            "algorithm_type": "LSTM",
            "progress": 50.0,
            "message_count": 5,
            "last_user_message": "Hello"
        }
    ]

    conversations = db_manager.get_user_conversations("user123")
    assert len(conversations) == 1
    assert conversations[0]["title"] == "Test Conv"
    mock_cursor.execute.assert_called_once()

def test_create_context_summary_success(mock_pymysql_connect):
    """测试创建上下文摘要成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()

    mock_cursor.lastrowid = 1 # Simulate a new summary ID

    summary_id = db_manager.create_context_summary(
        conversation_id="conv_1",
        summary_content="Summary content",
        key_points={"key": "value"},
        message_count=5
    )
    assert summary_id == "1"
    mock_cursor.execute.assert_called_once()
    args, kwargs = mock_cursor.execute.call_args
    assert "INSERT INTO context_summaries" in args[0]
    assert args[1][1] == "Summary content"
    assert json.loads(args[1][2]) == {"key": "value"}

def test_get_latest_context_summary_success(mock_pymysql_connect):
    """测试获取最新上下文摘要成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()

    mock_cursor.fetchone.return_value = {
        "id": 1,
        "conversation_id": "conv_1",
        "summary_content": "Latest summary",
        "key_points": '{"key": "value"}',
        "message_count": 10,
        "token_count": 20,
        "summary_time": datetime.now()
    }

    summary = db_manager.get_latest_context_summary("conv_1")
    assert summary["summary_content"] == "Latest summary"
    assert summary["key_points"] == {"key": "value"}
    mock_cursor.execute.assert_called_once()

def test_update_conversation_context_tokens_success(mock_pymysql_connect):
    """测试更新对话上下文token数量成功"""
    _, mock_conn, mock_cursor = mock_pymysql_connect
    db_manager = DatabaseManager()

    db_manager.update_conversation_context_tokens("conv_1", 100)
    mock_cursor.execute.assert_called_once()
    args, kwargs = mock_cursor.execute.call_args
    assert "UPDATE conversations SET context_token_count = %s" in args[0]
    assert args[1][0] == 100
