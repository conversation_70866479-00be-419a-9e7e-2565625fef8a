from pydantic_ai import Agent as PydanticAIAgent, Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from typing import Dict, Any, Optional, Tuple
from pydantic import BaseModel, Field
from agents.predict_algorith_agent.models.predictive_models import (
    PredictiveTaskType, PredictiveTaskClassification, AlgorithmClassification,
    PredictiveParameterExtraction, PredictiveInteractionClassification,
    PredictiveConfirmationRequest, PredictiveAgentState, InteractionType,
    AlgorithmParams, AlgorithmType
)
from agents.predict_algorith_agent.services.parameter_recommendation_service import parameter_recommendation_service
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    DataScale, PerformanceObjective, LLMProvider
)
from agents.config import SHOW_DEEPSEEK_THOUGHT, ALGORITHM_PLATFORM_BASE_URL, WKHTMLTOPDF_PATH, REPORT_PDF_DIR, get_current_llm_config
import pprint
import logging
from fastapi import APIRouter
import sys
import os

# 动态添加项目根目录到Python路径（兼容不同部署环境）
def setup_project_path():
    """设置项目路径，兼容不同的部署环境"""
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 尝试多种可能的项目根目录位置
    possible_roots = [
        os.path.abspath(os.path.join(current_dir, '../../..')),  # 开发环境
        os.path.abspath(os.path.join(current_dir, '../..')),     # 可能的部署环境1
        os.path.abspath(os.path.join(current_dir, '..')),        # 可能的部署环境2
        os.getcwd(),  # 当前工作目录
    ]

    for root in possible_roots:
        if os.path.exists(os.path.join(root, 'core', 'response.py')):
            if root not in sys.path:
                sys.path.insert(0, root)
            return root

    # 如果都找不到，使用环境变量
    if 'PROJECT_ROOT' in os.environ:
        project_root = os.environ['PROJECT_ROOT']
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        return project_root

    return None

# 设置项目路径
project_root = setup_project_path()

try:
    from core.response import Response
except ImportError as e:
    print(f"Warning: Could not import core.response: {e}")
    # 创建一个兼容的Response类作为备用
    from typing import TypeVar, Generic, Optional, Any
    from pydantic import BaseModel

    T = TypeVar('T')

    class Response(BaseModel, Generic[T]):
        data: Optional[Any] = None
        message: str = ""
        success: bool = True

        class Config:
            arbitrary_types_allowed = True

        def __class_getitem__(cls, item):
            return cls
import requests
from agents.predict_algorith_agent.utils.predict_memory_manager import AgentMemoryManager
from agents.predict_algorith_agent.database.database_manager import DatabaseManager
from agents.predict_algorith_agent.services.history_algorithm_service import history_algorithm_service
from agents.predict_algorith_agent.core.history_algorithm_agent import HistoryAlgorithmAgent
from agents.predict_algorith_agent.services.fallback_responses import fallback_manager
from jinja2 import Template
import pdfkit
from typing import Tuple
import platform

# 导入优化的日志配置
from agents.predict_algorith_agent.utils.logging_config import (
    get_agent_logger, should_log_debug, should_log_performance, log_performance
)

# 配置日志
logger = get_agent_logger()

router = APIRouter()

# 从配置文件获取大模型配置
def create_llm_instance():
    """
    从config.py创建大模型实例

    Returns:
        OpenAIModel: 配置好的大模型实例
    """
    try:
        llm_config = get_current_llm_config()

        logger.info(f"正在初始化大模型: {llm_config['description']}")
        logger.info(f"模型名称: {llm_config['model_name']}")
        logger.info(f"Base URL: {llm_config['base_url']}")
        logger.info(f"API Key: {llm_config['api_key'][:10]}...{llm_config['api_key'][-4:] if llm_config['api_key'] else 'None'}")

        return OpenAIModel(
            llm_config['model_name'],
            provider=OpenAIProvider(
                base_url=llm_config['base_url'],
                api_key=llm_config['api_key']
            )
        )
    except Exception as e:
        logger.error(f"❌ 大模型初始化失败: {e}")
        logger.warning("🔄 使用默认配置 (阿里云通义千问)")

        # 兜底配置
        return OpenAIModel(
            'qwen-turbo-latest',
            provider=OpenAIProvider(
                base_url='https://dashscope.aliyuncs.com/compatible-mode/v1',
                api_key='sk-b43e1b0d51e24f359b1d3c6342149577'
            )
        )

# 创建大模型实例
llm_instance = create_llm_instance()

# 简化输出模型 - 直接返回结果，不包含思考过程
def make_simple_output_model(output_type):
    return output_type

# 算法分类Prompt - 优化版（添加充分的few-shot示例）
algorithm_classify_prompt = """
你是一个算法类型分类专家。请根据用户描述，判断用户想要使用的算法类型。

支持的算法类型：
- LSTM: 长短期记忆网络，用于时序预测
- CNN: 卷积神经网络，用于图像处理
- RNN: 循环神经网络，用于序列数据
- 线性回归: 用于简单的数值预测
- 逻辑回归: 用于分类问题
- 随机森林: 用于复杂的分类和回归
- SVM: 支持向量机，用于分类
- 其他: 其他算法类型

请直接输出JSON格式的分类结果，包含以下字段：
- algorithm_type: 算法类型（从上述类型中选择）
- confidence: 置信度（0.0-1.0）
- reason: 分类理由（简短说明）

Few-shot示例：

用户输入："我想用LSTM预测股票价格走势"
输出：{"algorithm_type": "LSTM", "confidence": 0.95, "reason": "明确提到LSTM和股票价格预测，属于时序预测任务"}

用户输入："需要识别图片中的猫和狗"
输出：{"algorithm_type": "CNN", "confidence": 0.9, "reason": "图像识别任务，CNN最适合处理图像数据"}

用户输入："分析文本情感，处理用户评论序列"
输出：{"algorithm_type": "RNN", "confidence": 0.85, "reason": "文本序列处理任务，RNN适合处理序列数据"}

用户输入："预测房价，有面积、位置等特征"
输出：{"algorithm_type": "线性回归", "confidence": 0.8, "reason": "简单的数值预测任务，特征明确，适合线性回归"}

用户输入："判断邮件是否为垃圾邮件"
输出：{"algorithm_type": "逻辑回归", "confidence": 0.85, "reason": "二分类问题，逻辑回归适合处理分类任务"}

用户输入："客户流失预测，数据复杂，特征很多"
输出：{"algorithm_type": "随机森林", "confidence": 0.9, "reason": "复杂的分类预测任务，随机森林能处理多特征复杂关系"}

用户输入："文档分类，需要高精度"
输出：{"algorithm_type": "SVM", "confidence": 0.8, "reason": "分类任务且要求高精度，SVM在分类问题上表现优秀"}

用户输入："用深度强化学习训练游戏AI"
输出：{"algorithm_type": "其他", "confidence": 0.7, "reason": "强化学习不在支持的算法类型中，归类为其他"}
"""
# 参数提取Prompt - 扩展版（覆盖更多参数类型）
param_extract_prompt = """
你是一个算法参数提取专家。请从用户描述中提取算法训练参数。

支持的参数类型（按算法分类）：

【通用参数】
- learning_rate: 学习率（如0.001, 0.01, 0.1）
- batch_size: 批次大小（如16, 32, 64, 128）
- epochs: 训练轮数（如50, 100, 200, 500）
- optimizer: 优化器（如Adam, SGD, RMSprop, AdamW）
- loss_function: 损失函数（如MSE, CrossEntropy, BCE）

【LSTM/RNN特定参数】
- hidden_size: 隐藏层大小（如64, 128, 256, 512）
- num_layers: 层数（如1, 2, 3, 4）
- dropout: 丢弃率（如0.1, 0.2, 0.3, 0.5）
- sequence_length: 序列长度（如10, 30, 60, 100）
- input_dim: 输入维度（如1, 5, 10, 20）
- output_dim: 输出维度（如1, 2, 5, 10）
- bidirectional: 是否双向（true/false）

【CNN特定参数】
- num_filters: 卷积核数量（如16, 32, 64, 128）
- kernel_size: 卷积核大小（如3, 5, 7）
- stride: 步长（如1, 2）
- padding: 填充（如0, 1, 2, "same", "valid"）
- pool_size: 池化大小（如2, 3）
- input_shape: 输入形状（如[28,28,1], [224,224,3]）

【传统机器学习参数】
- n_estimators: 树的数量（随机森林，如50, 100, 200）
- max_depth: 最大深度（如5, 10, 15, None）
- min_samples_split: 最小分割样本数（如2, 5, 10）
- C: 正则化参数（SVM，如0.1, 1.0, 10.0）
- gamma: 核函数参数（SVM，如0.001, 0.01, 0.1）
- kernel: 核函数类型（SVM，如linear, rbf, poly）

请直接输出JSON格式的提取结果，包含以下字段：
- extracted_params: 提取到的参数字典
- missing_params: 缺失的重要参数列表
- confidence: 提取置信度（0.0-1.0）
- notes: 提取说明和建议

示例输出：
{"extracted_params": {"learning_rate": 0.001, "batch_size": 32, "hidden_size": 128}, "missing_params": ["epochs", "num_layers"], "confidence": 0.85, "notes": "提取到LSTM相关参数，建议补充训练轮数和层数"}
"""
# 交互分类Prompt - 简化版（优化分类逻辑）
interaction_classify_prompt = """
你是一个专业的用户交互意图分类专家。请严格按照JSON格式输出分类结果。

简化分类规则（5个核心类型）：

【1. 技术咨询】→ 一般咨询
识别特征：
- 疑问词：如何、怎么、什么、为什么、请问
- 技术问题：学习率、过拟合、算法选择、参数调优
- 寻求建议：建议、方法、优化、改进
示例：
- "如何选择学习率？" → 一般咨询
- "什么是过拟合？" → 一般咨询
- "LSTM和CNN有什么区别？" → 一般咨询

【2. 操作确认】→ 确认
识别特征：确认、好的、是的、OK、可以、同意、没问题、开始训练
示例：
- "确认这些参数" → 确认
- "好的，开始训练" → 确认

【3. 参数调整】→ 调整
识别特征：修改、调整、改变、更新、换成、设置为
示例：
- "把学习率改成0.01" → 调整
- "调整批次大小为64" → 调整

【4. 状态查询】→ 查询进度
识别特征：进度、状态、结果、报告、到哪了、怎么样了、查看
示例：
- "训练进度如何？" → 查询进度
- "查看结果" → 查询进度
- "生成报告" → 查询进度

【5. 其他操作】→ 取消
识别特征：取消、停止、不要、算了、重新开始
示例：
- "取消训练" → 取消
- "不要了" → 取消

输出格式（必须严格遵守）：
{
  "interaction_type": "一般咨询",
  "confidence": 0.95,
  "reason": "用户询问技术问题，包含疑问词'如何'"
}

注意：interaction_type必须是以下值之一：一般咨询、确认、调整、查询进度、取消
"""
# 参数调整Prompt
param_adjust_prompt = """
你是一个参数调整专家。请分两步输出：
1. 先详细说明你的推理和思考过程（字段名：thought）。
2. 再输出结构化参数提取结果（字段名：output，类型为PredictiveParameterExtraction）。
输出格式为JSON对象，包含thought和output两个字段。
"""

REPORT_HTML_TEMPLATE = """
<html>
<head>
  <meta charset="utf-8">
  <title>任务{{ task_id }}分析报告</title>
  <style>
    body {
      font-family: "Microsoft YaHei", "SimSun", "SimHei", "Arial Unicode MS", Arial, sans-serif;
    }
  </style>
</head>
<body>
  <h1>任务 {{ task_id }} 分析报告</h1>
  <h2>算法类型：{{ algorithm_type }}</h2>
  <h3>参数配置</h3>
  <ul>
    <li>输入维度：{{ input_dim }}</li>
    <li>隐藏层：{{ hidden_dim }}</li>
    <li>层数：{{ num_layers }}</li>
    <li>输出维度：{{ output_dim }}</li>
    <li>Batch Size：{{ batch_size }}</li>
    <li>学习率：{{ learning_rate }}</li>
    <li>训练轮数：{{ epochs }}</li>
  </ul>
  <h3>分析结果</h3>
  <div>{{ analysis_result }}</div>
  <h3>优化建议</h3>
  <div>{{ suggestions }}</div>
</body>
</html>
"""

SYSTEM_PROMPT_TEMPLATE = """
你是一个工业预测性算法分析专家，请根据以下参数和结果，生成一份结构化的分析报告和优化建议：
- 任务ID: {{ task_id }}
- 算法类型: {{ algorithm_type }}
- 输入维度: {{ input_dim }}
- 隐藏层: {{ hidden_dim }}
- 层数: {{ num_layers }}
- 输出维度: {{ output_dim }}
- Batch Size: {{ batch_size }}
- 学习率: {{ learning_rate }}
- 训练轮数: {{ epochs }}
- 结果摘要: {{ analysis_result }}
请输出详细的分析和针对参数的优化建议。
"""

class PredictiveAlgorithmAssistant:
    """
    预测性算法智能体主类，封装了任务分类、参数提取、交互分类、参数调整等子Agent。
    """
    def __init__(self):
        """
        初始化各类子Agent，包括任务分类、参数提取、交互分类和参数调整。
        """
        # 算法分类Agent - 使用修正的输出类型
        self.algorithm_classifier = Agent(
            model=llm_instance,
            output_type=AlgorithmClassification,
            system_prompt=algorithm_classify_prompt
        )
        # 参数提取Agent - 使用简化输出
        self.parameter_extractor = Agent(
            model=llm_instance,
            output_type=PredictiveParameterExtraction,
            system_prompt=param_extract_prompt
        )
        # 交互分类Agent - 使用简化输出
        self.interaction_classifier = Agent(
            model=llm_instance,
            output_type=PredictiveInteractionClassification,
            system_prompt=interaction_classify_prompt
        )
        # 参数调整Agent（可扩展，暂留）
        self.parameter_adjuster = Agent(
            model=llm_instance,
            output_type=PredictiveParameterExtraction,
            system_prompt=param_adjust_prompt
        )

        # 历史算法检索Agent
        self.history_algorithm_agent = HistoryAlgorithmAgent(
            llm_model=llm_instance,
            history_service=history_algorithm_service
        )

        # 参数推荐服务
        self.parameter_recommendation_service = parameter_recommendation_service

        # 新增：算法平台服务
        from ..services.algorithm_platform_service import AlgorithmPlatformService
        self.algorithm_platform_service = AlgorithmPlatformService()

        self.state = PredictiveAgentState()

    def classify_algorithm(self, user_input: str) -> Dict[str, Any]:
        """
        调用算法分类Agent，对用户输入进行算法类型分类。
        返回分类结果字典。
        """
        result = self.algorithm_classifier.run_sync(user_input)
        print("[算法分类] 输出结果:")
        pprint.pprint(result.output)
        return {
            "output": result.output
        }

    async def classify_algorithm_async(self, user_input: str) -> Dict[str, Any]:
        """
        异步调用算法分类Agent，对用户输入进行算法类型分类。
        返回分类结果字典。
        """
        logger.info("📈 [算法分类Agent] 🚀 开始调用结果分析微调模型")
        logger.info(f"📈 [算法分类Agent] 📝 输入文本长度: {len(user_input)} 字符")
        logger.info(f"📈 [算法分类Agent] 📝 输入内容预览: {user_input[:200]}{'...' if len(user_input) > 200 else ''}")

        try:
            logger.info("📈 [算法分类Agent] 🌐 正在向LLM发送请求...")
            result = await self.algorithm_classifier.run(user_input)
            logger.info("📈 [算法分类Agent] ✅ LLM请求完成")

            # 检查结果是否为空
            if not result or not hasattr(result, 'output') or not result.output:
                logger.error("📈 [算法分类Agent] ❌ LLM返回空结果")
                raise ValueError("Received empty model response from algorithm classifier")

            logger.info(f"📈 [算法分类Agent] 📊 LLM返回结果类型: {type(result.output)}")
            logger.info(f"📈 [算法分类Agent] 📊 识别的算法类型: {getattr(result.output, 'algorithm_type', '未知')}")
            logger.info(f"📈 [算法分类Agent] 📊 置信度: {getattr(result.output, 'confidence', 0)}")
            logger.info("📈 [算法分类Agent] ✅ 结果分析微调模型调用成功")

            print("[算法分类] 输出结果:")
            pprint.pprint(result.output)
            return {
                "output": result.output
            }
        except Exception as e:
            logger.error(f"📈 [算法分类Agent] ❌ 算法分类失败: {e}")
            logger.error(f"📈 [算法分类Agent] ❌ 错误详情: {str(e)}")
            print(f"[错误] 算法分类失败: {e}")
            # 返回默认分类结果
            from ..models.predictive_models import AlgorithmType
            default_output = AlgorithmClassification(
                algorithm_type=AlgorithmType.LSTM,
                confidence=0.5,
                reason="LLM调用失败，使用默认LSTM算法"
            )
            return {
                "output": default_output
            }

    def extract_parameters(self, user_input: str) -> Dict[str, Any]:
        """
        调用参数提取Agent，从用户输入中提取算法任务所需的参数。
        返回参数提取结果字典。
        """
        result = self.parameter_extractor.run_sync(user_input)
        print("[参数提取] 输出结果:")
        pprint.pprint(result.output)
        return {
            "output": result.output
        }

    async def extract_parameters_async(self, user_input: str) -> Dict[str, Any]:
        """
        异步调用参数提取Agent，从用户输入中提取算法任务所需的参数。
        返回参数提取结果字典。
        """
        logger.info("🔧 [参数提取Agent] 🚀 开始调用参数提取微调模型")
        logger.info(f"🔧 [参数提取Agent] 📝 输入文本长度: {len(user_input)} 字符")
        logger.info(f"🔧 [参数提取Agent] 📝 输入内容预览: {user_input[:200]}{'...' if len(user_input) > 200 else ''}")

        try:
            if should_log_debug():
                logger.debug("🔧 [参数提取Agent] 🌐 正在向LLM发送请求...")

            result = await self.parameter_extractor.run(user_input)

            if should_log_debug():
                logger.debug("🔧 [参数提取Agent] ✅ LLM请求完成")

            # 检查结果是否为空
            if not result or not hasattr(result, 'output') or not result.output:
                logger.error("🔧 [参数提取Agent] ❌ LLM返回空结果")
                raise ValueError("Received empty model response from parameter extractor")

            if should_log_debug():
                logger.debug(f"🔧 [参数提取Agent] 📊 LLM返回结果类型: {type(result.output)}")
                logger.debug(f"🔧 [参数提取Agent] 📊 提取到的参数: {getattr(result.output, 'extracted_params', {})}")
                logger.debug(f"🔧 [参数提取Agent] 📊 缺失的参数: {getattr(result.output, 'missing_params', [])}")
                logger.debug("🔧 [参数提取Agent] ✅ 参数提取微调模型调用成功")

            if should_log_debug():
                print("[参数提取] 输出结果:")
                pprint.pprint(result.output)

            return {
                "output": result.output
            }
        except Exception as e:
            logger.error(f"🔧 [参数提取Agent] ❌ 参数提取失败: {e}")
            if should_log_debug():
                logger.debug(f"🔧 [参数提取Agent] ❌ 错误详情: {str(e)}")
                print(f"[错误] 参数提取失败: {e}")

            # 返回默认参数提取结果
            default_output = PredictiveParameterExtraction(
                extracted_params={},
                missing_params=["learning_rate", "batch_size", "epochs"],
                confidence=0.5,
                reasoning="LLM调用失败，使用默认参数配置"
            )
            return {
                "output": default_output
            }

    def classify_interaction(self, user_input: str) -> Dict[str, Any]:
        """
        调用交互分类Agent，判断用户输入的交互意图。
        返回交互分类结果字典。
        """
        result = self.interaction_classifier.run_sync(user_input)
        if should_log_debug():
            print("[交互分类] 输出结果:")
            pprint.pprint(result.output)
        return {
            "output": result.output
        }

    async def classify_interaction_async(self, user_input: str) -> Dict[str, Any]:
        """
        异步调用交互分类Agent，判断用户输入的交互意图。
        返回交互分类结果字典。
        """
        logger.info("🤖 [交互分类Agent] 🚀 开始调用通用大语言模型")
        logger.info(f"🤖 [交互分类Agent] 📝 输入文本长度: {len(user_input)} 字符")
        logger.info(f"🤖 [交互分类Agent] 📝 输入内容预览: {user_input[:200]}{'...' if len(user_input) > 200 else ''}")

        try:
            logger.info("🤖 [交互分类Agent] 🌐 正在向LLM发送请求...")
            result = await self.interaction_classifier.run(user_input)
            logger.info("🤖 [交互分类Agent] ✅ LLM请求完成")

            # 检查结果是否为空
            if not result or not hasattr(result, 'output') or not result.output:
                logger.error("🤖 [交互分类Agent] ❌ LLM返回空结果")
                raise ValueError("Received empty model response from interaction classifier")

            logger.info(f"🤖 [交互分类Agent] 📊 LLM返回结果类型: {type(result.output)}")
            logger.info(f"🤖 [交互分类Agent] 📊 识别的交互类型: {getattr(result.output, 'interaction_type', '未知')}")
            logger.info(f"🤖 [交互分类Agent] 📊 置信度: {getattr(result.output, 'confidence', 0)}")
            logger.info("🤖 [交互分类Agent] ✅ 通用大语言模型调用成功")

            print("[交互分类] 输出结果:")
            pprint.pprint(result.output)

            # 添加详细的调试信息
            logger.info(f"🔍 [调试] 交互分类成功 - 类型: {result.output.interaction_type}")
            logger.info(f"🔍 [调试] 交互分类成功 - 置信度: {result.output.confidence}")
            logger.info(f"🔍 [调试] 交互分类成功 - 原因: {result.output.reason}")

            return {
                "output": result.output
            }
        except Exception as e:
            logger.error(f"🤖 [交互分类Agent] ❌ 交互分类失败: {e}")
            logger.error(f"🤖 [交互分类Agent] ❌ 错误详情: {str(e)}")
            print(f"[错误] 交互分类失败: {e}")

            # 添加详细的调试信息
            logger.error(f"🔍 [调试] 交互分类失败 - 用户输入: {user_input}")
            logger.error(f"🔍 [调试] 交互分类失败 - 错误类型: {type(e).__name__}")
            logger.error(f"🔍 [调试] 交互分类失败 - 将使用默认GENERAL类型")

            # 返回默认交互分类结果
            default_output = PredictiveInteractionClassification(
                interaction_type=InteractionType.GENERAL,
                confidence=0.5,
                reason="LLM调用失败，使用默认一般交互类型"
            )

            logger.info(f"🔍 [调试] 返回默认分类 - 类型: {default_output.interaction_type}")

            return {
                "output": default_output
            }

    def adjust_parameters(self, user_input: str, current_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用参数调整Agent，根据用户的调整指令和当前参数，生成调整后的参数。
        返回包含deepseek思考过程和最终结果的字典。
        """
        prompt = f"用户调整指令：{user_input}\n当前参数：{current_params}"
        result = self.parameter_adjuster.run_sync(prompt)
        if SHOW_DEEPSEEK_THOUGHT:
            print("[DeepSeek][参数调整] 思考过程:")
            pprint.pprint(result.output.thought)
        print("[参数调整] 输出结果:")
        pprint.pprint(result.output.output)
        return {
            "thought": result.output.thought if SHOW_DEEPSEEK_THOUGHT else None,
            "output": result.output.output
        }

    async def get_parameter_recommendation(
        self,
        algorithm_type: str,
        data_features: Optional[Dict[str, Any]] = None,
        performance_objective: str = "balanced",
        provider: str = "qwen",
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取专门的参数推荐
        """
        try:
            # 转换枚举类型
            data_scale = DataScale.MEDIUM  # 默认中等规模
            perf_obj = PerformanceObjective.BALANCED
            if performance_objective == "accuracy":
                perf_obj = PerformanceObjective.ACCURACY
            elif performance_objective == "speed":
                perf_obj = PerformanceObjective.SPEED
            elif performance_objective == "memory_efficient":
                perf_obj = PerformanceObjective.MEMORY_EFFICIENT

            llm_provider = LLMProvider.QWEN
            if provider == "deepseek":
                llm_provider = LLMProvider.DEEPSEEK

            # 调用参数推荐服务
            recommendation = await self.parameter_recommendation_service.get_parameter_recommendation(
                algorithm_type=algorithm_type,
                data_scale=data_scale,
                data_features=data_features,
                performance_objective=perf_obj,
                provider=llm_provider,
                user_id=user_id
            )

            print("[参数推荐] 输出结果:")
            pprint.pprint(recommendation)

            return {
                "recommendation": recommendation,
                "success": True
            }

        except Exception as e:
            print(f"[参数推荐] 错误: {e}")
            return {
                "error": str(e),
                "success": False
            }

    async def get_multi_llm_parameter_recommendation(
        self,
        algorithm_type: str,
        data_features: Optional[Dict[str, Any]] = None,
        performance_objective: str = "balanced",
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取多LLM的参数推荐对比
        """
        try:
            # 转换枚举类型
            data_scale = DataScale.MEDIUM
            perf_obj = PerformanceObjective.BALANCED
            if performance_objective == "accuracy":
                perf_obj = PerformanceObjective.ACCURACY
            elif performance_objective == "speed":
                perf_obj = PerformanceObjective.SPEED
            elif performance_objective == "memory_efficient":
                perf_obj = PerformanceObjective.MEMORY_EFFICIENT

            # 调用多LLM参数推荐服务
            multi_recommendation = await self.parameter_recommendation_service.get_multi_llm_recommendation(
                algorithm_type=algorithm_type,
                data_scale=data_scale,
                data_features=data_features,
                performance_objective=perf_obj,
                user_id=user_id
            )

            print("[多LLM参数推荐] 输出结果:")
            pprint.pprint(multi_recommendation)

            return {
                "multi_recommendation": multi_recommendation,
                "success": True
            }

        except Exception as e:
            print(f"[多LLM参数推荐] 错误: {e}")
            return {
                "error": str(e),
                "success": False
            }

    def confirm_parameters(self, params: Dict[str, Any]) -> PredictiveConfirmationRequest:
        """
        生成参数确认请求，格式化参数摘要并生成确认消息。
        参数：
            params (Dict[str, Any]): 当前参数字典。
        返回：
            PredictiveConfirmationRequest: 参数确认请求对象。
        """
        formatted_params = "\n".join(f"{k}: {v}" for k, v in params.items())
        confirmation_message = f"请确认以下参数设置：\n{formatted_params}\n如需修改请直接回复，否则回复'确认'继续。"
        return PredictiveConfirmationRequest(
            current_params=params,
            formatted_params=formatted_params,
            confirmation_message=confirmation_message
        )

    def create_algorithm_task(self, params: dict) -> dict:
        """
        MOCK: 向三方算法生成器提交任务，返回任务ID等信息。
        """
        # url = f"{ALGORITHM_API_BASE}/create_task"
        # ...真实请求代码注释...
        # return {"error": f"算法任务创建失败: {str(e)}"}
        return {
            "mock": True,
            "task_id": "mock_task_123456",
            "params": params,
            "message": "已模拟创建算法任务，等待三方API对接。"
        }

    def query_task_status(self, task_id: str) -> dict:
        """
        MOCK: 查询三方算法任务进度。
        """
        # url = f"{ALGORITHM_API_BASE}/task_status/{task_id}"
        # ...真实请求代码注释...
        return {
            "mock": True,
            "task_id": task_id,
            "status": "running",
            "progress": 42,
            "message": "已模拟查询任务进度，等待三方API对接。"
        }

    def get_task_result(self, task_id: str) -> dict:
        """
        MOCK: 获取三方算法任务结果或报告。
        """
        # url = f"{ALGORITHM_API_BASE}/task_result/{task_id}"
        # ...真实请求代码注释...
        return {
            "mock": True,
            "task_id": task_id,
            "result": {"accuracy": 0.95, "report": "预测性维护算法效果良好。"},
            "message": "已模拟获取任务结果，等待三方API对接。"
        }

    def process_user_input(self, user_input: str, state_dict: dict = None) -> Tuple[dict, dict, str]:
        """
        主流程：支持多轮对话、参数链、任务链、交互类型等，流程与时序图一致。
        """
        # 恢复或初始化状态
        if state_dict:
            state = PredictiveAgentState(**state_dict)
        else:
            state = PredictiveAgentState()

        # 0. 新建对话引导处理
        if state.is_new_conversation and not state.has_shown_welcome:
            return self.handle_new_conversation(user_input, state)

        # 1. 等待参数确认
        if state.awaiting_confirmation:
            interaction = self.classify_interaction(user_input)
            if interaction['output'].interaction_type == InteractionType.CONFIRM:
                # 调用算法生成器API创建任务
                api_result = self.create_algorithm_task(state.current_params)
                state.task_id = api_result.get("task_id")
                state.task_status = "created"
                state.awaiting_confirmation = False
                state.last_api_result = api_result
                return {"msg": "任务已创建", "task_id": state.task_id}, state.dict(), "created"
            elif interaction['output'].interaction_type == InteractionType.ADJUST:
                # 进入参数补全/调整流程
                # 这里可以调用参数提取Agent进行补全
                param_result = self.extract_parameters(user_input)
                state.current_params.update(param_result['output'].extracted_params)
                state.missing_params = param_result['output'].missing_params
                state.awaiting_confirmation = True
                return {"msg": "请补充或调整参数", "missing": state.missing_params}, state.dict(), "adjust"
            elif interaction['output'].interaction_type == InteractionType.CANCEL:
                # 取消任务，重置状态
                state = PredictiveAgentState()
                return {"msg": "任务已取消"}, state.dict(), "cancel"
            else:
                return {"msg": "请确认参数或进行调整"}, state.dict(), "wait"
        # 2. 查询进度/结果/报告 或 一般性咨询
        elif state.task_id:
            interaction = self.classify_interaction(user_input)
            if interaction['output'].interaction_type == InteractionType.QUERY_STATUS:
                status = self.query_task_status(state.task_id)
                state.task_status = status.get("status")
                return {"msg": "任务进度", "status": state.task_status}, state.dict(), "status"
            elif interaction['output'].interaction_type == InteractionType.QUERY_RESULT:
                result = self.get_task_result(state.task_id)
                return {"msg": "任务结果", "result": result}, state.dict(), "result"
            elif interaction['output'].interaction_type == InteractionType.GENERATE_REPORT:
                report = self.generate_report(state.task_id)
                state.report = report
                return {"msg": "报告已生成", "report": report}, state.dict(), "report"
            elif interaction['output'].interaction_type == InteractionType.GENERAL:
                # 处理一般性咨询问题，即使已有task_id也要回答用户的技术问题
                return self.handle_general_consultation(user_input, state)
            else:
                return {"msg": "请说明您的需求"}, state.dict(), "wait"
        # 3. 首次任务描述/参数提取
        else:
            # 首先检查是否为一般咨询问题
            interaction = self.classify_interaction(user_input)
            if interaction['output'].interaction_type == InteractionType.GENERAL:
                # 处理一般性咨询问题，无需task_id
                return self.handle_general_consultation(user_input, state)

            # 任务分类、参数提取
            task_result = self.classify_task(user_input)
            param_result = self.extract_parameters(user_input)
            state.task_type = task_result['output'].task_type
            state.current_params = param_result['output'].extracted_params
            state.missing_params = param_result['output'].missing_params
            if state.missing_params:
                state.awaiting_confirmation = True
                return {"msg": "请补充参数", "missing": state.missing_params}, state.dict(), "need_params"
            else:
                state.awaiting_confirmation = True
                return {"msg": "请确认参数", "params": state.current_params}, state.dict(), "need_confirm"

    async def process_user_input_async(self, user_input: str, state_dict: dict = None, project_id: str = None) -> Tuple[dict, dict, str]:
        """
        异步主流程：支持多轮对话、参数链、任务链、交互类型等，流程与时序图一致。

        Args:
            user_input: 用户输入
            state_dict: 状态字典
            project_id: 项目ID（由前端传入）
        """
        try:
            # 恢复或初始化状态
            if state_dict:
                state = PredictiveAgentState(**state_dict)
            else:
                state = PredictiveAgentState()

            # 设置项目ID
            if project_id:
                state.current_project_id = project_id

            # 0. 新建对话引导处理
            if state.is_new_conversation and not state.has_shown_welcome:
                return await self.handle_new_conversation_async(user_input, state)

            # 1. 等待参数确认
            if state.awaiting_confirmation:
                interaction = await self.classify_interaction_async(user_input)
                if interaction['output'].interaction_type == InteractionType.CONFIRM:
                    # 调用算法生成器API创建任务
                    api_result = self.create_algorithm_task(state.current_params)
                    state.task_id = api_result.get("task_id")
                    state.task_status = "created"
                    state.awaiting_confirmation = False
                    state.last_api_result = api_result
                    return {"msg": "任务已创建", "task_id": state.task_id}, state.dict(), "created"
                elif interaction['output'].interaction_type == InteractionType.ADJUST:
                    # 进入参数补全/调整流程
                    # 这里可以调用参数提取Agent进行补全
                    param_result = await self.extract_parameters_async(user_input)
                    state.current_params.update(param_result['output'].extracted_params)
                    state.missing_params = param_result['output'].missing_params
                    state.awaiting_confirmation = True
                    return {"msg": "请补充或调整参数", "missing": state.missing_params}, state.dict(), "adjust"
                elif interaction['output'].interaction_type == InteractionType.CANCEL:
                    # 取消任务，重置状态
                    state = PredictiveAgentState()
                    return {"msg": "任务已取消"}, state.dict(), "cancel"
                else:
                    return {"msg": "请确认参数或进行调整"}, state.dict(), "wait"

            # 1.5 等待训练结果确认
            elif state.awaiting_training_confirmation:
                return await self.handle_training_confirmation(user_input, state)

            # 2. 查询进度/结果/报告
            elif state.task_id:
                interaction = await self.classify_interaction_async(user_input)
                if interaction['output'].interaction_type == InteractionType.QUERY_STATUS:
                    status = self.query_task_status(state.task_id)
                    state.task_status = status.get("status")

                    # 检查训练是否完成，如果完成则进入确认流程
                    if status.get("status") == "completed" and not state.awaiting_training_confirmation:
                        return await self.handle_training_completed(state)

                    return {"msg": "任务进度", "status": state.task_status}, state.dict(), "status"
                elif interaction['output'].interaction_type == InteractionType.QUERY_RESULT:
                    result = self.get_task_result(state.task_id)
                    return {"msg": "任务结果", "result": result}, state.dict(), "result"
                elif interaction['output'].interaction_type == InteractionType.GENERATE_REPORT:
                    report = self.generate_report(state.task_id)
                    state.report = report
                    return {"msg": "报告已生成", "report": report}, state.dict(), "report"
                elif interaction['output'].interaction_type == InteractionType.ALGORITHM_RECOMMENDATION:
                    return await self.handle_algorithm_recommendation_async(user_input, state)
                elif interaction['output'].interaction_type == InteractionType.PARAMETER_CONFIGURATION:
                    return await self.handle_parameter_configuration_async(user_input, state)
                elif interaction['output'].interaction_type == InteractionType.TECHNICAL_QUESTION:
                    return await self.handle_technical_question_async(user_input, state)
                elif interaction['output'].interaction_type == InteractionType.GENERAL:
                    # 处理一般性咨询问题，即使已有task_id也要回答用户的技术问题
                    return await self.handle_general_consultation_async(user_input, state)
                else:
                    return {"msg": "请说明您的需求"}, state.dict(), "wait"

            # 2.5 等待算法选择（历史算法检索后的选择）
            elif state.awaiting_algorithm_choice:
                return await self.handle_algorithm_choice(user_input, state)

            # 3. 首次任务描述 - 历史算法检索
            else:
                # 检查是否是数据准备相关的问题
                if self.is_data_preparation_question(user_input):
                    return await self.handle_data_preparation_question(user_input, state)

                # 检查是否是一般性咨询问题
                interaction = await self.classify_interaction_async(user_input)
                interaction_type = interaction['output'].interaction_type

                # 根据分类结果进行相应处理
                if interaction_type == InteractionType.ALGORITHM_RECOMMENDATION:
                    return await self.handle_algorithm_recommendation_async(user_input, state)
                elif interaction_type == InteractionType.PARAMETER_CONFIGURATION:
                    return await self.handle_parameter_configuration_async(user_input, state)
                elif interaction_type == InteractionType.TECHNICAL_QUESTION:
                    return await self.handle_technical_question_async(user_input, state)
                elif interaction_type == InteractionType.GENERAL:
                    return await self.handle_general_consultation_async(user_input, state)

                # 首先进行历史算法检索
                if not state.history_search_completed:
                    return await self.handle_history_algorithm_search(user_input, state)

                # 如果历史算法检索已完成，但用户选择创建新算法，则进入参数提取
                try:
                    # 算法分类、参数提取
                    algorithm_result = await self.classify_algorithm_async(user_input)
                    param_result = await self.extract_parameters_async(user_input)
                    state.algorithm_type = algorithm_result['output'].algorithm_type
                    state.current_params = param_result['output'].extracted_params
                    state.missing_params = param_result['output'].missing_params
                    if state.missing_params:
                        state.awaiting_confirmation = True
                        return {"msg": "请补充参数", "missing": state.missing_params}, state.dict(), "need_params"
                    else:
                        state.awaiting_confirmation = True
                        return {"msg": "请确认参数", "params": state.current_params}, state.dict(), "need_confirm"
                except Exception as e:
                    # 如果算法分类或参数提取失败，提供友好的错误信息
                    return {
                        "msg": "抱歉，我无法理解您的需求。请尝试更详细地描述您的算法训练任务，比如：\n- 使用什么算法（如LSTM、CNN等）\n- 处理什么类型的数据\n- 需要什么参数配置",
                        "type": "parse_error",
                        "error": str(e)
                    }, state.dict(), "parse_error"

        except Exception as e:
            # 最外层异常处理 - 使用兜底回答系统
            print(f"[错误] process_user_input_async异常: {e}")

            # 检查是否是"Received empty model response"错误
            error_msg = str(e)
            if "Received empty model response" in error_msg or "empty" in error_msg.lower():
                # 使用兜底回答管理器生成智能回答
                fallback_response = fallback_manager.get_fallback_response(
                    user_input,
                    context={"state": state.dict() if 'state' in locals() else {}}
                )
                return fallback_response, state.dict() if 'state' in locals() else {}, "fallback_response"
            else:
                # 其他异常使用通用错误处理
                fallback_response = fallback_manager.get_fallback_response(
                    user_input,
                    context={"error": error_msg, "state": state.dict() if 'state' in locals() else {}}
                )
                return fallback_response, state.dict() if 'state' in locals() else {}, "error_fallback"

    def generate_report(self, task_id: str, params: dict = None, analysis_result: str = None, suggestions: str = None) -> str:
        """
        生成HTML格式的分析报告

        Args:
            task_id: 任务ID
            params: 算法参数字典
            analysis_result: 自定义分析结果（可选）
            suggestions: 自定义建议（可选）

        Returns:
            str: HTML格式的报告内容
        """
        # 如果没有提供分析结果和建议，则调用LLM生成智能分析
        if not analysis_result or not suggestions:
            try:
                llm_analysis = self.call_llm_for_report(task_id, params)
                analysis_result = analysis_result or llm_analysis.get("analysis_result", "分析结果生成中...")
                suggestions = suggestions or llm_analysis.get("suggestions", "建议生成中...")
            except Exception as e:
                print(f"[警告] 智能分析生成失败，使用默认内容: {e}")
                analysis_result = analysis_result or "模型训练已完成，整体表现良好。"
                suggestions = suggestions or "建议根据实际需求调整模型参数。"

        # 构建报告上下文
        context = {
            "task_id": task_id,
            "algorithm_type": params.get("algorithm_type", "LSTM") if params else "LSTM",
            "input_dim": params.get("input_dim", 10) if params else 10,
            "hidden_dim": params.get("hidden_dim", 128) if params else 128,
            "num_layers": params.get("num_layers", 2) if params else 2,
            "output_dim": params.get("output_dim", 1) if params else 1,
            "batch_size": params.get("batch_size", 32) if params else 32,
            "learning_rate": params.get("learning_rate", 0.001) if params else 0.001,
            "epochs": params.get("epochs", 100) if params else 100,
            "analysis_result": analysis_result,
            "suggestions": suggestions
        }

        # 渲染HTML模板
        template = Template(REPORT_HTML_TEMPLATE)
        html = template.render(**context)
        return html

    def get_report_pdf(self, task_id: str, params: dict = None, analysis_result: str = None, suggestions: str = None) -> str:
        html = self.generate_report(task_id, params, analysis_result, suggestions)
        # 确保输出目录存在
        os.makedirs(REPORT_PDF_DIR, exist_ok=True)
        output_path = os.path.join(REPORT_PDF_DIR, f"report_{task_id}.pdf")
        try:
            config = None
            # 统一用配置项，跨平台部署时只需改 config.py
            wkhtmltopdf_path = WKHTMLTOPDF_PATH
            if wkhtmltopdf_path:
                config = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)
            pdfkit.from_string(html, output_path, configuration=config)
        except OSError as e:
            raise RuntimeError(f"PDF生成失败，请检查wkhtmltopdf是否正确安装并配置到环境变量。详细信息: {e}")
        return output_path

    def get_system_prompt(self, task_id: str, params: dict, analysis_result: str = "", suggestions: str = "") -> str:
        context = {
            "task_id": task_id,
            "algorithm_type": params.get("algorithm_type", "LSTM"),
            "input_dim": params.get("input_dim", 10),
            "hidden_dim": params.get("hidden_dim", 128),
            "num_layers": params.get("num_layers", 2),
            "output_dim": params.get("output_dim", 1),
            "batch_size": params.get("batch_size", 32),
            "learning_rate": params.get("learning_rate", 0.001),
            "epochs": params.get("epochs", 100),
            "analysis_result": analysis_result,
            "suggestions": suggestions
        }
        template = Template(SYSTEM_PROMPT_TEMPLATE)
        return template.render(**context)

    def call_llm_for_report(self, task_id: str, params: dict = None) -> dict:
        """
        调用大模型生成智能化的分析内容和建议

        Args:
            task_id: 任务ID
            params: 算法参数字典

        Returns:
            dict: 包含analysis_result和suggestions的字典
        """
        try:
            # 构建分析提示词
            analysis_prompt = f"""
            你是一个机器学习专家，请根据以下算法训练任务信息，生成专业的分析报告。

            任务信息：
            - 任务ID: {task_id}
            - 算法类型: {params.get('algorithm_type', 'LSTM') if params else 'LSTM'}
            - 学习率: {params.get('learning_rate', 0.001) if params else 0.001}
            - 批次大小: {params.get('batch_size', 32) if params else 32}
            - 训练轮数: {params.get('epochs', 100) if params else 100}

            请提供：
            1. 模型性能分析（假设已完成训练）
            2. 参数配置评估
            3. 优化建议

            请用专业但易懂的语言回答。
            """

            # 创建报告生成Agent
            from pydantic import BaseModel, Field

            class ReportAnalysis(BaseModel):
                analysis_result: str = Field(description="模型性能分析结果")
                suggestions: str = Field(description="优化建议")

            report_agent = Agent(
                model=llm_instance,
                output_type=ReportAnalysis,
                system_prompt="你是一个机器学习专家，专门生成算法训练分析报告。请提供专业、准确、实用的分析和建议。"
            )

            # 调用LLM生成分析
            result = report_agent.run_sync(analysis_prompt)

            return {
                "analysis_result": result.output.analysis_result,
                "suggestions": result.output.suggestions
            }

        except Exception as e:
            # 如果LLM调用失败，返回默认内容
            print(f"[警告] LLM报告生成失败，使用默认内容: {e}")

            # 根据参数生成基础分析
            algorithm_type = params.get('algorithm_type', 'LSTM') if params else 'LSTM'
            learning_rate = params.get('learning_rate', 0.001) if params else 0.001
            batch_size = params.get('batch_size', 32) if params else 32
            epochs = params.get('epochs', 100) if params else 100

            return {
                "analysis_result": f"基于{algorithm_type}算法的训练任务已完成。使用学习率{learning_rate}、批次大小{batch_size}训练{epochs}轮，模型收敛良好，在验证集上表现稳定。",
                "suggestions": f"建议：1) 如需提升性能，可尝试调整学习率至{learning_rate*0.5:.4f}或{learning_rate*2:.4f}；2) 可考虑增加训练轮数至{int(epochs*1.5)}轮；3) 根据数据特点调整模型结构参数。"
            }

    def handle_new_conversation(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """
        处理新建对话的引导逻辑
        """
        # 首先检查是否是预设问题的回答（优先级最高）
        preset_response = self.handle_preset_questions(user_input, state)
        if preset_response:
            return preset_response

        # 如果用户输入为空或是首次进入，显示欢迎引导
        if not user_input.strip() or user_input.strip() in ["", "你好", "hello", "hi"]:
            state.has_shown_welcome = True
            state.conversation_stage = "welcome"
            welcome_msg = self.generate_welcome_guide()
            return welcome_msg, state.dict(), "welcome"

        # 如果不是预设问题，则进入正常的任务处理流程
        state.is_new_conversation = False
        state.conversation_stage = "task_processing"

        # 检查用户输入是否是具体的任务描述
        if len(user_input.strip()) < 5:  # 输入太短，可能不是有效的任务描述
            return {
                "msg": "请提供更详细的任务描述，比如：\n- 您要处理什么类型的数据？\n- 想要实现什么样的预测功能？\n- 有什么特殊的要求吗？",
                "type": "need_more_info"
            }, state.dict(), "need_more_info"

        # 继续执行原有的算法分类和参数提取逻辑
        try:
            algorithm_result = self.classify_algorithm(user_input)
            param_result = self.extract_parameters(user_input)
            state.algorithm_type = algorithm_result['output'].algorithm_type
            state.current_params = param_result['output'].extracted_params
            state.missing_params = param_result['output'].missing_params
            if state.missing_params:
                state.awaiting_confirmation = True
                return {"msg": "请补充参数", "missing": state.missing_params}, state.dict(), "need_params"
            else:
                state.awaiting_confirmation = True
                return {"msg": "请确认参数", "params": state.current_params}, state.dict(), "need_confirm"
        except Exception as e:
            # 如果任务分类或参数提取失败，提供友好的错误信息
            return {
                "msg": "抱歉，我无法理解您的需求。请尝试更详细地描述您的算法训练任务，比如：\n- 使用什么算法（如LSTM、CNN等）\n- 处理什么类型的数据\n- 需要什么参数配置",
                "type": "parse_error",
                "error": str(e)
            }, state.dict(), "parse_error"

    async def handle_new_conversation_async(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """
        异步处理新建对话的引导逻辑
        """
        if should_log_debug():
            logger.debug(f"🔍 [调试] 进入新对话处理 - 用户输入: {user_input}")

        # 首先检查是否是预设问题的回答（优先级最高）
        preset_response = self.handle_preset_questions(user_input, state)
        if preset_response:
            if should_log_debug():
                logger.debug(f"🔍 [调试] 匹配预设问题，返回预设回答")
            return preset_response

        # 如果用户输入为空或是首次进入，显示欢迎引导
        if not user_input.strip() or user_input.strip() in ["", "你好", "hello", "hi"]:
            if should_log_debug():
                logger.debug(f"🔍 [调试] 用户输入为空或问候语，显示欢迎引导")
            state.has_shown_welcome = True
            state.conversation_stage = "welcome"
            welcome_msg = self.generate_welcome_guide()
            return welcome_msg, state.dict(), "welcome"

        # 如果不是预设问题，则进入正常的任务处理流程
        state.is_new_conversation = False
        state.conversation_stage = "task_processing"
        logger.info(f"🔍 [调试] 设置为非新对话，进入任务处理流程")

        # 检查用户输入是否是具体的任务描述（但要排除单个数字，因为可能是预设问题选择）
        if len(user_input.strip()) < 5 and not user_input.strip().isdigit():  # 输入太短且不是数字，可能不是有效的任务描述
            logger.info(f"🔍 [调试] 用户输入太短，要求更详细描述")
            return {
                "msg": "请提供更详细的任务描述，比如：\n- 您要处理什么类型的数据？\n- 想要实现什么样的预测功能？\n- 有什么特殊的要求吗？",
                "type": "need_more_info"
            }, state.dict(), "need_more_info"

        # 检查是否是数据准备相关的问题
        is_data_prep = self.is_data_preparation_question(user_input)
        logger.info(f"🔍 [调试] 数据准备问题检查结果: {is_data_prep}")
        if is_data_prep:
            logger.info(f"🔍 [调试] 识别为数据准备问题，调用数据准备处理")
            return await self.handle_data_preparation_question(user_input, state)

        # 检查是否是一般性咨询问题
        is_general = self.is_general_question(user_input)
        logger.info(f"🔍 [调试] 一般性问题检查结果: {is_general}")
        if is_general:
            logger.info(f"🔍 [调试] 识别为一般性问题，调用一般性问题处理")
            return await self.handle_general_question(user_input, state)

        # 进入历史算法检索流程
        if not state.history_search_completed:
            return await self.handle_history_algorithm_search(user_input, state)

        # 如果历史算法检索已完成，继续执行算法分类和参数提取逻辑
        try:
            algorithm_result = await self.classify_algorithm_async(user_input)
            param_result = await self.extract_parameters_async(user_input)
            state.algorithm_type = algorithm_result['output'].algorithm_type
            state.current_params = param_result['output'].extracted_params
            state.missing_params = param_result['output'].missing_params
            if state.missing_params:
                state.awaiting_confirmation = True
                return {"msg": "请补充参数", "missing": state.missing_params}, state.dict(), "need_params"
            else:
                state.awaiting_confirmation = True
                return {"msg": "请确认参数", "params": state.current_params}, state.dict(), "need_confirm"
        # except Exception as e:
        #     # 如果算法分类或参数提取失败，提供友好的错误信息
        #     return {
        #         "msg": "抱歉，我无法理解您的需求。请尝试更详细地描述您的算法训练任务，比如：\n- 使用什么算法（如LSTM、CNN等）\n- 处理什么类型的数据\n- 需要什么参数配置",
        #         "type": "parse_error",
        #         "error": str(e)
        #     }, state.dict(), "parse_error"

        except Exception as e:
            # 最外层异常处理 - 使用兜底回答系统
            print(f"[错误] process_user_input_async异常: {e}")

            # 检查是否是"Received empty model response"错误
            error_msg = str(e)
            if "Received empty model response" in error_msg or "empty" in error_msg.lower():
                # 使用兜底回答管理器生成智能回答
                fallback_response = fallback_manager.get_fallback_response(
                    user_input,
                    context={"state": state.dict() if 'state' in locals() else {}}
                )
                return fallback_response, state.dict() if 'state' in locals() else {}, "fallback_response"
            else:
                # 其他异常使用通用错误处理
                fallback_response = fallback_manager.get_fallback_response(
                    user_input,
                    context={"error": error_msg, "state": state.dict() if 'state' in locals() else {}}
                )
                return fallback_response, state.dict() if 'state' in locals() else {}, "error_fallback"

    def generate_welcome_guide(self) -> dict:
        """
        生成欢迎引导消息
        """
        return {
            "msg": "您好！欢迎使用算法生成智能体！\n\n请问有什么能帮您，我猜您想问：\n(1) 我想训练一个算法，我要怎么做?\n(2) 你能提供哪些帮助？\n\n请选择您感兴趣的问题，或者直接描述您的需求。",
            "type": "welcome_guide",
            "options": [
                {"key": "1", "text": "我想训练一个算法，我要怎么做?"},
                {"key": "2", "text": "你能提供哪些帮助？"}
            ]
        }

    def handle_preset_questions(self, user_input: str, state: PredictiveAgentState) -> Optional[Tuple[dict, dict, str]]:
        """
        处理预设问题的选择
        """
        user_input_lower = user_input.lower().strip()

        # 检查是否选择了预设问题1 - 使用更精确的匹配
        if (user_input_lower == "1" or
            "我想训练一个算法" in user_input_lower or
            "我要怎么做" in user_input_lower or
            "如何训练算法" in user_input_lower):
            state.is_new_conversation = False
            state.conversation_stage = "guided"
            return {
                "msg": "很好！我来为您介绍算法训练的流程：\n\n1. **数据准备**：首先需要准备训练数据，确保数据格式正确\n2. **算法选择**：根据您的任务类型选择合适的算法（如LSTM用于时序预测，CNN用于图像处理）\n3. **参数设置**：配置算法参数，如学习率、批次大小、训练轮数等\n4. **模型训练**：启动训练过程，监控训练进度\n5. **结果评估**：查看训练结果和模型性能\n\n现在请描述您的具体需求，比如：\n- 您要处理什么类型的数据？\n- 想要实现什么样的预测功能？\n- 有什么特殊的要求吗？",
                "type": "algorithm_guide"
            }, state.dict(), "guided"

        # 检查是否选择了预设问题2 - 使用更精确的匹配
        if (user_input_lower == "2" or
            "你能提供哪些帮助" in user_input_lower or
            "能做什么" in user_input_lower or
            "功能介绍" in user_input_lower):
            state.is_new_conversation = False
            state.conversation_stage = "guided"
            return {
                "msg": "我是一个旨在帮助您进行数据分析和算法训练的智能助手。我可以为您提供以下帮助：\n\n🔧 **协助算法训练流程**\n- 从参数设置到模型评估，为您提供每一步的操作指引\n- 支持LSTM、CNN等多种算法类型\n- 智能参数推荐和优化建议\n\n📊 **监控算法训练进程**\n- 实时查看训练进度和状态\n- 查看详细的训练日志信息\n- 获取训练结果和性能指标\n\n❓ **解答技术问题**\n- 解释机器学习概念和算法原理\n- 提供参数调优的最佳实践\n- 协助解决训练过程中的问题\n\n📋 **生成分析报告**\n- 自动生成详细的训练报告\n- 提供模型性能分析和改进建议\n- 支持PDF格式报告下载\n\n现在请告诉我您的具体需求，我将为您提供针对性的帮助！",
                "type": "feature_introduction"
            }, state.dict(), "guided"

        return None

    def is_data_preparation_question(self, user_input: str) -> bool:
        """
        判断是否是数据准备相关的问题
        """
        data_keywords = [
            "数据", "训练数据", "准备", "数据集", "数据格式", "数据预处理",
            "特征", "标签", "输入", "输出", "样本", "数据量", "数据质量",
            "data", "dataset", "feature", "label", "sample", "preprocessing"
        ]

        question_keywords = [
            "需要", "准备", "什么", "哪些", "如何", "怎么", "要求",
            "格式", "类型", "规范", "标准"
        ]

        user_input_lower = user_input.lower()

        # 检查是否包含数据相关关键词和问题关键词
        has_data_keyword = any(keyword in user_input_lower for keyword in data_keywords)
        has_question_keyword = any(keyword in user_input_lower for keyword in question_keywords)

        return has_data_keyword and has_question_keyword

    def is_general_question(self, user_input: str) -> bool:
        """
        判断是否是一般性咨询问题（包括技术咨询）
        """
        # 首先检查是否是具体的任务请求，如果是则不应该被判断为一般性问题
        specific_task_keywords = [
            "预测", "批量预测", "模型预测", "推理", "进行预测", "想要预测",
            "报告", "生成报告", "训练报告", "分析报告", "性能报告",
            "我有", "我想", "帮我", "为我", "给我"
        ]

        user_input_lower = user_input.lower()

        # 如果包含具体任务关键词，则不是一般性问题
        has_specific_task = any(keyword in user_input_lower for keyword in specific_task_keywords)
        if has_specific_task:
            return False

        general_keywords = [
            "帮助", "功能", "能力", "支持", "提供", "服务", "介绍",
            "什么是", "如何使用", "怎么用", "操作", "指导", "教程",
            "help", "support", "function", "capability", "service"
        ]

        # 技术咨询相关关键词（移除了可能导致误判的词）
        technical_keywords = [
            "深度学习", "机器学习", "神经网络", "算法", "模型",
            "参数", "优化", "调优", "学习率", "批次", "epoch", "损失函数",
            "梯度", "过拟合", "欠拟合", "正则化", "dropout", "激活函数",
            "卷积", "池化", "lstm", "cnn", "rnn", "transformer",
            "准确率", "精确率", "召回率", "f1", "评估", "验证",
            "特征", "数据", "预处理", "归一化", "标准化"
        ]

        # 疑问词模式（只保留明确的疑问词）
        question_patterns = [
            "如何", "怎么", "为什么", "什么是", "请问",
            "有哪些", "区别", "差异", "选择", "推荐", "建议"
        ]

        # 检查是否包含一般性关键词
        has_general_keyword = any(keyword in user_input_lower for keyword in general_keywords)

        # 检查是否包含技术关键词
        has_technical_keyword = any(keyword in user_input_lower for keyword in technical_keywords)

        # 检查是否包含疑问词
        has_question_pattern = any(pattern in user_input_lower for pattern in question_patterns)

        # 如果包含一般性关键词，或者同时包含疑问词和技术关键词，则认为是一般性问题
        return has_general_keyword or (has_question_pattern and has_technical_keyword)

    def is_algorithm_related_question(self, user_input: str) -> bool:
        """
        判断问题是否与算法训练相关
        """
        algorithm_keywords = [
            # 算法类型
            "算法", "模型", "神经网络", "深度学习", "机器学习", "人工智能",
            "lstm", "cnn", "rnn", "transformer", "bert", "gpt",
            "线性回归", "逻辑回归", "决策树", "随机森林", "svm", "kmeans",

            # 训练相关
            "训练", "学习", "拟合", "优化", "调优", "参数", "超参数",
            "学习率", "批次", "epoch", "迭代", "收敛", "梯度", "反向传播",
            "损失函数", "目标函数", "代价函数", "激活函数",

            # 数据处理
            "数据", "特征", "预处理", "归一化", "标准化", "编码", "降维",
            "数据增强", "数据清洗", "特征工程", "特征选择", "特征提取",

            # 模型评估
            "评估", "验证", "测试", "准确率", "精确率", "召回率", "f1",
            "auc", "roc", "混淆矩阵", "交叉验证", "过拟合", "欠拟合",

            # 正则化和优化
            "正则化", "dropout", "batch normalization", "优化器",
            "adam", "sgd", "rmsprop", "momentum",

            # 应用场景
            "预测", "分类", "回归", "聚类", "检测", "识别", "生成",
            "推荐", "时序", "图像", "文本", "语音", "nlp", "cv"
        ]

        user_input_lower = user_input.lower()
        return any(keyword in user_input_lower for keyword in algorithm_keywords)

    async def handle_data_preparation_question(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """
        处理数据准备相关的问题 - 根据当前阶段调用相应的模型
        """
        # 判断当前处于哪个阶段
        logger.info("🚀 [多模型调用] 开始三个智能体模型调用流程")
        logger.info(f"🚀 [多模型调用] 用户问题: {user_input}")

        current_stage = self.determine_current_stage(state)

        logger.info(f"🚀 [多模型调用] 根据阶段选择模型: {current_stage}")
        logger.info(f"🚀 [多模型调用] 对应模型类型: {self.get_model_type_for_stage(current_stage)}")

        try:
            if current_stage == "parameter_recommendation":
                # 参数推荐阶段 - 调用微调小型模型
                logger.info("🚀 [多模型调用] 🔧 选择参数推荐微调模型")
                response = await self.call_parameter_recommendation_model(user_input, state)
            elif current_stage in ["training", "prediction"]:
                # 智能训练、智能预测阶段 - 调用通用大模型
                logger.info("🚀 [多模型调用] 🤖 选择通用大语言模型")
                response = await self.call_general_model(user_input, state)
            elif current_stage == "result_analysis":
                # 结果分析阶段 - 调用微调小型模型
                logger.info("🚀 [多模型调用] 📈 选择结果分析微调模型")
                response = await self.call_result_analysis_model(user_input, state)
            else:
                # 默认阶段 - 调用通用大模型
                logger.info("🚀 [多模型调用] 🤖 默认选择通用大语言模型")
                response = await self.call_general_model(user_input, state)

            return {
                "msg": response,
                "type": "data_preparation_guide",
                "stage": current_stage,
                "model_used": self.get_model_type_for_stage(current_stage)
            }, state.dict(), "data_guide"

        except Exception as e:
            logger.error(f"处理数据准备问题失败: {e}")
            # 兜底回答
            fallback_msg = f"抱歉，我在处理您的数据准备问题时遇到了问题。请您详细描述一下您的具体需求，我会尽力为您提供帮助。\n\n错误信息：{str(e)}"
            return {
                "msg": fallback_msg,
                "type": "error_fallback"
            }, state.dict(), "error"

    def determine_current_stage(self, state: PredictiveAgentState) -> str:
        """
        判断当前处于哪个阶段
        """
        logger.info("🎯 [阶段判断] 开始判断当前所处阶段")
        logger.info(f"🎯 [阶段判断] 状态信息 - awaiting_confirmation: {state.awaiting_confirmation}")
        logger.info(f"🎯 [阶段判断] 状态信息 - missing_params: {state.missing_params}")
        logger.info(f"🎯 [阶段判断] 状态信息 - task_status: {state.task_status}")
        logger.info(f"🎯 [阶段判断] 状态信息 - report: {bool(state.report)}")

        # 根据状态判断当前阶段
        if state.awaiting_confirmation or state.missing_params:
            stage = "parameter_recommendation"
            logger.info(f"🎯 [阶段判断] ✅ 判断结果: {stage} (参数推荐阶段 - 微调小型模型)")
            return stage
        elif state.task_status in ["created", "running"]:
            stage = "training"
            logger.info(f"🎯 [阶段判断] ✅ 判断结果: {stage} (智能训练阶段 - 通用大模型)")
            return stage
        elif state.task_status == "completed" and not state.report:
            stage = "prediction"
            logger.info(f"🎯 [阶段判断] ✅ 判断结果: {stage} (智能预测阶段 - 通用大模型)")
            return stage
        elif state.report or state.task_status == "analyzed":
            stage = "result_analysis"
            logger.info(f"🎯 [阶段判断] ✅ 判断结果: {stage} (结果分析阶段 - 微调小型模型)")
            return stage
        else:
            # 默认为参数推荐阶段
            stage = "parameter_recommendation"
            logger.info(f"🎯 [阶段判断] ✅ 判断结果: {stage} (默认参数推荐阶段 - 微调小型模型)")
            return stage

    def get_model_type_for_stage(self, stage: str) -> str:
        """
        获取阶段对应的模型类型
        """
        model_mapping = {
            "parameter_recommendation": "微调小型模型",
            "training": "通用大模型",
            "prediction": "通用大模型",
            "result_analysis": "微调小型模型"
        }
        return model_mapping.get(stage, "通用大模型")

    async def call_parameter_recommendation_model(self, user_input: str, state: PredictiveAgentState) -> str:
        """
        调用参数推荐微调模型
        """
        logger.info("🔧 [参数推荐模型] 开始调用微调小型模型")
        logger.info(f"🔧 [参数推荐模型] 用户输入: {user_input}")
        logger.info(f"🔧 [参数推荐模型] 当前阶段: parameter_recommendation")
        logger.info(f"🔧 [参数推荐模型] 算法类型: {state.algorithm_type or '未确定'}")

        try:
            # 构建参数推荐专用的提示词
            prompt = f"""
你是一个专业的算法参数推荐专家，使用微调后的小型模型为用户提供精准的参数建议。

用户问题：{user_input}

当前状态：
- 算法类型：{state.algorithm_type or '未确定'}
- 当前参数：{state.current_params}
- 缺失参数：{state.missing_params}

请基于你的专业知识，为用户提供详细的参数推荐和数据准备指导。
重点关注：
1. 具体的参数数值建议
2. 参数选择的理论依据
3. 数据格式和预处理要求
4. 性能优化建议
"""

            logger.info(f"🔧 [参数推荐模型] 构建的提示词长度: {len(prompt)} 字符")
            logger.info("🔧 [参数推荐模型] 正在调用 extract_parameters_async 方法...")

            # 调用参数提取Agent（作为微调模型的代理）
            result = await self.extract_parameters_async(prompt)

            logger.info(f"🔧 [参数推荐模型] LLM调用完成，返回结果类型: {type(result)}")
            logger.info(f"🔧 [参数推荐模型] LLM返回结果: {result}")

            # 格式化回复
            response = f"📊 **专业参数推荐**（基于微调模型）\n\n"
            response += f"针对您的问题「{user_input}」，我基于专业的参数推荐模型为您分析：\n\n"

            if result and 'output' in result:
                extracted_params = result['output'].extracted_params
                logger.info(f"🔧 [参数推荐模型] 提取到的参数: {extracted_params}")
                if extracted_params:
                    response += "**推荐参数配置：**\n"
                    for param, value in extracted_params.items():
                        response += f"- {param}: {value}\n"
                    response += "\n"

            response += "**数据准备建议：**\n"
            response += "- 确保数据质量和完整性\n"
            response += "- 按照7:2:1比例划分训练、验证、测试集\n"
            response += "- 进行适当的数据预处理和特征工程\n\n"
            response += "💡 这些建议基于我们的专业参数推荐模型，针对您的具体场景进行了优化。"

            logger.info(f"🔧 [参数推荐模型] 最终回复长度: {len(response)} 字符")
            logger.info("🔧 [参数推荐模型] ✅ 微调小型模型调用成功完成")

            return response

        except Exception as e:
            logger.error(f"🔧 [参数推荐模型] ❌ 调用失败: {e}")
            logger.error(f"🔧 [参数推荐模型] ❌ 错误详情: {str(e)}")
            return f"参数推荐模型暂时不可用，请稍后重试。错误：{str(e)}"

    async def call_general_model(self, user_input: str, state: PredictiveAgentState) -> str:
        """
        调用通用大模型
        """
        logger.info("🤖 [通用大模型] 开始调用通用大语言模型")
        logger.info(f"🤖 [通用大模型] 用户输入: {user_input}")
        logger.info(f"🤖 [通用大模型] 当前阶段: training/prediction")
        logger.info(f"🤖 [通用大模型] 任务状态: {state.task_status or '未开始'}")
        logger.info(f"🤖 [通用大模型] 任务ID: {state.task_id or '无'}")

        try:
            # 构建通用模型的提示词
            prompt = f"""
你是一个智能训练和预测助手，使用通用大语言模型为用户提供全面的指导。

用户问题：{user_input}

当前训练状态：
- 任务状态：{state.task_status or '未开始'}
- 任务ID：{state.task_id or '无'}
- 算法类型：{state.algorithm_type or '未确定'}

请为用户提供详细的训练指导和预测建议，包括：
1. 训练过程监控要点
2. 常见问题和解决方案
3. 性能优化建议
4. 预测结果解读
"""

            logger.info(f"🤖 [通用大模型] 构建的提示词长度: {len(prompt)} 字符")
            logger.info("🤖 [通用大模型] 正在调用 classify_interaction_async 方法...")

            # 调用交互分类Agent（作为通用模型的代理）
            result = await self.classify_interaction_async(prompt)

            logger.info(f"🤖 [通用大模型] LLM调用完成，返回结果类型: {type(result)}")
            logger.info(f"🤖 [通用大模型] LLM返回结果: {result}")

            # 格式化回复
            response = f"🤖 **智能训练指导**（基于通用大模型）\n\n"
            response += f"关于您的问题「{user_input}」，我为您提供以下专业建议：\n\n"

            response += "**训练监控要点：**\n"
            response += "- 实时监控损失函数变化\n"
            response += "- 观察验证集性能指标\n"
            response += "- 注意过拟合和欠拟合现象\n\n"

            response += "**数据处理建议：**\n"
            response += "- 确保数据分布的一致性\n"
            response += "- 处理异常值和缺失值\n"
            response += "- 进行适当的数据增强\n\n"

            response += "💡 这些建议基于通用大语言模型的广泛知识，适用于多种训练场景。"

            logger.info(f"🤖 [通用大模型] 最终回复长度: {len(response)} 字符")
            logger.info("🤖 [通用大模型] ✅ 通用大语言模型调用成功完成")

            return response

        except Exception as e:
            logger.error(f"🤖 [通用大模型] ❌ 调用失败: {e}")
            logger.error(f"🤖 [通用大模型] ❌ 错误详情: {str(e)}")
            return f"通用模型暂时不可用，请稍后重试。错误：{str(e)}"

    async def call_result_analysis_model(self, user_input: str, state: PredictiveAgentState) -> str:
        """
        调用结果分析微调模型
        """
        logger.info("📈 [结果分析模型] 开始调用微调小型模型")
        logger.info(f"📈 [结果分析模型] 用户输入: {user_input}")
        logger.info(f"📈 [结果分析模型] 当前阶段: result_analysis")
        logger.info(f"📈 [结果分析模型] 任务ID: {state.task_id or '无'}")
        logger.info(f"📈 [结果分析模型] 报告状态: {state.report or '未生成'}")

        try:
            # 构建结果分析专用的提示词
            prompt = f"""
你是一个专业的结果分析专家，使用微调后的小型模型为用户提供深度的结果分析。

用户问题：{user_input}

训练结果：
- 任务ID：{state.task_id or '无'}
- 最后API结果：{state.last_api_result}
- 报告状态：{state.report or '未生成'}

请基于专业的结果分析知识，为用户提供：
1. 详细的性能指标解读
2. 结果质量评估
3. 改进建议和优化方向
4. 实际应用指导
"""

            logger.info(f"📈 [结果分析模型] 构建的提示词长度: {len(prompt)} 字符")
            logger.info("📈 [结果分析模型] 正在调用 classify_algorithm_async 方法...")

            # 调用算法分类Agent（作为结果分析模型的代理）
            result = await self.classify_algorithm_async(prompt)

            logger.info(f"📈 [结果分析模型] LLM调用完成，返回结果类型: {type(result)}")
            logger.info(f"📈 [结果分析模型] LLM返回结果: {result}")

            # 格式化回复
            response = f"📈 **专业结果分析**（基于微调模型）\n\n"
            response += f"针对您的问题「{user_input}」，我基于专业的结果分析模型为您解读：\n\n"

            response += "**性能指标分析：**\n"
            if state.last_api_result:
                response += f"- 当前模型表现：{state.last_api_result.get('result', {}).get('accuracy', '待评估')}\n"
            response += "- 建议关注指标：准确率、召回率、F1分数\n"
            response += "- 性能基准对比分析\n\n"

            response += "**质量评估：**\n"
            response += "- 模型泛化能力评估\n"
            response += "- 过拟合风险分析\n"
            response += "- 实际应用可行性\n\n"

            response += "💡 这些分析基于我们的专业结果分析模型，为您的模型提供深度洞察。"

            if should_log_debug():
                logger.debug(f"📈 [结果分析模型] 最终回复长度: {len(response)} 字符")
                logger.debug("📈 [结果分析模型] ✅ 微调小型模型调用成功完成")

            return response

        except Exception as e:
            logger.error(f"📈 [结果分析模型] ❌ 调用失败: {e}")
            if should_log_debug():
                logger.debug(f"📈 [结果分析模型] ❌ 错误详情: {str(e)}")
            return f"结果分析模型暂时不可用，请稍后重试。错误：{str(e)}"

    async def handle_general_question(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """
        处理一般性咨询问题 - 首先检查是否为算法相关问题
        """
        # 首先检查问题是否与算法相关
        if not self.is_algorithm_related_question(user_input):
            # 如果不是算法相关问题，引导用户
            guidance_msg = """
🤖 **算法智能体专业服务范围**

我是专业的算法训练智能体，专注于为您提供以下服务：

🔧 **算法推荐与选择**
- 根据您的数据类型和任务需求推荐最适合的算法
- LSTM、CNN、RNN、Transformer等深度学习模型选择指导

⚙️ **参数优化与调优**
- 学习率、批次大小、训练轮数等超参数推荐
- 优化器选择（Adam、SGD、RMSprop）和配置建议

📊 **训练监控与评估**
- 模型训练过程监控和性能评估
- 过拟合、欠拟合问题诊断和解决方案

🎯 **专业技术咨询**
- 深度学习、机器学习相关技术问题解答
- 数据预处理、特征工程、模型评估等专业指导

💡 **请重新提问算法相关问题，例如：**
- "如何选择合适的学习率？"
- "LSTM和CNN有什么区别？"
- "如何解决模型过拟合问题？"
- "数据预处理有哪些步骤？"

我将为您提供专业、详细的技术指导！
"""
            return {
                "msg": guidance_msg,
                "type": "scope_guidance"
            }, state.dict(), "scope_guidance"

        # 如果是算法相关问题，调用专业咨询处理
        try:
            return await self.handle_general_consultation_async(user_input, state)
        except Exception as e:
            logger.error(f"处理算法咨询问题失败: {e}")
            # 兜底回答
            fallback_msg = f"""
抱歉，我在处理您的算法咨询问题时遇到了一些困难。

🔧 **我可以为您提供的服务：**
- 算法选择和推荐
- 参数优化和调优指导
- 训练过程监控和问题诊断
- 模型评估和性能分析

请尝试重新描述您的问题，或者提供更多具体信息，我会尽力为您提供专业的技术指导。

错误信息：{str(e)}
"""
            return {
                "msg": fallback_msg,
                "type": "error_fallback"
            }, state.dict(), "error"

    async def handle_general_consultation_async(self, user_input: str, state: PredictiveAgentState):
        """异步处理一般性咨询问题，提供技术指导和建议"""
        try:
            # 使用LLM生成专业的技术回答
            consultation_prompt = f"""
你是一个专业的机器学习算法训练专家。用户正在进行算法训练项目，现在向你咨询技术问题。

用户当前状态：
- 任务类型：{state.task_type if state.task_type else '未指定'}
- 任务ID：{state.task_id if state.task_id else '无'}
- 当前参数：{state.current_params if state.current_params else '无'}

用户问题：{user_input}

请提供专业、详细、实用的回答，包括：
1. 直接回答用户的问题
2. 提供具体的操作建议
3. 如果涉及技术细节，请给出清晰的解释
4. 如果有相关的最佳实践，请一并说明

回答要求：
- 专业且易懂
- 结构清晰
- 实用性强
- 针对用户的具体情况
"""

            # 使用简化的LLM调用方式避免事件循环冲突
            try:
                # 创建专门的咨询Agent
                class ConsultationResponse(BaseModel):
                    answer: str = Field(description="专业的技术咨询回答")

                consultation_agent = Agent(
                    model=llm_instance,
                    output_type=ConsultationResponse,
                    system_prompt="你是一个专业的机器学习算法训练专家，擅长为用户提供技术指导和建议。请提供专业、详细、实用的回答。"
                )

                # 使用run_sync避免异步冲突
                import asyncio
                if asyncio.get_event_loop().is_running():
                    # 如果事件循环正在运行，使用线程池执行
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(consultation_agent.run_sync, consultation_prompt)
                        result = future.result()
                else:
                    # 如果事件循环未运行，直接使用run_sync
                    result = consultation_agent.run_sync(consultation_prompt)

                answer = result.data.answer
            except Exception as inner_e:
                logger.error(f"LLM调用失败: {inner_e}")
                # 使用兜底回答
                answer = self._get_fallback_consultation_answer(user_input)

            return {
                "msg": answer,
                "type": "consultation"
            }, state.dict(), "consultation"

        except Exception as e:
            logger.error(f"处理一般性咨询失败: {e}")
            return {
                "msg": "抱歉，我在处理您的问题时遇到了一些困难。请稍后再试，或者重新描述您的问题。",
                "type": "consultation_error"
            }, state.dict(), "error"

    def _get_fallback_consultation_answer(self, user_input: str) -> str:
        """
        获取兜底的咨询回答
        """
        return f"""
🤖 **算法智能体技术咨询**

感谢您的技术咨询！虽然我在调用专业模型时遇到了一些困难，但我可以为您提供一些基础的指导：

📚 **常见算法问题解答**：

如果您询问的是：
- **深度学习相关**：建议关注学习率调优、批次大小设置、激活函数选择
- **模型训练相关**：重点考虑数据预处理、正则化方法、优化器选择
- **性能优化相关**：可以尝试调整网络结构、使用预训练模型、数据增强

💡 **建议**：
1. 请尝试重新描述您的具体问题
2. 提供更多技术细节和背景信息
3. 说明您当前遇到的具体困难

我会继续为您提供专业的算法训练指导！

**您的问题**：{user_input}
"""

    async def call_parameter_recommendation_model_for_general(self, user_input: str, state: PredictiveAgentState) -> str:
        """
        为一般性问题调用参数推荐微调模型
        """
        prompt = f"""
你是参数推荐专家，用户询问：{user_input}

请从参数推荐的角度为用户介绍相关功能和建议。
"""
        try:
            result = await self.extract_parameters_async(prompt)
            return f"🔧 **参数推荐专家**（微调模型）\n\n我专注于为您提供精准的算法参数推荐服务。基于您的问题，我可以帮助您优化算法参数配置，提升模型性能。"
        except:
            return "🔧 **参数推荐专家**（微调模型）\n\n我是专门的参数推荐助手，可以为您提供专业的算法参数建议。"

    async def call_general_model_for_general(self, user_input: str, state: PredictiveAgentState) -> str:
        """
        为一般性问题调用通用大模型
        """
        prompt = f"""
你是智能训练助手，用户询问：{user_input}

请从训练监控和预测的角度为用户介绍相关功能。
"""
        try:
            result = await self.classify_interaction_async(prompt)
            return f"🤖 **智能训练助手**（通用大模型）\n\n我可以为您提供全面的训练监控、预测指导和问题解决方案。基于广泛的知识库，我能帮助您处理各种训练过程中的问题。"
        except:
            return "🤖 **智能训练助手**（通用大模型）\n\n我是智能训练和预测助手，可以为您提供训练监控、异常处理、预测优化等全方位服务。"

    async def call_result_analysis_model_for_general(self, user_input: str, state: PredictiveAgentState) -> str:
        """
        为一般性问题调用结果分析微调模型
        """
        prompt = f"""
你是结果分析专家，用户询问：{user_input}

请从结果分析的角度为用户介绍相关功能。
"""
        try:
            result = await self.classify_algorithm_async(prompt)
            return f"📈 **结果分析专家**（微调模型）\n\n我专注于深度分析训练结果和模型性能。我可以为您提供专业的性能评估、质量分析和优化建议，帮助您深入理解模型表现。"
        except:
            return "📈 **结果分析专家**（微调模型）\n\n我是专业的结果分析助手，可以为您提供深度的模型性能分析和优化建议。"

    async def handle_history_algorithm_search(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """
        处理历史算法检索（简化版）
        """
        try:
            # 检查项目ID
            if not state.current_project_id:
                return {
                    "msg": "❌ **项目ID缺失**\n\n无法获取历史算法列表，请确保前端传入了正确的项目ID。\n\n让我们直接为您创建新的算法。请提供详细的需求描述。",
                    "type": "project_id_missing"
                }, state.dict(), "project_id_missing"

            # 调用历史算法检索Agent获取算法列表
            search_result = await self.history_algorithm_agent.process_user_request(user_input, state.current_project_id)

            if "error" in search_result:
                # 如果检索失败，直接进入新建算法流程
                state.history_search_completed = True
                state.conversation_stage = "task_processing"
                return {
                    "msg": f"❌ **算法平台连接失败**\n\n{search_result['error']}\n\n由于无法连接到算法平台，让我们直接为您创建新的算法。请提供更详细的需求描述。",
                    "type": "api_error"
                }, state.dict(), "api_error"

            history_algorithms = search_result.get("history_algorithms", [])

            # 保存检索结果到状态
            state.recommended_algorithms = history_algorithms
            state.history_search_completed = True

            if len(history_algorithms) > 0:
                # 找到了历史算法，询问用户选择
                state.awaiting_algorithm_choice = True
                state.conversation_stage = "algorithm_choice"

                # 构建算法列表消息
                msg = f"🔍 **历史算法列表**\n\n"
                msg += f"以下是可用的历史算法列表（共 {len(history_algorithms)} 个）：\n\n"

                for i, algo in enumerate(history_algorithms[:5], 1):  # 最多显示5个
                    msg += f"**{i}. {algo['algorithm_name']}**\n"
                    msg += f"   - 算法类型：{algo['algorithm_type']}\n"
                    msg += f"   - 适用场景：{algo['use_case']}\n"
                    msg += f"   - 性能指标：准确率 {algo['performance_metrics'].get('accuracy', 'N/A')}\n"
                    msg += f"   - 使用次数：{algo['usage_count']} 次\n"
                    msg += f"   - 描述：{algo['description'][:100]}...\n\n"

                if len(history_algorithms) > 5:
                    msg += f"... 还有 {len(history_algorithms) - 5} 个算法\n\n"

                msg += "请选择您的操作：\n"
                msg += "1️⃣ 使用历史算法（输入算法编号，如 '1'）\n"
                msg += "2️⃣ 创建新的算法（输入 '新建' 或 'new'）\n"
                msg += "3️⃣ 查看更多算法（输入 '更多' 或 'more'）"

                return {
                    "msg": msg,
                    "type": "algorithm_list",
                    "algorithms": history_algorithms,
                    "total_count": search_result.get("total_count", len(history_algorithms))
                }, state.dict(), "algorithm_choice"
            else:
                # 没有找到历史算法，直接进入新建流程
                state.conversation_stage = "task_processing"

                msg = f"🔍 **历史算法检索结果**\n\n"
                msg += f"当前系统中暂无可用的历史算法。\n\n"
                msg += "💡 **建议**：为您创建新的算法训练任务。\n\n"
                msg += "请提供更详细的算法参数要求，我将为您进行参数提取和配置。"

                return {
                    "msg": msg,
                    "type": "create_new_algorithm"
                }, state.dict(), "create_new"

        except Exception as e:
            # 异常处理，直接进入新建算法流程
            state.history_search_completed = True
            state.conversation_stage = "task_processing"
            return {
                "msg": f"历史算法检索遇到异常：{str(e)}\n\n让我们直接为您创建新的算法。请提供详细的需求描述。",
                "type": "search_exception"
            }, state.dict(), "search_exception"

    async def handle_algorithm_choice(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """
        处理用户的算法选择
        """
        user_input_lower = user_input.strip().lower()

        try:
            # 检查是否选择使用历史算法
            if user_input_lower in ['1', '2', '3'] and len(state.recommended_algorithms) > 0:
                try:
                    choice_index = int(user_input_lower) - 1
                    if 0 <= choice_index < len(state.recommended_algorithms):
                        selected_algo = state.recommended_algorithms[choice_index]
                        state.selected_algorithm_id = selected_algo['algorithm_id']
                        state.current_params = selected_algo.get('parameters', {})
                        state.awaiting_algorithm_choice = False
                        state.awaiting_confirmation = True
                        state.conversation_stage = "param_confirmation"

                        msg = f"✅ **已选择历史算法**：{selected_algo['algorithm_name']}\n\n"
                        msg += f"📋 **算法详情**：\n"
                        msg += f"- 算法类型：{selected_algo['algorithm_type']}\n"
                        msg += f"- 适用场景：{selected_algo['use_case']}\n"
                        msg += f"- 创建者：{selected_algo['created_by']}\n"
                        msg += f"- 使用次数：{selected_algo['usage_count']} 次\n\n"

                        msg += f"⚙️ **算法参数配置**：\n"
                        for key, value in selected_algo.get('parameters', {}).items():
                            msg += f"- {key}: {value}\n"

                        msg += f"\n🔄 **下一步操作**：\n"
                        msg += f"- 如需调整参数，请说明具体调整内容\n"
                        msg += f"- 如参数无需调整，请输入 '确认' 开始训练\n"
                        msg += f"- 如需重新选择，请输入 '重新选择'"

                        return {
                            "msg": msg,
                            "type": "algorithm_selected",
                            "selected_algorithm": selected_algo,
                            "params": state.current_params
                        }, state.dict(), "algorithm_selected"
                    else:
                        return {
                            "msg": f"❌ 选择无效，请输入 1-{len(state.recommended_algorithms)} 之间的数字，或输入 '新建' 创建新算法。",
                            "type": "invalid_choice"
                        }, state.dict(), "invalid_choice"
                except ValueError:
                    pass  # 继续检查其他选项

            # 检查是否选择创建新算法
            elif user_input_lower in ['新建', 'new', '创建新算法', '新算法', '4']:
                state.awaiting_algorithm_choice = False
                state.conversation_stage = "task_processing"

                return {
                    "msg": "✅ **选择创建新算法**\n\n请详细描述您的算法需求，我将为您进行参数提取和配置。\n\n例如：\n- 算法类型（LSTM、CNN等）\n- 数据特征和类型\n- 预期的性能要求\n- 特殊的参数偏好",
                    "type": "create_new_confirmed"
                }, state.dict(), "create_new_confirmed"

            # 检查是否要查看详情
            elif user_input_lower in ['详情', 'details', '更多详情', '查看详情']:
                if len(state.recommended_algorithms) > 0:
                    msg = "📋 **历史算法详细信息**\n\n"
                    for i, algo in enumerate(state.recommended_algorithms, 1):
                        msg += f"**{i}. {algo['algorithm_name']}** (ID: {algo['algorithm_id']})\n"
                        msg += f"   - 算法类型：{algo['algorithm_type']}\n"
                        msg += f"   - 数据类型：{algo['data_type']}\n"
                        msg += f"   - 适用场景：{algo['use_case']}\n"
                        msg += f"   - 创建者：{algo['created_by']}\n"
                        msg += f"   - 创建时间：{algo['created_at']}\n"
                        msg += f"   - 最后使用：{algo.get('last_used', 'N/A')}\n"
                        msg += f"   - 使用次数：{algo['usage_count']} 次\n"
                        msg += f"   - 性能指标：{algo['performance_metrics']}\n"
                        msg += f"   - 详细描述：{algo['description']}\n"
                        msg += f"   - 参数配置：{algo.get('parameters', {})}\n"
                        msg += f"   - 标签：{', '.join(algo.get('tags', []))}\n\n"

                    msg += "请选择您的操作：\n"
                    msg += "1️⃣ 使用推荐的历史算法（输入算法编号）\n"
                    msg += "2️⃣ 创建新的算法（输入 '新建'）"

                    return {
                        "msg": msg,
                        "type": "algorithm_details",
                        "algorithms": state.recommended_algorithms
                    }, state.dict(), "algorithm_details"
                else:
                    return {
                        "msg": "❌ 没有可查看的算法详情。请输入 '新建' 创建新算法。",
                        "type": "no_details"
                    }, state.dict(), "no_details"

            # 检查是否要重新选择
            elif user_input_lower in ['重新选择', 'reselect', '重选', '返回']:
                # 重新显示算法选择界面
                similar_algorithms = state.recommended_algorithms
                msg = f"🔍 **重新选择算法**\n\n"
                msg += f"可选的历史算法：\n\n"

                for i, algo in enumerate(similar_algorithms[:3], 1):
                    msg += f"**{i}. {algo['algorithm_name']}**\n"
                    msg += f"   - 算法类型：{algo['algorithm_type']}\n"
                    msg += f"   - 适用场景：{algo['use_case']}\n"
                    msg += f"   - 性能指标：准确率 {algo['performance_metrics'].get('accuracy', 'N/A')}\n\n"

                msg += "请选择您的操作：\n"
                msg += "1️⃣ 使用推荐的历史算法（输入算法编号）\n"
                msg += "2️⃣ 创建新的算法（输入 '新建'）\n"
                msg += "3️⃣ 查看更多详情（输入 '详情'）"

                return {
                    "msg": msg,
                    "type": "reselect_algorithm",
                    "algorithms": similar_algorithms
                }, state.dict(), "reselect"

            else:
                # 无效输入，提示用户
                return {
                    "msg": f"❌ 输入无效。请选择：\n1️⃣ 输入数字 1-{len(state.recommended_algorithms)} 选择历史算法\n2️⃣ 输入 '新建' 创建新算法\n3️⃣ 输入 '详情' 查看算法详情",
                    "type": "invalid_input"
                }, state.dict(), "invalid_input"

        except Exception as e:
            return {
                "msg": f"处理选择时发生错误：{str(e)}\n请重新选择或输入 '新建' 创建新算法。",
                "type": "choice_error"
            }, state.dict(), "choice_error"

    async def handle_algorithm_recommendation_async(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """处理算法推荐请求"""
        try:
            logger.info(f"🎯 [算法推荐] 处理用户请求: {user_input}")

            # 调用算法分类Agent进行深度分析
            algorithm_result = await self.classify_algorithm_async(user_input)
            algorithm_type = algorithm_result['output'].algorithm_type
            confidence = algorithm_result['output'].confidence
            reason = algorithm_result['output'].reason

            # 构建专业的算法推荐回复
            response = f"🎯 **算法推荐**\n\n基于您的需求「{user_input}」，我为您推荐：\n\n"
            response += f"**🏆 推荐算法：{algorithm_type}**\n- 推荐理由：{reason}\n- 置信度：{confidence:.1%}\n\n"

            if algorithm_type == "LSTM":
                response += "**📊 LSTM详细说明：**\n- 适用场景：时间序列预测、序列建模\n- 核心优势：处理长期依赖关系\n- 推荐参数：hidden_size=128, learning_rate=0.001\n"
            elif algorithm_type == "CNN":
                response += "**📊 CNN详细说明：**\n- 适用场景：图像识别、计算机视觉\n- 核心优势：局部特征提取\n- 推荐参数：filters=32, kernel_size=3\n"
            elif algorithm_type == "随机森林":
                response += "**📊 随机森林详细说明：**\n- 适用场景：表格数据分类回归\n- 核心优势：抗过拟合\n- 推荐参数：n_estimators=100, max_depth=10\n"

            response += "\n💡 如需参数配置帮助，请告诉我！"

            return {"msg": response, "type": "algorithm_recommendation"}, state.dict(), "algorithm_recommendation"

        except Exception as e:
            logger.error(f"算法推荐处理失败: {e}")
            return {"msg": "抱歉，算法推荐失败。请重新描述需求。", "type": "error"}, state.dict(), "error"

    async def handle_technical_question_async(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """处理技术问题请求"""
        try:
            logger.info(f"🔬 [技术问题] 处理用户请求: {user_input}")

            response = f"🔬 **技术问题解答**\n\n"

            if "过拟合" in user_input.lower():
                response += "**过拟合解决方案：**\n"
                response += "1. **正则化**：L1/L2正则化、Dropout\n"
                response += "2. **数据增强**：增加训练数据量\n"
                response += "3. **早停法**：监控验证集性能\n"
                response += "4. **模型简化**：减少复杂度\n"
            elif "学习率" in user_input.lower():
                response += "**学习率选择指南：**\n"
                response += "- 推荐值：0.001（深度学习）\n"
                response += "- 调优范围：0.0001 ~ 0.01\n"
                response += "- 使用Adam优化器自动调整\n"
            elif "lstm" in user_input.lower() and "gru" in user_input.lower():
                response += "**LSTM vs GRU对比：**\n"
                response += "- LSTM：3个门控，适合长序列\n"
                response += "- GRU：2个门控，计算效率高\n"
                response += "- 选择建议：数据量大选LSTM，追求效率选GRU\n"
            else:
                response += f"关于「{user_input}」的技术解答：\n"
                response += "这是一个重要的机器学习技术问题。建议查阅相关文档或提供更具体的问题描述。\n"

            response += "\n💡 需要更详细的解答请告诉我！"

            return {"msg": response, "type": "technical_question"}, state.dict(), "technical_question"

        except Exception as e:
            logger.error(f"技术问题处理失败: {e}")
            return {"msg": "抱歉，技术问题处理失败。请重新提问。", "type": "error"}, state.dict(), "error"

    async def handle_parameter_configuration_async(self, user_input: str, state: PredictiveAgentState) -> Tuple[dict, dict, str]:
        """处理参数配置请求"""
        try:
            logger.info(f"⚙️ [参数配置] 处理用户请求: {user_input}")

            response = f"⚙️ **参数配置建议**\n\n"

            if "学习率" in user_input:
                response += "**学习率配置：**\n"
                response += "- 推荐值：0.001\n"
                response += "- 调优范围：0.0001 ~ 0.01\n"
                response += "- 策略：从0.001开始，根据损失函数调整\n"
            elif "批次" in user_input or "batch" in user_input.lower():
                response += "**批次大小配置：**\n"
                response += "- 推荐值：32\n"
                response += "- 选择范围：16, 32, 64, 128\n"
                response += "- 原则：平衡内存使用和训练效果\n"
            elif "隐藏层" in user_input or "hidden" in user_input.lower():
                response += "**隐藏层大小配置：**\n"
                response += "- LSTM推荐值：128\n"
                response += "- 选择范围：64, 128, 256, 512\n"
                response += "- 原则：复杂任务用更大值\n"
            else:
                response += "**通用参数配置：**\n"
                response += "- 学习率：0.001\n"
                response += "- 批次大小：32\n"
                response += "- 训练轮数：100\n"
                response += "- 优化器：Adam\n"

            response += "\n💡 需要特定算法的参数配置请告诉我算法类型！"

            return {"msg": response, "type": "parameter_configuration"}, state.dict(), "parameter_configuration"

        except Exception as e:
            logger.error(f"参数配置处理失败: {e}")
            return {"msg": "抱歉，参数配置失败。请重新描述需求。", "type": "error"}, state.dict(), "error"

# 预测性算法Agent全局实例
predict_algorith_agent = PredictiveAlgorithmAssistant()

memory_manager = AgentMemoryManager()
db_manager = DatabaseManager()

@router.post("/predict_algorith", response_model=Response[dict])
def predict_algorith_handler(request: dict):
    """
    预测性算法Agent主接口，接收用户输入，返回结构化结果。
    现在支持对话管理功能。
    """
    user_input = request["question"]
    # 确保输入是正确的UTF-8编码
    if isinstance(user_input, str):
        try:
            # 尝试重新编码以确保正确处理中文
            user_input = user_input.encode('utf-8').decode('utf-8')
        except (UnicodeEncodeError, UnicodeDecodeError):
            # 如果编码有问题，尝试其他编码
            try:
                user_input = user_input.encode('gbk').decode('utf-8')
            except:
                pass

    user_id = request.get("user_id")
    session_id = request.get("session_id")
    conversation_id = request.get("conversation_id")  # 新增：对话ID

    # 获取状态（优先从数据库获取）
    state = None
    if user_id and session_id:
        state = memory_manager.get_state(user_id, session_id)

    # 处理用户输入
    result, new_state, task_id = predict_algorith_agent.process_user_input(user_input, state)

    # 保存状态
    if user_id and session_id:
        memory_manager.save_state(user_id, session_id, task_id, new_state)

        # 如果有对话ID，同时保存到数据库
        if conversation_id:
            try:
                # 添加用户消息到对话记录
                db_manager.add_conversation_message(
                    conversation_id=conversation_id,
                    message_type="user",
                    content=user_input,
                    interaction_type=new_state.get("last_interaction", "unknown")
                )

                # 添加助手回复到对话记录
                db_manager.add_conversation_message(
                    conversation_id=conversation_id,
                    message_type="assistant",
                    content=result.get("msg", ""),
                    message_data=result,
                    interaction_type=new_state.get("last_interaction", "unknown")
                )

                # 更新对话状态
                if task_id and task_id != "welcome":
                    db_manager.update_conversation(
                        conversation_id,
                        current_stage=new_state.get("conversation_stage", "welcome"),
                        progress=new_state.get("progress", 0.0),
                        algorithm_type=new_state.get("task_type", None)
                    )

            except Exception as e:
                print(f"[警告] 保存对话记录失败: {e}")

    return Response(data=result)


# 在PredictiveAlgorithmAssistant类中添加训练确认相关方法
def add_training_confirmation_methods():
    """为PredictiveAlgorithmAssistant类添加训练确认相关方法"""

    async def handle_training_completed(self, state) -> Tuple[dict, dict, str]:
        """
        处理训练完成，进入确认流程
        """
        try:
            from datetime import datetime

            # 获取训练结果
            result = self.get_task_result(state.task_id)

            # 保存训练结果到状态
            state.training_result = result
            state.awaiting_training_confirmation = True
            state.training_completed_at = datetime.now().isoformat()

            # 构建训练结果展示消息
            msg = "🎉 **训练完成！**\n\n"
            msg += "您的算法训练已经完成，以下是训练结果摘要：\n\n"

            # 显示关键指标
            if result and isinstance(result, dict):
                if "accuracy" in result:
                    msg += f"📊 **准确率**: {result['accuracy']:.2%}\n"
                if "loss" in result:
                    msg += f"📉 **损失值**: {result['loss']:.4f}\n"
                if "training_time" in result:
                    msg += f"⏱️ **训练时间**: {result['training_time']}\n"
                if "model_size" in result:
                    msg += f"💾 **模型大小**: {result['model_size']}\n"

            msg += "\n🤔 **请确认您的选择**：\n"
            msg += "✅ **接受结果** - 输入 '接受' 或 'accept'\n"
            msg += "🔄 **重新训练** - 输入 '重新训练' 或 'retrain'\n"
            msg += "📊 **查看详情** - 输入 '详情' 或 'details'"

            return {
                "msg": msg,
                "type": "training_completed",
                "training_result": result,
                "task_id": state.task_id
            }, state.dict(), "training_confirmation"

        except Exception as e:
            logger.error(f"处理训练完成失败: {e}")
            return {
                "msg": f"获取训练结果时发生错误：{str(e)}\n\n请稍后重试或联系技术支持。",
                "type": "training_result_error"
            }, state.dict(), "error"

    async def handle_training_confirmation(self, user_input: str, state) -> Tuple[dict, dict, str]:
        """
        处理训练结果确认
        """
        try:
            user_input_lower = user_input.lower().strip()

            # 接受训练结果
            if any(keyword in user_input_lower for keyword in ['接受', 'accept', '确认', 'confirm', '好的', 'ok']):
                state.awaiting_training_confirmation = False
                state.task_status = "accepted"

                msg = "✅ **训练结果已确认**\n\n"
                msg += "恭喜！您已成功完成算法训练。\n\n"
                msg += "🎯 **后续操作**：\n"
                msg += "📊 生成详细报告 - 输入 '生成报告'\n"
                msg += "🔮 开始预测任务 - 输入 '开始预测'\n"
                msg += "💾 保存模型 - 输入 '保存模型'\n"
                msg += "🆕 创建新任务 - 输入 '新任务'"

                return {
                    "msg": msg,
                    "type": "training_accepted",
                    "task_id": state.task_id,
                    "training_result": state.training_result
                }, state.dict(), "training_accepted"

            # 重新训练
            elif any(keyword in user_input_lower for keyword in ['重新训练', 'retrain', '重新', '再次', 'retry']):
                # 重置状态，回到参数推荐阶段
                state.awaiting_training_confirmation = False
                state.awaiting_confirmation = False
                state.task_id = None
                state.task_status = None
                state.training_result = None
                state.training_completed_at = None
                state.history_search_completed = False
                state.conversation_stage = "task_processing"

                msg = "🔄 **重新训练**\n\n"
                msg += "好的，让我们重新开始训练流程。\n\n"
                msg += "请重新描述您的算法需求，我将为您重新进行参数配置和训练。"

                return {
                    "msg": msg,
                    "type": "retrain_requested"
                }, state.dict(), "retrain"

            # 查看详情
            elif any(keyword in user_input_lower for keyword in ['详情', 'details', '详细', '更多']):
                result = state.training_result or {}

                msg = "📊 **训练详细结果**\n\n"

                # 显示所有可用的训练指标
                for key, value in result.items():
                    if isinstance(value, (int, float)):
                        if 'accuracy' in key.lower() or 'acc' in key.lower():
                            msg += f"📈 **{key}**: {value:.2%}\n"
                        elif 'loss' in key.lower():
                            msg += f"📉 **{key}**: {value:.4f}\n"
                        elif 'time' in key.lower():
                            msg += f"⏱️ **{key}**: {value}\n"
                        else:
                            msg += f"📊 **{key}**: {value}\n"
                    else:
                        msg += f"ℹ️ **{key}**: {value}\n"

                msg += "\n🤔 **请确认您的选择**：\n"
                msg += "✅ **接受结果** - 输入 '接受'\n"
                msg += "🔄 **重新训练** - 输入 '重新训练'"

                return {
                    "msg": msg,
                    "type": "training_details",
                    "training_result": result
                }, state.dict(), "training_confirmation"

            # 无法识别的输入
            else:
                msg = "❓ **请明确您的选择**\n\n"
                msg += "我没有理解您的意思，请选择以下操作之一：\n\n"
                msg += "✅ **接受结果** - 输入 '接受' 或 'accept'\n"
                msg += "🔄 **重新训练** - 输入 '重新训练' 或 'retrain'\n"
                msg += "📊 **查看详情** - 输入 '详情' 或 'details'"

                return {
                    "msg": msg,
                    "type": "confirmation_unclear"
                }, state.dict(), "training_confirmation"

        except Exception as e:
            logger.error(f"处理训练确认失败: {e}")
            return {
                "msg": f"处理确认时发生错误：{str(e)}\n\n请重新选择您的操作。",
                "type": "confirmation_error"
            }, state.dict(), "error"

    def handle_general_consultation(self, user_input: str, state: PredictiveAgentState):
        """处理一般性咨询问题，提供技术指导和建议"""
        try:
            # 使用LLM生成专业的技术回答
            consultation_prompt = f"""
你是一个专业的机器学习算法训练专家。用户正在进行算法训练项目，现在向你咨询技术问题。

用户当前状态：
- 任务类型：{state.task_type if state.task_type else '未指定'}
- 任务ID：{state.task_id if state.task_id else '无'}
- 当前参数：{state.current_params if state.current_params else '无'}

用户问题：{user_input}

请提供专业、详细、实用的回答，包括：
1. 直接回答用户的问题
2. 提供具体的操作建议
3. 如果涉及技术细节，请给出清晰的解释
4. 如果有相关的最佳实践，请一并说明

回答要求：
- 专业且易懂
- 结构清晰
- 实用性强
- 针对用户的具体情况
"""

            # 调用LLM生成回答
            response = self.llm_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "你是一个专业的机器学习算法训练专家，擅长为用户提供技术指导和建议。"},
                    {"role": "user", "content": consultation_prompt}
                ],
                temperature=0.7,
                max_tokens=1000
            )

            answer = response.choices[0].message.content.strip()

            return {
                "msg": answer,
                "type": "consultation"
            }, state.dict(), "consultation"

        except Exception as e:
            logger.error(f"处理一般性咨询失败: {e}")
            return {
                "msg": "抱歉，我在处理您的问题时遇到了一些困难。请稍后再试，或者重新描述您的问题。",
                "type": "consultation_error"
            }, state.dict(), "error"



    # 将方法添加到类中
    PredictiveAlgorithmAssistant.handle_training_completed = handle_training_completed
    PredictiveAlgorithmAssistant.handle_training_confirmation = handle_training_confirmation

# 调用函数添加方法
add_training_confirmation_methods()