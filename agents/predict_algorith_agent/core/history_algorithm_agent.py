"""
历史算法检索智能体
负责分析用户需求，搜索匹配的历史算法，并生成推荐建议
"""

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
from agents.predict_algorith_agent.services.history_algorithm_service import (
    HistoryAlgorithmService,
    HistoryAlgorithm,
    HistoryAlgorithmListResponse
)

logger = logging.getLogger(__name__)


class UserRequirementAnalysis(BaseModel):
    """用户需求分析结果"""
    keywords: List[str] = Field(..., description="从用户输入中提取的关键词")
    algorithm_types: List[str] = Field(..., description="推测的算法类型，如LSTM、CNN、RandomForest等")
    data_types: List[str] = Field(..., description="数据类型，如时序数据、图像数据、表格数据等")
    use_cases: List[str] = Field(..., description="使用场景，如预测性维护、异常检测、分类预测等")
    confidence: float = Field(..., description="分析置信度，0-1之间")
    reasoning: str = Field(..., description="分析推理过程")


class AlgorithmRecommendation(BaseModel):
    """算法推荐结果"""
    has_suitable_algorithms: bool = Field(..., description="是否找到合适的历史算法")
    recommended_algorithms: List[str] = Field(..., description="推荐的算法ID列表")
    recommendation_reasons: List[str] = Field(..., description="推荐理由列表，与算法ID一一对应")
    overall_recommendation: str = Field(..., description="总体推荐建议")
    confidence: float = Field(..., description="推荐置信度，0-1之间")
    should_create_new: bool = Field(..., description="是否建议创建新算法")
    create_new_reason: str = Field(..., description="建议创建新算法的原因")


class HistoryAlgorithmAgent:
    """历史算法检索智能体"""
    
    def __init__(self, llm_model: OpenAIModel, history_service: HistoryAlgorithmService):
        """
        初始化历史算法检索智能体
        
        Args:
            llm_model: LLM模型实例
            history_service: 历史算法服务实例
        """
        self.history_service = history_service
        
        # 用户需求分析Agent
        self.requirement_analyzer = Agent(
            model=llm_model,
            output_type=UserRequirementAnalysis,
            system_prompt="""你是一个专业的算法需求分析专家。你的任务是分析用户的自然语言输入，提取出算法训练的关键信息。

请仔细分析用户输入，提取以下信息：

1. **关键词**：从用户描述中提取重要的技术关键词和业务关键词
2. **算法类型**：根据用户描述推测可能适用的算法类型
   - LSTM：适用于时序数据预测、序列分析
   - CNN：适用于图像处理、信号处理、模式识别
   - RandomForest：适用于表格数据分类、回归
   - SVM：适用于分类、回归问题
   - XGBoost：适用于结构化数据的分类、回归
3. **数据类型**：判断用户处理的数据类型
   - 时序数据：传感器数据、股价数据、温度数据等
   - 图像数据：照片、医学影像、工业图像等
   - 表格数据：CSV文件、数据库记录等
   - 文本数据：文档、评论、日志等
4. **使用场景**：识别具体的应用场景
   - 预测性维护：设备故障预测、健康状态监控
   - 异常检测：异常行为识别、故障检测
   - 分类预测：图像分类、文本分类
   - 回归预测：数值预测、趋势分析

请基于用户输入进行深度分析，给出合理的推测和高置信度的结果。"""
        )
        
        # 算法推荐Agent
        self.recommendation_generator = Agent(
            model=llm_model,
            output_type=AlgorithmRecommendation,
            system_prompt="""你是一个专业的算法推荐专家。你的任务是基于用户需求和历史算法库，为用户推荐最合适的算法。

你会收到：
1. 用户的原始需求描述
2. 需求分析结果
3. 匹配的历史算法列表

请分析这些信息，并做出专业的推荐：

**推荐原则**：
1. **优先推荐历史算法**：如果有合适的历史算法，优先推荐复用
2. **考虑相似度**：算法类型、数据类型、使用场景的匹配度
3. **考虑性能**：历史算法的性能指标和使用频率
4. **考虑适用性**：算法参数和配置是否适合当前需求

**推荐策略**：
- 如果找到高度匹配的历史算法（相似度>80%），强烈推荐复用
- 如果找到中等匹配的历史算法（相似度50-80%），建议复用并调整参数
- 如果只有低匹配度的算法（相似度<50%），建议创建新算法
- 如果没有找到任何相关算法，建议创建新算法

**输出要求**：
1. 明确说明是否推荐使用历史算法
2. 如果推荐历史算法，说明推荐理由和预期效果
3. 如果建议创建新算法，说明原因和建议方向
4. 提供置信度评估"""
        )
    
    async def analyze_user_requirement(self, user_input: str) -> UserRequirementAnalysis:
        """
        分析用户需求
        
        Args:
            user_input: 用户输入的自然语言描述
            
        Returns:
            用户需求分析结果
        """
        try:
            result = await self.requirement_analyzer.run(user_input)
            logger.info(f"用户需求分析完成: {result.output}")
            return result.output
        except Exception as e:
            logger.error(f"用户需求分析失败: {e}")

            # 检查是否是简单的数字输入（用户选择）
            if user_input.strip().isdigit():
                # 这是用户选择，不需要需求分析
                raise ValueError(f"用户输入'{user_input}'是选择操作，不需要需求分析")

            # 返回默认分析结果
            return UserRequirementAnalysis(
                keywords=["算法", "训练"],
                algorithm_types=["LSTM"],
                data_types=["时序数据"],
                use_cases=["预测"],
                confidence=0.3,
                reasoning="LLM分析失败，使用默认配置"
            )
    
    async def get_history_algorithms(self, project_id: str = None) -> List[HistoryAlgorithm]:
        """
        获取历史算法列表

        Args:
            project_id: 项目ID（必传）

        Returns:
            历史算法列表

        Raises:
            ValueError: 当项目ID为空时
            AlgorithmPlatformError: 当API调用失败时
        """
        if not project_id:
            raise ValueError("项目ID不能为空")

        try:
            response = await self.history_service.get_history_algorithms(project_id)
            logger.info(f"获取到 {len(response.algorithms)} 个历史算法（项目ID: {project_id}）")

            return response.algorithms
        except Exception as e:
            logger.error(f"获取历史算法列表失败: {e}")
            # 直接抛出异常，不返回空列表
            raise
    
    async def generate_recommendation(
        self, 
        user_input: str, 
        requirement_analysis: UserRequirementAnalysis, 
        similar_algorithms: List[HistoryAlgorithm]
    ) -> AlgorithmRecommendation:
        """
        生成算法推荐
        
        Args:
            user_input: 用户原始输入
            requirement_analysis: 需求分析结果
            similar_algorithms: 相似的历史算法列表
            
        Returns:
            算法推荐结果
        """
        try:
            # 构建推荐输入
            algorithms_info = []
            for algo in similar_algorithms:
                algo_info = {
                    "id": algo.algorithm_id,
                    "name": algo.algorithm_name,
                    "type": algo.algorithm_type,
                    "description": algo.description,
                    "data_type": algo.data_type,
                    "use_case": algo.use_case,
                    "performance": algo.performance_metrics,
                    "usage_count": algo.usage_count,
                    "parameters": algo.parameters
                }
                algorithms_info.append(algo_info)
            
            recommendation_input = f"""
用户需求：{user_input}

需求分析结果：
- 关键词：{requirement_analysis.keywords}
- 算法类型：{requirement_analysis.algorithm_types}
- 数据类型：{requirement_analysis.data_types}
- 使用场景：{requirement_analysis.use_cases}
- 分析置信度：{requirement_analysis.confidence}
- 分析推理：{requirement_analysis.reasoning}

匹配的历史算法：
{algorithms_info}

请基于以上信息，为用户生成专业的算法推荐建议。
"""
            
            result = await self.recommendation_generator.run(recommendation_input)
            logger.info(f"算法推荐生成完成: {result.output}")
            return result.output
            
        except Exception as e:
            logger.error(f"算法推荐生成失败: {e}")
            # 返回默认推荐
            return AlgorithmRecommendation(
                has_suitable_algorithms=len(similar_algorithms) > 0,
                recommended_algorithms=[algo.algorithm_id for algo in similar_algorithms[:2]],
                recommendation_reasons=["系统推荐"] * min(2, len(similar_algorithms)),
                overall_recommendation="建议查看推荐的历史算法，如果不合适可以创建新算法",
                confidence=0.5,
                should_create_new=len(similar_algorithms) == 0,
                create_new_reason="未找到合适的历史算法" if len(similar_algorithms) == 0 else ""
            )
    
    async def process_user_request(self, user_input: str, project_id: str = None) -> Dict[str, Any]:
        """
        处理用户请求的简化流程

        Args:
            user_input: 用户输入
            project_id: 项目ID（必传）

        Returns:
            包含历史算法列表的响应
        """
        try:
            if not project_id:
                return {
                    "error": "项目ID不能为空",
                    "message": "无法获取历史算法列表，项目ID缺失",
                    "history_algorithms": [],
                    "next_action": "create_new_algorithm"
                }

            # 获取历史算法列表
            history_algorithms = await self.get_history_algorithms(project_id)

            # 构建响应
            response = {
                "message": "以下是可用的历史算法列表，您可以选择使用其中一个，或者选择创建新算法：",
                "history_algorithms": [algo.dict() for algo in history_algorithms],
                "total_count": len(history_algorithms),
                "next_action": "choose_algorithm_or_create_new"
            }

            return response

        except Exception as e:
            logger.error(f"处理用户请求失败: {e}")
            return {
                "error": f"获取历史算法列表时发生错误: {str(e)}",
                "message": "算法平台API调用失败，无法获取历史算法列表",
                "history_algorithms": [],
                "next_action": "create_new_algorithm"
            }
