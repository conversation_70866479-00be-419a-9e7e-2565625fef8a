<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法智能体 - API增强版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            height: 100%;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }

        .app-container {
            display: table;
            width: 100%;
            height: 100%;
            table-layout: fixed;
        }
        
        /* 左侧边栏 */
        .sidebar {
            display: table-cell;
            width: 320px;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            vertical-align: top;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .settings-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
        }
        
        .setting-item:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .setting-icon {
            width: 16px;
            height: 16px;
        }
        
        /* 项目选择器 */
        .project-section {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .project-label {
            font-size: 14px;
            margin-bottom: 8px;
            color: rgba(255,255,255,0.9);
        }
        
        .project-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 14px;
        }
        
        .project-select option {
            background: #1e40af;
            color: white;
        }
        
        /* API功能区 */
        .api-section {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .api-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: rgba(255,255,255,0.9);
        }
        
        .api-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .api-btn {
            padding: 8px 12px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            text-align: left;
            transition: all 0.2s;
        }
        
        .api-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .api-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .conversations-section {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: rgba(255,255,255,0.9);
        }
        
        .new-chat-btn {
            width: 100%;
            padding: 12px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
            transition: all 0.2s;
        }
        
        .new-chat-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .conversation-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .conversation-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .conversation-item:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .conversation-item.active {
            background: rgba(255,255,255,0.2);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        
        .conversation-info {
            flex: 1;
            min-width: 0;
        }
        
        .conversation-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .conversation-preview {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .conversation-time {
            font-size: 11px;
            color: rgba(255,255,255,0.6);
            white-space: nowrap;
        }
        
        /* 右侧主区域 */
        .main-area {
            display: table-cell;
            background: white;
            vertical-align: top;
            position: relative;
            height: 100%;
        }
        
        .chat-header {
            position: fixed;
            top: 0;
            left: 320px; /* 左侧边栏宽度 */
            right: 0;
            height: 80px;
            padding: 20px 30px;
            border-bottom: 1px solid #e5e7eb;
            display: block;
            background: white;
            z-index: 100; /* 提高z-index确保不被遮挡 */
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 添加阴影增强视觉效果 */
        }

        .chat-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
        }
        
        .agent-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .agent-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 连接状态指示器 */
        .connection-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
            transition: background-color 0.3s ease;
            display: inline-block;
            vertical-align: middle;
        }

        .connection-indicator.disconnected {
            background-color: #ef4444; /* 红色 - 未连接 */
        }

        .connection-indicator.connecting {
            background-color: #f59e0b; /* 黄色 - 连接中 */
            animation: pulse 1.5s infinite;
        }

        .connection-indicator.connected {
            background-color: #10b981; /* 绿色 - 已连接 */
        }

        .connection-indicator.reconnecting {
            background-color: #f97316; /* 橙色 - 重连中 */
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .action-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            color: #6b7280;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .action-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .chat-messages {
            position: absolute;
            top: 80px;
            left: 0;
            right: 0;
            bottom: 100px;
            padding: 20px 30px;
            overflow-y: auto;
            background: #f9fafb;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .message.api .message-avatar {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e5e7eb;
            color: #1f2937;
        }
        
        .message.user .message-content {
            background: #3b82f6;
            color: white;
        }
        
        .message.api .message-content {
            background: #ecfdf5;
            border: 1px solid #10b981;
            color: #1f2937;
        }
        
        .chat-input {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 100px;
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            background: white;
            z-index: 10;
        }
        
        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .message-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            resize: none;
            font-size: 14px;
            font-family: inherit;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .message-input:focus {
            border-color: #3b82f6;
        }
        
        .send-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }
        
        .send-btn:hover:not(:disabled) {
            background: #2563eb;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }


        
        .status-indicator {
            padding: 8px 16px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-indicator.connected {
            color: #059669;
            background: #ecfdf5;
        }

        .status-indicator.connecting {
            color: #d97706;
            background: #fef3c7;
        }

        .status-indicator.reconnecting {
            color: #ea580c;
            background: #fed7aa;
        }

        .status-indicator.disconnected {
            color: #dc2626;
            background: #fef2f2;
        }

        /* 登录模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 0;
            border: none;
            border-radius: 8px;
            width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #1f2937;
        }

        .close {
            color: #9ca3af;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #374151;
            font-weight: 500;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-primary, .btn-secondary {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
        }

        /* API参数表单样式 */
        .api-modal .modal-content {
            width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .param-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .param-grid .form-group {
            margin-bottom: 0;
        }

        .param-full-width {
            grid-column: 1 / -1;
        }

        /* API结果显示样式 */
        .api-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-size: 13px;
        }

        .api-result.success {
            background: #ecfdf5;
            border: 1px solid #10b981;
            color: #065f46;
        }

        .api-result.error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }

        .api-result-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .api-result-content {
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
        }

        /* Markdown 内容样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 12px 0 8px 0;
            font-weight: 600;
            line-height: 1.3;
        }

        .message-content h1 { font-size: 1.5em; color: #1f2937; }
        .message-content h2 { font-size: 1.3em; color: #374151; }
        .message-content h3 { font-size: 1.1em; color: #4b5563; }

        .message-content p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
            line-height: 1.5;
        }

        .message-content code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e11d48;
        }

        .message-content pre {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .message-content pre code {
            background: none;
            padding: 0;
            color: #1f2937;
        }

        /* 确保用户消息不应用markdown样式 */
        .message.user .message-content h1,
        .message.user .message-content h2,
        .message.user .message-content h3,
        .message.user .message-content h4,
        .message.user .message-content h5,
        .message.user .message-content h6,
        .message.user .message-content p,
        .message.user .message-content ul,
        .message.user .message-content ol,
        .message.user .message-content li,
        .message.user .message-content code,
        .message.user .message-content pre {
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;
            line-height: inherit;
        }
    </style>

    <!-- 引入 markdown-it 库 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
</head>
<body>
    <!-- 用户登录模态框 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>用户登录</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="userId">用户ID:</label>
                        <input type="text" id="userId" name="userId" required placeholder="请输入用户ID">
                    </div>
                    <div class="form-group">
                        <label for="username">用户名:</label>
                        <input type="text" id="username" name="username" required placeholder="请输入用户名">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">登录</button>
                        <button type="button" id="guestLogin" class="btn-secondary">访客登录</button>
                        <button type="button" id="quickTestLogin" class="btn-secondary" style="background: #10b981;">快速测试</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 训练算法参数模态框 -->
    <div id="trainAlgorithmModal" class="modal api-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>训练新算法</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="trainAlgorithmForm">
                    <div class="param-grid">
                        <div class="form-group">
                            <label for="param1">算法名称:</label>
                            <input type="text" id="param1" name="parameter1" required placeholder="例如：测试算法1">
                        </div>
                        <div class="form-group">
                            <label for="param2">算法类型:</label>
                            <select id="param2" name="parameter2" required>
                                <option value="">请选择</option>
                                <option value="风险预警">风险预警</option>
                                <option value="故障诊断">故障诊断</option>
                                <option value="预测维护">预测维护</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="param3">数据集名称:</label>
                            <input type="text" id="param3" name="parameter3" required placeholder="例如：水泵数据集0723">
                        </div>
                        <div class="form-group">
                            <label for="param4">输入特征维度:</label>
                            <input type="number" id="param4" name="parameter4" required placeholder="例如：5">
                        </div>
                        <div class="form-group">
                            <label for="param5">LSTM隐藏层大小:</label>
                            <input type="number" id="param5" name="parameter5" required placeholder="例如：64">
                        </div>
                        <div class="form-group">
                            <label for="param6">LSTM层数:</label>
                            <input type="number" id="param6" name="parameter6" required placeholder="例如：3">
                        </div>
                        <div class="form-group">
                            <label for="param7">Dropout比例:</label>
                            <input type="number" id="param7" name="parameter7" step="0.1" required placeholder="例如：0.2">
                        </div>
                        <div class="form-group">
                            <label for="param8">双向LSTM:</label>
                            <select id="param8" name="parameter8" required>
                                <option value="">请选择</option>
                                <option value="YES">是</option>
                                <option value="NO">否</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="param9">输出维度:</label>
                            <input type="number" id="param9" name="parameter9" required placeholder="例如：10">
                        </div>
                        <div class="form-group">
                            <label for="param10">学习率:</label>
                            <input type="number" id="param10" name="parameter10" step="0.001" required placeholder="例如：0.01">
                        </div>
                        <div class="form-group">
                            <label for="param11">样本划分比例:</label>
                            <input type="number" id="param11" name="parameter11" step="0.1" required placeholder="例如：1">
                        </div>
                        <div class="form-group">
                            <label for="param12">优化器:</label>
                            <select id="param12" name="parameter12" required>
                                <option value="">请选择</option>
                                <option value="Adam">Adam</option>
                                <option value="SGD">SGD</option>
                                <option value="RMSprop">RMSprop</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="param13">损失函数:</label>
                            <select id="param13" name="parameter13" required>
                                <option value="">请选择</option>
                                <option value="MSE">MSE</option>
                                <option value="MAE">MAE</option>
                                <option value="CrossEntropy">CrossEntropy</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="param14">标签列:</label>
                            <input type="text" id="param14" name="parameter14" required placeholder="例如：4">
                        </div>
                        <div class="form-group">
                            <label for="param15">数据列:</label>
                            <input type="text" id="param15" name="parameter15" required placeholder="例如：5">
                        </div>
                        <div class="form-group">
                            <label for="param16">字段:</label>
                            <input type="text" id="param16" name="parameter16" required placeholder="例如：P11111124112047702">
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">开始训练</button>
                        <button type="button" class="btn-secondary close-modal">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 启动监控服务参数模态框 -->
    <div id="monitoringServiceModal" class="modal api-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>启动监控分析服务</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="monitoringServiceForm">
                    <div class="form-group">
                        <label for="dbIp">数据库地址:</label>
                        <input type="text" id="dbIp" name="db_ip" required placeholder="例如：***********">
                    </div>
                    <div class="form-group">
                        <label for="dbName">数据库表名:</label>
                        <input type="text" id="dbName" name="db_name" required placeholder="例如：data_1815320358166839298">
                    </div>
                    <div class="form-group">
                        <label for="listName">关键数据字段:</label>
                        <input type="text" id="listName" name="list_name" required placeholder="例如：P11111124112047702">
                    </div>
                    <div class="form-group">
                        <label for="implementName">服务名称:</label>
                        <input type="text" id="implementName" name="implement_name" required placeholder="例如：TEST_MONITOR">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">启动服务</button>
                        <button type="button" class="btn-secondary close-modal">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 查询参数模态框 -->
    <div id="queryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="queryModalTitle">查询参数</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="queryForm">
                    <div id="queryFormContent">
                        <!-- 动态生成查询表单内容 -->
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">查询</button>
                        <button type="button" class="btn-secondary close-modal">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="app-container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo-section">
                    <div class="logo">🤖</div>
                    <div class="app-title">算法智能体</div>
                </div>
                <div class="settings-section">
                    <div class="setting-item" id="userInfo">
                        <span class="setting-icon">👤</span>
                        <span id="currentUser">未登录</span>
                    </div>
                    <div class="setting-item" id="logoutBtn" style="display: none;">
                        <span class="setting-icon">🚪</span>
                        <span>退出登录</span>
                    </div>
                </div>
            </div>

            <!-- 项目选择器 -->
            <div class="project-section">
                <div class="project-label">选择项目</div>
                <select class="project-select" id="projectSelect">
                    <option value="">请选择项目</option>
                    <option value="285">项目285</option>
                    <option value="286">项目286</option>
                    <option value="287">项目287</option>
                </select>
            </div>

            <!-- API功能区 -->
            <div class="api-section">
                <div class="api-title">🔍 查询功能</div>
                <div class="api-buttons">
                    <button class="api-btn" id="getAllAlgorithmsBtn" disabled>📊 查询全部算法名称</button>
                    <button class="api-btn" id="getAllDatasetsBtn" disabled>📁 查询全部数据集名称</button>
                    <button class="api-btn" id="getAllIotBtn" disabled>🌐 查询全部IOT名称</button>
                    <button class="api-btn" id="getAllServicesBtn" disabled>⚙️ 查询全部服务名称</button>
                    <button class="api-btn" id="getAlgorithmLogBtn" disabled>📋 查询算法训练信息</button>
                    <button class="api-btn" id="getServicePredictionBtn" disabled>🔮 查询服务预测结果</button>
                </div>
            </div>

            <div class="api-section">
                <div class="api-title">⚡ 操作功能</div>
                <div class="api-buttons">
                    <button class="api-btn" id="trainAlgorithmBtn" disabled>🚀 训练新算法</button>
                    <button class="api-btn" id="startMonitoringBtn" disabled>📡 启动监控分析服务</button>
                </div>
            </div>

            <div class="conversations-section">
                <div class="section-title">对话</div>
                <button class="new-chat-btn" id="newChatBtn">
                    ➕ 新建对话
                </button>

                <div class="section-title">历史信息</div>
                <div class="conversation-list" id="conversationList">
                    <!-- 历史对话列表将在这里动态生成 -->
                </div>
            </div>
        </div>

        <!-- 右侧主区域 -->
        <div class="main-area">
            <div class="status-indicator" id="statusIndicator">未连接</div>

            <div class="chat-header">
                <div class="chat-header-content">
                    <div class="agent-info">
                        <div class="agent-avatar">🤖</div>
                        <div class="agent-name">智能体 - API增强版</div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" id="connectBtn">
                            <div class="connection-indicator disconnected" id="connectionIndicator"></div>
                            连接
                        </button>
                        <button class="action-btn" id="disconnectBtn" disabled>❌ 断开</button>
                        <button class="action-btn" id="reconnectBtn" disabled style="display: none;">🔄 重连</button>
                        <button class="action-btn" id="clearBtn">🗑️ 清空</button>
                    </div>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <!-- 对话消息将在这里显示 -->
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <textarea
                        class="message-input"
                        id="messageInput"
                        placeholder="输入消息进行自然语言交互，或使用左侧API功能进行直接调用"
                        disabled
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" disabled>
                        📤
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 markdown-it 实例
        const md = window.markdownit({
            html: true,        // 允许HTML标签
            breaks: true,      // 将换行符转换为<br>
            linkify: true,     // 自动识别链接
            typographer: true  // 启用一些语言中性的替换和引号美化
        });

        // WebSocket连接管理
        let ws = null;
        let currentSessionId = null;
        let currentConversationId = null;
        let isConnected = false;

        // 连接状态枚举
        const CONNECTION_STATES = {
            DISCONNECTED: 'disconnected',
            CONNECTING: 'connecting',
            CONNECTED: 'connected',
            RECONNECTING: 'reconnecting'
        };

        let currentConnectionState = CONNECTION_STATES.DISCONNECTED;

        // 重连配置
        const RECONNECT_CONFIG = {
            maxAttempts: 5,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 1.5
        };

        let reconnectAttempts = 0;
        let reconnectTimer = null;

        // 默认项目ID（用于测试）- 必须是字符串类型
        const DEFAULT_PROJECT_ID = "285";

        // 用户信息管理
        let currentUser = {
            userId: null,
            username: null,
            isLoggedIn: false
        };

        // DOM元素
        const statusIndicator = document.getElementById('statusIndicator');
        const connectionIndicator = document.getElementById('connectionIndicator');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const reconnectBtn = document.getElementById('reconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const chatMessages = document.getElementById('chatMessages');
        const conversationList = document.getElementById('conversationList');
        const newChatBtn = document.getElementById('newChatBtn');
        const projectSelect = document.getElementById('projectSelect');

        // 用户登录相关DOM元素
        const loginModal = document.getElementById('loginModal');
        const loginForm = document.getElementById('loginForm');
        const guestLoginBtn = document.getElementById('guestLogin');
        const currentUserSpan = document.getElementById('currentUser');
        const logoutBtn = document.getElementById('logoutBtn');

        // API相关DOM元素
        const trainAlgorithmModal = document.getElementById('trainAlgorithmModal');
        const trainAlgorithmForm = document.getElementById('trainAlgorithmForm');
        const monitoringServiceModal = document.getElementById('monitoringServiceModal');
        const monitoringServiceForm = document.getElementById('monitoringServiceForm');
        const queryModal = document.getElementById('queryModal');
        const queryForm = document.getElementById('queryForm');
        const queryModalTitle = document.getElementById('queryModalTitle');
        const queryFormContent = document.getElementById('queryFormContent');

        // API按钮
        const getAllAlgorithmsBtn = document.getElementById('getAllAlgorithmsBtn');
        const getAllDatasetsBtn = document.getElementById('getAllDatasetsBtn');
        const getAllIotBtn = document.getElementById('getAllIotBtn');
        const getAllServicesBtn = document.getElementById('getAllServicesBtn');
        const getAlgorithmLogBtn = document.getElementById('getAlgorithmLogBtn');
        const getServicePredictionBtn = document.getElementById('getServicePredictionBtn');
        const trainAlgorithmBtn = document.getElementById('trainAlgorithmBtn');
        const startMonitoringBtn = document.getElementById('startMonitoringBtn');

        // 生成会话ID - 使用32位UUID
        function generateSessionId() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            }).replace(/-/g, '');
        }

        // 用户登录相关函数
        function showLoginModal() {
            console.log('显示登录模态框');
            if (loginModal) {
                loginModal.style.display = 'block';
            } else {
                console.error('登录模态框元素未找到');
            }
        }

        function hideLoginModal() {
            console.log('隐藏登录模态框');
            if (loginModal) {
                loginModal.style.display = 'none';
            }
        }

        function updateUserDisplay() {
            if (currentUser.isLoggedIn) {
                currentUserSpan.textContent = currentUser.username;
                logoutBtn.style.display = 'block';
            } else {
                currentUserSpan.textContent = '未登录';
                logoutBtn.style.display = 'none';
            }
        }

        function loginUser(userId, username) {
            console.log('用户登录:', userId, username);
            try {
                currentUser.userId = userId;
                currentUser.username = username;
                currentUser.isLoggedIn = true;
                updateUserDisplay();
                hideLoginModal();

                console.log('登录成功，准备连接WebSocket');
                // 登录成功后自动连接
                setTimeout(() => {
                    connect();
                }, 500); // 延迟500ms确保UI更新完成
            } catch (error) {
                console.error('登录过程出错:', error);
                alert('登录失败: ' + error.message);
            }
        }

        function logoutUser() {
            // 断开连接并清理定时器
            if (isConnected || currentConnectionState !== CONNECTION_STATES.DISCONNECTED) {
                disconnect();
            }

            // 清理心跳定时器
            if (heartbeatTimer) {
                clearInterval(heartbeatTimer);
                heartbeatTimer = null;
            }

            // 清除用户信息
            currentUser.userId = null;
            currentUser.username = null;
            currentUser.isLoggedIn = false;
            updateUserDisplay();

            // 清空对话
            chatMessages.innerHTML = '';
            conversationList.innerHTML = '';

            // 显示登录模态框
            showLoginModal();
        }

        // 更新连接状态
        function updateConnectionStatus(state, message = null) {
            currentConnectionState = state;
            isConnected = (state === CONNECTION_STATES.CONNECTED);

            // 更新状态文本和样式
            let statusText = '';
            let statusClass = 'status-indicator';

            switch(state) {
                case CONNECTION_STATES.DISCONNECTED:
                    statusText = message || '未连接';
                    statusClass += ' disconnected';
                    break;
                case CONNECTION_STATES.CONNECTING:
                    statusText = message || '连接中...';
                    statusClass += ' connecting';
                    break;
                case CONNECTION_STATES.CONNECTED:
                    statusText = message || '已连接';
                    statusClass += ' connected';
                    reconnectAttempts = 0; // 重置重连计数
                    break;
                case CONNECTION_STATES.RECONNECTING:
                    statusText = message || `重连中... (${reconnectAttempts}/${RECONNECT_CONFIG.maxAttempts})`;
                    statusClass += ' reconnecting';
                    break;
            }

            statusIndicator.textContent = statusText;
            statusIndicator.className = statusClass;

            // 更新连接指示器
            connectionIndicator.className = `connection-indicator ${state}`;

            // 更新连接按钮文本
            let buttonText = '';
            switch(state) {
                case CONNECTION_STATES.DISCONNECTED:
                    buttonText = '连接';
                    break;
                case CONNECTION_STATES.CONNECTING:
                    buttonText = '连接中...';
                    break;
                case CONNECTION_STATES.CONNECTED:
                    buttonText = '已连接';
                    break;
                case CONNECTION_STATES.RECONNECTING:
                    buttonText = '重连中...';
                    break;
            }

            // 更新按钮内容（保持圆点和文字）
            connectBtn.innerHTML = `
                <div class="connection-indicator ${state}" id="connectionIndicator"></div>
                ${buttonText}
            `;

            // 重新获取connectionIndicator引用（因为innerHTML重新创建了元素）
            const newConnectionIndicator = document.getElementById('connectionIndicator');

            // 更新按钮状态
            const isConnectedOrConnecting = (state === CONNECTION_STATES.CONNECTED || state === CONNECTION_STATES.CONNECTING);
            const isReconnecting = (state === CONNECTION_STATES.RECONNECTING);

            connectBtn.disabled = isConnectedOrConnecting || isReconnecting;
            disconnectBtn.disabled = !(isConnectedOrConnecting || isReconnecting);

            // 重连按钮显示逻辑
            if (state === CONNECTION_STATES.DISCONNECTED && reconnectAttempts > 0) {
                reconnectBtn.style.display = 'inline-block';
                reconnectBtn.disabled = false;
            } else {
                reconnectBtn.style.display = 'none';
                reconnectBtn.disabled = true;
            }

            messageInput.disabled = !isConnected;
            sendBtn.disabled = !isConnected;

            updateAPIButtonsState();
        }

        // 更新API按钮状态
        function updateAPIButtonsState() {
            const projectId = projectSelect.value;
            const buttonsEnabled = isConnected && projectId;

            getAllAlgorithmsBtn.disabled = !isConnected;
            getAllDatasetsBtn.disabled = !isConnected;
            getAllIotBtn.disabled = !buttonsEnabled;
            getAllServicesBtn.disabled = !buttonsEnabled;
            getAlgorithmLogBtn.disabled = !buttonsEnabled;
            getServicePredictionBtn.disabled = !buttonsEnabled;
            trainAlgorithmBtn.disabled = !buttonsEnabled;
            startMonitoringBtn.disabled = !buttonsEnabled;
        }

        // 添加消息到聊天区域
        function addMessage(type, content, timestamp = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';

            if (type === 'user') {
                avatar.textContent = '👤';
            } else if (type === 'api') {
                avatar.textContent = '⚡';
            } else {
                avatar.textContent = '🤖';
            }

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            // 对AI回复的消息进行markdown解析，用户消息保持原样
            if (type === 'assistant') {
                const renderedContent = md.render(content);
                contentDiv.innerHTML = renderedContent;
            } else {
                contentDiv.textContent = content;
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加API结果消息
        function addAPIResultMessage(action, status, result) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message api';

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '⚡';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            const resultDiv = document.createElement('div');
            resultDiv.className = `api-result ${status}`;

            const titleDiv = document.createElement('div');
            titleDiv.className = 'api-result-title';
            titleDiv.textContent = `API调用: ${getActionDisplayName(action)} - ${status === 'success' ? '成功' : '失败'}`;

            const contentResultDiv = document.createElement('div');
            contentResultDiv.className = 'api-result-content';
            contentResultDiv.textContent = JSON.stringify(result, null, 2);

            resultDiv.appendChild(titleDiv);
            resultDiv.appendChild(contentResultDiv);
            contentDiv.appendChild(resultDiv);

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 获取API动作的显示名称
        function getActionDisplayName(action) {
            const actionNames = {
                'get_all_algorithms': '查询全部算法名称',
                'get_all_datasets': '查询全部数据集名称',
                'get_all_iot': '查询全部IOT名称',
                'get_all_services': '查询全部服务名称',
                'get_algorithm_log': '查询算法训练信息',
                'get_service_prediction': '查询服务预测结果',
                'train_algorithm': '训练新算法',
                'start_monitoring_service': '启动监控分析服务'
            };
            return actionNames[action] || action;
        }

        // 清除重连定时器
        function clearReconnectTimer() {
            if (reconnectTimer) {
                clearTimeout(reconnectTimer);
                reconnectTimer = null;
            }
        }

        // 计算重连延迟时间（指数退避）
        function getReconnectDelay() {
            const delay = Math.min(
                RECONNECT_CONFIG.baseDelay * Math.pow(RECONNECT_CONFIG.backoffFactor, reconnectAttempts),
                RECONNECT_CONFIG.maxDelay
            );
            return delay + Math.random() * 1000; // 添加随机抖动
        }

        // 自动重连
        function attemptReconnect() {
            if (reconnectAttempts >= RECONNECT_CONFIG.maxAttempts) {
                console.log('达到最大重连次数，停止重连');
                updateConnectionStatus(CONNECTION_STATES.DISCONNECTED, '连接失败，请手动重连');
                return;
            }

            reconnectAttempts++;
            const delay = getReconnectDelay();

            console.log(`准备第${reconnectAttempts}次重连，延迟${Math.round(delay)}ms`);
            updateConnectionStatus(CONNECTION_STATES.RECONNECTING);

            reconnectTimer = setTimeout(() => {
                console.log(`执行第${reconnectAttempts}次重连尝试`);
                connectWebSocket();
            }, delay);
        }

        // WebSocket连接核心函数
        function connectWebSocket() {
            console.log('开始连接WebSocket...');

            // 清除现有连接
            if (ws) {
                ws.onopen = null;
                ws.onmessage = null;
                ws.onclose = null;
                ws.onerror = null;
                if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
                    ws.close();
                }
                ws = null;
            }

            // 如果用户未登录，先进行自动登录
            if (!currentUser.isLoggedIn) {
                console.log('用户未登录，进行自动登录');
                currentUser.userId = 'test_user_' + Date.now();
                currentUser.username = '测试用户';
                currentUser.isLoggedIn = true;
                updateUserDisplay();
            }

            if (!currentSessionId) {
                currentSessionId = generateSessionId();
            }

            const wsUrl = `ws://localhost:8008/api/ws/chat?session_id=${currentSessionId}&user_id=${currentUser.userId}&username=${encodeURIComponent(currentUser.username)}`;
            console.log('WebSocket连接URL:', wsUrl);

            try {
                updateConnectionStatus(CONNECTION_STATES.CONNECTING);
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    console.log('✅ WebSocket连接已建立');
                    clearReconnectTimer();
                    updateConnectionStatus(CONNECTION_STATES.CONNECTED);

                    // 启动心跳检测
                    if (heartbeatTimer) {
                        clearInterval(heartbeatTimer);
                    }
                    heartbeatTimer = startHeartbeat();

                    // 连接成功后加载历史对话列表
                    setTimeout(() => {
                        if (typeof loadConversations === 'function') {
                            loadConversations();
                        }
                    }, 1000);
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('收到消息:', data);

                        if (data.type === 'welcome' || data.type === 'chat_response') {
                            addMessage('assistant', data.data.message);
                        } else if (data.type === 'api_response') {
                            handleAPIResponse(data.data);
                        } else if (data.type === 'conversations_list') {
                            handleConversationsList(data.data);
                        } else if (data.type === 'conversation_restored') {
                            handleConversationRestored(data.data);
                        } else if (data.type === 'conversation_switched') {
                            console.log('对话切换成功:', data.data.conversation_id);
                        } else if (data.type === 'new_conversation_created') {
                            handleNewConversationCreated(data.data);
                        } else if (data.type === 'thinking') {
                            console.log('AI正在思考...');
                        } else if (data.type === 'pong') {
                            console.log('收到心跳响应');
                        }
                    } catch (error) {
                        console.error('解析消息失败:', error);
                        addMessage('assistant', '消息解析失败');
                    }
                };

                ws.onclose = function(event) {
                    console.log('❌ WebSocket连接已关闭', event);

                    // 停止心跳检测
                    if (heartbeatTimer) {
                        clearInterval(heartbeatTimer);
                        heartbeatTimer = null;
                    }

                    // 判断关闭原因和是否需要重连
                    let shouldReconnect = false;
                    let statusMessage = '';

                    if (event.code === 1000) {
                        // 正常关闭
                        statusMessage = '连接已正常断开';
                    } else if (event.code === 1001) {
                        // 端点离开
                        statusMessage = '服务器端点离开';
                        shouldReconnect = true;
                    } else if (event.code === 1006) {
                        // 异常关闭
                        statusMessage = '连接异常断开';
                        shouldReconnect = true;
                    } else {
                        // 其他错误
                        statusMessage = `连接关闭 (代码: ${event.code})`;
                        shouldReconnect = true;
                    }

                    console.log('关闭原因:', statusMessage);

                    // 只有在非手动断开且需要重连的情况下才自动重连
                    if (shouldReconnect && currentConnectionState !== CONNECTION_STATES.DISCONNECTED) {
                        attemptReconnect();
                    } else {
                        updateConnectionStatus(CONNECTION_STATES.DISCONNECTED, statusMessage);
                    }
                };

                ws.onerror = function(error) {
                    console.error('❌ WebSocket错误:', error);

                    // 停止心跳检测
                    if (heartbeatTimer) {
                        clearInterval(heartbeatTimer);
                        heartbeatTimer = null;
                    }

                    // 连接错误时尝试重连
                    if (currentConnectionState === CONNECTION_STATES.CONNECTING ||
                        currentConnectionState === CONNECTION_STATES.CONNECTED) {
                        console.log('连接错误，准备重连');
                        attemptReconnect();
                    }
                };

            } catch (error) {
                console.error('创建WebSocket失败:', error);
                updateConnectionStatus(CONNECTION_STATES.DISCONNECTED, '连接失败: ' + error.message);

                // 创建失败也尝试重连
                if (reconnectAttempts < RECONNECT_CONFIG.maxAttempts) {
                    attemptReconnect();
                }
            }
        }

        // 主连接函数
        function connect() {
            console.log('用户手动连接');
            clearReconnectTimer();
            reconnectAttempts = 0;
            connectWebSocket();
        }

        // 获取连接状态信息
        function getConnectionStatusInfo() {
            const info = {
                state: currentConnectionState,
                isConnected: isConnected,
                reconnectAttempts: reconnectAttempts,
                maxAttempts: RECONNECT_CONFIG.maxAttempts,
                sessionId: currentSessionId
            };

            if (ws) {
                info.readyState = ws.readyState;
                info.readyStateText = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'][ws.readyState];
            }

            return info;
        }

        // 断开连接
        function disconnect() {
            console.log('用户手动断开连接');
            clearReconnectTimer();

            // 设置为断开状态，阻止自动重连
            updateConnectionStatus(CONNECTION_STATES.DISCONNECTED, '已断开连接');

            if (ws) {
                // 清除事件处理器，避免触发重连
                ws.onopen = null;
                ws.onmessage = null;
                ws.onclose = null;
                ws.onerror = null;

                if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
                    ws.close(1000, '用户手动断开');
                }
                ws = null;
            }
        }

        // 检查连接状态
        function checkConnectionHealth() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                // 发送心跳包
                try {
                    ws.send(JSON.stringify({
                        type: 'ping',
                        timestamp: Date.now()
                    }));
                } catch (error) {
                    console.error('发送心跳包失败:', error);
                    attemptReconnect();
                }
            }
        }

        // 启动心跳检测
        function startHeartbeat() {
            return setInterval(checkConnectionHealth, 30000); // 每30秒检查一次
        }

        let heartbeatTimer = null;

        // 处理API响应
        function handleAPIResponse(data) {
            const action = data.action;
            const status = data.status;
            const result = status === 'success' ? data.result : data.error;

            addAPIResultMessage(action, status, result);
        }

        // 发送聊天消息
        function sendMessage() {
            console.log('sendMessage 函数被调用');

            if (!messageInput) {
                console.error('messageInput 元素未找到');
                return;
            }

            const message = messageInput.value.trim();
            console.log('输入消息:', message);
            console.log('连接状态:', isConnected);

            if (!message) {
                console.log('消息为空，不发送');
                return;
            }

            if (!isConnected) {
                console.log('未连接，不发送消息');
                alert('请先连接服务器');
                return;
            }

            if (!ws) {
                console.error('WebSocket 连接不存在');
                alert('WebSocket连接异常，请重新连接');
                return;
            }

            // 显示用户消息
            addMessage('user', message);

            // 发送到服务器
            const messageData = {
                type: 'chat',
                data: {
                    message: message,
                    project_id: DEFAULT_PROJECT_ID  // 使用默认项目ID
                },
                session_id: currentSessionId,
                user_id: currentUser.userId
            };

            console.log('发送消息数据:', messageData);
            console.log('项目ID类型:', typeof messageData.data.project_id, '值:', messageData.data.project_id);
            ws.send(JSON.stringify(messageData));

            // 清空输入框
            messageInput.value = '';
            adjustTextareaHeight();
        }

        // 发送API调用
        function sendAPICall(action, parameters = {}) {
            if (!isConnected) {
                alert('请先连接服务器');
                return;
            }

            const projectId = projectSelect.value || DEFAULT_PROJECT_ID; // 如果没有选择项目，使用默认项目ID

            const messageData = {
                type: 'api_call',
                data: {
                    action: action,
                    project_id: projectId,
                    parameters: parameters
                },
                session_id: currentSessionId,
                user_id: currentUser.userId
            };

            console.log('发送API调用:', messageData);
            ws.send(JSON.stringify(messageData));

            // 显示API调用消息
            addMessage('user', `API调用: ${getActionDisplayName(action)}`);
        }

        // 清空消息
        function clearMessages() {
            chatMessages.innerHTML = '';
        }

        // 自动调整输入框高度
        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // 模态框处理函数
        function showModal(modal) {
            modal.style.display = 'block';
        }

        function hideModal(modal) {
            modal.style.display = 'none';
        }

        function hideAllModals() {
            hideModal(loginModal);
            hideModal(trainAlgorithmModal);
            hideModal(monitoringServiceModal);
            hideModal(queryModal);
        }

        // 显示查询模态框
        function showQueryModal(action, title, fields) {
            queryModalTitle.textContent = title;

            let formHTML = '';
            fields.forEach(field => {
                formHTML += `
                    <div class="form-group">
                        <label for="${field.id}">${field.label}:</label>
                        <input type="${field.type || 'text'}" id="${field.id}" name="${field.name}"
                               ${field.required ? 'required' : ''} placeholder="${field.placeholder || ''}">
                    </div>
                `;
            });

            queryFormContent.innerHTML = formHTML;
            queryForm.dataset.action = action;
            showModal(queryModal);
        }

        // API按钮事件处理
        function handleAPIButtonClick(action) {
            switch(action) {
                case 'get_all_algorithms':
                case 'get_all_datasets':
                case 'get_all_iot':
                case 'get_all_services':
                    sendAPICall(action);
                    break;

                case 'get_algorithm_log':
                    showQueryModal(action, '查询算法训练信息', [
                        { id: 'algorithmName', name: 'algorithm_name', label: '算法名称', required: true, placeholder: '例如：水泵电流预警算法0723' }
                    ]);
                    break;

                case 'get_service_prediction':
                    showQueryModal(action, '查询服务预测结果', [
                        { id: 'serviceName', name: 'service_name', label: '服务名称', required: true, placeholder: '例如：水泵电流预警算法0612' }
                    ]);
                    break;

                case 'train_algorithm':
                    showModal(trainAlgorithmModal);
                    break;

                case 'start_monitoring_service':
                    showModal(monitoringServiceModal);
                    break;
            }
        }

        // 历史对话相关函数（简化版本）
        function loadConversations() {
            if (!isConnected) {
                console.log('未连接，跳过加载历史对话');
                return;
            }

            const requestData = {
                type: 'get_conversations',
                data: {
                    user_id: currentSessionId,
                    limit: 20,
                    offset: 0
                },
                session_id: currentSessionId
            };

            ws.send(JSON.stringify(requestData));
        }

        function handleConversationsList(data) {
            const conversations = data.conversations || [];

            if (conversations.length === 0) {
                conversationList.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 20px;">暂无历史对话</div>';
                return;
            }

            conversationList.innerHTML = conversations.map(conv => {
                const date = new Date(conv.last_message_at || conv.created_at);
                const timeStr = formatTime(date);
                const preview = conv.last_user_message || '新建算法训练任务';

                return `
                    <div class="conversation-item" data-id="${conv.conversation_id}">
                        <div class="user-avatar">U</div>
                        <div class="conversation-info">
                            <div class="conversation-name">${conv.title}</div>
                            <div class="conversation-preview">${preview.substring(0, 20)}${preview.length > 20 ? '...' : ''}</div>
                        </div>
                        <div class="conversation-time">${timeStr}</div>
                    </div>
                `;
            }).join('');

            // 绑定点击事件
            conversationList.querySelectorAll('.conversation-item').forEach(item => {
                item.addEventListener('click', () => {
                    conversationList.querySelectorAll('.conversation-item').forEach(i =>
                        i.classList.remove('active')
                    );
                    item.classList.add('active');
                    const conversationId = item.dataset.id;
                    switchConversation(conversationId);
                });
            });
        }

        function formatTime(date) {
            const now = new Date();
            const diff = now - date;
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (days === 0) {
                return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            } else if (days === 1) {
                return '昨天';
            } else if (days < 7) {
                return `${days}天前`;
            } else {
                return date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' });
            }
        }

        function switchConversation(conversationId) {
            if (!isConnected) {
                alert('请先连接服务器');
                return;
            }

            const requestData = {
                type: 'switch_conversation',
                data: {
                    conversation_id: conversationId
                },
                session_id: currentSessionId
            };

            ws.send(JSON.stringify(requestData));
        }

        function createNewConversation() {
            if (!isConnected) {
                alert('请先连接服务器');
                return;
            }

            clearMessages();
            conversationList.querySelectorAll('.conversation-item').forEach(item =>
                item.classList.remove('active')
            );

            const requestData = {
                type: 'new_conversation',
                data: {
                    title: '新建算法训练任务'
                },
                session_id: currentSessionId
            };

            ws.send(JSON.stringify(requestData));
        }

        function handleConversationRestored(data) {
            clearMessages();
            const messages = data.messages || [];
            messages.forEach(msg => {
                addMessage(msg.type, msg.content, msg.timestamp);
            });
            console.log(`对话恢复完成: ${data.conversation_id}, 共${data.total_messages}条消息`);
        }

        function handleNewConversationCreated(data) {
            console.log(`新对话创建成功: ${data.conversation_id}`);
            setTimeout(() => {
                loadConversations();
            }, 500);
        }

        // 显示连接状态详情
        function showConnectionDetails() {
            const info = getConnectionStatusInfo();
            const details = `
连接状态详情：
- 当前状态: ${info.state}
- 是否已连接: ${info.isConnected ? '是' : '否'}
- 重连尝试次数: ${info.reconnectAttempts}/${info.maxAttempts}
- 会话ID: ${info.sessionId || '未生成'}
${info.readyState !== undefined ? `- WebSocket状态: ${info.readyStateText} (${info.readyState})` : ''}
            `.trim();

            alert(details);
        }

        // 事件监听器
        // 基础功能事件
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        reconnectBtn.addEventListener('click', function() {
            console.log('用户手动重连');
            clearReconnectTimer();
            reconnectAttempts = 0;
            connectWebSocket();
        });
        clearBtn.addEventListener('click', clearMessages);

        // 连接按钮右键点击显示详情
        connectBtn.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            showConnectionDetails();
        });

        // 发送按钮事件 - 增加调试信息
        if (sendBtn) {
            sendBtn.addEventListener('click', function() {
                console.log('发送按钮被点击');
                sendMessage();
            });
            console.log('发送按钮事件监听器已添加');
        } else {
            console.error('发送按钮元素未找到，无法添加事件监听器');
        }

        newChatBtn.addEventListener('click', createNewConversation);

        // 项目选择器事件
        projectSelect.addEventListener('change', updateAPIButtonsState);

        // 用户登录相关事件
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const userId = document.getElementById('userId').value.trim();
            const username = document.getElementById('username').value.trim();

            if (userId && username) {
                loginUser(userId, username);
            } else {
                alert('请输入用户ID和用户名');
            }
        });

        guestLoginBtn.addEventListener('click', function() {
            const guestId = 'guest_' + Date.now();
            const guestName = '访客用户';
            loginUser(guestId, guestName);
        });

        // 快速测试登录按钮
        const quickTestLoginBtn = document.getElementById('quickTestLogin');
        if (quickTestLoginBtn) {
            quickTestLoginBtn.addEventListener('click', function() {
                const testId = 'test_user_' + Date.now();
                const testName = '测试用户';
                loginUser(testId, testName);
            });
        }

        logoutBtn.addEventListener('click', logoutUser);

        // API按钮事件
        getAllAlgorithmsBtn.addEventListener('click', () => handleAPIButtonClick('get_all_algorithms'));
        getAllDatasetsBtn.addEventListener('click', () => handleAPIButtonClick('get_all_datasets'));
        getAllIotBtn.addEventListener('click', () => handleAPIButtonClick('get_all_iot'));
        getAllServicesBtn.addEventListener('click', () => handleAPIButtonClick('get_all_services'));
        getAlgorithmLogBtn.addEventListener('click', () => handleAPIButtonClick('get_algorithm_log'));
        getServicePredictionBtn.addEventListener('click', () => handleAPIButtonClick('get_service_prediction'));
        trainAlgorithmBtn.addEventListener('click', () => handleAPIButtonClick('train_algorithm'));
        startMonitoringBtn.addEventListener('click', () => handleAPIButtonClick('start_monitoring_service'));

        // 训练算法表单事件
        trainAlgorithmForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const parameters = {};

            for (let [key, value] of formData.entries()) {
                parameters[key] = value;
            }

            sendAPICall('train_algorithm', parameters);
            hideModal(trainAlgorithmModal);
        });

        // 监控服务表单事件
        monitoringServiceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const parameters = {};

            for (let [key, value] of formData.entries()) {
                parameters[key] = value;
            }

            sendAPICall('start_monitoring_service', parameters);
            hideModal(monitoringServiceModal);
        });

        // 查询表单事件
        queryForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const action = this.dataset.action;
            const formData = new FormData(this);
            const parameters = {};

            for (let [key, value] of formData.entries()) {
                parameters[key] = value;
            }

            sendAPICall(action, parameters);
            hideModal(queryModal);
        });

        // 模态框关闭事件
        document.querySelectorAll('.close, .close-modal').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                hideModal(modal);
            });
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                hideModal(event.target);
            }
        });

        // 输入框事件
        // 输入框事件监听器 - 增加调试信息
        if (messageInput) {
            messageInput.addEventListener('input', adjustTextareaHeight);
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    console.log('Enter键被按下，发送消息');
                    e.preventDefault();
                    sendMessage();
                }
            });
            console.log('输入框事件监听器已添加');
        } else {
            console.error('输入框元素未找到，无法添加事件监听器');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化中...');

            // 验证关键DOM元素是否存在
            console.log('验证DOM元素:');
            console.log('messageInput:', messageInput ? '✓' : '✗');
            console.log('sendBtn:', sendBtn ? '✓' : '✗');
            console.log('connectBtn:', connectBtn ? '✓' : '✗');
            console.log('chatMessages:', chatMessages ? '✓' : '✗');
            console.log('默认项目ID:', DEFAULT_PROJECT_ID);

            updateConnectionStatus(CONNECTION_STATES.DISCONNECTED);
            updateUserDisplay();
            updateAPIButtonsState();

            // 检查URL参数是否包含自动登录
            const urlParams = new URLSearchParams(window.location.search);
            const autoLogin = urlParams.get('auto_login');

            if (autoLogin === 'guest') {
                // 自动访客登录
                console.log('执行自动访客登录');
                const guestId = 'guest_' + Date.now();
                const guestName = '访客用户';
                loginUser(guestId, guestName);
            } else {
                // 显示登录模态框
                showLoginModal();
            }
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            console.log('页面即将卸载，清理WebSocket连接');
            clearReconnectTimer();
            if (heartbeatTimer) {
                clearInterval(heartbeatTimer);
                heartbeatTimer = null;
            }
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.close(1000, '页面关闭');
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                console.log('页面隐藏');
            } else {
                console.log('页面显示');
                // 页面重新显示时检查连接状态
                if (currentConnectionState === CONNECTION_STATES.CONNECTED && ws && ws.readyState !== WebSocket.OPEN) {
                    console.log('检测到连接异常，尝试重连');
                    attemptReconnect();
                }
            }
        });
    </script>
</body>
</html>
