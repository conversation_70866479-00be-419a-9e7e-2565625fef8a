<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法智能体 - 专业版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            height: 100vh;
            overflow: hidden;
            background: #f5f5f5;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        /* 左侧边栏 */
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 600;
        }
        
        .settings-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
        }
        
        .setting-item:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .setting-icon {
            width: 16px;
            height: 16px;
        }
        
        .conversations-section {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: rgba(255,255,255,0.9);
        }
        
        .new-chat-btn {
            width: 100%;
            padding: 12px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
            transition: all 0.2s;
        }
        
        .new-chat-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .conversation-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .conversation-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .conversation-item:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .conversation-item.active {
            background: rgba(255,255,255,0.2);
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        
        .conversation-info {
            flex: 1;
            min-width: 0;
        }
        
        .conversation-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .conversation-preview {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .conversation-time {
            font-size: 11px;
            color: rgba(255,255,255,0.6);
            white-space: nowrap;
        }
        
        /* 右侧主区域 */
        .main-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .chat-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: white;
        }
        
        .agent-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .agent-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            color: #6b7280;
            transition: all 0.2s;
        }
        
        .action-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
            background: #f9fafb;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .message.assistant .message-avatar {
            background: linear-gradient(135deg, #6366f1, #4f46e5);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e5e7eb;
            color: #1f2937;
        }
        
        .message.user .message-content {
            background: #3b82f6;
            color: white;
        }
        
        .chat-input {
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            background: white;
        }
        
        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }
        
        .message-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            resize: none;
            font-size: 14px;
            font-family: inherit;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .message-input:focus {
            border-color: #3b82f6;
        }
        
        .send-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }
        
        .send-btn:hover:not(:disabled) {
            background: #2563eb;
        }
        
        .send-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .status-indicator {
            padding: 8px 16px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-indicator.connected {
            color: #059669;
            background: #ecfdf5;
        }
        
        .status-indicator.disconnected {
            color: #dc2626;
            background: #fef2f2;
        }
        /* 登录模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 0;
            border: none;
            border-radius: 8px;
            width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #1f2937;
        }

        .close {
            color: #9ca3af;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #374151;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #374151;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn-primary, .btn-secondary {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2563eb;
        }

        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
        }

        /* Markdown 内容样式 */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 12px 0 8px 0;
            font-weight: 600;
            line-height: 1.3;
        }

        .message-content h1 { font-size: 1.5em; color: #1f2937; }
        .message-content h2 { font-size: 1.3em; color: #374151; }
        .message-content h3 { font-size: 1.1em; color: #4b5563; }

        .message-content p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
            line-height: 1.5;
        }

        .message-content code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e11d48;
        }

        .message-content pre {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .message-content pre code {
            background: none;
            padding: 0;
            color: #1f2937;
        }

        .message-content blockquote {
            border-left: 4px solid #3b82f6;
            margin: 12px 0;
            padding: 8px 16px;
            background: #f8fafc;
            color: #4b5563;
            font-style: italic;
        }

        .message-content strong {
            font-weight: 600;
            color: #1f2937;
        }

        .message-content em {
            font-style: italic;
            color: #4b5563;
        }

        .message-content a {
            color: #3b82f6;
            text-decoration: none;
        }

        .message-content a:hover {
            text-decoration: underline;
        }

        .message-content hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 16px 0;
        }

        /* 确保用户消息不应用markdown样式 */
        .message.user .message-content h1,
        .message.user .message-content h2,
        .message.user .message-content h3,
        .message.user .message-content h4,
        .message.user .message-content h5,
        .message.user .message-content h6,
        .message.user .message-content p,
        .message.user .message-content ul,
        .message.user .message-content ol,
        .message.user .message-content li,
        .message.user .message-content code,
        .message.user .message-content pre,
        .message.user .message-content blockquote {
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;
            line-height: inherit;
        }
    </style>

    <!-- 引入 markdown-it 库 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
</head>
<body>
    <!-- 用户登录模态框 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>用户登录</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="userId">用户ID:</label>
                        <input type="text" id="userId" name="userId" required placeholder="请输入用户ID">
                    </div>
                    <div class="form-group">
                        <label for="username">用户名:</label>
                        <input type="text" id="username" name="username" required placeholder="请输入用户名">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">登录</button>
                        <button type="button" id="guestLogin" class="btn-secondary">访客登录</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="app-container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo-section">
                    <div class="logo">🤖</div>
                    <div class="app-title">算法智能体</div>
                </div>
                <div class="settings-section">
                    <div class="setting-item" id="userInfo">
                        <span class="setting-icon">👤</span>
                        <span id="currentUser">未登录</span>
                    </div>
                    <div class="setting-item" id="logoutBtn" style="display: none;">
                        <span class="setting-icon">🚪</span>
                        <span>退出登录</span>
                    </div>
                </div>
            </div>
            
            <div class="conversations-section">
                <div class="section-title">对话</div>
                <button class="new-chat-btn" id="newChatBtn">
                    ➕ 新建对话
                </button>
                
                <div class="section-title">历史信息</div>
                <div class="conversation-list" id="conversationList">
                    <!-- 历史对话列表将在这里动态生成 -->
                </div>
            </div>
        </div>
        
        <!-- 右侧主区域 -->
        <div class="main-area">
            <div class="status-indicator" id="statusIndicator">未连接</div>
            
            <div class="chat-header">
                <div class="agent-info">
                    <div class="agent-avatar">🤖</div>
                    <div class="agent-name">智能体1</div>
                </div>
                <div class="header-actions">
                    <button class="action-btn" id="connectBtn">🔗 连接</button>
                    <button class="action-btn" id="disconnectBtn" disabled>❌ 断开</button>
                    <button class="action-btn" id="clearBtn">🗑️ 清空</button>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <!-- 对话消息将在这里显示 -->
            </div>
            
            <div class="chat-input">
                <div class="input-container">
                    <textarea 
                        class="message-input" 
                        id="messageInput" 
                        placeholder="输入消息，例如：我想用LSTM做预测，学习率0.001，批次大小32"
                        disabled
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn" disabled>
                        📤
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 markdown-it 实例
        const md = window.markdownit({
            html: true,        // 允许HTML标签
            breaks: true,      // 将换行符转换为<br>
            linkify: true,     // 自动识别链接
            typographer: true  // 启用一些语言中性的替换和引号美化
        });

        // WebSocket连接管理
        let ws = null;
        let currentSessionId = null;
        let currentConversationId = null;
        let isConnected = false;

        // 用户信息管理
        let currentUser = {
            userId: null,
            username: null,
            isLoggedIn: false
        };

        // DOM元素
        const statusIndicator = document.getElementById('statusIndicator');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const chatMessages = document.getElementById('chatMessages');
        const conversationList = document.getElementById('conversationList');
        const newChatBtn = document.getElementById('newChatBtn');

        // 用户登录相关DOM元素
        const loginModal = document.getElementById('loginModal');
        const loginForm = document.getElementById('loginForm');
        const guestLoginBtn = document.getElementById('guestLogin');
        const currentUserSpan = document.getElementById('currentUser');
        const logoutBtn = document.getElementById('logoutBtn');
        const closeBtn = document.querySelector('.close');

        // 生成会话ID - 使用32位UUID
        function generateSessionId() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            }).replace(/-/g, '');
        }

        // 用户登录相关函数
        function showLoginModal() {
            loginModal.style.display = 'block';
        }

        function hideLoginModal() {
            loginModal.style.display = 'none';
        }

        function updateUserDisplay() {
            if (currentUser.isLoggedIn) {
                currentUserSpan.textContent = currentUser.username;
                logoutBtn.style.display = 'block';
            } else {
                currentUserSpan.textContent = '未登录';
                logoutBtn.style.display = 'none';
            }
        }

        function loginUser(userId, username) {
            currentUser.userId = userId;
            currentUser.username = username;
            currentUser.isLoggedIn = true;
            updateUserDisplay();
            hideLoginModal();

            // 登录成功后自动连接
            connect();
        }

        function logoutUser() {
            // 断开连接
            if (isConnected) {
                disconnect();
            }

            // 清除用户信息
            currentUser.userId = null;
            currentUser.username = null;
            currentUser.isLoggedIn = false;
            updateUserDisplay();

            // 清空对话
            chatMessages.innerHTML = '';
            conversationList.innerHTML = '';

            // 显示登录模态框
            showLoginModal();
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            if (connected) {
                statusIndicator.textContent = '已连接';
                statusIndicator.className = 'status-indicator connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                messageInput.disabled = false;
                sendBtn.disabled = false;
            } else {
                statusIndicator.textContent = '未连接';
                statusIndicator.className = 'status-indicator disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                messageInput.disabled = true;
                sendBtn.disabled = true;
            }
        }

        // 添加消息到聊天区域
        function addMessage(type, content, timestamp = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'user' ? '👤' : '🤖';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            // 对AI回复的消息进行markdown解析，用户消息保持原样
            if (type === 'assistant') {
                // 使用markdown-it解析内容
                const renderedContent = md.render(content);
                contentDiv.innerHTML = renderedContent;
            } else {
                // 用户消息使用textContent，不解析markdown
                contentDiv.textContent = content;
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // WebSocket连接
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                return;
            }

            // 检查用户是否已登录
            if (!currentUser.isLoggedIn) {
                showLoginModal();
                return;
            }

            currentSessionId = generateSessionId();
            const wsUrl = `ws://localhost:8008/api/ws/chat?session_id=${currentSessionId}&user_id=${currentUser.userId}&username=${encodeURIComponent(currentUser.username)}`;
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    console.log('WebSocket连接已建立');
                    updateConnectionStatus(true);
                    // 连接成功后加载历史对话列表
                    setTimeout(() => {
                        loadConversations();
                    }, 1000);
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('收到消息:', data);

                        if (data.type === 'welcome' || data.type === 'chat_response') {
                            addMessage('assistant', data.data.message);
                        } else if (data.type === 'conversations_list') {
                            handleConversationsList(data.data);
                        } else if (data.type === 'conversation_restored') {
                            handleConversationRestored(data.data);
                        } else if (data.type === 'conversation_switched') {
                            console.log('对话切换成功:', data.data.conversation_id);
                        } else if (data.type === 'new_conversation_created') {
                            handleNewConversationCreated(data.data);
                        } else if (data.type === 'thinking') {
                            // 可以显示思考状态
                            console.log('AI正在思考...');
                        }
                    } catch (error) {
                        console.error('解析消息失败:', error);
                        addMessage('assistant', '消息解析失败');
                    }
                };
                
                ws.onclose = function(event) {
                    console.log('WebSocket连接已关闭');
                    updateConnectionStatus(false);
                };
                
                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    updateConnectionStatus(false);
                };
                
            } catch (error) {
                console.error('连接失败:', error);
                alert('连接失败，请检查服务器是否启动');
            }
        }

        // 断开连接
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateConnectionStatus(false);
        }

        // 发送消息
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !isConnected) return;

            // 显示用户消息
            addMessage('user', message);
            
            // 发送到服务器
            const messageData = {
                type: 'chat',
                data: {
                    message: message
                },
                session_id: currentSessionId
            };
            
            ws.send(JSON.stringify(messageData));
            
            // 清空输入框
            messageInput.value = '';
            adjustTextareaHeight();
        }

        // 清空消息
        function clearMessages() {
            chatMessages.innerHTML = '';
        }

        // 自动调整输入框高度
        function adjustTextareaHeight() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // 加载历史对话列表
        function loadConversations() {
            if (!isConnected) {
                console.log('未连接，跳过加载历史对话');
                return;
            }

            // 发送获取对话列表请求
            const requestData = {
                type: 'get_conversations',
                data: {
                    user_id: currentSessionId,
                    limit: 20,
                    offset: 0
                },
                session_id: currentSessionId
            };

            ws.send(JSON.stringify(requestData));
        }

        // 处理对话列表响应
        function handleConversationsList(data) {
            const conversations = data.conversations || [];

            if (conversations.length === 0) {
                conversationList.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 20px;">暂无历史对话</div>';
                return;
            }

            conversationList.innerHTML = conversations.map(conv => {
                const date = new Date(conv.last_message_at || conv.created_at);
                const timeStr = formatTime(date);
                const preview = conv.last_user_message || '新建算法训练任务';

                return `
                    <div class="conversation-item" data-id="${conv.conversation_id}">
                        <div class="user-avatar">U</div>
                        <div class="conversation-info">
                            <div class="conversation-name">${conv.title}</div>
                            <div class="conversation-preview">${preview.substring(0, 20)}${preview.length > 20 ? '...' : ''}</div>
                        </div>
                        <div class="conversation-time">${timeStr}</div>
                    </div>
                `;
            }).join('');

            // 绑定点击事件
            conversationList.querySelectorAll('.conversation-item').forEach(item => {
                item.addEventListener('click', () => {
                    // 移除其他项的active状态
                    conversationList.querySelectorAll('.conversation-item').forEach(i =>
                        i.classList.remove('active')
                    );
                    // 添加当前项的active状态
                    item.classList.add('active');

                    // 切换到对应的历史对话
                    const conversationId = item.dataset.id;
                    switchConversation(conversationId);
                });
            });
        }

        // 格式化时间
        function formatTime(date) {
            const now = new Date();
            const diff = now - date;
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (days === 0) {
                return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            } else if (days === 1) {
                return '昨天';
            } else if (days < 7) {
                return `${days}天前`;
            } else {
                return date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' });
            }
        }

        // 切换对话
        function switchConversation(conversationId) {
            if (!isConnected) {
                alert('请先连接服务器');
                return;
            }

            const requestData = {
                type: 'switch_conversation',
                data: {
                    conversation_id: conversationId
                },
                session_id: currentSessionId
            };

            ws.send(JSON.stringify(requestData));
        }

        // 创建新对话
        function createNewConversation() {
            if (!isConnected) {
                alert('请先连接服务器');
                return;
            }

            // 清空当前消息显示
            clearMessages();

            // 移除所有对话项的active状态
            conversationList.querySelectorAll('.conversation-item').forEach(item =>
                item.classList.remove('active')
            );

            // 发送创建新对话请求
            const requestData = {
                type: 'new_conversation',
                data: {
                    title: '新建算法训练任务'
                },
                session_id: currentSessionId
            };

            ws.send(JSON.stringify(requestData));
        }

        // 处理对话恢复响应
        function handleConversationRestored(data) {
            // 清空当前消息
            clearMessages();

            // 加载历史消息
            const messages = data.messages || [];
            messages.forEach(msg => {
                addMessage(msg.type, msg.content, msg.timestamp);
            });

            console.log(`对话恢复完成: ${data.conversation_id}, 共${data.total_messages}条消息`);
        }

        // 处理新对话创建成功响应
        function handleNewConversationCreated(data) {
            console.log(`新对话创建成功: ${data.conversation_id}`);

            // 刷新历史对话列表
            setTimeout(() => {
                loadConversations();
            }, 500);
        }

        // 事件监听
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearBtn.addEventListener('click', clearMessages);
        sendBtn.addEventListener('click', sendMessage);
        newChatBtn.addEventListener('click', () => {
            createNewConversation();
        });

        // 用户登录相关事件监听
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const userId = document.getElementById('userId').value.trim();
            const username = document.getElementById('username').value.trim();

            if (userId && username) {
                loginUser(userId, username);
            } else {
                alert('请输入用户ID和用户名');
            }
        });

        guestLoginBtn.addEventListener('click', function() {
            const guestId = 'guest_' + Date.now();
            const guestName = '访客用户';
            loginUser(guestId, guestName);
        });

        logoutBtn.addEventListener('click', logoutUser);

        closeBtn.addEventListener('click', hideLoginModal);

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            if (event.target === loginModal) {
                hideLoginModal();
            }
        });

        messageInput.addEventListener('input', adjustTextareaHeight);
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus(false);
            updateUserDisplay();

            // 显示登录模态框
            showLoginModal();
        });
    </script>
</body>
</html>
