-- =====================================================
-- 算法智能体数据库表结构设计
-- 数据库：indusaio_agent
-- 字符集：utf8mb4
-- 创建时间：2025-01-22
-- =====================================================

-- 使用指定数据库
USE indusaio_agent;

-- =====================================================
-- 1. 对话管理表 (conversations)
-- 管理用户与智能体的对话会话
-- =====================================================
CREATE TABLE IF NOT EXISTS conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '对话唯一标识',
    conversation_id VARCHAR(64) NOT NULL UNIQUE COMMENT '对话ID（业务主键）',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    session_id VARCHAR(100) COMMENT 'WebSocket会话ID',
    title VARCHAR(200) DEFAULT '新建算法训练任务' COMMENT '对话标题',
    conversation_type ENUM('training', 'query', 'report', 'guide') DEFAULT 'training' COMMENT '对话类型',
    status ENUM('active', 'completed', 'failed', 'cancelled') DEFAULT 'active' COMMENT '对话状态',
    current_stage ENUM('welcome', 'guided', 'param_extraction', 'param_confirmation', 'training', 'completed') DEFAULT 'welcome' COMMENT '当前阶段',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比(0-100)',
    task_summary TEXT COMMENT '任务摘要描述',
    algorithm_type VARCHAR(50) COMMENT '算法类型(LSTM/CNN/等)',
    last_message_at TIMESTAMP NULL COMMENT '最后消息时间',
    context_token_count INT DEFAULT 0 COMMENT '上下文token数量',
    auto_title BOOLEAN DEFAULT TRUE COMMENT '是否自动生成标题',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',

    INDEX idx_user_id (user_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_session_id (session_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_conversation_type (conversation_type),
    INDEX idx_last_message_at (last_message_at),
    INDEX idx_user_last_message (user_id, last_message_at DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对话管理表';

-- =====================================================
-- 2. 对话消息记录表 (conversation_messages)
-- 存储对话中的每条消息记录
-- =====================================================
CREATE TABLE IF NOT EXISTS conversation_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息唯一标识',
    conversation_id VARCHAR(64) NOT NULL COMMENT '对话ID',
    message_type ENUM('user', 'assistant', 'system') NOT NULL COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    message_data JSON COMMENT '消息附加数据(参数、选项等)',
    interaction_type VARCHAR(50) COMMENT '交互类型(welcome_guide/preset_question_1等)',
    message_sequence INT NOT NULL DEFAULT 0 COMMENT '消息序号',
    key_info JSON COMMENT '关键信息提取',
    token_count INT DEFAULT 0 COMMENT '消息token数量',
    parent_message_id BIGINT COMMENT '父消息ID（用于消息树）',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间',

    INDEX idx_conversation_id (conversation_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_message_type (message_type),
    INDEX idx_message_sequence (conversation_id, message_sequence),
    INDEX idx_token_count (token_count),
    INDEX idx_parent_message (parent_message_id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对话消息记录表';

-- =====================================================
-- 3. 算法任务表 (algorithm_tasks)
-- 存储具体的算法训练任务信息
-- =====================================================
CREATE TABLE IF NOT EXISTS algorithm_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务唯一标识',
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '任务ID（业务主键）',
    conversation_id VARCHAR(64) NOT NULL COMMENT '关联的对话ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    task_name VARCHAR(200) COMMENT '任务名称',
    algorithm_type ENUM('LSTM', 'CNN', 'RNN', 'Transformer', 'LinearRegression', 'RandomForest', 'SVM', 'Other') NOT NULL COMMENT '算法类型',
    task_description TEXT COMMENT '任务描述',
    dataset_info JSON COMMENT '数据集信息',
    status ENUM('created', 'parameter_setting', 'training', 'completed', 'failed', 'cancelled') DEFAULT 'created' COMMENT '任务状态',
    training_progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '训练进度(0-100)',
    start_time TIMESTAMP NULL COMMENT '训练开始时间',
    end_time TIMESTAMP NULL COMMENT '训练结束时间',
    estimated_duration INT COMMENT '预计训练时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_algorithm_type (algorithm_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='算法任务表';

-- =====================================================
-- 4. 任务参数表 (task_parameters)
-- 存储算法任务的详细参数配置
-- =====================================================
CREATE TABLE IF NOT EXISTS task_parameters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '参数记录唯一标识',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    parameter_name VARCHAR(100) NOT NULL COMMENT '参数名称',
    parameter_value TEXT COMMENT '参数值',
    parameter_type ENUM('int', 'float', 'string', 'boolean', 'json') DEFAULT 'string' COMMENT '参数类型',
    is_required BOOLEAN DEFAULT FALSE COMMENT '是否必需参数',
    default_value TEXT COMMENT '默认值',
    description TEXT COMMENT '参数描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_parameter_name (parameter_name),
    UNIQUE KEY uk_task_parameter (task_id, parameter_name),
    FOREIGN KEY (task_id) REFERENCES algorithm_tasks(task_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务参数表';

-- =====================================================
-- 5. 训练日志表 (training_logs)
-- 存储训练过程中的日志信息
-- =====================================================
CREATE TABLE IF NOT EXISTS training_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志唯一标识',
    task_id VARCHAR(64) NOT NULL COMMENT '任务ID',
    log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO' COMMENT '日志级别',
    log_type ENUM('training', 'validation', 'system', 'error') DEFAULT 'training' COMMENT '日志类型',
    message TEXT NOT NULL COMMENT '日志消息',
    metrics JSON COMMENT '训练指标数据(loss, accuracy等)',
    epoch INT COMMENT '训练轮次',
    step INT COMMENT '训练步数',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '日志时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_log_level (log_level),
    INDEX idx_log_type (log_type),
    INDEX idx_timestamp (timestamp),
    INDEX idx_epoch (epoch),
    FOREIGN KEY (task_id) REFERENCES algorithm_tasks(task_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='训练日志表';

-- =====================================================
-- 6. 任务结果表 (task_results)
-- 存储任务的最终结果和评估指标
-- =====================================================
CREATE TABLE IF NOT EXISTS task_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '结果唯一标识',
    task_id VARCHAR(64) NOT NULL UNIQUE COMMENT '任务ID',
    result_type ENUM('training_result', 'evaluation_result', 'prediction_result') DEFAULT 'training_result' COMMENT '结果类型',
    accuracy DECIMAL(8,6) COMMENT '准确率',
    loss DECIMAL(10,6) COMMENT '损失值',
    metrics JSON COMMENT '详细评估指标',
    model_path VARCHAR(500) COMMENT '模型文件路径',
    model_size BIGINT COMMENT '模型文件大小(字节)',
    evaluation_report TEXT COMMENT '评估报告',
    gb_standard_score DECIMAL(5,2) COMMENT 'GB标准评分',
    gb_standard_level ENUM('优秀', '良好', '合格', '不合格') COMMENT 'GB标准等级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_task_id (task_id),
    INDEX idx_result_type (result_type),
    INDEX idx_accuracy (accuracy),
    INDEX idx_gb_standard_level (gb_standard_level),
    FOREIGN KEY (task_id) REFERENCES algorithm_tasks(task_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务结果表';

-- =====================================================
-- 7. 会话状态表 (session_states)
-- 存储会话的详细状态信息（原有功能兼容）
-- =====================================================
CREATE TABLE IF NOT EXISTS session_states (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '状态记录唯一标识',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    conversation_id VARCHAR(64) COMMENT '关联的对话ID',
    task_id VARCHAR(64) COMMENT '关联的任务ID',
    state_data JSON NOT NULL COMMENT '状态数据(JSON格式)',
    context_summary JSON COMMENT '上下文摘要',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后活动时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_user_session (user_id, session_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_task_id (task_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_is_active (is_active),
    UNIQUE KEY uk_user_session (user_id, session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话状态表';

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入示例对话记录（可选）
-- INSERT INTO conversations (conversation_id, user_id, title, conversation_type, status) 
-- VALUES ('conv_demo_001', 'demo_user', '示例算法训练任务', 'training', 'active');

-- =====================================================
-- 8. 对话摘要表 (context_summaries)
-- 存储对话的智能摘要信息，用于上下文管理
-- =====================================================
CREATE TABLE IF NOT EXISTS context_summaries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '摘要唯一标识',
    conversation_id VARCHAR(64) NOT NULL COMMENT '对话ID',
    summary_content TEXT NOT NULL COMMENT '摘要内容',
    key_points JSON COMMENT '关键要点',
    message_count INT DEFAULT 0 COMMENT '摘要的消息数量',
    token_count INT DEFAULT 0 COMMENT '摘要token数量',
    summary_type ENUM('auto', 'manual', 'periodic') DEFAULT 'auto' COMMENT '摘要类型',
    summary_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '摘要生成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_conversation_id (conversation_id),
    INDEX idx_summary_time (summary_time),
    INDEX idx_summary_type (summary_type),
    FOREIGN KEY (conversation_id) REFERENCES conversations(conversation_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对话摘要表';

-- =====================================================
-- 创建完成提示
-- =====================================================
SELECT 'Database schema created successfully!' as message;
