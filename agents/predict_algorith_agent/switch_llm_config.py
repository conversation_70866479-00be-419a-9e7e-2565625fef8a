#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速切换大模型配置的脚本
"""

import sys
import os

# 添加agents目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

def show_usage():
    """显示使用说明"""
    print("""
🤖 大模型配置快速切换工具

📋 使用方法:
  python switch_llm_config.py [命令] [参数]

🔧 可用命令:
  show                    - 显示当前配置
  list                    - 显示所有提供商
  switch <provider>       - 切换提供商 (ds-int8/qwen/deepseek/openai/claude)
  update <provider> <key> - 更新API Key
  test                    - 测试当前配置
  
📝 示例:
  python switch_llm_config.py show
  python switch_llm_config.py switch deepseek
  python switch_llm_config.py update qwen sk-new-api-key-here
  python switch_llm_config.py test
    """)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    try:
        from agents.config import (
            get_current_llm_config, set_llm_provider, 
            update_llm_api_key, get_llm_providers_info,
            LLM_PROVIDERS, CURRENT_LLM_PROVIDER
        )
        
        if command == "show":
            print("🤖 当前大模型配置")
            print("=" * 40)
            
            config = get_current_llm_config()
            print(f"📋 提供商: {CURRENT_LLM_PROVIDER}")
            print(f"🏷️ 描述: {config['description']}")
            print(f"🎯 模型: {config['model_name']}")
            print(f"🌐 URL: {config['base_url']}")
            print(f"🔑 Key: {config['api_key'][:10]}...{config['api_key'][-4:] if config['api_key'] else 'None'}")
            
        elif command == "list":
            print("🌟 所有可用提供商")
            print("=" * 40)
            
            for name, config in LLM_PROVIDERS.items():
                status = "✅ 当前" if name == CURRENT_LLM_PROVIDER else "⭕ 可选"
                key_status = "🔑" if config['api_key'] else "❌"
                print(f"{status} {name}: {config['description']} {key_status}")
            
        elif command == "switch":
            if len(sys.argv) < 3:
                print("❌ 请指定提供商名称")
                print("💡 可用: ds-int8, qwen, deepseek, openai, claude")
                return
            
            provider = sys.argv[2].lower()
            if set_llm_provider(provider):
                print("✅ 切换成功")
                print("⚠️ 请重启服务使配置生效")
            
        elif command == "update":
            if len(sys.argv) < 4:
                print("❌ 请指定提供商名称和API Key")
                print("💡 格式: python switch_llm_config.py update <provider> <api_key>")
                return
            
            provider = sys.argv[2].lower()
            api_key = sys.argv[3]
            
            if update_llm_api_key(provider, api_key):
                print("✅ API Key更新成功")
                print("⚠️ 请重启服务使配置生效")
            
        elif command == "test":
            print("🧪 测试当前大模型配置")
            print("=" * 40)
            
            try:
                from pydantic_ai import Agent
                from pydantic_ai.models.openai import OpenAIModel
                from pydantic_ai.providers.openai import OpenAIProvider
                from pydantic import BaseModel, Field
                
                config = get_current_llm_config()
                
                # 创建测试模型
                test_model = OpenAIModel(
                    config['model_name'],
                    provider=OpenAIProvider(
                        base_url=config['base_url'],
                        api_key=config['api_key']
                    )
                )
                
                class TestResponse(BaseModel):
                    message: str = Field(description="测试响应")
                
                test_agent = Agent(
                    model=test_model,
                    output_type=TestResponse,
                    system_prompt="你是测试助手，请简单回复。"
                )
                
                print("📤 发送测试请求...")
                result = test_agent.run_sync("你好")
                
                print("📥 测试响应:")
                print(f"   {result.data.message}")
                print("✅ 大模型配置测试成功")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                print("💡 请检查API Key和网络连接")
        
        elif command == "help" or command == "-h" or command == "--help":
            show_usage()
            
        else:
            print(f"❌ 未知命令: {command}")
            show_usage()
            
    except ImportError as e:
        print(f"❌ 导入配置失败: {e}")
        print("💡 请确保在正确的目录中运行此脚本")
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    main()
