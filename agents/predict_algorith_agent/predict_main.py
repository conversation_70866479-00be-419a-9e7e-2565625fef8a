from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
import sys
import locale
import logging
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 首先设置部署环境
from agents.predict_algorith_agent.utils.deploy_config import setup_environment, print_environment_info
setup_environment()

from agents.predict_algorith_agent.core.predictive_agent import router as predictive_router, PredictiveAlgorithmAssistant
from agents.predict_algorith_agent.api.conversation_routes import router as conversation_router
# 导入WebSocket路由模块
from agents.predict_algorith_agent.api.websocket_routes import router as websocket_router
from agents.config import PREDICT_MAIN_HOST, PREDICT_MAIN_PORT
import time

# 导入统一日志配置
from agents.predict_algorith_agent.utils.logging_config import (
    configure_global_logging, get_main_logger, should_log_debug, get_log_config_summary
)

# 设置编码
import os
os.environ['PYTHONIOENCODING'] = 'utf-8'
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# 配置统一日志 - 使用环境变量配置
configure_global_logging()
logger = get_main_logger()

# 打印日志配置摘要
if should_log_debug():
    logger.info("=== 日志配置摘要 ===")
    config_summary = get_log_config_summary()
    for key, value in config_summary.items():
        logger.info(f"  {key}: {value}")
    logger.info("===================")

app = FastAPI()

if should_log_debug():
    logger.debug("=== 主应用启动，注册路由 ===")
    logger.debug(f"注册预测算法路由，路由数量: {len(predictive_router.routes)}")

app.include_router(predictive_router, prefix="/api")

if should_log_debug():
    logger.debug(f"注册对话管理路由，路由数量: {len(conversation_router.routes)}")
    for route in conversation_router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            logger.debug(f"  - /api{route.path} [{list(route.methods)}]")

app.include_router(conversation_router, prefix="/api")

# 注册WebSocket路由
if should_log_debug():
    logger.debug(f"注册WebSocket路由，路由数量: {len(websocket_router.routes)}")
    for route in websocket_router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            logger.debug(f"  - /api{route.path} [{list(route.methods)}]")

app.include_router(websocket_router, prefix="/api")

if should_log_debug():
    logger.debug("WebSocket路由注册完成")

# 配置静态文件服务
static_path = os.path.join(os.path.dirname(__file__), "static")
if should_log_debug():
    logger.debug(f"配置静态文件服务，路径: {static_path}")
app.mount("/static", StaticFiles(directory=static_path), name="static")

logger.info(f"主应用启动完成，总路由数量: {len(app.routes)}")

# 列出所有注册的路由用于调试
if should_log_debug():
    logger.debug("=== 所有注册的路由 ===")
    for i, route in enumerate(app.routes):
        if hasattr(route, 'path'):
            methods = getattr(route, 'methods', ['UNKNOWN'])
            logger.debug(f"  {i+1}. {route.path} [{list(methods)}]")

# 跨域配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可根据需要指定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 使用统一日志配置，无需重复配置

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info(f"Request: {request.method} {request.url}")
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    logger.info(f"Response status: {response.status_code} ({duration:.3f}s)")
    return response

# 健康检查接口
@app.get("/health")
def health_check():
    return {"status": "ok"}

# PDF报告下载接口
@app.get("/api/predict_algorith/report/pdf")
def download_pdf(task_id: str):
    agent = PredictiveAlgorithmAssistant()
    pdf_path = agent.get_report_pdf(task_id)
    return FileResponse(pdf_path, filename=f"report_{task_id}.pdf")

if __name__ == "__main__":
    import uvicorn
    # 使用直接的app对象引用，避免reload模式下的模块重载问题
    uvicorn.run(app, host=PREDICT_MAIN_HOST, port=PREDICT_MAIN_PORT, log_level="info")