#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法平台API数据模型
定义与算法平台交互的所有数据结构
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime


# ==================== 通用响应模型 ====================

class BaseApiResponse(BaseModel):
    """API响应基础模型"""
    msg: str = Field(..., description="请求结果说明")


# ==================== 1. 查询全部算法名称 ====================

class AlgorithmInfo(BaseModel):
    """算法信息"""
    name: str = Field(..., description="算法名称")
    project_id: str = Field(..., description="项目号")


class GetAllAlgorithmResponse(BaseApiResponse):
    """查询全部算法名称响应"""
    algorithm_name: List[List[str]] = Field(..., description="算法名称及其所属的项目号")


# ==================== 2. 查询全部数据集名称 ====================

class DatasetInfo(BaseModel):
    """数据集信息"""
    name: str = Field(..., description="数据集名称")
    project_id: str = Field(..., description="项目号")


class GetAllDatasetResponse(BaseApiResponse):
    """查询全部数据集名称响应"""
    dataset_name: List[List[str]] = Field(..., description="全部的数据集名称和项目号")


# ==================== 3. 训练新算法 ====================

class AlgorithmTrainingRequest(BaseModel):
    """训练新算法请求"""
    parameter1: str = Field(..., description="算法名称")
    parameter2: str = Field(..., description="算法类型")
    parameter3: str = Field(..., description="数据集")
    parameter4: str = Field(..., description="输入特征维度,迭代次数")
    parameter5: str = Field(..., description="LSTM隐藏层的大小")
    parameter6: str = Field(..., description="lstm层数")
    parameter7: str = Field(..., description="lstm的dropout比例")
    parameter8: str = Field(..., description="是否使用双向lstm")
    parameter9: str = Field(..., description="输出维度的个数")
    parameter10: str = Field(..., description="学习率大小")
    parameter11: str = Field(..., description="样本划分比例")
    parameter12: str = Field(..., description="优化器")
    parameter13: str = Field(..., description="损失函数")
    parameter14: str = Field(..., description="标签列")
    parameter15: str = Field(..., description="数据列")
    parameter16: str = Field(..., description="字段")
    project_number: str = Field(..., description="项目编号")

    def to_form_data(self) -> Dict[str, str]:
        """转换为FormData格式"""
        return {
            'parameter1': self.parameter1,
            'parameter2': self.parameter2,
            'parameter3': self.parameter3,
            'parameter4': self.parameter4,
            'parameter5': self.parameter5,
            'parameter6': self.parameter6,
            'parameter7': self.parameter7,
            'parameter8': self.parameter8,
            'parameter9': self.parameter9,
            'parameter10': self.parameter10,
            'parameter11': self.parameter11,
            'parameter12': self.parameter12,
            'parameter13': self.parameter13,
            'parameter14': self.parameter14,
            'parameter15': self.parameter15,
            'parameter16': self.parameter16,
            'project_number': self.project_number
        }


class AlgorithmTrainingResponse(BaseApiResponse):
    """训练新算法响应"""
    message: str = Field(..., description="启动结果说明")
    algoname: str = Field(..., description="启动算法的名称")
    dataset: str = Field(..., description="算法用的数据集")


# ==================== 4. 查询全部IOT名称 ====================

class IOTInfo(BaseModel):
    """IOT信息"""
    tables: Dict[str, str] = Field(..., description="IOT数据库表名映射")
    fields: Dict[str, str] = Field(..., description="用户定义的字段值及其物理含义")
    project_id: str = Field(..., description="所属项目号")


class GetAllIOTResponse(BaseApiResponse):
    """查询全部IOT名称响应"""
    iot_info: List[List[Any]] = Field(..., description="IOT的数据列表")


# ==================== 5. 启动监控分析服务 ====================

class MonitoringServiceRequest(BaseModel):
    """启动监控分析服务请求"""
    db_ip: str = Field(..., description="数据库地址")
    db_name: str = Field(..., description="数据库表名")
    list_name: str = Field(..., description="关键数据的字段")
    implement_name: str = Field(..., description="服务名称")
    project_name: str = Field(..., description="所属的项目号")

    def to_form_data(self) -> Dict[str, str]:
        """转换为FormData格式"""
        return {
            'db_ip': self.db_ip,
            'db_name': self.db_name,
            'list_name': self.list_name,
            'implement_name': self.implement_name,
            'project_name': self.project_name
        }


class MonitoringServiceResponse(BaseApiResponse):
    """启动监控分析服务响应"""
    name: str = Field(..., description="服务名称")


# ==================== 6. 查询算法训练信息 ====================

class AlgorithmLogInfo(BaseModel):
    """算法训练日志信息"""
    Algorithm_parameter: str = Field(..., description="算法训练参数")
    Datasetname: str = Field(..., description="数据集名称")
    MAE: Optional[float] = Field(None, description="平均绝对误差")
    MAPE: Optional[float] = Field(None, description="平均绝对百分比误差")


class GetAlgorithmLogResponse(BaseApiResponse):
    """查询算法训练信息响应"""
    algorithm_log: AlgorithmLogInfo = Field(..., description="算法训练时的信息日志")


# ==================== 7. 查询全部服务名称 ====================

class ServiceInfo(BaseModel):
    """服务信息"""
    algorithm_name: str = Field(..., description="所调用的算法名称")
    field_value: str = Field(..., description="分析的字段")
    status: str = Field(..., description="运行状态")
    tablename: str = Field(..., description="分析的数据表名称")


class GetAllImplementResponse(BaseApiResponse):
    """查询全部服务名称响应"""
    implement_info: List[ServiceInfo] = Field(..., description="该项目下已生成服务的数据信息")


# ==================== 8. 查询服务预测结果 ====================

class PredictionResult(BaseModel):
    """预测结果"""
    probability: str = Field(..., description="概率")
    riskLevel: str = Field(..., description="风险等级")
    riskType: str = Field(..., description="风险类型")


class GetImplementLogResponse(BaseApiResponse):
    """查询服务预测结果响应"""
    implement_info: PredictionResult = Field(..., description="所生成算法的分析结论")


# ==================== 请求参数模型 ====================

class GetIOTRequest(BaseModel):
    """查询IOT请求参数"""
    project: str = Field(..., description="项目号")


class GetAlgorithmLogRequest(BaseModel):
    """查询算法训练信息请求参数"""
    name: str = Field(..., description="算法名称")
    project: str = Field(..., description="项目号")


class GetImplementRequest(BaseModel):
    """查询服务请求参数"""
    project: str = Field(..., description="项目号")


class GetImplementLogRequest(BaseModel):
    """查询服务预测结果请求参数"""
    name: str = Field(..., description="服务名称")
    project: str = Field(..., description="项目号")


# ==================== 配置模型 ====================

class AlgorithmPlatformConfig(BaseModel):
    """算法平台配置"""
    base_url: str = Field(default="http://***********:8081", description="算法平台基础URL")
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    retry_times: int = Field(default=3, description="重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    enable_cache: bool = Field(default=True, description="启用缓存")
    cache_ttl: int = Field(default=1800, description="缓存生存时间（秒）")


# ==================== 错误模型 ====================

class AlgorithmPlatformError(Exception):
    """算法平台API错误"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class NetworkError(AlgorithmPlatformError):
    """网络连接错误"""
    pass


class TimeoutError(AlgorithmPlatformError):
    """请求超时错误"""
    pass


class APIError(AlgorithmPlatformError):
    """API调用错误"""
    pass
