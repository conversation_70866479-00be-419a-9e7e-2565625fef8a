"""
数据模型模块

包含所有的数据模型定义和类型声明。
"""

# 预测相关模型
from .predictive_models import (
    PredictiveAgentState,
    PredictiveTaskClassification,
    PredictiveParameterExtraction,
    PredictiveInteractionClassification,
    AlgorithmClassification,
    AlgorithmType,
    InteractionType
)

# 参数推荐模型
from .parameter_recommendation_models import (
    ParameterRecommendationRequest,
    ParameterRecommendationResponse
)

__all__ = [
    'PredictiveAgentState',
    'PredictiveTaskClassification',
    'PredictiveParameterExtraction',
    'PredictiveInteractionClassification',
    'AlgorithmClassification',
    'AlgorithmType',
    'InteractionType',
    'ParameterRecommendationRequest',
    'ParameterRecommendationResponse'
]
