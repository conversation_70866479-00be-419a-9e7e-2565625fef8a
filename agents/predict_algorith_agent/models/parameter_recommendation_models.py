"""
参数推荐相关的数据模型
支持多LLM提供商的参数推荐服务
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from datetime import datetime


class LLMProvider(str, Enum):
    """LLM提供商枚举"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    QWEN = "qwen"
    BAIDU = "baidu"
    CUSTOM = "custom"


class PerformanceObjective(str, Enum):
    """性能目标枚举"""
    ACCURACY = "accuracy"  # 准确性优先
    SPEED = "speed"  # 速度优先
    BALANCED = "balanced"  # 平衡
    MEMORY_EFFICIENT = "memory_efficient"  # 内存效率优先


class DataScale(str, Enum):
    """数据规模枚举"""
    SMALL = "small"  # 小数据集 (<1K)
    MEDIUM = "medium"  # 中等数据集 (1K-100K)
    LARGE = "large"  # 大数据集 (100K-1M)
    HUGE = "huge"  # 超大数据集 (>1M)


class LLMProviderConfig(BaseModel):
    """LLM提供商配置"""
    provider: LLMProvider = Field(..., description="提供商类型")
    model_name: str = Field(..., description="模型名称")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")
    max_tokens: int = Field(default=2000, description="最大token数")
    temperature: float = Field(default=0.7, description="温度参数")
    timeout: int = Field(default=30, description="请求超时时间")


class ParameterRecommendationRequest(BaseModel):
    """参数推荐请求"""
    algorithm_type: str = Field(..., description="算法类型，如LSTM、CNN等")
    data_scale: DataScale = Field(..., description="数据规模")
    data_features: Dict[str, Any] = Field(default_factory=dict, description="数据特征描述")
    performance_objective: PerformanceObjective = Field(default=PerformanceObjective.BALANCED, description="性能目标")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="约束条件，如内存限制、时间限制等")
    current_params: Dict[str, Any] = Field(default_factory=dict, description="当前已有的参数")
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="用户偏好")
    context: str = Field(default="", description="额外的上下文信息")


class ParameterRecommendation(BaseModel):
    """单个参数推荐"""
    parameter_name: str = Field(..., description="参数名称")
    recommended_value: Any = Field(..., description="推荐值")
    value_range: Optional[Dict[str, Any]] = Field(None, description="值范围，包含min、max、step等")
    confidence: float = Field(..., description="推荐置信度", ge=0.0, le=1.0)
    reasoning: str = Field(..., description="推荐理由")
    impact_description: str = Field(..., description="参数影响描述")
    tuning_suggestions: List[str] = Field(default_factory=list, description="调优建议")


class ParameterRecommendationResponse(BaseModel):
    """参数推荐响应"""
    algorithm_type: str = Field(..., description="算法类型")
    recommended_parameters: List[ParameterRecommendation] = Field(..., description="推荐参数列表")
    overall_confidence: float = Field(..., description="整体推荐置信度", ge=0.0, le=1.0)
    performance_prediction: Dict[str, float] = Field(default_factory=dict, description="性能预测")
    optimization_suggestions: List[str] = Field(default_factory=list, description="优化建议")
    alternative_configs: List[Dict[str, Any]] = Field(default_factory=list, description="备选配置")
    llm_provider: LLMProvider = Field(..., description="使用的LLM提供商")
    model_name: str = Field(..., description="使用的模型名称")
    generation_time: float = Field(..., description="生成耗时（秒）")
    reasoning_process: str = Field(default="", description="推理过程")


class MultiLLMRecommendationResponse(BaseModel):
    """多LLM推荐响应"""
    algorithm_type: str = Field(..., description="算法类型")
    recommendations: List[ParameterRecommendationResponse] = Field(..., description="各LLM的推荐结果")
    consensus_parameters: Dict[str, Any] = Field(default_factory=dict, description="一致性参数")
    divergent_parameters: Dict[str, List[Any]] = Field(default_factory=dict, description="分歧参数")
    final_recommendation: Dict[str, Any] = Field(default_factory=dict, description="最终推荐参数")
    confidence_analysis: Dict[str, float] = Field(default_factory=dict, description="置信度分析")
    model_comparison: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="模型对比")


class AlgorithmParameterTemplate(BaseModel):
    """算法参数模板"""
    algorithm_type: str = Field(..., description="算法类型")
    required_parameters: List[str] = Field(..., description="必需参数列表")
    optional_parameters: List[str] = Field(default_factory=list, description="可选参数列表")
    parameter_defaults: Dict[str, Any] = Field(default_factory=dict, description="默认参数值")
    parameter_ranges: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="参数范围")
    parameter_descriptions: Dict[str, str] = Field(default_factory=dict, description="参数描述")
    parameter_dependencies: Dict[str, List[str]] = Field(default_factory=dict, description="参数依赖关系")
    tuning_priorities: List[str] = Field(default_factory=list, description="调优优先级")


class ParameterOptimizationSuggestion(BaseModel):
    """参数优化建议"""
    parameter_name: str = Field(..., description="参数名称")
    current_value: Any = Field(..., description="当前值")
    suggested_value: Any = Field(..., description="建议值")
    optimization_direction: str = Field(..., description="优化方向：increase/decrease/adjust")
    expected_improvement: str = Field(..., description="预期改进效果")
    risk_assessment: str = Field(..., description="风险评估")
    priority: int = Field(..., description="优化优先级", ge=1, le=10)


class ParameterRecommendationHistory(BaseModel):
    """参数推荐历史记录"""
    request_id: str = Field(..., description="请求ID")
    user_id: str = Field(..., description="用户ID")
    algorithm_type: str = Field(..., description="算法类型")
    request_data: ParameterRecommendationRequest = Field(..., description="请求数据")
    response_data: ParameterRecommendationResponse = Field(..., description="响应数据")
    user_feedback: Optional[Dict[str, Any]] = Field(None, description="用户反馈")
    actual_performance: Optional[Dict[str, float]] = Field(None, description="实际性能结果")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


# 预定义的算法参数模板
ALGORITHM_PARAMETER_TEMPLATES = {
    "LSTM": AlgorithmParameterTemplate(
        algorithm_type="LSTM",
        required_parameters=["input_dim", "hidden_dim", "num_layers", "output_dim"],
        optional_parameters=["batch_size", "learning_rate", "epochs", "dropout", "sequence_length"],
        parameter_defaults={
            "batch_size": 32,
            "learning_rate": 0.001,
            "epochs": 100,
            "dropout": 0.2,
            "sequence_length": 60
        },
        parameter_ranges={
            "hidden_dim": {"min": 32, "max": 512, "step": 32},
            "num_layers": {"min": 1, "max": 5, "step": 1},
            "batch_size": {"min": 8, "max": 256, "step": 8},
            "learning_rate": {"min": 0.0001, "max": 0.1, "type": "log"},
            "dropout": {"min": 0.0, "max": 0.8, "step": 0.1}
        },
        parameter_descriptions={
            "input_dim": "输入特征维度",
            "hidden_dim": "隐藏层维度，影响模型容量",
            "num_layers": "LSTM层数，影响模型深度",
            "output_dim": "输出维度",
            "batch_size": "批次大小，影响训练稳定性和速度",
            "learning_rate": "学习率，影响收敛速度和稳定性",
            "epochs": "训练轮数",
            "dropout": "丢弃率，防止过拟合",
            "sequence_length": "序列长度，时序数据的窗口大小"
        },
        tuning_priorities=["learning_rate", "hidden_dim", "dropout", "batch_size", "num_layers"]
    ),
    "CNN": AlgorithmParameterTemplate(
        algorithm_type="CNN",
        required_parameters=["input_shape", "num_filters", "kernel_size"],
        optional_parameters=["pool_size", "dense_units", "batch_size", "learning_rate", "epochs", "dropout"],
        parameter_defaults={
            "pool_size": 2,
            "dense_units": 128,
            "batch_size": 32,
            "learning_rate": 0.001,
            "epochs": 100,
            "dropout": 0.5
        },
        parameter_ranges={
            "num_filters": {"min": 16, "max": 512, "step": 16},
            "kernel_size": {"min": 3, "max": 7, "step": 2},
            "pool_size": {"min": 2, "max": 4, "step": 1},
            "dense_units": {"min": 32, "max": 512, "step": 32},
            "batch_size": {"min": 8, "max": 256, "step": 8},
            "learning_rate": {"min": 0.0001, "max": 0.1, "type": "log"},
            "dropout": {"min": 0.0, "max": 0.8, "step": 0.1}
        },
        parameter_descriptions={
            "input_shape": "输入数据形状",
            "num_filters": "卷积核数量，影响特征提取能力",
            "kernel_size": "卷积核大小，影响感受野",
            "pool_size": "池化大小，影响特征降维",
            "dense_units": "全连接层单元数",
            "batch_size": "批次大小",
            "learning_rate": "学习率",
            "epochs": "训练轮数",
            "dropout": "丢弃率"
        },
        tuning_priorities=["learning_rate", "num_filters", "dropout", "batch_size", "kernel_size"]
    )
}
