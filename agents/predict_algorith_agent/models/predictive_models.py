from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from datetime import datetime

# 任务类型枚举
class PredictiveTaskType(str, Enum):
    INITIATE = "发起算法任务"
    QUERY = "查询任务进度"
    REPORT = "生成分析报告"
    UNSUPPORTED = "不支持的操作"

# 算法类型枚举
class AlgorithmType(str, Enum):
    LSTM = "LSTM"
    CNN = "CNN"
    RNN = "RNN"
    LINEAR_REGRESSION = "线性回归"
    LOGISTIC_REGRESSION = "逻辑回归"
    RANDOM_FOREST = "随机森林"
    SVM = "SVM"
    OTHER = "其他"

# 交互类型枚举 - 简化版（与提示词保持一致）
class InteractionType(str, Enum):
    # 简化的5个核心交互类型
    GENERAL = "一般咨询"      # 技术咨询、概念解释、寻求建议
    CONFIRM = "确认"         # 操作确认、同意继续
    ADJUST = "调整"          # 参数调整、修改配置
    QUERY_STATUS = "查询进度" # 状态查询、结果查看、报告生成
    CANCEL = "取消"          # 取消操作、停止任务

    # 保留的扩展类型（向后兼容）
    QUERY_RESULT = "查询结果"
    GENERATE_REPORT = "生成报告"
    ALGORITHM_RECOMMENDATION = "算法推荐"
    PARAMETER_CONFIGURATION = "参数配置"
    TECHNICAL_QUESTION = "技术问题"
    WELCOME_GUIDE = "欢迎引导"
    PRESET_QUESTION_1 = "预设问题1"
    PRESET_QUESTION_2 = "预设问题2"
    UNKNOWN = "未知"

class InteractionClassification(BaseModel):
    interaction_type: InteractionType
    confidence: float
    reason: str

# LSTM参数模型
class LSTMParams(BaseModel):
    input_dim: int = Field(..., description="输入特征维度")
    hidden_dim: int = Field(..., description="隐藏层维度")
    num_layers: int = Field(..., description="LSTM层数")
    output_dim: int = Field(..., description="输出维度")
    batch_size: int = Field(..., description="批次大小")
    learning_rate: float = Field(..., description="学习率")
    epochs: int = Field(..., description="训练轮数")

# CNN参数模型
class CNNParams(BaseModel):
    input_shape: List[int] = Field(..., description="输入形状")
    num_filters: int = Field(..., description="卷积核数量")
    kernel_size: int = Field(..., description="卷积核大小")
    pool_size: int = Field(..., description="池化大小")
    dense_units: int = Field(..., description="全连接层单元数")
    batch_size: int = Field(..., description="批次大小")
    learning_rate: float = Field(..., description="学习率")
    epochs: int = Field(..., description="训练轮数")

AlgorithmParams = Union[LSTMParams, CNNParams, Dict[str, Any]]  # 可扩展

# 预测性算法Agent状态
class PredictiveAgentState(BaseModel):
    history: List[Dict[str, Any]] = Field(default_factory=list, description="多轮对话历史")
    current_params: Dict[str, Any] = Field(default_factory=dict, description="当前参数")
    missing_params: List[str] = Field(default_factory=list, description="缺失参数")
    confirmed: bool = False
    task_type: Optional[PredictiveTaskType] = None  # 任务类型（发起、查询等）
    algorithm_type: Optional[AlgorithmType] = None  # 算法类型（LSTM、CNN等）
    task_id: Optional[str] = None
    task_status: Optional[str] = None  # 任务进度
    report: Optional[str] = None
    awaiting_confirmation: bool = False
    last_interaction: Optional[str] = None
    last_api_result: Optional[Dict[str, Any]] = None
    # 新增：引导相关状态
    is_new_conversation: bool = True  # 标识是否为新建对话
    conversation_stage: str = "welcome"  # 对话阶段：welcome, guided, task_processing, history_search, algorithm_choice
    has_shown_welcome: bool = False  # 是否已显示欢迎消息
    # 新增：历史算法检索相关状态
    awaiting_algorithm_choice: bool = False  # 是否等待用户选择算法
    recommended_algorithms: List[Dict[str, Any]] = Field(default_factory=list, description="推荐的历史算法列表")
    selected_algorithm_id: Optional[str] = None  # 用户选择的算法ID
    history_search_completed: bool = False  # 历史算法搜索是否完成
    requirement_analysis: Optional[Dict[str, Any]] = None  # 需求分析结果
    # 新增：训练结果确认相关状态
    awaiting_training_confirmation: bool = False  # 是否等待训练结果确认
    training_result: Optional[Dict[str, Any]] = None  # 训练结果数据
    training_completed_at: Optional[str] = None  # 训练完成时间

    # 新增：算法平台相关字段
    current_project_id: Optional[str] = Field(default=None, description="当前项目ID（由前端传入）")
    platform_algorithm_list: Optional[List[Dict[str, Any]]] = Field(default=None, description="算法平台算法列表")
    platform_dataset_list: Optional[List[Dict[str, Any]]] = Field(default=None, description="算法平台数据集列表")
    training_task_id: Optional[str] = Field(default=None, description="训练任务ID")
    monitoring_service_name: Optional[str] = Field(default=None, description="监控服务名称")

# 任务分类结果
class PredictiveTaskClassification(BaseModel):
    task_type: PredictiveTaskType = Field(description="任务类型")
    confidence: float = Field(description="分类置信度", ge=0.0, le=1.0)
    reason: str = Field(description="分类理由")

# 算法分类结果
class AlgorithmClassification(BaseModel):
    algorithm_type: AlgorithmType = Field(description="算法类型")
    confidence: float = Field(description="分类置信度", ge=0.0, le=1.0)
    reason: str = Field(description="分类理由")

# 参数提取结果
class PredictiveParameterExtraction(BaseModel):
    extracted_params: Dict[str, Any] = Field(default_factory=dict, description="提取的参数")
    missing_params: List[str] = Field(default_factory=list, description="缺失的参数")
    confidence: float = Field(description="参数提取置信度", ge=0.0, le=1.0)
    notes: str = Field(default="", description="参数提取说明")

# 交互分类结果
class PredictiveInteractionClassification(BaseModel):
    interaction_type: InteractionType = Field(description="交互类型")
    confidence: float = Field(description="分类置信度", ge=0.0, le=1.0)
    reason: str = Field(description="分类理由")

# 参数确认请求
class PredictiveConfirmationRequest(BaseModel):
    current_params: Dict[str, Any] = Field(description="当前参数")
    formatted_params: str = Field(description="格式化参数显示")
    confirmation_message: str = Field(description="确认消息")

# =====================================================
# 对话管理相关数据模型
# =====================================================

# 对话类型枚举
class ConversationType(str, Enum):
    TRAINING = "training"
    QUERY = "query"
    REPORT = "report"
    GUIDE = "guide"

# 对话状态枚举
class ConversationStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# 对话阶段枚举
class ConversationStage(str, Enum):
    WELCOME = "welcome"
    GUIDED = "guided"
    PARAM_EXTRACTION = "param_extraction"
    PARAM_CONFIRMATION = "param_confirmation"
    TRAINING = "training"
    COMPLETED = "completed"

# 创建对话请求
class CreateConversationRequest(BaseModel):
    user_id: str = Field(description="用户ID")
    title: str = Field(default="新建算法训练任务", description="对话标题")
    conversation_type: ConversationType = Field(default=ConversationType.TRAINING, description="对话类型")

# 对话摘要信息
class ConversationSummary(BaseModel):
    conversation_id: str = Field(description="对话ID")
    title: str = Field(description="对话标题")
    conversation_type: ConversationType = Field(description="对话类型")
    status: ConversationStatus = Field(description="对话状态")
    current_stage: ConversationStage = Field(description="当前阶段")
    progress: float = Field(description="进度百分比")
    algorithm_type: Optional[str] = Field(description="算法类型")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

# 对话详情信息
class ConversationDetail(BaseModel):
    conversation_id: str = Field(description="对话ID")
    user_id: str = Field(description="用户ID")
    title: str = Field(description="对话标题")
    conversation_type: ConversationType = Field(description="对话类型")
    status: ConversationStatus = Field(description="对话状态")
    current_stage: ConversationStage = Field(description="当前阶段")
    progress: float = Field(description="进度百分比")
    task_summary: Optional[str] = Field(description="任务摘要")
    algorithm_type: Optional[str] = Field(description="算法类型")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    completed_at: Optional[datetime] = Field(description="完成时间")

# 对话消息
class ConversationMessage(BaseModel):
    id: int = Field(description="消息ID")
    message_type: str = Field(description="消息类型")
    content: str = Field(description="消息内容")
    message_data: Optional[Dict[str, Any]] = Field(description="消息附加数据")
    interaction_type: Optional[str] = Field(description="交互类型")
    timestamp: datetime = Field(description="消息时间")

# 对话列表响应
class ConversationListResponse(BaseModel):
    conversations: List[ConversationSummary] = Field(description="对话列表")
    total: int = Field(description="总数")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    total_pages: int = Field(description="总页数")

# 对话详情响应
class ConversationDetailResponse(BaseModel):
    conversation: ConversationDetail = Field(description="对话详情")
    messages: List[ConversationMessage] = Field(description="对话消息列表")
    message_count: int = Field(description="消息总数")