# 算法智能体用户使用指南

## 🚀 快速开始

### 访问系统
1. 打开浏览器，访问：`http://localhost:8008/static/html/chat_interface_v2.html`
2. 点击"访客登录"按钮
3. 开始与算法智能体对话

### 系统界面介绍
- **左侧面板**: 对话管理和历史记录
- **中央区域**: 聊天对话界面
- **右侧面板**: 智能体连接状态
- **底部**: 消息输入框和发送按钮

## 🎯 核心功能

### 1. 批量预测
**使用场景**: 您有一个训练好的模型，想要进行批量数据预测

**操作步骤**:
1. 输入：`我有一个训练好的模型，想要进行批量预测，输入数据是一个包含100个样本的数组`
2. 系统会显示可用的历史算法列表
3. 选择合适的算法（输入数字1-3）
4. 查看算法详情和参数配置
5. 确认参数或进行调整
6. 开始预测任务

**示例对话**:
```
用户: 我有一个训练好的模型，想要进行批量预测，输入数据是一个包含100个样本的数组

AI: 🔍 历史算法列表
以下是可用的历史算法列表（共 3 个）：

1. 设备预测性维护LSTM模型
- 算法类型：LSTM
- 适用场景：预测性维护
- 性能指标：准确率 0.92
- 使用次数：15 次

请选择您的操作：
1️⃣ 使用历史算法（输入算法编号，如 '1'）
2️⃣ 创建新的算法（输入 '新建' 或 'new'）
3️⃣ 查看更多算法（输入 '更多' 或 'more'）
```

### 2. 生成训练报告
**使用场景**: 您需要生成详细的模型训练报告

**操作步骤**:
1. 首先选择或创建一个算法
2. 输入：`请为我生成一份详细的训练报告，包括模型性能、损失曲线、准确率等指标`
3. 系统会基于选定的算法生成报告
4. 查看详细的性能分析和建议

**注意**: 必须先选择算法才能生成报告

### 3. 算法训练指导
**使用场景**: 您想了解如何训练算法

**操作方式**:
- 输入数字 `1` 或说 `我想训练一个算法，我要怎么做`
- 系统会提供详细的训练流程指导

**训练流程**:
1. **数据准备**: 准备训练数据，确保数据格式正确
2. **算法选择**: 根据任务类型选择合适的算法
3. **参数设置**: 配置学习率、批次大小、训练轮数等
4. **模型训练**: 启动训练过程，监控训练进度
5. **结果评估**: 查看训练结果和模型性能

### 4. 功能介绍
**使用场景**: 您想了解系统能提供哪些帮助

**操作方式**:
- 输入数字 `2` 或说 `你能提供哪些帮助`

**系统功能**:
- 🔧 **协助算法训练流程**: 参数设置、模型评估、操作指引
- 📊 **监控算法训练进程**: 实时进度、训练日志、性能指标
- ❓ **解答技术问题**: 概念解释、参数调优、问题解决
- 📋 **生成分析报告**: 详细报告、性能分析、PDF下载

### 5. 技术问答
**使用场景**: 您想了解机器学习相关概念

**示例问题**:
- `什么是深度学习？`
- `LSTM和CNN有什么区别？`
- `如何选择学习率？`
- `什么是过拟合？`

**AI回复特点**:
- 📚 详细的概念解释
- 🎯 实用的技术建议
- 💡 最佳实践指导
- 🔗 相关知识链接

## 💡 使用技巧

### 输入建议
1. **具体描述**: 详细描述您的需求和数据类型
2. **明确意图**: 清楚表达您想要做什么
3. **提供上下文**: 说明您的使用场景和目标

### 有效的输入示例
✅ **好的输入**:
- `我想用LSTM预测股价，数据是每日收盘价，学习率设为0.001`
- `我有图像分类任务，数据集包含1000张图片，分为5个类别`
- `请帮我分析这个CNN模型的性能，准确率只有60%`

❌ **不够清晰的输入**:
- `训练`
- `帮我`
- `不行`

### 对话流程建议
1. **明确需求** → 详细描述您的任务
2. **选择算法** → 从历史算法中选择或创建新算法
3. **配置参数** → 根据建议调整算法参数
4. **执行任务** → 开始训练或预测
5. **查看结果** → 分析结果并获取改进建议

## 🎨 界面功能

### Markdown格式支持
系统支持丰富的文本格式显示：

- **粗体文本**: 重要信息会以粗体显示
- **列表结构**: 有序和无序列表，支持嵌套
- **表情符号**: 🎯🔧📊❓📋等增强可读性
- **段落分离**: 清晰的内容结构
- **代码高亮**: 代码片段会有特殊样式

### 对话管理
- **新建对话**: 点击"➕ 新建对话"开始新的会话
- **历史记录**: 左侧显示历史对话列表
- **连接状态**: 右侧显示智能体连接状态
- **清空对话**: 可以清空当前对话内容

## 🔧 常见问题

### Q1: 为什么我的输入没有得到预期回复？
**A**: 请确保输入足够具体和清晰。系统需要明确的任务描述才能提供准确的帮助。

### Q2: 如何选择合适的算法？
**A**: 
- **时序数据预测**: 选择LSTM模型
- **图像处理**: 选择CNN模型  
- **文本处理**: 选择Transformer模型
- **不确定**: 描述您的数据类型，AI会推荐合适的算法

### Q3: 参数应该如何设置？
**A**: 系统会根据您的任务类型提供参数建议。一般建议：
- **学习率**: 0.001-0.01之间
- **批次大小**: 16-128之间
- **训练轮数**: 根据数据量调整

### Q4: 如何提高模型性能？
**A**: 
1. **数据质量**: 确保数据清洁和标注准确
2. **参数调优**: 尝试不同的学习率和网络结构
3. **正则化**: 使用dropout防止过拟合
4. **数据增强**: 增加训练数据的多样性

### Q5: 系统支持哪些算法类型？
**A**: 目前支持：
- **LSTM**: 长短期记忆网络，适用于时序数据
- **CNN**: 卷积神经网络，适用于图像数据
- **Transformer**: 注意力机制，适用于NLP任务
- **其他**: 根据需求可以扩展更多算法

## 📞 技术支持

### 获取帮助
- **在线帮助**: 输入"帮助"或"help"
- **功能介绍**: 输入数字"2"
- **技术问答**: 直接询问技术问题

### 反馈建议
如果您在使用过程中遇到问题或有改进建议，请：
1. 详细描述问题现象
2. 提供输入和输出示例
3. 说明期望的结果

### 系统状态
- **连接状态**: 右侧面板显示"已连接"表示系统正常
- **响应时间**: 正常情况下AI会在3-8秒内回复
- **错误处理**: 如遇到错误，系统会提供详细的指导信息

## 🎯 最佳实践

### 高效使用建议
1. **循序渐进**: 从简单任务开始，逐步尝试复杂功能
2. **保存记录**: 重要的对话和参数配置可以截图保存
3. **多次尝试**: 不同的表达方式可能得到不同的建议
4. **学习积累**: 通过技术问答功能学习相关知识

### 避免的操作
1. **过于简短的输入**: 如"训练"、"预测"等
2. **频繁刷新页面**: 可能导致对话历史丢失
3. **同时多个任务**: 建议一次专注一个任务
4. **忽略系统建议**: AI的参数建议通常是经过优化的

---
*用户使用指南 - 2025-07-28*
*算法智能体团队*
