# 大模型配置管理指南

## 📋 概述

本项目已实现统一的大模型配置管理系统，支持多种大模型提供商的动态切换和配置管理。

## 🎯 配置文件位置

### 主配置文件
- **`agents/config.py`** - 统一的大模型配置管理
- **`agents/predict_algorith_agent/core/predictive_agent.py`** - 从config.py读取配置

### 工具文件
- **`agents/predict_algorith_agent/utils/llm_config_manager.py`** - 配置管理工具类
- **`agents/predict_algorith_agent/switch_llm_config.py`** - 快速切换脚本

## 🔧 支持的大模型提供商

| 提供商 | 模型名称 | 描述 | 状态 |
|--------|----------|------|------|
| **qwen** | qwen-turbo-latest | 阿里云通义千问 | ✅ 默认使用 |
| **deepseek** | deepseek-chat | DeepSeek大模型 | ⭕ 可选择 |
| **openai** | gpt-4o-mini | OpenAI GPT模型 | ⭕ 需配置Key |
| **claude** | claude-3-sonnet-20240229 | Anthropic Claude | ⭕ 需配置Key |

## 🚀 快速使用

### 1. 查看当前配置
```bash
python switch_llm_config.py show
```

### 2. 查看所有提供商
```bash
python switch_llm_config.py list
```

### 3. 切换大模型提供商
```bash
# 切换到DeepSeek
python switch_llm_config.py switch deepseek

# 切换到OpenAI
python switch_llm_config.py switch openai
```

### 4. 更新API Key
```bash
# 更新通义千问API Key
python switch_llm_config.py update qwen sk-your-new-qwen-key

# 更新OpenAI API Key  
python switch_llm_config.py update openai sk-your-openai-key
```

### 5. 测试配置
```bash
python switch_llm_config.py test
```

## 🔧 高级配置管理

### 交互式配置管理器
```bash
python utils/llm_config_manager.py
```

提供完整的交互式界面，支持：
- 查看和切换配置
- 添加自定义提供商
- 导入/导出配置
- 实时测试

### 环境变量配置
可以通过环境变量覆盖默认配置：

```bash
# 设置默认提供商
export LLM_PROVIDER=deepseek

# 设置模型参数
export LLM_TEMPERATURE=0.8
export LLM_MAX_TOKENS=3000
export LLM_TIMEOUT=60
```

## 📝 配置文件结构

### agents/config.py 中的配置结构
```python
LLM_PROVIDERS = {
    "qwen": {
        "model_name": "qwen-turbo-latest",
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1", 
        "api_key": "sk-your-qwen-key",
        "description": "阿里云通义千问"
    },
    "deepseek": {
        "model_name": "deepseek-chat",
        "base_url": "https://api.deepseek.com",
        "api_key": "sk-your-deepseek-key", 
        "description": "DeepSeek大模型"
    }
    # ... 其他提供商
}
```

## 🔄 添加新的大模型提供商

### 方法1: 直接修改config.py
在 `LLM_PROVIDERS` 字典中添加新的提供商配置：

```python
"your_provider": {
    "model_name": "your-model-name",
    "base_url": "https://your-api-endpoint.com/v1",
    "api_key": "your-api-key",
    "description": "您的大模型描述"
}
```

### 方法2: 使用配置管理器
```bash
python utils/llm_config_manager.py
# 选择 "5. 添加自定义提供商"
```

## ⚠️ 重要注意事项

1. **重启服务**: 修改配置后需要重启WebSocket服务才能生效
2. **API Key安全**: 请妥善保管API Key，不要提交到版本控制
3. **网络连接**: 确保服务器能访问对应的API端点
4. **配置备份**: 建议定期备份配置文件

## 🧪 测试配置

### 测试当前配置
```bash
python switch_llm_config.py test
```

### 测试特定提供商
```bash
# 先切换
python switch_llm_config.py switch deepseek
# 再测试
python switch_llm_config.py test
```

## 🔍 故障排除

### 常见问题

1. **API Key无效**
   ```bash
   python switch_llm_config.py update qwen sk-new-valid-key
   ```

2. **网络连接问题**
   - 检查防火墙设置
   - 验证API端点可访问性

3. **模型不支持**
   - 检查模型名称是否正确
   - 确认API兼容性

### 日志查看
```bash
tail -f logs/agent_production.log | grep -i "llm\|model\|api"
```

## 📞 技术支持

如果遇到配置问题，请：
1. 运行 `python switch_llm_config.py test` 进行诊断
2. 查看日志文件获取详细错误信息
3. 确认API Key和网络连接正常
