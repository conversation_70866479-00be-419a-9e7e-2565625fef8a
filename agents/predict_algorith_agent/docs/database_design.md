# 算法智能体数据库设计文档

## 📋 概述

本文档描述了算法智能体项目的数据库设计，包括表结构、关系设计、索引策略等。

**数据库信息**：
- 数据库名：`indusaio_agent`
- 字符集：`utf8mb4`
- 排序规则：`utf8mb4_unicode_ci`
- MySQL版本：5.7.34+

## 🏗️ 表结构设计

### 1. 对话管理表 (conversations)

**用途**：管理用户与智能体的对话会话

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 对话唯一标识 |
| conversation_id | VARCHAR(64) | UNIQUE, NOT NULL | 对话ID（业务主键） |
| user_id | VARCHAR(50) | NOT NULL | 用户ID |
| title | VARCHAR(200) | DEFAULT '新建算法训练任务' | 对话标题 |
| conversation_type | ENUM | DEFAULT 'training' | 对话类型 |
| status | ENUM | DEFAULT 'active' | 对话状态 |
| current_stage | ENUM | DEFAULT 'welcome' | 当前阶段 |
| progress | DECIMAL(5,2) | DEFAULT 0.00 | 进度百分比 |
| task_summary | TEXT | NULL | 任务摘要描述 |
| algorithm_type | VARCHAR(50) | NULL | 算法类型 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| completed_at | TIMESTAMP | NULL | 完成时间 |

**枚举值**：
- conversation_type: 'training', 'query', 'report', 'guide'
- status: 'active', 'completed', 'failed', 'cancelled'
- current_stage: 'welcome', 'guided', 'param_extraction', 'param_confirmation', 'training', 'completed'

**索引**：
- idx_user_id (user_id)
- idx_conversation_id (conversation_id)
- idx_status (status)
- idx_created_at (created_at)

### 2. 对话消息记录表 (conversation_messages)

**用途**：存储对话中的每条消息记录

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 消息唯一标识 |
| conversation_id | VARCHAR(64) | NOT NULL, FK | 对话ID |
| message_type | ENUM | NOT NULL | 消息类型 |
| content | TEXT | NOT NULL | 消息内容 |
| message_data | JSON | NULL | 消息附加数据 |
| interaction_type | VARCHAR(50) | NULL | 交互类型 |
| timestamp | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 消息时间 |

**枚举值**：
- message_type: 'user', 'assistant', 'system'

**外键**：
- conversation_id → conversations.conversation_id (CASCADE DELETE)

### 3. 算法任务表 (algorithm_tasks)

**用途**：存储具体的算法训练任务信息

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 任务唯一标识 |
| task_id | VARCHAR(64) | UNIQUE, NOT NULL | 任务ID（业务主键） |
| conversation_id | VARCHAR(64) | NOT NULL, FK | 关联的对话ID |
| user_id | VARCHAR(50) | NOT NULL | 用户ID |
| task_name | VARCHAR(200) | NULL | 任务名称 |
| algorithm_type | ENUM | NOT NULL | 算法类型 |
| task_description | TEXT | NULL | 任务描述 |
| dataset_info | JSON | NULL | 数据集信息 |
| status | ENUM | DEFAULT 'created' | 任务状态 |
| training_progress | DECIMAL(5,2) | DEFAULT 0.00 | 训练进度 |
| start_time | TIMESTAMP | NULL | 训练开始时间 |
| end_time | TIMESTAMP | NULL | 训练结束时间 |
| estimated_duration | INT | NULL | 预计训练时长(秒) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**枚举值**：
- algorithm_type: 'LSTM', 'CNN', 'RNN', 'Transformer', 'LinearRegression', 'RandomForest', 'SVM', 'Other'
- status: 'created', 'parameter_setting', 'training', 'completed', 'failed', 'cancelled'

### 4. 任务参数表 (task_parameters)

**用途**：存储算法任务的详细参数配置

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 参数记录唯一标识 |
| task_id | VARCHAR(64) | NOT NULL, FK | 任务ID |
| parameter_name | VARCHAR(100) | NOT NULL | 参数名称 |
| parameter_value | TEXT | NULL | 参数值 |
| parameter_type | ENUM | DEFAULT 'string' | 参数类型 |
| is_required | BOOLEAN | DEFAULT FALSE | 是否必需参数 |
| default_value | TEXT | NULL | 默认值 |
| description | TEXT | NULL | 参数描述 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**枚举值**：
- parameter_type: 'int', 'float', 'string', 'boolean', 'json'

**唯一约束**：
- uk_task_parameter (task_id, parameter_name)

### 5. 训练日志表 (training_logs)

**用途**：存储训练过程中的日志信息

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 日志唯一标识 |
| task_id | VARCHAR(64) | NOT NULL, FK | 任务ID |
| log_level | ENUM | DEFAULT 'INFO' | 日志级别 |
| log_type | ENUM | DEFAULT 'training' | 日志类型 |
| message | TEXT | NOT NULL | 日志消息 |
| metrics | JSON | NULL | 训练指标数据 |
| epoch | INT | NULL | 训练轮次 |
| step | INT | NULL | 训练步数 |
| timestamp | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 日志时间 |

**枚举值**：
- log_level: 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'
- log_type: 'training', 'validation', 'system', 'error'

### 6. 任务结果表 (task_results)

**用途**：存储任务的最终结果和评估指标

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 结果唯一标识 |
| task_id | VARCHAR(64) | UNIQUE, NOT NULL, FK | 任务ID |
| result_type | ENUM | DEFAULT 'training_result' | 结果类型 |
| accuracy | DECIMAL(8,6) | NULL | 准确率 |
| loss | DECIMAL(10,6) | NULL | 损失值 |
| metrics | JSON | NULL | 详细评估指标 |
| model_path | VARCHAR(500) | NULL | 模型文件路径 |
| model_size | BIGINT | NULL | 模型文件大小(字节) |
| evaluation_report | TEXT | NULL | 评估报告 |
| gb_standard_score | DECIMAL(5,2) | NULL | GB标准评分 |
| gb_standard_level | ENUM | NULL | GB标准等级 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**枚举值**：
- result_type: 'training_result', 'evaluation_result', 'prediction_result'
- gb_standard_level: '优秀', '良好', '合格', '不合格'

### 7. 会话状态表 (session_states)

**用途**：存储会话的详细状态信息（兼容原有功能）

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PK, AUTO_INCREMENT | 状态记录唯一标识 |
| user_id | VARCHAR(50) | NOT NULL | 用户ID |
| session_id | VARCHAR(100) | NOT NULL | 会话ID |
| conversation_id | VARCHAR(64) | NULL | 关联的对话ID |
| task_id | VARCHAR(64) | NULL | 关联的任务ID |
| state_data | JSON | NOT NULL | 状态数据 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**唯一约束**：
- uk_user_session (user_id, session_id)

## 🔗 关系设计

### 主要关系

1. **conversations → conversation_messages** (1:N)
   - 一个对话包含多条消息
   - 级联删除：删除对话时自动删除所有消息

2. **conversations → algorithm_tasks** (1:N)
   - 一个对话可以包含多个任务（通常是1个）
   - 级联删除：删除对话时自动删除所有任务

3. **algorithm_tasks → task_parameters** (1:N)
   - 一个任务包含多个参数
   - 级联删除：删除任务时自动删除所有参数

4. **algorithm_tasks → training_logs** (1:N)
   - 一个任务产生多条训练日志
   - 级联删除：删除任务时自动删除所有日志

5. **algorithm_tasks → task_results** (1:1)
   - 一个任务对应一个结果记录
   - 级联删除：删除任务时自动删除结果

## 📊 索引策略

### 主要索引

1. **conversations表**：
   - PRIMARY KEY (id)
   - UNIQUE KEY (conversation_id)
   - INDEX (user_id) - 用户对话查询
   - INDEX (status) - 状态筛选
   - INDEX (created_at) - 时间排序

2. **conversation_messages表**：
   - PRIMARY KEY (id)
   - INDEX (conversation_id) - 对话消息查询
   - INDEX (timestamp) - 时间排序

3. **algorithm_tasks表**：
   - PRIMARY KEY (id)
   - UNIQUE KEY (task_id)
   - INDEX (conversation_id) - 对话任务查询
   - INDEX (user_id) - 用户任务查询
   - INDEX (status) - 状态筛选
   - INDEX (algorithm_type) - 算法类型筛选

## 🛠️ 使用说明

### 数据库初始化

1. **执行建表脚本**：
   ```bash
   python agents/predict_algorith_agent/test/test_database_connection.py
   ```

   **SQL脚本位置**：`agents/predict_algorith_agent/sql/database_schema.sql`

2. **验证表结构**：
   ```sql
   SHOW TABLES;
   DESCRIBE conversations;
   ```

### 数据库管理器使用

```python
from agents.predict_algorith_agent.database_manager import DatabaseManager

# 创建管理器实例
db = DatabaseManager()

# 创建对话
conversation_id = db.create_conversation(
    user_id="user123",
    title="LSTM算法训练",
    conversation_type="training"
)

# 创建任务
task_id = db.create_algorithm_task(
    conversation_id=conversation_id,
    user_id="user123",
    algorithm_type="LSTM",
    task_name="设备预测性维护"
)

# 添加消息
db.add_conversation_message(
    conversation_id=conversation_id,
    message_type="user",
    content="我想训练一个LSTM算法"
)
```

## ✅ 测试验证

所有功能已通过测试验证：

- ✅ 数据库连接测试
- ✅ 表结构创建测试
- ✅ 对话管理功能测试
- ✅ 任务管理功能测试
- ✅ 会话状态兼容性测试
- ✅ 数据清理功能测试

## 📈 扩展性考虑

1. **分表策略**：当数据量增大时，可考虑按时间或用户ID分表
2. **索引优化**：根据实际查询模式调整索引策略
3. **数据归档**：定期归档历史数据，保持表性能
4. **读写分离**：高并发场景下可考虑主从复制

## 🔄 版本历史

- **v1.0** (2025-01-22): 初始版本，完成基础表结构设计
- 支持对话管理、任务管理、消息记录等核心功能
- 兼容原有会话状态管理功能
