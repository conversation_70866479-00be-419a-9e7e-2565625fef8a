# 算法智能体部署检查清单

## 📋 部署前准备检查

### 🖥️ 服务器环境检查
- [ ] **操作系统**: Ubuntu 20.04+ / CentOS 7+ / RHEL 8+
- [ ] **内存**: 最低2GB，推荐4GB+
- [ ] **磁盘空间**: 最低10GB可用空间
- [ ] **网络**: 可访问外网（下载依赖包）
- [ ] **用户权限**: 有sudo权限的非root用户

### 🔧 软件依赖检查
- [ ] **Python**: 3.10-3.12版本
- [ ] **MySQL**: 5.7+ 或 8.0+（可访问）
- [ ] **Redis**: 6.0+（可访问）
- [ ] **Git**: 用于代码管理
- [ ] **SSH**: 用于远程连接

### 🌐 网络配置检查
- [ ] **应用端口**: 8008端口未被占用
- [ ] **数据库连接**: 可连接MySQL和Redis服务器
- [ ] **防火墙**: 相关端口已开放
- [ ] **域名解析**: 如使用域名，DNS解析正常

## 🚀 自动部署脚本执行

### 1. 下载部署脚本
```bash
# 在服务器上执行
wget https://your-repo/deploy.sh
chmod +x deploy.sh
```

### 2. 执行自动部署
```bash
./deploy.sh
```

### 3. 检查部署结果
- [ ] **项目目录创建**: `~/projects/twinbuilder-agents`
- [ ] **虚拟环境创建**: `venv/`目录存在
- [ ] **依赖安装完成**: 关键包导入测试通过
- [ ] **配置文件生成**: `config_template.py`存在
- [ ] **启动脚本创建**: `start_algorithm_agent.py`可执行
- [ ] **systemd服务**: `algorithm-agent.service`已创建

## 📁 代码上传检查

### 1. 修改上传脚本配置
**Windows用户编辑**: `upload_code.bat`
**Linux/Mac用户编辑**: `upload_code.sh`

需要修改的配置：
- [ ] **LOCAL_PROJECT_DIR**: 本地项目路径
- [ ] **REMOTE_USER**: 远程服务器用户名
- [ ] **REMOTE_HOST**: 远程服务器IP地址
- [ ] **REMOTE_PROJECT_DIR**: 远程项目路径

### 2. 执行代码上传
```bash
# Windows
upload_code.bat

# Linux/Mac
chmod +x upload_code.sh
./upload_code.sh
```

### 3. 验证上传结果
- [ ] **agents目录**: 完整上传到远程服务器
- [ ] **core目录**: 完整上传到远程服务器
- [ ] **文件权限**: 正确设置执行权限
- [ ] **目录结构**: 与本地保持一致

## ⚙️ 配置文件修改

### 1. 复制配置模板
```bash
cd ~/projects/twinbuilder-agents
cp config_template.py agents/config.py
```

### 2. 修改数据库配置
编辑 `agents/config.py`，修改以下配置：

#### MySQL配置
- [ ] **MYSQL_HOST**: 数据库服务器地址
- [ ] **MYSQL_PORT**: 数据库端口（默认3306）
- [ ] **MYSQL_USER**: 数据库用户名
- [ ] **MYSQL_PASSWORD**: 数据库密码
- [ ] **MYSQL_DB**: 数据库名称

#### Redis配置
- [ ] **REDIS_HOST**: Redis服务器地址
- [ ] **REDIS_PORT**: Redis端口（默认6379）
- [ ] **REDIS_PASSWORD**: Redis密码
- [ ] **REDIS_DB**: Redis数据库编号

#### 应用配置
- [ ] **PREDICT_MAIN_HOST**: 监听地址（建议0.0.0.0）
- [ ] **PREDICT_MAIN_PORT**: 监听端口（默认8008）

#### API配置
- [ ] **ALGORITHM_API_BASE**: 算法API地址
- [ ] **TRAING_API_BASE**: 训练API地址
- [ ] **HISTORY_ALGORITHM_API_BASE**: 历史算法API地址

## 🔍 连接测试

### 1. 激活虚拟环境
```bash
cd ~/projects/twinbuilder-agents
source venv/bin/activate
```

### 2. 测试数据库连接
```bash
python test_db_connection.py
```

检查结果：
- [ ] **MySQL连接**: ✅ MySQL连接成功!
- [ ] **Redis连接**: ✅ Redis连接成功!
- [ ] **总体结果**: 🎉 所有数据库连接测试通过!

### 3. 测试模块导入
```bash
python -c "
import sys
sys.path.insert(0, '.')
from agents.config import PREDICT_MAIN_HOST
from core.response import Response
from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
print('✅ 所有模块导入成功!')
"
```

## 🚀 服务启动测试

### 1. 前台启动测试
```bash
cd ~/projects/twinbuilder-agents
source venv/bin/activate
python start_algorithm_agent.py
```

检查启动日志：
- [ ] **项目根目录**: 正确识别
- [ ] **Python版本**: 3.10+
- [ ] **服务地址**: 显示正确的监听地址
- [ ] **启动成功**: 无错误信息

### 2. 基础功能测试
在另一个终端执行：
```bash
# 健康检查
curl http://localhost:8008/health
# 预期结果: {"status": "ok"}

# 静态文件访问
curl -I http://localhost:8008/static/html/chat_interface_v2.html
# 预期结果: HTTP/1.1 200 OK
```

### 3. 停止前台服务
按 `Ctrl+C` 停止前台服务

## 🔄 生产环境部署

### 1. 启动systemd服务
```bash
sudo systemctl start algorithm-agent
```

### 2. 检查服务状态
```bash
sudo systemctl status algorithm-agent
```

检查状态：
- [ ] **服务状态**: Active (running)
- [ ] **进程ID**: 显示正确的PID
- [ ] **启动时间**: 最近启动时间
- [ ] **无错误**: 没有错误信息

### 3. 设置开机自启
```bash
sudo systemctl enable algorithm-agent
```

### 4. 查看服务日志
```bash
sudo journalctl -u algorithm-agent -f
```

## 🌐 网络访问测试

### 1. 本地访问测试
```bash
# API测试
curl http://localhost:8008/health

# WebSocket测试（如果安装了websocat）
echo '{"type": "ping"}' | websocat ws://localhost:8008/api/ws
```

### 2. 远程访问测试
在本地机器执行：
```bash
# 替换为实际服务器IP
curl http://server-ip:8008/health

# 浏览器访问
# http://server-ip:8008/static/html/chat_interface_v2.html
```

### 3. 防火墙配置（如需要）
```bash
# Ubuntu/Debian
sudo ufw allow 8008

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8008/tcp
sudo firewall-cmd --reload
```

## 🔧 Nginx反向代理（可选）

### 1. 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx -y

# CentOS/RHEL
sudo yum install nginx -y
```

### 2. 配置Nginx
创建配置文件：`/etc/nginx/sites-available/algorithm-agent`

### 3. 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/algorithm-agent /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 4. 测试Nginx代理
```bash
curl http://server-ip/health
```

## 📊 性能监控

### 1. 系统资源监控
```bash
# 内存使用
free -h

# CPU使用
top
htop

# 磁盘使用
df -h

# 网络连接
netstat -tlnp | grep 8008
```

### 2. 应用日志监控
```bash
# 实时日志
sudo journalctl -u algorithm-agent -f

# 错误日志
sudo journalctl -u algorithm-agent --since "1 hour ago" | grep ERROR

# 访问日志
tail -f ~/projects/twinbuilder-agents/logs/access.log
```

### 3. 性能测试
```bash
# 安装压测工具
sudo apt install apache2-utils -y

# 简单压测
ab -n 100 -c 10 http://localhost:8008/health
```

## ✅ 部署完成检查

### 功能验证
- [ ] **健康检查**: `/health` 接口返回正常
- [ ] **静态文件**: 前端页面可正常访问
- [ ] **WebSocket**: WebSocket连接正常
- [ ] **数据库**: 数据库连接正常
- [ ] **API接口**: 核心API接口响应正常

### 服务管理
- [ ] **启动服务**: `sudo systemctl start algorithm-agent`
- [ ] **停止服务**: `sudo systemctl stop algorithm-agent`
- [ ] **重启服务**: `sudo systemctl restart algorithm-agent`
- [ ] **查看状态**: `sudo systemctl status algorithm-agent`
- [ ] **查看日志**: `sudo journalctl -u algorithm-agent -f`

### 运维准备
- [ ] **备份脚本**: `backup.sh` 可正常执行
- [ ] **监控告警**: 系统监控配置完成
- [ ] **日志轮转**: 日志管理策略实施
- [ ] **更新流程**: 代码更新流程文档化

## 🚨 故障排除

### 常见问题快速检查
- [ ] **端口占用**: `netstat -tlnp | grep 8008`
- [ ] **权限问题**: `ls -la ~/projects/twinbuilder-agents/`
- [ ] **模块导入**: Python路径和导入测试
- [ ] **数据库连接**: 网络连通性和认证
- [ ] **内存不足**: `free -h` 和 `top`

### 日志分析
- [ ] **应用日志**: 检查错误和警告信息
- [ ] **系统日志**: 检查系统级错误
- [ ] **网络日志**: 检查连接和超时问题

## 📝 部署记录

### 部署信息记录
- **部署日期**: ___________
- **服务器IP**: ___________
- **部署版本**: ___________
- **部署人员**: ___________

### 配置信息记录
- **MySQL地址**: ___________
- **Redis地址**: ___________
- **应用端口**: ___________
- **域名**: ___________

### 验证结果记录
- **功能测试**: □ 通过 □ 失败
- **性能测试**: □ 通过 □ 失败
- **安全检查**: □ 通过 □ 失败
- **备份验证**: □ 通过 □ 失败

---
*部署检查清单 - 2025-07-28*
*算法智能体团队*
