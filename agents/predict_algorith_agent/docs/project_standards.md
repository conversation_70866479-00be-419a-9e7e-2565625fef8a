# 算法智能体项目开发规范

## 📁 文件组织规范

### 1. 文档管理规范
- 📋 **所有文档** → `agents/predict_algorith_agent/doc/`
  - 说明文档：项目说明、需求分析、技术文档
  - 最终文档：完成的功能文档、用户指南等
  - 中间文档：开发过程中的分析报告、对比文档等

### 2. 测试文件规范
- 🧪 **所有测试脚本** → `agents/predict_algorith_agent/test/`
  - 临时测试脚本：开发过程中的调试脚本
  - API测试文件：HTTP Client测试文件
  - 功能验证脚本：各种功能测试脚本

### 3. SQL文件管理规范 ⭐ **新增顶级规范**
- 🗂️ **所有SQL文件** → `agents/predict_algorith_agent/sql/`
  - 数据库建表脚本
  - 数据迁移脚本
  - 数据修复脚本
  - 索引优化脚本
  - **所有SQL文件的修改和调整都必须在此目录下进行**

### 4. 核心代码规范
- 💻 **核心业务代码** → `agents/predict_algorith_agent/`
  - 主要业务逻辑文件
  - 数据模型定义
  - 配置文件

## 📂 当前目录结构

```
agents/predict_algorith_agent/
├── doc/                          # 📋 文档目录
│   ├── requirements_comparison_0721.md
│   ├── database_design.md
│   ├── project_standards.md      # 本文档
│   └── PowerShell_Profile_UTF8.ps1
├── test/                         # 🧪 测试目录
│   ├── predict_algo_agent.http
│   ├── test_database_connection.py
│   └── test_database_manager.py
├── sql/                          # 🗂️ SQL文件目录 ⭐ 新增
│   └── database_schema.sql
├── predict_main.py               # 💻 核心代码
├── predictive_agent.py
├── predictive_models.py
├── predict_memory_manager.py
├── database_manager.py
└── __init__.py
```

## 🔧 开发流程规范

### 1. 新功能开发
1. **需求分析** → 在 `doc/` 目录下创建分析文档
2. **数据库设计** → SQL脚本放在 `sql/` 目录下
3. **代码实现** → 核心代码在根目录下
4. **测试验证** → 测试脚本放在 `test/` 目录下
5. **文档更新** → 更新 `doc/` 目录下的相关文档

### 2. 数据库变更流程
1. **设计变更** → 在 `sql/` 目录下创建新的SQL脚本
2. **版本管理** → 使用版本号命名（如：`v1.1_add_new_table.sql`）
3. **测试验证** → 在测试环境验证SQL脚本
4. **文档更新** → 更新数据库设计文档

### 3. 测试规范
1. **单元测试** → 针对单个功能模块的测试
2. **集成测试** → 针对多个模块协作的测试
3. **API测试** → 使用HTTP Client插件测试REST API
4. **数据库测试** → 验证数据库操作的正确性

## 📝 命名规范

### 1. 文件命名
- **文档文件**：使用下划线分隔，描述性命名
  - `requirements_comparison_0721.md`
  - `database_design.md`
- **测试文件**：以 `test_` 开头
  - `test_database_connection.py`
  - `test_database_manager.py`
- **SQL文件**：描述性命名，可包含版本号
  - `database_schema.sql`
  - `v1.1_add_conversation_table.sql`

### 2. 目录命名
- 使用小写字母和下划线
- 简洁明了，表达目录用途
- 避免使用特殊字符和空格

## 🚀 版本控制规范

### 1. 提交规范
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **test**: 测试相关
- **sql**: 数据库相关

### 2. 分支管理
- **main**: 主分支，稳定版本
- **develop**: 开发分支
- **feature/***: 功能分支
- **hotfix/***: 热修复分支

## ✅ 质量保证

### 1. 代码审查
- 所有代码变更需要经过审查
- 重点关注安全性、性能、可维护性

### 2. 测试覆盖
- 新功能必须包含相应的测试用例
- 数据库变更必须包含验证脚本

### 3. 文档同步
- 代码变更必须同步更新相关文档
- 保持文档的时效性和准确性

## 📊 监控和维护

### 1. 性能监控
- 定期检查数据库性能
- 监控API响应时间

### 2. 日志管理
- 统一的日志格式
- 合理的日志级别设置

### 3. 备份策略
- 定期备份数据库
- 重要配置文件的版本管理

---

## 🎯 重要提醒

### ⭐ 顶级规范（必须严格遵守）

#### 1. SQL文件管理规范
所有SQL相关的文件操作都必须在 `agents/predict_algorith_agent/sql/` 目录下进行，包括：
- 新建SQL脚本
- 修改现有SQL脚本
- 数据库迁移脚本
- 数据修复脚本

#### 2. REST API方法限制规范 ⭐ **新增顶级规范**
**只能使用GET和POST方法，禁止使用其他HTTP方法**：
- ✅ **允许**：GET、POST
- ❌ **禁止**：PUT、DELETE、PATCH、HEAD、OPTIONS等

**实施要求**：
- 查询操作使用GET方法
- 创建、更新、删除操作都使用POST方法
- 通过URL路径区分不同操作：
  - 创建：`POST /api/resource`
  - 更新：`POST /api/resource/{id}/update`
  - 删除：`POST /api/resource/{id}/delete`

这些规范有助于：
- 统一管理数据库相关文件
- 简化API设计和客户端实现
- 提高团队协作效率
- 降低系统复杂度和维护成本
