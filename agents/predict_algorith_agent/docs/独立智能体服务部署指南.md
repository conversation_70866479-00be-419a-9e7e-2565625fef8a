# 独立智能体服务部署指南

## 📋 部署概览

本指南专注于独立的算法智能体服务部署，包括Agent和WebSocket功能，不涉及Nginx等额外组件。

### 🎯 部署目标
- **独立FastAPI服务** - 直接暴露8008端口
- **WebSocket支持** - 实时通信功能
- **智能体核心功能** - 算法训练、预测、报告生成
- **简化配置** - 最小化依赖和配置

### 🏗️ 简化架构
```
用户 → FastAPI (8008端口) → 智能体服务 → MySQL/Redis
```

## 🚀 快速部署流程

### 方案A：一键自动部署（推荐）
```bash
# 1. 下载精简部署脚本
wget https://your-repo/deploy_standalone.sh
chmod +x deploy_standalone.sh
./deploy_standalone.sh

# 2. 上传代码
# Windows: upload_code.bat
# Linux/Mac: upload_code.sh

# 3. 配置数据库
cp config_template.py agents/config.py
nano agents/config.py

# 4. 启动独立服务
python start_algorithm_agent.py
```

### 方案B：手动部署
按照下面的详细步骤执行。

## 🔧 详细部署步骤

### 第一步：服务器环境准备

#### 1.1 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / RHEL 8+
- **内存**: 最低2GB，推荐4GB+
- **磁盘**: 最低5GB可用空间
- **Python**: 3.10-3.12

#### 1.2 安装基础依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3.10 python3.10-venv python3.10-dev python3-pip \
                   git curl libmysqlclient-dev pkg-config

# CentOS/RHEL
sudo yum update -y
sudo yum install -y python310 python310-pip python310-devel \
                   git curl mysql-devel pkgconfig
```

#### 1.3 创建项目目录
```bash
mkdir -p ~/algorithm-agent
cd ~/algorithm-agent
```

### 第二步：Python环境配置

#### 2.1 创建虚拟环境
```bash
python3.10 -m venv venv
source venv/bin/activate
pip install --upgrade pip
```

#### 2.2 安装核心依赖
```bash
# 安装智能体服务必需的包
pip install fastapi uvicorn pydantic pydantic_ai requests \
           pymysql redis sqlalchemy python-dotenv websockets
```

### 第三步：代码部署

#### 3.1 上传代码文件
```bash
# 使用rsync上传（推荐）
rsync -avz --exclude="__pycache__" --exclude="*.pyc" \
  your-local-path/agents/ ~/algorithm-agent/agents/
rsync -avz --exclude="__pycache__" --exclude="*.pyc" \
  your-local-path/core/ ~/algorithm-agent/core/
```

#### 3.2 设置文件权限
```bash
chmod -R 644 ~/algorithm-agent/
find ~/algorithm-agent/ -type d -exec chmod 755 {} \;
chmod +x ~/algorithm-agent/start_algorithm_agent.py
```

### 第四步：配置文件设置

#### 4.1 创建配置文件
```bash
cd ~/algorithm-agent
cp config_template.py agents/config.py
```

#### 4.2 修改核心配置
编辑 `agents/config.py`：
```python
# 独立服务配置
PREDICT_MAIN_HOST = "0.0.0.0"  # 监听所有接口
PREDICT_MAIN_PORT = 8008       # 服务端口

# MySQL数据库配置
MYSQL_HOST = "your-mysql-host"
MYSQL_PORT = 3306
MYSQL_USER = "your-mysql-user"
MYSQL_PASSWORD = "your-mysql-password"
MYSQL_DB = "your-database"

# Redis配置
REDIS_HOST = "your-redis-host"
REDIS_PORT = 6379
REDIS_PASSWORD = "your-redis-password"

# API配置（根据实际情况修改）
ALGORITHM_API_BASE = "http://your-algorithm-api/api"
TRAING_API_BASE = "http://your-training-api/api"
HISTORY_ALGORITHM_API_BASE = "http://your-history-api/api"
```

### 第五步：启动脚本配置

#### 5.1 创建独立启动脚本
```bash
cat > start_standalone.py << 'EOF'
#!/usr/bin/env python3
"""
独立智能体服务启动脚本
专注于Agent和WebSocket功能
"""
import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))
os.environ['PROJECT_ROOT'] = str(project_root)
os.environ['PYTHONPATH'] = str(project_root)

print("🚀 启动独立智能体服务...")
print(f"📁 项目目录: {project_root}")
print(f"🌐 服务地址: http://0.0.0.0:8008")

try:
    from agents.predict_algorith_agent.predict_main import app
    import uvicorn
    
    # 启动独立服务
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8008,
        log_level="info",
        access_log=True,
        reload=False  # 生产环境关闭自动重载
    )
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF

chmod +x start_standalone.py
```

#### 5.2 创建数据库测试脚本
```bash
cat > test_connections.py << 'EOF'
#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.getcwd())

def test_mysql():
    try:
        from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
        import pymysql
        
        print("🔍 测试MySQL连接...")
        connection = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB,
            charset='utf8mb4'
        )
        print("✅ MySQL连接成功!")
        connection.close()
        return True
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_redis():
    try:
        from agents.config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
        import redis
        
        print("🔍 测试Redis连接...")
        r = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        r.ping()
        print("✅ Redis连接成功!")
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

if __name__ == "__main__":
    mysql_ok = test_mysql()
    redis_ok = test_redis()
    
    if mysql_ok and redis_ok:
        print("🎉 所有数据库连接测试通过!")
        sys.exit(0)
    else:
        print("💥 数据库连接测试失败，请检查配置!")
        sys.exit(1)
EOF

chmod +x test_connections.py
```

### 第六步：服务启动与测试

#### 6.1 测试数据库连接
```bash
source venv/bin/activate
python test_connections.py
```

#### 6.2 启动独立服务
```bash
# 前台启动（用于测试）
python start_standalone.py

# 后台启动（生产环境）
nohup python start_standalone.py > logs/service.log 2>&1 &
```

#### 6.3 验证服务功能
```bash
# 健康检查
curl http://localhost:8008/health

# 静态文件访问
curl -I http://localhost:8008/static/html/chat_interface_v2.html

# WebSocket测试（如果有websocat）
echo '{"type": "ping"}' | websocat ws://localhost:8008/api/ws
```

## 🔧 生产环境配置

### systemd服务配置（可选）
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/algorithm-agent.service << EOF
[Unit]
Description=Algorithm Agent Standalone Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$HOME/algorithm-agent
Environment=PROJECT_ROOT=$HOME/algorithm-agent
Environment=PYTHONPATH=$HOME/algorithm-agent
ExecStart=$HOME/algorithm-agent/venv/bin/python start_standalone.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable algorithm-agent
sudo systemctl start algorithm-agent
```

### 防火墙配置
```bash
# 开放8008端口
sudo ufw allow 8008/tcp

# 或者CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8008/tcp
sudo firewall-cmd --reload
```

## 📊 服务监控

### 基础监控
```bash
# 检查服务状态
sudo systemctl status algorithm-agent

# 查看实时日志
tail -f logs/service.log

# 检查端口监听
netstat -tlnp | grep 8008

# 检查进程
ps aux | grep python
```

### 性能监控
```bash
# 内存使用
free -h

# CPU使用
top -p $(pgrep -f start_standalone.py)

# 网络连接
ss -tlnp | grep 8008
```

## 🚨 故障排除

### 常见问题
1. **端口被占用**
```bash
sudo netstat -tlnp | grep 8008
sudo kill -9 <PID>
```

2. **模块导入错误**
```bash
export PYTHONPATH=$HOME/algorithm-agent:$PYTHONPATH
```

3. **数据库连接失败**
```bash
python test_connections.py
```

4. **权限问题**
```bash
chmod +x start_standalone.py
chown -R $USER:$USER ~/algorithm-agent
```

## ✅ 部署验证清单

### 基础验证
- [ ] Python 3.10+ 安装完成
- [ ] 虚拟环境创建成功
- [ ] 核心依赖安装完成
- [ ] 代码文件上传完成
- [ ] 配置文件修改完成

### 功能验证
- [ ] 数据库连接测试通过
- [ ] 服务启动成功
- [ ] 健康检查返回200
- [ ] 静态文件可访问
- [ ] WebSocket连接正常

### 生产验证
- [ ] systemd服务配置完成
- [ ] 防火墙端口开放
- [ ] 服务自动重启正常
- [ ] 日志记录正常

## 🎯 服务管理

### 日常操作
```bash
# 启动服务
sudo systemctl start algorithm-agent

# 停止服务
sudo systemctl stop algorithm-agent

# 重启服务
sudo systemctl restart algorithm-agent

# 查看状态
sudo systemctl status algorithm-agent

# 查看日志
sudo journalctl -u algorithm-agent -f
```

### 更新部署
```bash
# 1. 停止服务
sudo systemctl stop algorithm-agent

# 2. 备份当前版本
tar -czf backup_$(date +%Y%m%d_%H%M%S).tar.gz agents/ core/

# 3. 更新代码
# 重新上传新版本代码

# 4. 重启服务
sudo systemctl start algorithm-agent
```

## 📝 配置模板

### 最小化配置示例
```python
# agents/config.py - 独立服务最小配置
PREDICT_MAIN_HOST = "0.0.0.0"
PREDICT_MAIN_PORT = 8008

# 数据库配置
MYSQL_HOST = "localhost"
MYSQL_PORT = 3306
MYSQL_USER = "agent_user"
MYSQL_PASSWORD = "your_password"
MYSQL_DB = "algorithm_agent"

REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_PASSWORD = "your_redis_password"

# API配置（可选）
ALGORITHM_API_BASE = "http://localhost:8001/api"
TRAING_API_BASE = "http://localhost:8002/api"
HISTORY_ALGORITHM_API_BASE = "http://localhost:8003/api"
```

## 🎉 部署完成

### 验证部署成功
```bash
# 一键验证脚本
curl -f http://localhost:8008/health && \
echo "✅ 独立智能体服务部署成功!" || \
echo "❌ 部署验证失败，请检查日志"
```

### 访问服务
- **健康检查**: http://your-server-ip:8008/health
- **前端界面**: http://your-server-ip:8008/static/html/chat_interface_v2.html
- **WebSocket**: ws://your-server-ip:8008/api/ws

---
*独立智能体服务部署指南 - 2025-07-28*
*专注于Agent和WebSocket功能的简化部署*
