# 算法智能体WebSocket客户端接入指南

## 📋 概述

本文档详细介绍如何在前端应用中接入算法智能体的WebSocket服务，实现实时的多轮对话交互。

## 🔗 连接地址

### 主要聊天端点
```
ws://localhost:8008/api/ws/chat?user_id=<用户ID>&session_id=<会话ID>&username=<用户名>
```

**参数说明**：
- `user_id`：**必需参数**，用于标识当前登录用户，将存储到数据库
- `session_id`：可选参数，32位随机字符串，如果不提供将自动生成
- `username`：可选参数，用户显示名称，如果不提供将使用`user_{user_id}`

**重要说明**：
- `user_id`是必需的，缺少此参数连接将被拒绝（错误码4001）
- `session_id`会自动生成为32位UUID字符串（如：`a1b2c3d4e5f6789012345678901234ab`）
- 所有会话信息将存储到数据库的相关表中

### 连接示例
```javascript
// 1. 基本连接（必须提供user_id）
const userId = "12345";  // 从登录系统获取的用户ID
const ws = new WebSocket(`ws://localhost:8008/api/ws/chat?user_id=${userId}`);

// 2. 完整参数连接（推荐）
const userId = "12345";
const sessionId = generateSessionId(); // 生成32位随机字符串
const username = "张三";
const ws = new WebSocket(`ws://localhost:8008/api/ws/chat?user_id=${userId}&session_id=${sessionId}&username=${encodeURIComponent(username)}`);

// 3. 生成32位session_id的函数
function generateSessionId() {
    return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
        const r = Math.random() * 16 | 0;
        return r.toString(16);
    });
}
```

## 📡 HTTP辅助接口

### WebSocket服务状态检查
```http
GET http://localhost:8008/api/ws/health
```

**响应示例**：
```json
{
  "status": "healthy",
  "service": "WebSocket Chat Service",
  "active_connections": 5,
  "total_sessions": 12,
  "timestamp": "2025-01-22T19:00:00.000Z"
}
```

### 连接统计信息
```http
GET http://localhost:8008/api/ws/stats
```

**响应示例**：
```json
{
  "success": true,
  "data": {
    "active_connections": 5,
    "total_sessions": 12,
    "status": "running"
  },
  "message": "获取统计信息成功"
}
```

### 广播消息（管理功能）
```http
POST http://localhost:8008/api/ws/broadcast
Content-Type: application/json

{
  "type": "system_notice",
  "data": {
    "message": "系统将在10分钟后进行维护"
  }
}
```

### 断开指定会话（管理功能）
```http
POST http://localhost:8008/api/ws/session/{session_id}/disconnect
```

## 💬 消息协议

### 客户端发送消息格式

#### 基本对话消息
```json
{
  "type": "chat",
  "data": {
    "message": "我想用LSTM做预测性维护，学习率0.001，批次大小32"
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "user_id": "12345"
}
```

#### 查询任务进度
```json
{
  "type": "get_progress",
  "data": {
    "task_id": "task_abc123"
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "user_id": "12345"
}
```

#### 生成分析报告
```json
{
  "type": "generate_report",
  "data": {
    "task_id": "task_abc123",
    "report_type": "detailed"
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "user_id": "12345"
}
```

#### 获取对话历史
```json
{
  "type": "get_history",
  "data": {
    "user_id": "12345",
    "limit": 10
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "user_id": "12345"
}
```

#### 心跳保持
```json
{
  "type": "ping",
  "data": {},
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "user_id": "12345"
}
```

### 服务端响应消息格式

#### 欢迎消息
```json
{
  "type": "welcome",
  "data": {
    "message": "连接成功！欢迎使用算法生成智能体",
    "session_id": "a1b2c3d4e5f6789012345678901234ab",
    "user_id": "12345",
    "username": "张三",
    "features": [
      "智能算法推荐",
      "参数自动提取",
      "实时对话交互",
      "训练进度监控",
      "分析报告生成"
    ]
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "status": "success"
}
```

#### 思考状态
```json
{
  "type": "thinking",
  "data": {
    "message": "正在分析您的需求..."
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "a1b2c3d4e5f6789012345678901234ab",
  "user_id": "12345",
  "status": "success"
}
```

#### 对话回复
```json
{
  "type": "chat_response",
  "data": {
    "message": "我理解您想要使用LSTM进行预测性维护。根据您提供的参数...",
    "task_id": "task_abc123",
    "state": "need_confirm",
    "params": {
      "algorithm_type": "LSTM",
      "learning_rate": 0.001,
      "batch_size": 32
    },
    "has_error": false
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "user_123_1642867200000",
  "status": "success"
}
```

#### 进度信息
```json
{
  "type": "progress_data",
  "data": {
    "task_id": "task_abc123",
    "progress": 65.5,
    "status": "training",
    "current_epoch": 65,
    "total_epochs": 100,
    "estimated_time_remaining": "5分钟"
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "user_123_1642867200000",
  "status": "success"
}
```

#### 错误信息
```json
{
  "type": "error",
  "data": {
    "message": "参数验证失败：学习率必须在0.0001到0.1之间",
    "error_code": "PARAM_VALIDATION_ERROR",
    "details": "learning_rate value 1.5 is out of valid range"
  },
  "timestamp": "2025-01-22T19:00:00.000Z",
  "session_id": "user_123_1642867200000",
  "status": "error"
}
```

## 🛠️ 前端集成实现

### JavaScript原生实现

```javascript
class AlgorithmAgentClient {
    constructor(userId) {
        this.userId = userId;
        this.sessionId = `user_${userId}_${Date.now()}`;
        this.ws = null;
        this.isConnected = false;
        this.messageQueue = [];
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
    }

    connect() {
        const wsUrl = `ws://localhost:8008/api/ws/chat?session_id=${this.sessionId}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
            console.log('WebSocket连接已建立');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            
            // 发送队列中的消息
            this.flushMessageQueue();
        };

        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('消息解析错误:', error);
            }
        };

        this.ws.onclose = (event) => {
            console.log('WebSocket连接已关闭', event.code, event.reason);
            this.isConnected = false;
            
            // 自动重连
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                setTimeout(() => {
                    this.reconnectAttempts++;
                    console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    this.connect();
                }, this.reconnectInterval);
            }
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    sendMessage(text, type = 'chat') {
        const message = {
            type: type,
            data: type === 'chat' ? { message: text } : text,
            timestamp: new Date().toISOString(),
            session_id: this.sessionId
        };

        if (this.isConnected) {
            this.ws.send(JSON.stringify(message));
        } else {
            // 连接断开时将消息加入队列
            this.messageQueue.push(message);
            console.log('连接断开，消息已加入队列');
        }
    }

    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.ws.send(JSON.stringify(message));
        }
    }

    handleMessage(message) {
        switch (message.type) {
            case 'welcome':
                this.onWelcome(message.data);
                break;
            case 'thinking':
                this.onThinking(message.data);
                break;
            case 'chat_response':
                this.onChatResponse(message.data);
                break;
            case 'progress_data':
                this.onProgressUpdate(message.data);
                break;
            case 'report_data':
                this.onReportGenerated(message.data);
                break;
            case 'error':
                this.onError(message.data);
                break;
            default:
                console.log('未知消息类型:', message);
        }
    }

    // 事件处理方法（可以被重写）
    onWelcome(data) {
        console.log('欢迎消息:', data.message);
    }

    onThinking(data) {
        console.log('AI正在思考...');
    }

    onChatResponse(data) {
        console.log('AI回复:', data.message);
        if (data.task_id) {
            console.log('任务ID:', data.task_id);
        }
    }

    onProgressUpdate(data) {
        console.log(`任务进度: ${data.progress}%`);
    }

    onReportGenerated(data) {
        console.log('报告生成完成:', data);
    }

    onError(data) {
        console.error('错误:', data.message);
    }

    // 便捷方法
    queryProgress(taskId) {
        this.sendMessage({ task_id: taskId }, 'get_progress');
    }

    generateReport(taskId, reportType = 'detailed') {
        this.sendMessage({ 
            task_id: taskId, 
            report_type: reportType 
        }, 'generate_report');
    }

    getHistory(limit = 10) {
        this.sendMessage({ 
            user_id: this.userId, 
            limit: limit 
        }, 'get_history');
    }

    ping() {
        this.sendMessage({}, 'ping');
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 使用示例
const client = new AlgorithmAgentClient('user123');
client.connect();

// 发送消息
client.sendMessage('我想用LSTM做预测性维护');

// 查询进度
client.queryProgress('task_abc123');

// 生成报告
client.generateReport('task_abc123');
```

### Vue.js集成示例

```vue
<template>
  <div class="algorithm-chat">
    <!-- 连接状态 -->
    <div class="connection-status" :class="{ connected: isConnected }">
      {{ isConnected ? '已连接' : '连接中...' }}
    </div>

    <!-- 消息列表 -->
    <div class="messages-container" ref="messagesContainer">
      <div
        v-for="msg in messages"
        :key="msg.id"
        :class="['message', msg.type]"
      >
        <div class="message-content">
          <div class="text">{{ msg.content }}</div>
          <div class="timestamp">{{ formatTime(msg.timestamp) }}</div>
        </div>

        <!-- 进度条（如果有） -->
        <div v-if="msg.progress !== undefined" class="progress-bar">
          <div class="progress-fill" :style="{ width: msg.progress + '%' }"></div>
          <span class="progress-text">{{ msg.progress }}%</span>
        </div>

        <!-- 参数信息（如果有） -->
        <div v-if="msg.params" class="params-info">
          <h4>检测到的参数：</h4>
          <ul>
            <li v-for="(value, key) in msg.params" :key="key">
              {{ key }}: {{ value }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-wrapper">
        <input
          v-model="inputText"
          @keyup.enter="sendMessage"
          placeholder="请输入您的算法需求..."
          :disabled="!isConnected || isSending"
          class="message-input"
        />
        <button
          @click="sendMessage"
          :disabled="!isConnected || !inputText.trim() || isSending"
          class="send-button"
        >
          {{ isSending ? '发送中...' : '发送' }}
        </button>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions" v-if="currentTaskId">
        <button @click="queryProgress" class="action-btn">查询进度</button>
        <button @click="generateReport" class="action-btn">生成报告</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AlgorithmChat',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      ws: null,
      isConnected: false,
      isSending: false,
      messages: [],
      inputText: '',
      sessionId: `user_${this.userId}_${Date.now()}`,
      currentTaskId: null,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5
    };
  },
  mounted() {
    this.connectWebSocket();
  },
  beforeUnmount() {
    if (this.ws) {
      this.ws.close();
    }
  },
  methods: {
    connectWebSocket() {
      const wsUrl = `ws://localhost:8008/api/ws/chat?session_id=${this.sessionId}`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        console.log('WebSocket连接成功');
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('消息解析错误:', error);
        }
      };

      this.ws.onclose = () => {
        this.isConnected = false;
        console.log('WebSocket连接断开');

        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.reconnectAttempts++;
            this.connectWebSocket();
          }, 3000);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error);
      };
    },

    sendMessage() {
      if (!this.inputText.trim() || !this.isConnected || this.isSending) return;

      this.isSending = true;
      const userMessage = this.inputText;

      // 添加用户消息到界面
      this.addMessage({
        type: 'user',
        content: userMessage,
        timestamp: new Date()
      });

      // 发送WebSocket消息
      const message = {
        type: 'chat',
        data: { message: userMessage },
        timestamp: new Date().toISOString(),
        session_id: this.sessionId
      };

      this.ws.send(JSON.stringify(message));
      this.inputText = '';
      this.isSending = false;
    },

    handleMessage(message) {
      switch (message.type) {
        case 'welcome':
          this.addMessage({
            type: 'assistant',
            content: message.data.message,
            timestamp: new Date(message.timestamp)
          });
          break;

        case 'thinking':
          this.addMessage({
            type: 'system',
            content: message.data.message,
            timestamp: new Date(message.timestamp)
          });
          break;

        case 'chat_response':
          this.addMessage({
            type: 'assistant',
            content: message.data.message,
            timestamp: new Date(message.timestamp),
            params: message.data.params,
            taskId: message.data.task_id
          });

          if (message.data.task_id) {
            this.currentTaskId = message.data.task_id;
          }
          break;

        case 'progress_data':
          this.addMessage({
            type: 'progress',
            content: `任务进度更新：${message.data.progress}%`,
            timestamp: new Date(message.timestamp),
            progress: message.data.progress,
            taskId: message.data.task_id
          });
          break;

        case 'report_data':
          this.addMessage({
            type: 'report',
            content: '分析报告已生成',
            timestamp: new Date(message.timestamp),
            reportData: message.data
          });
          break;

        case 'error':
          this.addMessage({
            type: 'error',
            content: `错误：${message.data.message}`,
            timestamp: new Date(message.timestamp)
          });
          break;
      }

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    addMessage(message) {
      this.messages.push({
        id: Date.now() + Math.random(),
        ...message
      });
    },

    queryProgress() {
      if (!this.currentTaskId) return;

      const message = {
        type: 'get_progress',
        data: { task_id: this.currentTaskId },
        timestamp: new Date().toISOString(),
        session_id: this.sessionId
      };

      this.ws.send(JSON.stringify(message));
    },

    generateReport() {
      if (!this.currentTaskId) return;

      const message = {
        type: 'generate_report',
        data: {
          task_id: this.currentTaskId,
          report_type: 'detailed'
        },
        timestamp: new Date().toISOString(),
        session_id: this.sessionId
      };

      this.ws.send(JSON.stringify(message));
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }
};
</script>

<style scoped>
.algorithm-chat {
  display: flex;
  flex-direction: column;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.connection-status {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #666;
  font-size: 12px;
  text-align: center;
}

.connection-status.connected {
  background: #e8f5e8;
  color: #2d8f2d;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
}

.message {
  margin-bottom: 16px;
  max-width: 80%;
}

.message.user {
  margin-left: auto;
}

.message.user .message-content {
  background: #007bff;
  color: white;
  border-radius: 18px 18px 4px 18px;
}

.message.assistant .message-content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 18px 18px 18px 4px;
}

.message.system .message-content {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  font-style: italic;
}

.message.error .message-content {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  color: #721c24;
}

.message-content {
  padding: 12px 16px;
  word-wrap: break-word;
}

.timestamp {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
}

.progress-bar {
  margin-top: 8px;
  background: #e9ecef;
  border-radius: 4px;
  height: 20px;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  background: #28a745;
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #333;
}

.params-info {
  margin-top: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 14px;
}

.params-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.params-info ul {
  margin: 0;
  padding-left: 16px;
}

.input-area {
  padding: 16px;
  background: white;
  border-top: 1px solid #ddd;
}

.input-wrapper {
  display: flex;
  gap: 8px;
}

.message-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

.message-input:focus {
  border-color: #007bff;
}

.send-button {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
}

.send-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.quick-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.action-btn:hover {
  background: #e9ecef;
}
</style>
```

### React集成示例

```jsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import './AlgorithmChat.css';

const AlgorithmChat = ({ userId }) => {
  const [ws, setWs] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [currentTaskId, setCurrentTaskId] = useState(null);
  const messagesEndRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const sessionId = `user_${userId}_${Date.now()}`;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addMessage = useCallback((message) => {
    setMessages(prev => [...prev, {
      id: Date.now() + Math.random(),
      ...message
    }]);
  }, []);

  const handleMessage = useCallback((message) => {
    switch (message.type) {
      case 'welcome':
        addMessage({
          type: 'assistant',
          content: message.data.message,
          timestamp: new Date(message.timestamp)
        });
        break;

      case 'thinking':
        addMessage({
          type: 'system',
          content: message.data.message,
          timestamp: new Date(message.timestamp)
        });
        break;

      case 'chat_response':
        addMessage({
          type: 'assistant',
          content: message.data.message,
          timestamp: new Date(message.timestamp),
          params: message.data.params,
          taskId: message.data.task_id
        });

        if (message.data.task_id) {
          setCurrentTaskId(message.data.task_id);
        }
        break;

      case 'progress_data':
        addMessage({
          type: 'progress',
          content: `任务进度更新：${message.data.progress}%`,
          timestamp: new Date(message.timestamp),
          progress: message.data.progress,
          taskId: message.data.task_id
        });
        break;

      case 'report_data':
        addMessage({
          type: 'report',
          content: '分析报告已生成',
          timestamp: new Date(message.timestamp),
          reportData: message.data
        });
        break;

      case 'error':
        addMessage({
          type: 'error',
          content: `错误：${message.data.message}`,
          timestamp: new Date(message.timestamp)
        });
        break;

      default:
        console.log('未知消息类型:', message);
    }
  }, [addMessage]);

  const connectWebSocket = useCallback(() => {
    const wsUrl = `ws://localhost:8008/api/ws/chat?session_id=${sessionId}`;
    const websocket = new WebSocket(wsUrl);

    websocket.onopen = () => {
      setIsConnected(true);
      reconnectAttempts.current = 0;
      console.log('WebSocket连接成功');
    };

    websocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleMessage(message);
      } catch (error) {
        console.error('消息解析错误:', error);
      }
    };

    websocket.onclose = () => {
      setIsConnected(false);
      console.log('WebSocket连接断开');

      // 自动重连
      if (reconnectAttempts.current < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.current++;
          connectWebSocket();
        }, 3000);
      }
    };

    websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };

    setWs(websocket);
  }, [sessionId, handleMessage]);

  useEffect(() => {
    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [connectWebSocket]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = () => {
    if (!inputText.trim() || !isConnected || isSending) return;

    setIsSending(true);
    const userMessage = inputText;

    // 添加用户消息到界面
    addMessage({
      type: 'user',
      content: userMessage,
      timestamp: new Date()
    });

    // 发送WebSocket消息
    const message = {
      type: 'chat',
      data: { message: userMessage },
      timestamp: new Date().toISOString(),
      session_id: sessionId
    };

    ws.send(JSON.stringify(message));
    setInputText('');
    setIsSending(false);
  };

  const queryProgress = () => {
    if (!currentTaskId || !ws) return;

    const message = {
      type: 'get_progress',
      data: { task_id: currentTaskId },
      timestamp: new Date().toISOString(),
      session_id: sessionId
    };

    ws.send(JSON.stringify(message));
  };

  const generateReport = () => {
    if (!currentTaskId || !ws) return;

    const message = {
      type: 'generate_report',
      data: {
        task_id: currentTaskId,
        report_type: 'detailed'
      },
      timestamp: new Date().toISOString(),
      session_id: sessionId
    };

    ws.send(JSON.stringify(message));
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  return (
    <div className="algorithm-chat">
      {/* 连接状态 */}
      <div className={`connection-status ${isConnected ? 'connected' : ''}`}>
        {isConnected ? '已连接' : '连接中...'}
      </div>

      {/* 消息列表 */}
      <div className="messages-container">
        {messages.map((msg) => (
          <div key={msg.id} className={`message ${msg.type}`}>
            <div className="message-content">
              <div className="text">{msg.content}</div>
              <div className="timestamp">{formatTime(msg.timestamp)}</div>
            </div>

            {/* 进度条 */}
            {msg.progress !== undefined && (
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${msg.progress}%` }}
                ></div>
                <span className="progress-text">{msg.progress}%</span>
              </div>
            )}

            {/* 参数信息 */}
            {msg.params && (
              <div className="params-info">
                <h4>检测到的参数：</h4>
                <ul>
                  {Object.entries(msg.params).map(([key, value]) => (
                    <li key={key}>{key}: {value}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="input-area">
        <div className="input-wrapper">
          <input
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="请输入您的算法需求..."
            disabled={!isConnected || isSending}
            className="message-input"
          />
          <button
            onClick={sendMessage}
            disabled={!isConnected || !inputText.trim() || isSending}
            className="send-button"
          >
            {isSending ? '发送中...' : '发送'}
          </button>
        </div>

        {/* 快捷操作 */}
        {currentTaskId && (
          <div className="quick-actions">
            <button onClick={queryProgress} className="action-btn">
              查询进度
            </button>
            <button onClick={generateReport} className="action-btn">
              生成报告
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default AlgorithmChat;
```

## 🌐 环境配置

### 开发环境
```bash
# WebSocket地址
ws://localhost:8008/api/ws/chat

# HTTP接口基础地址
http://localhost:8008/api/ws/
```

### 生产环境
```bash
# WebSocket地址（使用WSS加密）
wss://your-domain.com/api/ws/chat

# HTTP接口基础地址
https://your-domain.com/api/ws/
```

### 环境变量配置
```javascript
// config.js
const config = {
  development: {
    wsUrl: 'ws://localhost:8008/api/ws/chat',
    httpUrl: 'http://localhost:8008/api/ws/'
  },
  production: {
    wsUrl: 'wss://your-domain.com/api/ws/chat',
    httpUrl: 'https://your-domain.com/api/ws/'
  }
};

export default config[process.env.NODE_ENV || 'development'];
```

## 🔧 最佳实践

### 1. 连接管理
- **自动重连**：实现指数退避重连策略
- **心跳保持**：定期发送ping消息保持连接活跃
- **连接状态监控**：实时显示连接状态给用户

### 2. 消息处理
- **消息队列**：连接断开时缓存消息，重连后发送
- **错误处理**：完善的错误处理和用户提示
- **消息去重**：避免重复消息的处理

### 3. 用户体验
- **加载状态**：显示消息发送和处理状态
- **进度指示**：实时显示任务进度
- **历史记录**：保存和恢复对话历史

### 4. 性能优化
- **消息分页**：大量历史消息的分页加载
- **虚拟滚动**：处理大量消息时的性能优化
- **内存管理**：及时清理不需要的消息和状态

## 🛡️ 安全考虑

### 1. 连接安全
- **WSS协议**：生产环境使用加密的WebSocket连接
- **身份验证**：在连接时验证用户身份
- **会话管理**：安全的会话ID生成和管理

### 2. 数据验证
- **输入验证**：验证用户输入的合法性
- **消息格式验证**：确保消息格式正确
- **XSS防护**：防止跨站脚本攻击

### 3. 访问控制
- **权限验证**：验证用户操作权限
- **频率限制**：防止消息发送频率过高
- **资源保护**：保护服务器资源不被滥用

## 📝 故障排除

### 常见问题

#### 1. 连接失败
```javascript
// 检查服务器状态
fetch('http://localhost:8008/api/ws/health')
  .then(response => response.json())
  .then(data => console.log('服务器状态:', data))
  .catch(error => console.error('服务器不可用:', error));
```

#### 2. 消息发送失败
- 检查WebSocket连接状态
- 验证消息格式是否正确
- 确认session_id是否有效

#### 3. 自动重连不工作
- 检查重连逻辑是否正确实现
- 验证重连次数限制
- 确认网络连接状态

### 调试技巧

#### 1. 启用详细日志
```javascript
// 在浏览器控制台中启用WebSocket调试
localStorage.setItem('debug', 'websocket:*');
```

#### 2. 监控网络请求
- 使用浏览器开发者工具的Network面板
- 查看WebSocket连接和消息传输
- 检查HTTP请求状态

#### 3. 服务器日志
- 查看服务器端的WebSocket连接日志
- 检查消息处理错误日志
- 监控服务器性能指标

## 🗄️ 数据库存储

### 会话数据存储
WebSocket连接建立后，系统会自动将会话信息存储到数据库中：

#### conversations表
```sql
CREATE TABLE conversations (
    id VARCHAR(50) PRIMARY KEY,           -- conversation_id
    user_id VARCHAR(50) NOT NULL,         -- 用户ID
    session_id VARCHAR(32) NOT NULL,      -- 32位session_id
    username VARCHAR(100),                -- 用户名
    title VARCHAR(200),                   -- 对话标题
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### conversation_messages表
```sql
CREATE TABLE conversation_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    conversation_id VARCHAR(50) NOT NULL, -- 关联conversations.id
    message_type ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,                -- 消息内容
    metadata JSON,                        -- 额外元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id)
);
```

#### session_states表
```sql
CREATE TABLE session_states (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(32) NOT NULL,      -- 32位session_id
    user_id VARCHAR(50) NOT NULL,         -- 用户ID
    state_data JSON,                      -- 会话状态数据
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_session (session_id)
);
```

### 数据存储流程
1. **连接建立**：创建conversation记录和session_states记录
2. **消息交互**：每条消息存储到conversation_messages表
3. **状态更新**：会话状态实时更新到session_states表
4. **连接断开**：更新last_activity时间戳

### Session ID生成规则
- **格式**：32位十六进制字符串
- **生成方式**：使用`uuid.uuid4().hex`
- **示例**：`a1b2c3d4e5f6789012345678901234ab`
- **唯一性**：全局唯一，避免冲突

## 📚 参考资料

- [WebSocket API文档](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
- [FastAPI WebSocket文档](https://fastapi.tiangolo.com/advanced/websockets/)
- [Vue.js官方文档](https://vuejs.org/)
- [React官方文档](https://reactjs.org/)

---

**注意**：本文档基于当前的WebSocket服务实现，如有更新请及时同步文档内容。
