# 前端直传参数流程设计

## 概述
根据算法平台API接口文档，设计支持前端直接传递完整参数给智能体的交互流程，智能体作为API调用的代理层。

---

## 1. 流程设计原则

### 1.1 双模式支持
- **模式1**: 自然语言交互 - 用户描述需求，智能体提取参数
- **模式2**: 前端直传参数 - 前端UI收集参数，直接调用API

### 1.2 智能体角色定位
- **API代理层**: 接收前端参数，调用算法平台API
- **错误处理器**: 处理API调用异常，返回友好错误信息
- **状态管理器**: 跟踪异步任务状态（训练、监控服务）

---

## 2. WebSocket消息协议

### 2.1 前端直传参数消息格式
```json
{
    "type": "api_call",
    "data": {
        "action": "train_algorithm",
        "project_id": "实际的项目ID",
        "parameters": {
            "parameter1": "测试算法1",
            "parameter2": "风险预警", 
            "parameter3": "水泵数据集0612",
            "parameter4": "5",
            "parameter5": "64",
            "parameter6": "3",
            "parameter7": "0.2",
            "parameter8": "YES",
            "parameter9": "10",
            "parameter10": "0.01",
            "parameter11": "1",
            "parameter12": "Adam",
            "parameter13": "MSE",
            "parameter14": "4",
            "parameter15": "5",
            "parameter16": "P11111124112047702"
        }
    }
}
```

### 2.2 支持的API调用动作

#### 查询类接口（GET）

**1. 查询全部算法名称**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_algorithms",
        "project_id": "项目ID"
    }
}
```
*说明：返回所有项目的算法列表，前端可根据需要进行选择和过滤*

**2. 查询全部数据集名称**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_datasets",
        "project_id": "项目ID"
    }
}
```
*说明：返回所有项目的数据集列表，前端可根据需要进行选择和过滤*

```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_iot",
        "project_id": "项目ID"
    }
}
```

```json
{
    "type": "api_call",
    "data": {
        "action": "get_algorithm_log",
        "project_id": "项目ID",
        "parameters": {
            "algorithm_name": "算法名称"
        }
    }
}
```

```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_services",
        "project_id": "项目ID"
    }
}
```

```json
{
    "type": "api_call",
    "data": {
        "action": "get_service_prediction",
        "project_id": "项目ID",
        "parameters": {
            "service_name": "服务名称"
        }
    }
}
```

#### 操作类接口（POST）
```json
{
    "type": "api_call",
    "data": {
        "action": "start_monitoring_service",
        "project_id": "项目ID",
        "parameters": {
            "db_ip": "***********",
            "db_name": "data_1815320358166839298",
            "list_name": "P11111124112047702",
            "implement_name": "TEST"
        }
    }
}
```

### 2.3 智能体响应格式

#### 成功响应
```json
{
    "type": "api_response",
    "data": {
        "action": "train_algorithm",
        "status": "success",
        "result": {
            "message": "启动成功",
            "algoname": "测试算法1",
            "dataset": "水泵数据集0612"
        },
        "timestamp": "2025-01-31T10:30:00Z"
    }
}
```

#### 错误响应
```json
{
    "type": "api_response",
    "data": {
        "action": "train_algorithm",
        "status": "error",
        "error": {
            "code": "ALGORITHM_PLATFORM_ERROR",
            "message": "算法平台API调用失败: 连接超时",
            "details": "无法连接到 http://***********:8081"
        },
        "timestamp": "2025-01-31T10:30:00Z"
    }
}
```

---

## 3. 智能体实现方案

### 3.1 新增消息处理器
在 `api/websocket_routes.py` 中添加：

```python
async def handle_api_call_message(self, session_id: str, message_data: Dict[str, Any]):
    """处理前端直传参数的API调用消息"""
    try:
        action = message_data.get("action")
        project_id = message_data.get("project_id")
        parameters = message_data.get("parameters", {})
        
        if not project_id:
            await self.send_error_response(session_id, "项目ID不能为空")
            return
            
        # 根据action调用对应的API
        result = await self._execute_api_call(action, project_id, parameters)
        
        # 发送成功响应
        await self.send_api_response(session_id, action, "success", result)
        
    except Exception as e:
        # 发送错误响应
        await self.send_api_response(session_id, action, "error", {
            "code": "API_CALL_ERROR",
            "message": str(e)
        })
```

### 3.2 API调用执行器
```python
async def _execute_api_call(self, action: str, project_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
    """执行具体的API调用"""
    platform_service = AlgorithmPlatformService()
    
    if action == "train_algorithm":
        request = AlgorithmTrainingRequest(
            parameter1=parameters.get("parameter1"),
            parameter2=parameters.get("parameter2"),
            # ... 其他参数
            project_number=project_id
        )
        response = await platform_service.train_algorithm(request)
        return response.dict()
        
    elif action == "get_all_algorithms":
        response = await platform_service.get_all_algorithms()
        return response.dict()
        
    # ... 其他API调用
    
    else:
        raise ValueError(f"不支持的API调用: {action}")
```

---

## 4. 前端集成指南

### 4.1 前端UI设计
- **参数收集表单**: 16个训练参数的输入框
- **项目选择器**: 项目ID选择下拉框
- **API调用按钮**: 直接调用对应接口
- **结果显示区**: 显示API调用结果或错误信息

### 4.2 前端调用示例
```javascript
// 训练算法
function trainAlgorithm() {
    const message = {
        type: "api_call",
        data: {
            action: "train_algorithm",
            project_id: document.getElementById("projectId").value,
            parameters: {
                parameter1: document.getElementById("param1").value,
                parameter2: document.getElementById("param2").value,
                // ... 其他参数
            }
        }
    };
    
    websocket.send(JSON.stringify(message));
}

// 查询算法列表
function getAlgorithms() {
    const message = {
        type: "api_call",
        data: {
            action: "get_all_algorithms",
            project_id: document.getElementById("projectId").value
        }
    };
    
    websocket.send(JSON.stringify(message));
}
```

---

## 5. 实现优先级

### 5.1 第一阶段（立即实现）
1. **WebSocket消息处理器** - 支持api_call消息类型
2. **API调用执行器** - 实现8个接口的直接调用
3. **错误处理机制** - 统一的错误响应格式

### 5.2 第二阶段（后续优化）
1. **参数验证器** - 验证前端传入参数的完整性
2. **异步任务跟踪** - 跟踪训练和监控服务状态
3. **调用日志记录** - 记录所有API调用历史

---

## 6. 兼容性说明

### 6.1 保持向后兼容
- 原有的自然语言交互模式继续支持
- 智能体仍可以从对话中提取参数
- 两种模式可以在同一会话中混合使用

### 6.2 模式识别
- `type: "chat"` - 自然语言交互模式
- `type: "api_call"` - 前端直传参数模式

---

## 7. 测试方案

### 7.1 单接口测试
- 测试每个API接口的直接调用
- 验证参数传递的正确性
- 测试错误处理机制

### 7.2 集成测试
- 前端UI与WebSocket的完整交互
- 异步接口的状态跟踪
- 多项目数据隔离测试

---

## 8. 总结

通过这种设计，实现了：
- **灵活性**: 支持两种交互模式
- **简洁性**: 前端可直接调用API，无需复杂的参数提取
- **可靠性**: 统一的错误处理和状态管理
- **扩展性**: 易于添加新的API接口支持

这样的设计既满足了前端直传参数的需求，又保持了智能体的对话能力。
