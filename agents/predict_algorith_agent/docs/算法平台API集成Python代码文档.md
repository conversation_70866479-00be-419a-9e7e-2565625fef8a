# 算法平台API集成Python代码文档

## 概述
本文档详细说明如何将8个算法平台API接口集成到现有的智能体工程中，包括具体的Python代码实现和类结构设计。

---

## 1. 整体架构设计

### 1.1 集成策略
- **新增服务层**: 创建 `AlgorithmPlatformService` 作为API调用的统一入口
- **扩展现有智能体**: 在 `PredictiveAlgorithmAssistant` 中集成算法平台功能
- **增强历史算法服务**: 升级 `HistoryAlgorithmService` 支持真实API调用
- **新增API路由**: 在现有路由中添加算法平台相关接口

### 1.2 文件结构
```
agents/predict_algorith_agent/
├── services/
│   ├── algorithm_platform_service.py      # 新增：算法平台API服务
│   └── history_algorithm_service.py       # 修改：集成真实API调用
├── models/
│   └── algorithm_platform_models.py       # 新增：算法平台数据模型
├── core/
│   ├── predictive_agent.py               # 修改：集成算法平台功能
│   └── history_algorithm_agent.py        # 修改：支持真实算法列表
├── api/
│   ├── conversation_routes.py            # 修改：添加算法平台路由
│   └── websocket_routes.py              # 修改：支持算法平台消息
└── utils/
    └── config_manager.py                 # 修改：添加算法平台配置
```

---

## 2. 核心服务类实现

### 2.1 AlgorithmPlatformService (新增)
**文件位置**: `services/algorithm_platform_service.py`

#### 主要方法映射：
| API接口 | 方法名 | 功能描述 |
|---------|--------|----------|
| 接口1 | `get_all_algorithms()` | 查询全部算法名称 |
| 接口2 | `get_all_datasets()` | 查询全部数据集名称 |
| 接口3 | `train_algorithm()` | 训练新算法（异步） |
| 接口4 | `get_all_iot(project_id)` | 查询全部IOT名称 |
| 接口5 | `start_monitoring_service()` | 启动监控分析服务（异步） |
| 接口6 | `get_algorithm_log(algorithm_name, project_id)` | 查询算法训练信息 |
| 接口7 | `get_all_services(project_id)` | 查询全部服务名称 |
| 接口8 | `get_service_prediction(service_name, project_id)` | 查询服务预测结果 |

#### 核心代码结构：
```python
class AlgorithmPlatformService:
    def __init__(self):
        # 从配置文件读取API配置
        from agents.config import ALGORITHM_PLATFORM_BASE_URL
        self.base_url = ALGORITHM_PLATFORM_BASE_URL
        self._session: Optional[aiohttp.ClientSession] = None

    # 查询类接口（同步调用）
    async def get_all_algorithms(self) -> GetAllAlgorithmResponse
    async def get_all_datasets(self) -> GetAllDatasetResponse
    async def get_all_iot(self, project_id: str) -> GetAllIOTResponse
    async def get_algorithm_log(self, algorithm_name: str, project_id: str) -> GetAlgorithmLogResponse
    async def get_all_services(self, project_id: str) -> GetAllImplementResponse
    async def get_service_prediction(self, service_name: str, project_id: str) -> GetImplementLogResponse

    # 操作类接口（异步调用）
    async def train_algorithm(self, request: AlgorithmTrainingRequest) -> AlgorithmTrainingResponse
    async def start_monitoring_service(self, request: MonitoringServiceRequest) -> MonitoringServiceResponse
```

#### 错误处理策略：
- **禁用Mock数据**：API调用失败时直接抛出异常
- **WebSocket错误传递**：异常信息通过WebSocket发送给前端
- **不使用降级方案**：确保前端能及时感知API服务状态

---

## 3. 智能体类集成

### 3.1 PredictiveAlgorithmAssistant (修改)
**文件位置**: `core/predictive_agent.py`

#### 新增属性：
```python
class PredictiveAlgorithmAssistant:
    def __init__(self):
        # 现有属性...

        # 新增：算法平台服务
        self.algorithm_platform_service = AlgorithmPlatformService()

        # 注意：不设置默认项目ID，项目ID由前端WebSocket传入
```

#### 新增方法：
```python
# 1. 算法管理相关方法
async def get_available_algorithms(self, project_id: str) -> Dict[str, Any]:
    """获取可用算法列表 - 项目ID必传"""

async def get_available_datasets(self, project_id: str) -> Dict[str, Any]:
    """获取可用数据集列表 - 项目ID必传"""

# 2. 训练管理相关方法
async def start_algorithm_training(self, training_params: Dict[str, Any], project_id: str) -> Dict[str, Any]:
    """启动算法训练 - 项目ID必传"""

async def check_training_status(self, algorithm_name: str, project_id: str) -> Dict[str, Any]:
    """检查训练状态 - 项目ID必传"""

# 3. 监控服务相关方法
async def start_monitoring_service(self, monitoring_params: Dict[str, Any], project_id: str) -> Dict[str, Any]:
    """启动监控分析服务 - 项目ID必传"""

async def get_monitoring_services(self, project_id: str) -> Dict[str, Any]:
    """获取监控服务列表 - 项目ID必传"""

async def get_prediction_results(self, service_name: str, project_id: str) -> Dict[str, Any]:
    """获取预测结果 - 项目ID必传"""

# 4. IOT设备管理方法
async def get_iot_devices(self, project_id: str) -> Dict[str, Any]:
    """获取IOT设备列表 - 项目ID必传"""
```

#### 修改现有方法：
```python
async def handle_algorithm_training_async(self, user_input: str, state: PredictiveAgentState) -> Tuple[Dict[str, Any], Dict[str, Any], str]:
    """处理算法训练请求 - 集成真实API调用"""
    
    # 1. 参数提取（现有逻辑）
    extracted_params = self.extract_parameters(user_input)
    
    # 2. 检查参数完整性
    if self._is_parameters_complete(extracted_params):
        # 3. 调用真实API启动训练
        training_request = self._build_training_request(extracted_params, state)
        training_result = await self.algorithm_platform_service.train_algorithm(training_request)
        
        # 4. 更新状态并返回结果
        state.current_task_id = training_result.algoname
        return self._format_training_response(training_result), state.dict(), "training_started"
    else:
        # 参数不完整，继续收集
        return await self._collect_missing_parameters(extracted_params, state)
```

### 3.2 HistoryAlgorithmService (修改)
**文件位置**: `services/history_algorithm_service.py`

#### 修改现有方法：
```python
async def get_history_algorithms(self, project_id: str) -> HistoryAlgorithmListResponse:
    """获取历史算法列表 - 使用真实API，项目ID必传"""
    try:
        # 调用算法平台API获取真实算法列表
        platform_service = AlgorithmPlatformService()
        algorithms_response = await platform_service.get_all_algorithms()

        # 转换为HistoryAlgorithm格式
        history_algorithms = self._convert_to_history_algorithms(algorithms_response.algorithm_name, project_id)

        return HistoryAlgorithmListResponse(
            algorithms=history_algorithms,
            total_count=len(history_algorithms),
            fetch_time_ms=50.0
        )
    except Exception as e:
        logger.error(f"算法平台API调用失败: {e}")
        # 直接抛出异常，不使用Mock数据
        raise AlgorithmPlatformError(f"无法获取算法列表: {e}")

def _convert_to_history_algorithms(self, algorithm_data: List[List[str]], current_project_id: str) -> List[HistoryAlgorithm]:
    """将算法平台数据转换为历史算法格式"""
    history_algorithms = []
    for algo_info in algorithm_data:
        algorithm_name, algo_project_id = algo_info
        # 只返回当前项目的算法
        if algo_project_id == current_project_id:
            history_algorithm = HistoryAlgorithm(
                algorithm_id=f"platform_{hash(algorithm_name)}",
                algorithm_name=algorithm_name,
                algorithm_type="LSTM",  # 默认类型，可通过其他API获取详细信息
                description=f"来自算法平台的算法：{algorithm_name}",
                data_type="时序数据",
                use_case="预测分析",
                parameters={},  # 可通过get_algorithm_log获取详细参数
                performance_metrics={},
                created_by="算法平台",
                created_at=datetime.now(),
                last_used=datetime.now(),
                usage_count=0,
                tags=["算法平台", "LSTM"],
                project_id=algo_project_id
            )
            history_algorithms.append(history_algorithm)
    return history_algorithms
```

---

## 4. API路由集成

### 4.1 conversation_routes.py (修改)
**文件位置**: `api/conversation_routes.py`

#### 新增路由：
```python
# 算法平台相关路由
@router.get("/algorithm-platform/algorithms", response_model=Response[Dict[str, Any]])
async def get_platform_algorithms(project_id: str = Query(..., description="项目ID（必传）")):
    """获取算法平台的算法列表"""

@router.get("/algorithm-platform/datasets", response_model=Response[Dict[str, Any]])
async def get_platform_datasets(project_id: str = Query(..., description="项目ID（必传）")):
    """获取算法平台的数据集列表"""

@router.post("/algorithm-platform/train", response_model=Response[Dict[str, Any]])
async def start_platform_training(request: AlgorithmTrainingRequest):
    """启动算法平台训练（项目ID在request中）"""

@router.get("/algorithm-platform/training-status", response_model=Response[Dict[str, Any]])
async def get_training_status(
    algorithm_name: str = Query(..., description="算法名称"),
    project_id: str = Query(..., description="项目ID（必传）")
):
    """查询训练状态"""

@router.get("/algorithm-platform/services", response_model=Response[Dict[str, Any]])
async def get_platform_services(project_id: str = Query(..., description="项目ID（必传）")):
    """获取监控服务列表"""

@router.get("/algorithm-platform/predictions", response_model=Response[Dict[str, Any]])
async def get_platform_predictions(
    service_name: str = Query(..., description="服务名称"),
    project_id: str = Query(..., description="项目ID（必传）")
):
    """获取预测结果"""
```

### 4.2 websocket_routes.py (修改)
**文件位置**: `api/websocket_routes.py`

#### 新增消息类型处理：
```python
# 在ConnectionManager.handle_message中新增
async def handle_message(self, websocket: WebSocket, session_id: str, message_data: Dict[str, Any]):
    """处理接收到的消息"""
    message = WebSocketMessage(**message_data)
    
    if message.type == "algorithm_platform_query":
        await self._handle_algorithm_platform_query(session_id, message)
    elif message.type == "training_status_check":
        await self._handle_training_status_check(session_id, message)
    elif message.type == "monitoring_service_query":
        await self._handle_monitoring_service_query(session_id, message)
    # ... 现有消息类型处理

async def _handle_algorithm_platform_query(self, session_id: str, message: WebSocketMessage):
    """处理算法平台查询消息"""
    
async def _handle_training_status_check(self, session_id: str, message: WebSocketMessage):
    """处理训练状态检查消息"""
    
async def _handle_monitoring_service_query(self, session_id: str, message: WebSocketMessage):
    """处理监控服务查询消息"""
```

---

## 5. 配置管理

### 5.1 agents/config.py (修改)
**文件位置**: `agents/config.py`

#### 新增算法平台API配置：
```python
# 算法平台API配置 - 替换原有的假API配置
ALGORITHM_PLATFORM_BASE_URL = "http://10.0.42.241:8081"

# 算法平台8个接口的完整URL配置
ALGORITHM_PLATFORM_ENDPOINTS = {
    "get_all_algorithm": "/get_all_algorithm",
    "get_all_dataset": "/get_all_dataset",
    "algorithm_parameter": "/algorithm_parameter",
    "get_all_iot": "/get_all_iot",
    "implement_parameter": "/implement_parameter",
    "get_algorithm_log": "/get_algorithm_log",
    "get_all_implement": "/get_all_implement",
    "get_implement_log": "/get_implement_log"
}

# 算法平台请求配置
ALGORITHM_PLATFORM_TIMEOUT = 30
ALGORITHM_PLATFORM_RETRY_TIMES = 3
ALGORITHM_PLATFORM_RETRY_DELAY = 1.0
```

#### 移除的配置：
```python
# 删除这些假的API配置
# ALGORITHM_API_BASE = "http://thirdparty-algorithm/api"  # 删除
# TRAING_API_BASE = "http://thirdparty-training/api"     # 删除
```

### 5.2 utils/config_manager.py (修改)
**文件位置**: `utils/config_manager.py`

#### 新增配置类：
```python
class AlgorithmPlatformConfig(BaseModel):
    """算法平台配置"""
    base_url: str = Field(description="算法平台基础URL")
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    retry_times: int = Field(default=3, description="重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")
    enable_cache: bool = Field(default=True, description="启用缓存")
    cache_ttl: int = Field(default=1800, description="缓存生存时间（秒）")
    # 注意：移除默认项目ID，项目ID由前端传入

class AgentConfig(BaseModel):
    """智能体配置 - 扩展现有配置"""
    # 现有配置...

    # 新增算法平台配置
    algorithm_platform: AlgorithmPlatformConfig = Field(default_factory=AlgorithmPlatformConfig)
```

---

## 6. 数据模型扩展

### 6.1 algorithm_platform_models.py (已创建)
**文件位置**: `models/algorithm_platform_models.py`

该文件已包含所有必要的数据模型，无需修改。

### 6.2 现有模型扩展
**文件位置**: `models/predictive_models.py`

#### 扩展PredictiveAgentState：
```python
class PredictiveAgentState(BaseModel):
    # 现有字段...

    # 新增算法平台相关字段
    current_project_id: Optional[str] = Field(default=None, description="当前项目ID（由前端传入）")
    platform_algorithm_list: Optional[List[Dict[str, Any]]] = Field(default=None, description="算法平台算法列表")
    platform_dataset_list: Optional[List[Dict[str, Any]]] = Field(default=None, description="算法平台数据集列表")
    training_task_id: Optional[str] = Field(default=None, description="训练任务ID")
    monitoring_service_name: Optional[str] = Field(default=None, description="监控服务名称")
```

---

## 7. 实现优先级和集成步骤

### 7.1 第一阶段（高优先级）
1. **创建AlgorithmPlatformService** - 实现查询类接口（1,2,4,6,7,8）
2. **修改HistoryAlgorithmService** - 集成真实API调用
3. **扩展PredictiveAgentState** - 添加算法平台相关状态
4. **添加基础配置** - 在config_manager中添加算法平台配置

### 7.2 第二阶段（中优先级）
1. **集成到PredictiveAlgorithmAssistant** - 添加算法平台相关方法
2. **修改训练流程** - 使用真实API启动训练（接口3）
3. **添加监控服务** - 实现监控服务启动和查询（接口5）
4. **扩展API路由** - 添加算法平台相关路由

### 7.3 第三阶段（低优先级）
1. **WebSocket集成** - 支持算法平台相关消息类型
2. **缓存机制** - 实现算法列表和数据集列表缓存
3. **错误处理优化** - 完善异常处理和重试机制
4. **性能监控** - 添加API调用性能监控

---

## 8. 测试策略

### 8.1 单元测试
- **AlgorithmPlatformService测试** - 测试所有8个API接口
- **数据模型测试** - 验证请求/响应数据格式
- **错误处理测试** - 测试网络异常、超时等情况

### 8.2 集成测试
- **智能体集成测试** - 测试PredictiveAlgorithmAssistant的新功能
- **API路由测试** - 测试新增的HTTP接口
- **WebSocket测试** - 测试实时消息处理

### 8.3 端到端测试
- **完整训练流程测试** - 从参数收集到训练启动的完整流程
- **监控服务测试** - 监控服务启动和结果查询
- **多项目支持测试** - 测试不同项目ID的数据隔离

---

## 9. 部署注意事项

### 9.1 环境配置
- 确保算法平台服务器 `http://10.0.42.241:8081` 可访问
- 配置适当的网络超时和重试参数
- 设置合理的缓存策略

### 9.2 监控和日志
- 添加API调用监控和告警
- 记录详细的请求/响应日志
- 监控异步任务的执行状态

### 9.3 容错机制
- 实现API不可用时的降级策略
- 保持Mock数据作为备用方案
- 添加断路器模式防止级联故障

---

## 10. 重要调整说明

### 10.1 核心调整要求
1. **禁用Mock数据**：API调用失败时直接抛出异常，通过WebSocket发送错误信息给前端
2. **移除默认项目ID**：所有硬编码的项目ID "280" 都要移除
3. **参数化项目ID**：项目ID改为从前端WebSocket传入的参数
4. **配置文件集中管理**：在 `agents/config.py` 中添加8个API接口的配置
5. **移除硬编码**：所有涉及项目ID=280的地方改为传参方式

### 10.2 WebSocket消息格式调整

#### 方式1：自然语言交互（保留原有功能）
```json
{
    "type": "chat",
    "data": {
        "message": "我想用LSTM做预测",
        "project_id": "实际的项目ID"
    }
}
```

#### 方式2：前端直传参数调用API（新增功能）
```json
{
    "type": "api_call",
    "data": {
        "action": "train_algorithm",
        "project_id": "实际的项目ID",
        "parameters": {
            "parameter1": "测试算法1",
            "parameter2": "风险预警",
            "parameter3": "水泵数据集0612",
            "parameter4": "5",
            "parameter5": "64",
            "parameter6": "3",
            "parameter7": "0.2",
            "parameter8": "YES",
            "parameter9": "10",
            "parameter10": "0.01",
            "parameter11": "1",
            "parameter12": "Adam",
            "parameter13": "MSE",
            "parameter14": "4",
            "parameter15": "5",
            "parameter16": "P11111124112047702"
        }
    }
}
```

#### 支持的API调用动作：
- `train_algorithm` - 训练新算法
- `start_monitoring_service` - 启动监控分析服务
- `get_all_algorithms` - 查询全部算法名称
- `get_all_datasets` - 查询全部数据集名称
- `get_all_iot` - 查询全部IOT名称
- `get_algorithm_log` - 查询算法训练信息
- `get_all_services` - 查询全部服务名称
- `get_service_prediction` - 查询服务预测结果

### 10.3 错误处理调整
```python
# 错误处理示例
try:
    result = await self.algorithm_platform_service.get_all_algorithms()
    return result
except AlgorithmPlatformError as e:
    # 通过WebSocket发送错误信息给前端
    error_message = {
        "type": "error",
        "data": {
            "message": f"算法平台API调用失败: {str(e)}",
            "error_code": "ALGORITHM_PLATFORM_ERROR"
        }
    }
    await self.send_websocket_message(session_id, error_message)
    raise
```

### 10.4 配置文件更新
需要更新 `agents/config.py` 文件，添加算法平台配置，移除假的API配置。

---

## 11. 总结

本文档详细描述了将8个算法平台API接口集成到现有智能体工程的完整方案。通过分阶段实施，可以逐步实现真实API调用的集成，同时确保：

- **无默认项目ID**：所有项目ID都由前端传入
- **无Mock数据降级**：API失败时直接抛出异常
- **配置集中管理**：所有API配置在 `agents/config.py` 中统一管理
- **参数化设计**：所有方法都要求传入必要的项目ID参数

这样的设计确保了系统的严格性和数据的准确性，避免了因默认值或Mock数据导致的潜在问题。
