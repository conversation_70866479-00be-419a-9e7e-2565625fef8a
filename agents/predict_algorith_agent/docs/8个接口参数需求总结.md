# 8个算法平台API接口参数需求总结

## 概述
本文档详细总结了8个算法平台API接口在"前端UI收集参数 → 直接传给智能体 → 智能体调用算法平台API"流程中的参数需求。

---

## 接口参数需求详细分析

### 1. 查询全部算法名称 (get_all_algorithms)

**前端需要收集的参数：**
- `project_id` (不必传) - 项目ID（仅用于消息格式统一，不用于过滤）

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_algorithms",
        "project_id": ""
    }
}
```

**智能体处理：**
- 调用算法平台API获取所有算法
- 直接返回完整的algorithm_name列表（包含所有项目的算法）
- 前端可根据需要进行选择和过滤

---

### 2. 查询全部数据集名称 (get_all_datasets)

**前端需要收集的参数：**
- `project_id` (不必传) - 项目ID（仅用于消息格式统一，不用于过滤）

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_datasets",
        "project_id": ""
    }
}
```

**智能体处理：**
- 调用算法平台API获取所有数据集
- 直接返回完整的dataset_name列表（包含所有项目的数据集）
- 前端可根据需要进行选择和过滤

---

### 3. 训练新算法 (train_algorithm)

**前端需要收集的参数：**
- `project_id` (必传) - 项目ID
- `parameter1` - 算法名称
- `parameter2` - 算法类型
- `parameter3` - 数据集名称
- `parameter4` - 输入特征维度
- `parameter5` - LSTM隐藏层大小
- `parameter6` - LSTM层数
- `parameter7` - Dropout比例
- `parameter8` - 双向LSTM (YES/NO)
- `parameter9` - 输出维度
- `parameter10` - 学习率
- `parameter11` - 样本划分比例
- `parameter12` - 优化器 (Adam/SGD/RMSprop)
- `parameter13` - 损失函数 (MSE/MAE/CrossEntropy)
- `parameter14` - 标签列
- `parameter15` - 数据列
- `parameter16` - 字段

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "train_algorithm",
        "project_id": "285",
        "parameters": {
            "parameter1": "测试算法1",
            "parameter2": "风险预警",
            "parameter3": "水泵数据集0723",
            "parameter4": "5",
            "parameter5": "64",
            "parameter6": "3",
            "parameter7": "0.2",
            "parameter8": "YES",
            "parameter9": "10",
            "parameter10": "0.01",
            "parameter11": "1",
            "parameter12": "Adam",
            "parameter13": "MSE",
            "parameter14": "4",
            "parameter15": "5",
            "parameter16": "P11111124112047702"
        }
    }
}
```

**智能体处理：**
- 构建AlgorithmTrainingRequest对象
- 调用算法平台API启动训练（异步）
- 返回训练启动结果

---

### 4. 查询全部IOT名称 (get_all_iot)

**前端需要收集的参数：**
- `project_id` (必传) - 项目ID

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_iot",
        "project_id": "285"
    }
}
```

**智能体处理：**
- 直接调用算法平台API，传入project_id
- 返回该项目下的IOT信息列表

---

### 5. 启动监控分析服务 (start_monitoring_service)

**前端需要收集的参数：**
- `project_id` (必传) - 项目ID
- `db_ip` - 数据库地址
- `db_name` - 数据库表名
- `list_name` - 关键数据字段
- `implement_name` - 服务名称

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "start_monitoring_service",
        "project_id": "285",
        "parameters": {
            "db_ip": "***********",
            "db_name": "data_1815320358166839298",
            "list_name": "P11111124112047702",
            "implement_name": "TEST_MONITOR"
        }
    }
}
```

**智能体处理：**
- 构建MonitoringServiceRequest对象
- 调用算法平台API启动监控服务（异步）
- 返回服务启动结果

---

### 6. 查询算法训练信息 (get_algorithm_log)

**前端需要收集的参数：**
- `project_id` (必传) - 项目ID
- `algorithm_name` (必传) - 算法名称

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_algorithm_log",
        "project_id": "285",
        "parameters": {
            "algorithm_name": "水泵电流预警算法0723"
        }
    }
}
```

**智能体处理：**
- 验证algorithm_name参数不为空
- 调用算法平台API查询训练信息
- 返回算法训练日志和参数信息

---

### 7. 查询全部服务名称 (get_all_services)

**前端需要收集的参数：**
- `project_id` (必传) - 项目ID

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_all_services",
        "project_id": "285"
    }
}
```

**智能体处理：**
- 直接调用算法平台API，传入project_id
- 返回该项目下的所有服务信息

---

### 8. 查询服务预测结果 (get_service_prediction)

**前端需要收集的参数：**
- `project_id` (必传) - 项目ID
- `service_name` (必传) - 服务名称

**WebSocket消息格式：**
```json
{
    "type": "api_call",
    "data": {
        "action": "get_service_prediction",
        "project_id": "285",
        "parameters": {
            "service_name": "水泵电流预警算法0612"
        }
    }
}
```

**智能体处理：**
- 验证service_name参数不为空
- 调用算法平台API查询预测结果
- 返回服务的实时分析结论

---

## 总结

### 参数收集模式分类：

1. **返回全量数据** (2个接口)：
   - get_all_algorithms（返回所有项目的算法）
   - get_all_datasets（返回所有项目的数据集）

2. **仅需项目ID过滤** (2个接口)：
   - get_all_iot
   - get_all_services

3. **需要项目ID + 1个额外参数** (2个接口)：
   - get_algorithm_log (+ algorithm_name)
   - get_service_prediction (+ service_name)

4. **需要项目ID + 多个参数** (2个接口)：
   - train_algorithm (+ 16个parameter参数)
   - start_monitoring_service (+ 4个监控参数)

### 前端UI设计建议：

1. **项目选择器**：所有接口都需要项目ID，应在页面顶部提供项目选择功能
2. **分类界面**：按功能分组设计UI界面
   - 查询类：算法、数据集、IOT、服务列表
   - 训练类：16参数表单
   - 监控类：4参数表单
   - 详情类：算法训练信息、服务预测结果
3. **参数验证**：前端应验证必填参数的完整性
4. **结果展示**：统一的成功/错误响应处理

### 智能体角色：

- **参数转换器**：将前端参数转换为算法平台API格式
- **项目过滤器**：对返回结果按项目ID进行过滤
- **错误处理器**：统一处理API调用异常
- **响应格式化器**：返回统一格式的成功/错误响应

这样的设计完全符合"前端UI收集参数 → 直接传给智能体 → 智能体调用算法平台API"的流程需求。
