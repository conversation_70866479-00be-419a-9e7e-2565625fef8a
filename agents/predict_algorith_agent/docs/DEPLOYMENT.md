# 算法智能体部署指南

## 📋 部署环境兼容性

本项目已经过重构优化，支持在多种环境下部署：

- ✅ **Windows开发环境**
- ✅ **Linux服务器**
- ✅ **Docker容器**
- ✅ **云服务器（阿里云、腾讯云等）**

## 🚀 快速部署

### 1. Linux服务器部署

```bash
# 1. 上传项目文件到服务器
scp -r twinbuilder-agents user@server:/path/to/deployment/

# 2. 进入项目目录
cd /path/to/deployment/twinbuilder-agents

# 3. 安装依赖
pip install -r requirements.txt

# 4. 设置环境变量（可选，系统会自动检测）
export PROJECT_ROOT=/path/to/deployment/twinbuilder-agents

# 5. 启动服务
python agents/predict_algorith_agent/predict_main.py
```

### 2. Docker部署

```dockerfile
# Dockerfile示例
FROM python:3.10-slim

WORKDIR /app
COPY . /app

# 设置项目根目录环境变量
ENV PROJECT_ROOT=/app

RUN pip install -r requirements.txt

EXPOSE 8008

CMD ["python", "agents/predict_algorith_agent/predict_main.py"]
```

```bash
# 构建和运行
docker build -t algorithm-agent .
docker run -p 8008:8008 -e PROJECT_ROOT=/app algorithm-agent
```

### 3. 云服务器部署

```bash
# 1. 克隆代码
git clone <repository-url>
cd twinbuilder-agents

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux
# 或 venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动服务
python agents/predict_algorith_agent/predict_main.py
```

## 🔧 环境配置

### 自动路径检测

项目包含智能路径检测功能，会自动尝试以下路径：

1. **开发环境路径**: `agents/predict_algorith_agent/utils/` → 项目根目录
2. **部署环境路径1**: 相对于agents目录
3. **部署环境路径2**: 相对于当前目录
4. **环境变量路径**: `$PROJECT_ROOT`
5. **当前工作目录**: `pwd`

### 手动设置环境变量

如果自动检测失败，可以手动设置：

```bash
# Linux/Mac
export PROJECT_ROOT=/path/to/twinbuilder-agents

# Windows
set PROJECT_ROOT=C:\path\to\twinbuilder-agents

# Docker
ENV PROJECT_ROOT=/app
```

## 📁 部署目录结构

确保部署后的目录结构如下：

```
deployment-directory/
├── core/                    # 项目核心模块
│   └── response.py
├── agents/
│   └── predict_algorith_agent/
│       ├── core/
│       ├── models/
│       ├── services/
│       ├── api/
│       ├── database/
│       ├── network/
│       ├── utils/
│       │   └── deploy_config.py
│       ├── static/
│       ├── docs/
│       │   └── DEPLOYMENT.md
│       └── predict_main.py
└── requirements.txt
```

## 🔍 部署验证

### 1. 检查环境设置

```python
# 运行环境检查
python agents/predict_algorith_agent/utils/deploy_config.py
```

输出示例：
```
✅ 项目根目录设置为: /path/to/twinbuilder-agents
==================================================
🔧 部署环境信息
==================================================
操作系统: Linux/Unix
Docker环境: 否
项目根目录: /path/to/twinbuilder-agents
当前工作目录: /path/to/twinbuilder-agents
Python路径数量: 15
==================================================
```

### 2. 测试服务启动

```bash
# 启动服务
python agents/predict_algorith_agent/predict_main.py

# 检查服务状态
curl http://localhost:8008/health
```

### 3. 测试前端访问

访问: `http://your-server:8008/static/html/chat_interface_v2.html`

## ⚠️ 常见问题

### 1. 导入错误

**问题**: `ModuleNotFoundError: No module named 'core.response'`

**解决方案**:
```bash
# 设置环境变量
export PROJECT_ROOT=/path/to/project/root

# 或者检查目录结构是否正确
ls -la core/response.py
```

### 2. 路径问题

**问题**: 找不到项目文件

**解决方案**:
```bash
# 确保在正确的目录下运行
cd /path/to/twinbuilder-agents
python agents/predict_algorith_agent/predict_main.py
```

### 3. 权限问题

**问题**: Linux服务器权限不足

**解决方案**:
```bash
# 设置正确的文件权限
chmod +x agents/predict_algorith_agent/predict_main.py
chown -R user:group /path/to/deployment/
```

## 🔒 安全配置

### 1. 生产环境配置

```bash
# 设置生产环境变量
export ENVIRONMENT=production
export DEBUG=false

# 配置防火墙
ufw allow 8008
```

### 2. 反向代理配置

```nginx
# Nginx配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8008;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📞 技术支持

如果遇到部署问题，请检查：

1. ✅ Python版本 (3.10+)
2. ✅ 依赖包安装完整
3. ✅ 目录结构正确
4. ✅ 环境变量设置
5. ✅ 网络端口开放

**联系方式**: 请在项目仓库提交Issue
