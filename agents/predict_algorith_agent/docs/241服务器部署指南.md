# 241服务器部署指南 (更新版)

## 📋 部署概述

本指南用于将算法生成智能体WebSocket服务部署到***********服务器。

### 最新更新内容 (2025-08-26)
- ✅ 数据库配置优化（使用***********服务器配置）
- ✅ 欢迎消息配置开关（默认关闭，由前端提供引导词）
- ✅ 环境变量配置支持
- ✅ 服务稳定性优化
- ✅ 日志管理改进
- ✅ 部署脚本自动化

## 🚀 快速部署

### 方法1: 自动化部署脚本（推荐）

```bash
# 1. 进入脚本目录
cd agents/predict_algorith_agent/scripts

# 2. 给脚本执行权限
chmod +x deploy_to_241_server.sh

# 3. 执行部署（会提示确认）
./deploy_to_241_server.sh
```

**脚本会自动完成以下操作：**
- ✅ 检查SSH连接
- ✅ 备份现有服务
- ✅ 上传最新代码
- ✅ 安装Python依赖
- ✅ 配置环境变量（包括欢迎消息开关）
- ✅ 创建systemd服务
- ✅ 启动服务并验证

### 方法2: 手动部署步骤

#### 步骤1: 准备工作
```bash
# 检查SSH连接
ssh root@*********** "echo 'SSH连接正常'"

# 备份现有服务
ssh root@*********** "systemctl stop predict-algorithm-agent"
ssh root@*********** "cp -r /opt/twinbuilder-agents /opt/backup/$(date +%Y%m%d_%H%M%S)/"
```

#### 步骤2: 上传代码
```bash
# 创建代码压缩包
cd /path/to/your/twinbuilder-agents
tar -czf agents_updated.tar.gz agents/ --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' --exclude='.venv' --exclude='logs/*'

# 上传到服务器
scp agents_updated.tar.gz root@***********:/tmp/

# 在服务器上解压
ssh root@*********** << 'EOF'
cd /opt/twinbuilder-agents
tar -xzf /tmp/agents_updated.tar.gz
rm /tmp/agents_updated.tar.gz
chown -R root:root /opt/twinbuilder-agents
EOF
```

#### 步骤3: 配置环境
```bash
ssh root@*********** << 'EOF'
cd /opt/twinbuilder-agents/agents/predict_algorith_agent

# 确保配置文件正确
cat > .env.production << 'ENVEOF'
# 生产环境配置
PRODUCTION_MODE=true
LOG_LEVEL=WARNING
ENABLE_FILE_LOGGING=true
LOG_FILE_PATH=logs/agent_production.log

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8008

# 数据库配置
DB_HOST=***********
DB_PORT=3306
DB_NAME=indusaio_agent
DB_USER=root
DB_PASSWORD=Spsm2021+

# Redis配置
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=Sygy@2025

# WebSocket欢迎消息配置（默认关闭，由前端提供引导词）
ENABLE_WELCOME_MESSAGE=false

# 大模型配置
LLM_PROVIDER=qwen
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2000
LLM_TIMEOUT=30
ENVEOF

# 创建日志目录
mkdir -p logs
chmod 600 .env.production
EOF
```

#### 步骤4: 安装依赖
```bash
ssh root@*********** << 'EOF'
cd /opt/twinbuilder-agents

# 检查Python版本
python3 --version

# 安装核心依赖
pip3 install fastapi uvicorn websockets pymysql python-dotenv pydantic pydantic-ai redis

# 如果有requirements.txt文件，也可以使用
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
fi

echo "依赖安装完成"
EOF
```

#### 步骤5: 配置systemd服务
```bash
ssh root@*********** << 'EOF'
cat > /etc/systemd/system/predict-algorithm-agent.service << 'SERVICEEOF'
[Unit]
Description=Predict Algorithm Agent WebSocket Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/twinbuilder-agents/agents/predict_algorith_agent
Environment=PYTHONPATH=/opt/twinbuilder-agents
Environment=PYTHONUNBUFFERED=1
ExecStart=/usr/bin/python3 predict_main.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
SERVICEEOF

systemctl daemon-reload
systemctl enable predict-algorithm-agent
EOF
```

#### 步骤6: 启动服务
```bash
ssh root@*********** << 'EOF'
# 启动服务
systemctl start predict-algorithm-agent

# 等待服务启动
sleep 5

# 检查服务状态
systemctl status predict-algorithm-agent

# 检查端口监听
netstat -tlnp | grep :8008

# 检查进程
pgrep -f predict_main.py
EOF
```

## ⚙️ 重要配置说明

### 欢迎消息配置
从2025-08-26版本开始，agent的欢迎消息默认关闭，由前端提供引导词：

```bash
# 在.env.production中设置
ENABLE_WELCOME_MESSAGE=false  # 默认关闭
ENABLE_WELCOME_MESSAGE=true   # 如需启用agent欢迎消息
```

### 数据库配置
确保数据库连接配置正确：
```bash
DB_HOST=***********
DB_PORT=3306
DB_NAME=indusaio_agent
DB_USER=root
DB_PASSWORD=Spsm2021+
```

### Redis配置
Redis用于缓存和会话管理：
```bash
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=Sygy@2025
```

### 大模型配置
默认使用阿里云通义千问：
```bash
LLM_PROVIDER=qwen
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2000
LLM_TIMEOUT=30
```

## 🔍 部署验证

### 自动验证测试
```bash
# 在本地运行验证测试
cd agents/predict_algorith_agent/scripts
python test_websocket_241_deployment.py ***********

# 或者在服务器上运行测试
ssh root@*********** << 'EOF'
cd /opt/twinbuilder-agents/agents/predict_algorith_agent/scripts
python3 test_database_connection.py
python3 complete_self_test.py
EOF
```

### 手动验证步骤

#### 1. 检查服务状态
```bash
ssh root@*********** << 'EOF'
# 服务状态
systemctl status predict-algorithm-agent

# 端口监听
netstat -tlnp | grep :8008

# 进程检查
pgrep -f predict_main.py

# 资源使用
ps aux | grep predict_main.py
EOF
```

#### 2. 检查日志
```bash
ssh root@*********** << 'EOF'
# 系统日志
journalctl -u predict-algorithm-agent -n 20

# 应用日志
tail -20 /opt/twinbuilder-agents/agents/predict_algorith_agent/logs/agent_production.log

# 实时日志监控
journalctl -u predict-algorithm-agent -f
EOF
```

#### 3. 测试WebSocket连接
```bash
# 使用curl测试HTTP端点（如果有）
curl -I http://***********:8008/

# 测试WebSocket连接（需要提供user_id参数）
# ws://***********:8008/api/ws/chat?user_id=test_user&username=测试用户

# 在服务器上测试本地连接
ssh root@*********** "telnet localhost 8008"
```

## 📊 监控和维护

### 服务管理命令
```bash
# 启动服务
systemctl start predict-algorithm-agent

# 停止服务
systemctl stop predict-algorithm-agent

# 重启服务
systemctl restart predict-algorithm-agent

# 查看状态
systemctl status predict-algorithm-agent

# 查看日志
journalctl -u predict-algorithm-agent -f
```

### 日志监控
```bash
# 实时查看应用日志
tail -f /opt/twinbuilder-agents/agents/predict_algorith_agent/logs/agent_production.log

# 查看系统日志
journalctl -u predict-algorithm-agent -f

# 查看错误日志
journalctl -u predict-algorithm-agent -p err
```

### 性能监控
```bash
# 检查资源使用
top -p $(pgrep -f predict_main.py)

# 检查内存使用
ps aux | grep predict_main.py

# 检查网络连接
netstat -an | grep :8008
```

## 🛠️ 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查详细错误
journalctl -u predict-algorithm-agent -n 50

# 检查配置文件
cat /opt/twinbuilder-agents/agents/predict_algorith_agent/.env.production

# 手动启动测试
cd /opt/twinbuilder-agents/agents/predict_algorith_agent
python3 predict_main.py
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务
systemctl status mysql
```

#### 3. WebSocket连接失败
```bash
# 检查端口监听
netstat -tlnp | grep :8008

# 检查防火墙
ufw status
iptables -L

# 测试本地连接
telnet localhost 8008
```

#### 4. 权限问题
```bash
# 检查文件权限
ls -la /opt/twinbuilder-agents/agents/predict_algorith_agent/

# 修复权限
chown -R root:root /opt/twinbuilder-agents/
chmod +x /opt/twinbuilder-agents/agents/predict_algorith_agent/predict_main.py
```

## 🔄 回滚方案

### 快速回滚
```bash
ssh root@************* << 'EOF'
# 停止当前服务
systemctl stop predict-algorithm-agent

# 恢复备份（替换为实际备份路径）
BACKUP_PATH="/opt/backup/20250807_103000"  # 替换为实际备份路径
rm -rf /opt/twinbuilder-agents
cp -r ${BACKUP_PATH}/twinbuilder-agents /opt/

# 重启服务
systemctl start predict-algorithm-agent
systemctl status predict-algorithm-agent
EOF
```

## 📞 技术支持

### 联系信息
- 如遇到部署问题，请提供以下信息：
  1. 错误日志：`journalctl -u predict-algorithm-agent -n 50`
  2. 服务状态：`systemctl status predict-algorithm-agent`
  3. 系统信息：`uname -a`
  4. Python版本：`python3 --version`

### 有用的调试命令
```bash
# 完整的系统状态检查
ssh root@************* << 'EOF'
echo "=== 系统信息 ==="
uname -a
python3 --version

echo "=== 服务状态 ==="
systemctl status predict-algorithm-agent

echo "=== 端口监听 ==="
netstat -tlnp | grep :8008

echo "=== 进程信息 ==="
pgrep -f predict_main.py

echo "=== 最新日志 ==="
journalctl -u predict-algorithm-agent -n 10

echo "=== 磁盘空间 ==="
df -h

echo "=== 内存使用 ==="
free -h
EOF
```

---

## 📝 部署后检查清单

- [ ] 服务状态正常：`systemctl status predict-algorithm-agent`
- [ ] 端口8008正在监听：`netstat -tlnp | grep :8008`
- [ ] 数据库连接正常：运行数据库测试脚本
- [ ] Redis连接正常：检查Redis服务状态
- [ ] 日志文件正常生成：检查logs目录
- [ ] WebSocket连接测试通过
- [ ] 欢迎消息配置符合预期（默认关闭）

## 🔄 版本更新说明

**2025-08-26 更新内容：**
- 新增欢迎消息配置开关（`ENABLE_WELCOME_MESSAGE`）
- 更新服务器IP为***********
- 优化环境变量配置
- 增强部署脚本功能
- 改进日志和监控

**注意**:
1. 欢迎消息默认关闭，由前端提供引导词
2. 请确保服务器IP地址、用户名和路径配置正确
3. 部署前建议先在测试环境验证
4. 如需启用agent欢迎消息，设置`ENABLE_WELCOME_MESSAGE=true`
