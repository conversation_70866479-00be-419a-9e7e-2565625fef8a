# WebSocket集成指南

## 📋 概述

本文档介绍如何将算法生成智能体与前端UI对话框通过WebSocket进行实时交互。

## 🏗️ 架构设计

### 系统架构
```
前端UI对话框 <--WebSocket--> WebSocket管理器 <--> 算法智能体 <--> 数据库
```

### 核心组件
- **WebSocket管理器** (`websocket_manager.py`) - 处理连接管理和消息路由
- **WebSocket路由** (`websocket_routes.py`) - 定义WebSocket端点
- **连接管理器** (`ConnectionManager`) - 管理多用户连接和会话状态

## 🔌 WebSocket端点

### 注： <server_ip> 是服务器固定IP或域名，端口<port>根据服务对外映射的端口确定

### 主要端点

- `ws://<server_ip>:<port>/api/ws/chat?user_id=<用户ID>&session_id=<会话ID>&username=<用户名>` - 主要聊天WebSocket端点
- `GET /api/ws/stats` - 获取连接统计信息
- `POST /api/ws/broadcast` - 广播消息
- `DELETE /api/ws/session/{session_id}` - 断开指定会话
- `GET /api/ws/health` - 健康检查

### 连接参数说明
- `user_id`：**必需参数**，当前登录用户的唯一标识
- `session_id`：可选参数，32位随机字符串，如不提供将自动生成
- `username`：可选参数，用户显示名称

## 📡 消息协议

### 客户端发送消息格式
```json
{
    "type": "chat",
    "data": {
        "message": "我想用LSTM做预测，学习率0.001，批次大小32"
    },
    "session_id": "a1b2c3d4e5f6789012345678901234ab",
    "user_id": "12345"
}
```

### 服务器响应消息格式
```json
{
    "type": "chat_response",
    "data": {
        "message": "我理解您想使用LSTM进行预测...",
        "task_id": "task_001",
        "conversation_id": "conv_67890",
        "state": {...},
        "has_error": false
    },
    "timestamp": "2025-01-22T18:00:00",
    "session_id": "a1b2c3d4e5f6789012345678901234ab",
    "user_id": "12345",
    "status": "success"
}
```

### 支持的消息类型

#### 客户端 → 服务器
- `chat` - 对话消息
- `get_progress` - 查询训练进度
- `generate_report` - 生成分析报告
- `get_history` - 获取历史记录
- `ping` - 心跳检测

#### 服务器 → 客户端
- `welcome` - 欢迎消息
- `chat_response` - 对话响应
- `thinking` - 正在思考状态
- `progress_update` - 进度更新
- `report_generated` - 报告生成完成
- `history_data` - 历史数据
- `error` - 错误消息
- `pong` - 心跳响应

## 🌐 前端集成示例

### JavaScript WebSocket客户端
```javascript
class AlgorithmChatClient {
    constructor(userId, sessionId = null, username = null) {
        this.userId = userId;  // 必需参数
        this.sessionId = sessionId || this.generateSessionId();  // 32位随机字符串
        this.username = username || `user_${userId}`;
        this.websocket = null;
        this.messageHandlers = {};
    }

    generateSessionId() {
        // 生成32位随机字符串
        return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
            const r = Math.random() * 16 | 0;
            return r.toString(16);
        });
    }

    connect() {
        const wsUrl = `ws://<server_ip>:<port>/api/ws/chat?user_id=${this.userId}&session_id=${this.sessionId}&username=${encodeURIComponent(this.username)}`;
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = (event) => {
            console.log('WebSocket连接已建立');
            this.onConnectionOpen(event);
        };
        
        this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.websocket.onclose = (event) => {
            console.log('WebSocket连接已关闭');
            this.onConnectionClose(event);
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.onConnectionError(error);
        };
    }
    
    sendMessage(type, data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            const message = {
                type: type,
                data: data,
                session_id: this.sessionId,
                user_id: this.userId
            };
            this.websocket.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接');
        }
    }
    
    sendChatMessage(message) {
        this.sendMessage('chat', { message: message });
    }
    
    requestProgress(taskId) {
        this.sendMessage('get_progress', { task_id: taskId });
    }
    
    generateReport(taskId) {
        this.sendMessage('generate_report', { task_id: taskId });
    }
    
    handleMessage(message) {
        const handler = this.messageHandlers[message.type];
        if (handler) {
            handler(message);
        } else {
            console.log('收到消息:', message);
        }
    }
    
    onMessage(type, handler) {
        this.messageHandlers[type] = handler;
    }
    
    onConnectionOpen(event) {
        // 连接建立后的处理
    }
    
    onConnectionClose(event) {
        // 连接关闭后的处理
    }
    
    onConnectionError(error) {
        // 连接错误处理
    }
    
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 使用示例
const chatClient = new AlgorithmChatClient('12345', null, '张三');

// 设置消息处理器
chatClient.onMessage('welcome', (message) => {
    console.log('欢迎消息:', message.data.message);
    displayWelcomeMessage(message.data);
});

chatClient.onMessage('chat_response', (message) => {
    console.log('AI回复:', message.data.message);
    displayChatMessage(message.data);
});

chatClient.onMessage('thinking', (message) => {
    showThinkingIndicator(message.data.message);
});

chatClient.onMessage('progress_update', (message) => {
    updateProgressBar(message.data);
});

chatClient.onMessage('report_generated', (message) => {
    displayReport(message.data);
});

chatClient.onMessage('error', (message) => {
    showErrorMessage(message.data.error);
});

// 连接WebSocket
chatClient.connect();

// 发送消息
function sendUserMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    if (message) {
        chatClient.sendChatMessage(message);
        input.value = '';
        displayUserMessage(message);
    }
}
```

### Vue.js 3 完整客户端实现

#### 1. Vue Composition API Hook
```javascript
// composables/useAlgorithmChat.js
import { ref, reactive, computed, onMounted, onUnmounted, watch, watchEffect } from 'vue'

export function useAlgorithmChat(userId, sessionId = null, username = null) {
    // 响应式状态
    const state = reactive({
        messages: [],
        conversations: [],
        currentConversationId: null,
        isConnected: false,
        isThinking: false,
        connectionStatus: 'disconnected', // disconnected, connecting, connected, error
        lastError: null,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5
    })

    const websocket = ref(null)
    const reconnectTimer = ref(null)

    // 生成32位session_id
    const generateSessionId = () => {
        return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
            const r = Math.random() * 16 | 0;
            return r.toString(16);
        });
    };

    const finalSessionId = sessionId || generateSessionId();
    const finalUsername = username || `user_${userId}`;

    // 计算属性
    const canSendMessage = computed(() => {
        return state.isConnected && !state.isThinking
    })

    const connectionStatusText = computed(() => {
        switch (state.connectionStatus) {
            case 'connecting': return '连接中...'
            case 'connected': return '已连接'
            case 'disconnected': return '未连接'
            case 'error': return '连接错误'
            default: return '未知状态'
        }
    })

    // WebSocket连接
    const connect = () => {
        if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
            return // 已经连接
        }

        state.connectionStatus = 'connecting'
        state.lastError = null

        const wsUrl = `ws://<server_ip>:<port>/api/ws/chat?user_id=${userId}&session_id=${finalSessionId}&username=${encodeURIComponent(finalUsername)}`;
        websocket.value = new WebSocket(wsUrl);

        websocket.value.onopen = () => {
            state.isConnected = true;
            state.connectionStatus = 'connected'
            state.reconnectAttempts = 0
            console.log('WebSocket连接已建立');

            // 连接成功后获取对话列表
            requestConversations()
        };

        websocket.value.onmessage = (event) => {
            const data = JSON.parse(event.data);
            handleMessage(data);
        };

        websocket.value.onclose = (event) => {
            state.isConnected = false;
            state.connectionStatus = 'disconnected'
            console.log('WebSocket连接已关闭', event.code, event.reason);

            // 自动重连逻辑
            if (event.code !== 1000 && state.reconnectAttempts < state.maxReconnectAttempts) {
                scheduleReconnect()
            }
        };

        websocket.value.onerror = (error) => {
            state.connectionStatus = 'error'
            state.lastError = error
            console.error('WebSocket错误:', error);
        };
    };

    // 消息处理
    const handleMessage = (data) => {
        console.log('收到消息:', data.type, data)

        switch (data.type) {
            case 'welcome':
                console.log('收到欢迎消息:', data.data.message);
                addSystemMessage(data.data.message)
                break;

            case 'thinking':
                state.isThinking = true;
                addSystemMessage('AI正在思考...')
                break;

            case 'chat_response':
                state.isThinking = false;
                addMessage({
                    type: 'assistant',
                    content: data.data.message,
                    timestamp: new Date(),
                    taskId: data.data.task_id,
                    conversationId: data.data.conversation_id
                });

                // 更新当前对话ID
                if (data.data.conversation_id) {
                    state.currentConversationId = data.data.conversation_id
                }
                break;

            case 'conversations_list':
                state.conversations = data.data.conversations || []
                console.log('收到对话列表:', state.conversations.length, '个对话')
                break;

            case 'conversation_created':
                state.currentConversationId = data.data.conversation_id
                console.log('新对话已创建:', data.data.conversation_id)
                // 重新获取对话列表
                requestConversations()
                break;

            case 'conversation_history':
                // 加载历史消息
                const history = data.data.messages || []
                state.messages = history.map(msg => ({
                    type: msg.message_type,
                    content: msg.content,
                    timestamp: new Date(msg.created_at),
                    id: msg.id
                }))
                console.log('加载历史消息:', history.length, '条')
                break;

            case 'error':
                state.lastError = data.data.message
                console.error('服务器错误:', data.data.message)
                addSystemMessage(`错误: ${data.data.message}`, 'error')
                break;

            case 'pong':
                console.log('收到心跳回复')
                break;

            default:
                console.log('未处理的消息类型:', data.type)
        }
    };

    // 添加消息
    const addMessage = (message) => {
        state.messages.push({
            id: Date.now(),
            ...message
        })
    }

    // 添加系统消息
    const addSystemMessage = (content, level = 'info') => {
        addMessage({
            type: 'system',
            content,
            timestamp: new Date(),
            level
        })
    }

    // 发送消息
    const sendMessage = (message) => {
        if (!canSendMessage.value) {
            console.warn('无法发送消息：连接未就绪或AI正在思考')
            return false
        }

        // 添加用户消息到界面
        addMessage({
            type: 'user',
            content: message,
            timestamp: new Date()
        });

        // 发送到服务器
        sendWebSocketMessage({
            type: 'chat',
            data: { message },
            session_id: finalSessionId,
            user_id: userId
        });

        return true
    };

    // 发送WebSocket消息的通用方法
    const sendWebSocketMessage = (messageData) => {
        if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
            websocket.value.send(JSON.stringify(messageData));
            return true
        } else {
            console.error('WebSocket未连接，无法发送消息')
            return false
        }
    }

    // 请求对话列表
    const requestConversations = () => {
        sendWebSocketMessage({
            type: 'get_conversations',
            data: {
                limit: 20,
                offset: 0
            },
            session_id: finalSessionId,
            user_id: userId
        })
    }

    // 创建新对话
    const createNewConversation = () => {
        sendWebSocketMessage({
            type: 'create_conversation',
            data: {
                title: '新建对话'
            },
            session_id: finalSessionId,
            user_id: userId
        })
    }

    // 切换到指定对话
    const switchToConversation = (conversationId) => {
        sendWebSocketMessage({
            type: 'switch_conversation',
            data: {
                conversation_id: conversationId
            },
            session_id: finalSessionId,
            user_id: userId
        })

        // 请求该对话的历史消息
        sendWebSocketMessage({
            type: 'get_conversation_history',
            data: {
                conversation_id: conversationId,
                limit: 50
            },
            session_id: finalSessionId,
            user_id: userId
        })
    }

    // 删除对话
    const deleteConversation = (conversationId) => {
        sendWebSocketMessage({
            type: 'delete_conversation',
            data: {
                conversation_id: conversationId
            },
            session_id: finalSessionId,
            user_id: userId
        })
    }

    // 发送心跳
    const sendHeartbeat = () => {
        sendWebSocketMessage({
            type: 'ping',
            data: {},
            session_id: finalSessionId,
            user_id: userId
        })
    }

    // 自动重连
    const scheduleReconnect = () => {
        if (reconnectTimer.value) {
            clearTimeout(reconnectTimer.value)
        }

        state.reconnectAttempts++
        const delay = Math.min(1000 * Math.pow(2, state.reconnectAttempts), 30000) // 指数退避，最大30秒

        console.log(`${delay/1000}秒后尝试重连 (第${state.reconnectAttempts}次)`)

        reconnectTimer.value = setTimeout(() => {
            if (!state.isConnected) {
                connect()
            }
        }, delay)
    }

    // 断开连接
    const disconnect = () => {
        if (reconnectTimer.value) {
            clearTimeout(reconnectTimer.value)
            reconnectTimer.value = null
        }

        if (websocket.value) {
            websocket.value.close(1000, '用户主动断开')
        }

        state.isConnected = false
        state.connectionStatus = 'disconnected'
    };

    // 清空消息
    const clearMessages = () => {
        state.messages = []
    }

    // 生命周期钩子
    onMounted(() => {
        connect();

        // 设置心跳定时器
        const heartbeatInterval = setInterval(() => {
            if (state.isConnected) {
                sendHeartbeat()
            }
        }, 30000) // 每30秒发送一次心跳

        // 清理定时器
        onUnmounted(() => {
            clearInterval(heartbeatInterval)
        })
    });

    onUnmounted(() => {
        disconnect();
    });

    // 监听连接状态变化
    watch(() => state.isConnected, (newValue, oldValue) => {
        console.log('连接状态变化:', oldValue, '->', newValue)
    })

    return {
        // 状态
        state,
        canSendMessage,
        connectionStatusText,

        // 方法
        sendMessage,
        connect,
        disconnect,
        clearMessages,
        requestConversations,
        createNewConversation,
        switchToConversation,
        deleteConversation,
        sendHeartbeat,

        // 原始数据（向后兼容）
        messages: computed(() => state.messages),
        isConnected: computed(() => state.isConnected),
        isThinking: computed(() => state.isThinking)
    };
}
```

#### 2. Vue 3 完整组件示例
```vue
<template>
  <div class="algorithm-chat">
    <!-- 连接状态栏 -->
    <div class="connection-status" :class="state.connectionStatus">
      <div class="status-indicator">
        <span class="status-dot"></span>
        {{ connectionStatusText }}
      </div>
      <div class="connection-actions">
        <button @click="connect" :disabled="state.isConnected">连接</button>
        <button @click="disconnect" :disabled="!state.isConnected">断开</button>
        <button @click="clearMessages">清空</button>
      </div>
    </div>

    <!-- 对话列表 -->
    <div class="conversations-panel">
      <div class="conversations-header">
        <h3>对话列表</h3>
        <button @click="createNewConversation" :disabled="!state.isConnected">
          新建对话
        </button>
      </div>
      <div class="conversations-list">
        <div
          v-for="conversation in state.conversations"
          :key="conversation.conversation_id"
          class="conversation-item"
          :class="{ active: conversation.conversation_id === state.currentConversationId }"
          @click="switchToConversation(conversation.conversation_id)"
        >
          <div class="conversation-title">{{ conversation.title }}</div>
          <div class="conversation-time">{{ formatTime(conversation.created_at) }}</div>
          <button
            class="delete-btn"
            @click.stop="deleteConversation(conversation.conversation_id)"
          >
            删除
          </button>
        </div>
      </div>
    </div>

    <!-- 消息区域 -->
    <div class="messages-container">
      <div class="messages-list" ref="messagesContainer">
        <div
          v-for="message in state.messages"
          :key="message.id"
          class="message"
          :class="[message.type, message.level]"
        >
          <div class="message-header">
            <span class="message-type">{{ getMessageTypeText(message.type) }}</span>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
          <div class="message-content" v-html="formatMessageContent(message.content)"></div>
          <div v-if="message.taskId" class="message-meta">
            任务ID: {{ message.taskId }}
          </div>
        </div>

        <!-- 思考状态指示器 -->
        <div v-if="state.isThinking" class="thinking-indicator">
          <div class="thinking-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span>AI正在思考...</span>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-container">
          <textarea
            v-model="inputMessage"
            placeholder="输入消息，例如：我想用LSTM做预测，学习率0.001，批次大小32"
            :disabled="!canSendMessage"
            @keydown.enter.exact.prevent="handleSendMessage"
            @keydown.enter.shift.exact="addNewLine"
            rows="3"
          ></textarea>
          <button
            @click="handleSendMessage"
            :disabled="!canSendMessage || !inputMessage.trim()"
            class="send-button"
          >
            发送
          </button>
        </div>
        <div class="input-hint">
          按 Enter 发送，Shift + Enter 换行
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="state.lastError" class="error-toast">
      {{ state.lastError }}
      <button @click="state.lastError = null">×</button>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { useAlgorithmChat } from '@/composables/useAlgorithmChat'

// Props
const props = defineProps({
  userId: {
    type: String,
    required: true
  },
  username: {
    type: String,
    default: ''
  },
  sessionId: {
    type: String,
    default: null
  }
})

// 使用WebSocket Hook
const {
  state,
  canSendMessage,
  connectionStatusText,
  sendMessage,
  connect,
  disconnect,
  clearMessages,
  requestConversations,
  createNewConversation,
  switchToConversation,
  deleteConversation,
  sendHeartbeat
} = useAlgorithmChat(props.userId, props.sessionId, props.username)

// 本地状态
const inputMessage = ref('')
const messagesContainer = ref(null)

// 方法
const handleSendMessage = () => {
  const message = inputMessage.value.trim()
  if (message && canSendMessage.value) {
    sendMessage(message)
    inputMessage.value = ''
  }
}

const addNewLine = () => {
  inputMessage.value += '\n'
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getMessageTypeText = (type) => {
  const typeMap = {
    'user': '用户',
    'assistant': 'AI助手',
    'system': '系统',
    'error': '错误'
  }
  return typeMap[type] || type
}

const formatMessageContent = (content) => {
  // 简单的Markdown渲染
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>')
}

// 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 监听消息变化，自动滚动
watch(() => state.messages.length, scrollToBottom)
watch(() => state.isThinking, scrollToBottom)
</script>

<style scoped>
.algorithm-chat {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

/* 连接状态栏 */
.connection-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.connection-status.connected {
  background: #e8f5e8;
}

.connection-status.error {
  background: #ffeaea;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
}

.connection-status.connected .status-dot {
  background: #4caf50;
}

.connection-status.error .status-dot {
  background: #f44336;
}

.connection-actions {
  display: flex;
  gap: 8px;
}

.connection-actions button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.connection-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 对话列表 */
.conversations-panel {
  width: 300px;
  border-right: 1px solid #e0e0e0;
  background: #fafafa;
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.conversations-header h3 {
  margin: 0;
  font-size: 14px;
}

.conversations-list {
  max-height: 400px;
  overflow-y: auto;
}

.conversation-item {
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  position: relative;
}

.conversation-item:hover {
  background: #f0f0f0;
}

.conversation-item.active {
  background: #e3f2fd;
}

.conversation-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.conversation-time {
  font-size: 12px;
  color: #666;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  font-size: 12px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: white;
  cursor: pointer;
}

/* 消息区域 */
.messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.messages-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: white;
}

.message {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.message.user {
  background: #e3f2fd;
  margin-left: 20%;
}

.message.assistant {
  background: #f8f9fa;
  margin-right: 20%;
}

.message.system {
  background: #fff3e0;
  text-align: center;
  margin: 8px 0;
}

.message.error {
  background: #ffebee;
  border-color: #f44336;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.message-content {
  line-height: 1.5;
}

.message-meta {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

/* 思考指示器 */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f0f0f0;
  border-radius: 8px;
  margin: 8px 0;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #666;
  animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* 输入区域 */
.input-area {
  border-top: 1px solid #e0e0e0;
  background: white;
  padding: 16px;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-container textarea {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.input-container textarea:focus {
  outline: none;
  border-color: #2196f3;
}

.send-button {
  padding: 12px 24px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.input-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  background: #f44336;
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1000;
}

.error-toast button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

#### 3. Vue 3 使用示例
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)
app.mount('#app')
```

```vue
<!-- App.vue -->
<template>
  <div id="app">
    <AlgorithmChat
      :userId="currentUserId"
      :username="currentUsername"
      :sessionId="currentSessionId"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AlgorithmChat from '@/components/AlgorithmChat.vue'

// 用户信息（通常从登录系统获取）
const currentUserId = ref('user_12345')
const currentUsername = ref('测试用户')
const currentSessionId = ref(null) // 自动生成
</script>
```

#### 4. 所有通信场景的完整示例

```javascript
// 完整的WebSocket通信场景示例
export function useAdvancedAlgorithmChat(userId, sessionId = null, username = null) {
    const { state, sendWebSocketMessage, ...baseChat } = useAlgorithmChat(userId, sessionId, username)

    // 扩展的通信方法
    const advancedMethods = {
        // 1. 算法训练相关
        startTraining: (algorithmConfig) => {
            return sendWebSocketMessage({
                type: 'start_training',
                data: {
                    algorithm_type: algorithmConfig.type, // 'LSTM', 'CNN', 'Transformer'
                    parameters: algorithmConfig.parameters,
                    dataset_path: algorithmConfig.datasetPath,
                    training_config: algorithmConfig.trainingConfig
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        stopTraining: (taskId) => {
            return sendWebSocketMessage({
                type: 'stop_training',
                data: { task_id: taskId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getTrainingProgress: (taskId) => {
            return sendWebSocketMessage({
                type: 'get_training_progress',
                data: { task_id: taskId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 2. 预测相关
        makePrediction: (modelId, inputData) => {
            return sendWebSocketMessage({
                type: 'make_prediction',
                data: {
                    model_id: modelId,
                    input_data: inputData,
                    prediction_type: 'real_time'
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        batchPrediction: (modelId, batchData) => {
            return sendWebSocketMessage({
                type: 'batch_prediction',
                data: {
                    model_id: modelId,
                    batch_data: batchData,
                    output_format: 'json'
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 3. 模型管理
        getModelList: () => {
            return sendWebSocketMessage({
                type: 'get_models',
                data: {
                    user_id: userId,
                    include_public: true
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        saveModel: (modelData) => {
            return sendWebSocketMessage({
                type: 'save_model',
                data: {
                    model_name: modelData.name,
                    model_config: modelData.config,
                    model_weights: modelData.weights,
                    description: modelData.description
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        deleteModel: (modelId) => {
            return sendWebSocketMessage({
                type: 'delete_model',
                data: { model_id: modelId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 4. 数据集管理
        uploadDataset: (datasetInfo) => {
            return sendWebSocketMessage({
                type: 'upload_dataset',
                data: {
                    dataset_name: datasetInfo.name,
                    dataset_type: datasetInfo.type, // 'csv', 'json', 'image'
                    file_path: datasetInfo.filePath,
                    metadata: datasetInfo.metadata
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getDatasetList: () => {
            return sendWebSocketMessage({
                type: 'get_datasets',
                data: { user_id: userId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        previewDataset: (datasetId, limit = 100) => {
            return sendWebSocketMessage({
                type: 'preview_dataset',
                data: {
                    dataset_id: datasetId,
                    limit: limit
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 5. 分析报告
        generateReport: (taskId, reportType = 'detailed') => {
            return sendWebSocketMessage({
                type: 'generate_report',
                data: {
                    task_id: taskId,
                    report_type: reportType, // 'summary', 'detailed', 'visual'
                    include_charts: true,
                    format: 'html'
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getReportList: () => {
            return sendWebSocketMessage({
                type: 'get_reports',
                data: {
                    user_id: userId,
                    limit: 20
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        downloadReport: (reportId, format = 'pdf') => {
            return sendWebSocketMessage({
                type: 'download_report',
                data: {
                    report_id: reportId,
                    format: format // 'pdf', 'html', 'json'
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 6. 系统状态
        getSystemStatus: () => {
            return sendWebSocketMessage({
                type: 'get_system_status',
                data: {},
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getResourceUsage: () => {
            return sendWebSocketMessage({
                type: 'get_resource_usage',
                data: {
                    include_gpu: true,
                    include_memory: true,
                    include_disk: true
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 7. 用户设置
        updateUserSettings: (settings) => {
            return sendWebSocketMessage({
                type: 'update_user_settings',
                data: {
                    settings: settings,
                    user_id: userId
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getUserSettings: () => {
            return sendWebSocketMessage({
                type: 'get_user_settings',
                data: { user_id: userId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 8. 实时监控
        subscribeToTrainingUpdates: (taskId) => {
            return sendWebSocketMessage({
                type: 'subscribe_training_updates',
                data: { task_id: taskId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        unsubscribeFromTrainingUpdates: (taskId) => {
            return sendWebSocketMessage({
                type: 'unsubscribe_training_updates',
                data: { task_id: taskId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 9. 协作功能
        shareConversation: (conversationId, shareWith) => {
            return sendWebSocketMessage({
                type: 'share_conversation',
                data: {
                    conversation_id: conversationId,
                    share_with: shareWith, // 用户ID数组
                    permissions: ['read', 'comment']
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getSharedConversations: () => {
            return sendWebSocketMessage({
                type: 'get_shared_conversations',
                data: { user_id: userId },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        // 10. 高级查询
        searchConversations: (query, filters = {}) => {
            return sendWebSocketMessage({
                type: 'search_conversations',
                data: {
                    query: query,
                    filters: filters,
                    user_id: userId,
                    limit: 20
                },
                session_id: finalSessionId,
                user_id: userId
            })
        },

        getConversationAnalytics: (timeRange = '7d') => {
            return sendWebSocketMessage({
                type: 'get_conversation_analytics',
                data: {
                    user_id: userId,
                    time_range: timeRange, // '1d', '7d', '30d', '90d'
                    metrics: ['message_count', 'session_duration', 'algorithm_usage']
                },
                session_id: finalSessionId,
                user_id: userId
            })
        }
    }

    // 扩展消息处理
    const handleAdvancedMessage = (data) => {
        switch (data.type) {
            case 'training_started':
                state.currentTaskId = data.data.task_id
                addSystemMessage(`训练已开始，任务ID: ${data.data.task_id}`)
                break

            case 'training_progress':
                const progress = data.data.progress
                addSystemMessage(`训练进度: ${progress.epoch}/${progress.total_epochs} - 损失: ${progress.loss.toFixed(4)}`)
                break

            case 'training_completed':
                addSystemMessage(`训练完成！模型已保存，ID: ${data.data.model_id}`)
                state.currentTaskId = null
                break

            case 'prediction_result':
                const result = data.data.result
                addMessage({
                    type: 'assistant',
                    content: `预测结果: ${JSON.stringify(result, null, 2)}`,
                    timestamp: new Date(),
                    predictionId: data.data.prediction_id
                })
                break

            case 'model_list':
                state.availableModels = data.data.models
                console.log('收到模型列表:', state.availableModels.length, '个模型')
                break

            case 'dataset_list':
                state.availableDatasets = data.data.datasets
                console.log('收到数据集列表:', state.availableDatasets.length, '个数据集')
                break

            case 'report_generated':
                addSystemMessage(`报告已生成: ${data.data.report_name}`)
                state.latestReportId = data.data.report_id
                break

            case 'system_status':
                state.systemStatus = data.data.status
                console.log('系统状态更新:', state.systemStatus)
                break

            case 'resource_usage':
                state.resourceUsage = data.data.usage
                console.log('资源使用情况:', state.resourceUsage)
                break

            case 'user_settings':
                state.userSettings = data.data.settings
                console.log('用户设置已更新')
                break

            case 'conversation_shared':
                addSystemMessage(`对话已分享给 ${data.data.shared_with.length} 个用户`)
                break

            case 'search_results':
                state.searchResults = data.data.results
                console.log('搜索结果:', state.searchResults.length, '条')
                break

            case 'analytics_data':
                state.analyticsData = data.data.analytics
                console.log('分析数据已更新')
                break

            default:
                // 调用基础消息处理
                baseChat.handleMessage(data)
        }
    }

    // 扩展状态
    const extendedState = reactive({
        ...state,
        currentTaskId: null,
        availableModels: [],
        availableDatasets: [],
        latestReportId: null,
        systemStatus: null,
        resourceUsage: null,
        userSettings: null,
        searchResults: [],
        analyticsData: null
    })

    return {
        ...baseChat,
        state: extendedState,
        handleMessage: handleAdvancedMessage,
        ...advancedMethods
    }
}
```

#### 5. Vue 3 高级组件示例

```vue
<template>
  <div class="advanced-algorithm-chat">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-section">
        <button @click="showTrainingDialog = true" :disabled="!state.isConnected">
          开始训练
        </button>
        <button @click="showPredictionDialog = true" :disabled="!state.isConnected">
          进行预测
        </button>
        <button @click="generateReport(state.currentTaskId)" :disabled="!state.currentTaskId">
          生成报告
        </button>
      </div>

      <div class="toolbar-section">
        <button @click="getModelList">模型列表</button>
        <button @click="getDatasetList">数据集</button>
        <button @click="getSystemStatus">系统状态</button>
      </div>
    </div>

    <!-- 训练对话框 -->
    <div v-if="showTrainingDialog" class="dialog-overlay" @click="showTrainingDialog = false">
      <div class="dialog" @click.stop>
        <h3>开始训练</h3>
        <form @submit.prevent="startTrainingTask">
          <div class="form-group">
            <label>算法类型:</label>
            <select v-model="trainingConfig.type" required>
              <option value="LSTM">LSTM</option>
              <option value="CNN">CNN</option>
              <option value="Transformer">Transformer</option>
              <option value="RandomForest">随机森林</option>
            </select>
          </div>

          <div class="form-group">
            <label>学习率:</label>
            <input type="number" v-model.number="trainingConfig.parameters.learning_rate"
                   step="0.0001" min="0.0001" max="1" required>
          </div>

          <div class="form-group">
            <label>批次大小:</label>
            <input type="number" v-model.number="trainingConfig.parameters.batch_size"
                   min="1" max="1024" required>
          </div>

          <div class="form-group">
            <label>训练轮数:</label>
            <input type="number" v-model.number="trainingConfig.trainingConfig.epochs"
                   min="1" max="1000" required>
          </div>

          <div class="form-group">
            <label>数据集:</label>
            <select v-model="trainingConfig.datasetPath" required>
              <option v-for="dataset in state.availableDatasets"
                      :key="dataset.id" :value="dataset.path">
                {{ dataset.name }}
              </option>
            </select>
          </div>

          <div class="dialog-actions">
            <button type="button" @click="showTrainingDialog = false">取消</button>
            <button type="submit">开始训练</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 预测对话框 -->
    <div v-if="showPredictionDialog" class="dialog-overlay" @click="showPredictionDialog = false">
      <div class="dialog" @click.stop>
        <h3>进行预测</h3>
        <form @submit.prevent="makePredictionTask">
          <div class="form-group">
            <label>选择模型:</label>
            <select v-model="predictionConfig.modelId" required>
              <option v-for="model in state.availableModels"
                      :key="model.id" :value="model.id">
                {{ model.name }} ({{ model.algorithm_type }})
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>输入数据 (JSON格式):</label>
            <textarea v-model="predictionConfig.inputData"
                      rows="6" placeholder='{"feature1": 1.0, "feature2": 2.0}' required>
            </textarea>
          </div>

          <div class="dialog-actions">
            <button type="button" @click="showPredictionDialog = false">取消</button>
            <button type="submit">开始预测</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 系统状态面板 -->
    <div v-if="state.systemStatus" class="status-panel">
      <h4>系统状态</h4>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">CPU使用率:</span>
          <span class="value">{{ state.systemStatus.cpu_usage }}%</span>
        </div>
        <div class="status-item">
          <span class="label">内存使用率:</span>
          <span class="value">{{ state.systemStatus.memory_usage }}%</span>
        </div>
        <div class="status-item">
          <span class="label">GPU使用率:</span>
          <span class="value">{{ state.systemStatus.gpu_usage }}%</span>
        </div>
        <div class="status-item">
          <span class="label">活跃任务:</span>
          <span class="value">{{ state.systemStatus.active_tasks }}</span>
        </div>
      </div>
    </div>

    <!-- 基础聊天组件 -->
    <AlgorithmChat
      :userId="userId"
      :username="username"
      :sessionId="sessionId"
      :customMessageHandler="handleAdvancedMessage"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAdvancedAlgorithmChat } from '@/composables/useAdvancedAlgorithmChat'
import AlgorithmChat from './AlgorithmChat.vue'

const props = defineProps({
  userId: String,
  username: String,
  sessionId: String
})

// 使用高级WebSocket Hook
const {
  state,
  startTraining,
  makePrediction,
  generateReport,
  getModelList,
  getDatasetList,
  getSystemStatus,
  handleMessage: handleAdvancedMessage
} = useAdvancedAlgorithmChat(props.userId, props.sessionId, props.username)

// 对话框状态
const showTrainingDialog = ref(false)
const showPredictionDialog = ref(false)

// 训练配置
const trainingConfig = reactive({
  type: 'LSTM',
  parameters: {
    learning_rate: 0.001,
    batch_size: 32
  },
  trainingConfig: {
    epochs: 100
  },
  datasetPath: ''
})

// 预测配置
const predictionConfig = reactive({
  modelId: '',
  inputData: ''
})

// 方法
const startTrainingTask = () => {
  startTraining(trainingConfig)
  showTrainingDialog.value = false
}

const makePredictionTask = () => {
  try {
    const inputData = JSON.parse(predictionConfig.inputData)
    makePrediction(predictionConfig.modelId, inputData)
    showPredictionDialog.value = false
  } catch (error) {
    alert('输入数据格式错误，请输入有效的JSON')
  }
}
</script>

<style scoped>
.advanced-algorithm-chat {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.toolbar-section {
  display: flex;
  gap: 8px;
}

.toolbar button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  padding: 24px;
  min-width: 400px;
  max-width: 600px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.status-panel {
  background: #f8f9fa;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.status-item .label {
  font-weight: 500;
}

.status-item .value {
  color: #2196f3;
  font-weight: bold;
}
</style>
```

## 🗄️ 数据存储设计

### 数据存储流程

1. **WebSocket连接建立**
   - 验证`user_id`参数是否传入（user_id必需）
   - 生成或验证`session_id`（32位UUID）
   - 创建`conversations`记录
   - 创建`session_states`记录

2. **消息交互处理**
   - 每条用户消息存储到`conversation_messages`表
   - 每条AI回复存储到`conversation_messages`表
   - 更新`session_states`中的状态数据

3. **会话状态管理**
   - 实时更新`last_activity`时间戳
   - 保存算法训练状态、参数等到`state_data`
   - 支持会话恢复和状态持久化

4. **连接断开处理**
   - 更新`last_activity`时间戳
   - 保持会话数据完整性
   - 支持重新连接时状态恢复

### Session ID生成规则

```javascript
// 32位随机字符串生成
function generateSessionId() {
    return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
        const r = Math.random() * 16 | 0;
        return r.toString(16);
    });
}

// 或使用UUID库
import { v4 as uuidv4 } from 'uuid';
const sessionId = uuidv4().replace(/-/g, ''); // 移除连字符得到32位字符串
```

## 🔧 部署和配置

### 1. 启动服务
```bash
cd /path/twinbuilder-agents
python agents/predict_algorith_agent/predict_main.py
```

### 2. 测试WebSocket连接
```bash
# 检查WebSocket健康状态
curl http://<server_ip>:<port>/api/ws/health

# 查看连接统计
curl http://<server_ip>:<port>/api/ws/stats
```

### 3. 前端配置
- WebSocket URL: `ws://<server_ip>:<port>/api/ws/chat?user_id=<用户ID>&session_id=<会话ID>&username=<用户名>`
- `user_id`参数是必需的，缺少将导致连接失败
- `session_id`支持自动生成32位随机字符串或手动指定
- 建议实现断线重连机制
- 建议实现心跳检测

## 🚀 高级特性

### 1. 多用户支持
- 每个用户独立的会话状态
- 支持并发连接
- 会话状态持久化

### 2. 实时进度监控
- 训练进度实时推送
- 状态变化通知
- 错误实时反馈

### 3. 智能报告生成
- 异步报告生成
- 生成状态实时通知
- 支持HTML和PDF格式

### 4. 错误处理和恢复

#### 常见错误码
- `4000`: 一般错误
- `4001`: 认证失败（缺少user_id参数）
- `4002`: 会话不存在
- `4003`: 消息格式错误
- `4004`: 服务器内部错误
- `4005`: 数据库连接错误
- `4006`: Session ID格式错误

#### 连接参数验证
```javascript
function validateConnectionParams(userId, sessionId = null) {
    if (!userId || userId.trim() === '') {
        throw new Error('user_id是必需参数，不能为空');
    }

    if (sessionId && sessionId.length !== 32) {
        throw new Error('session_id必须是32位字符串');
    }

    return true;
}
```

#### 错误处理示例
```javascript
websocket.onclose = (event) => {
    switch (event.code) {
        case 4001:
            console.error('认证失败：请检查user_id参数');
            break;
        case 4005:
            console.error('数据库连接错误：服务器暂时不可用');
            setTimeout(() => reconnect(), 10000);
            break;
        default:
            if (event.code !== 1000) {
                setTimeout(() => reconnect(), 5000);
            }
    }
};
```

#### 自动重连机制
- 连接断开自动重连
- 消息重发机制
- 优雅的错误提示

## 📊 监控和调试

### 日志监控
- WebSocket连接日志
- 消息处理日志
- 错误异常日志

### 性能监控
- 连接数统计
- 消息处理延迟
- 内存使用情况

### 调试工具
- WebSocket连接测试工具
- 消息格式验证
- 实时连接状态查看

## 🔒 安全考虑

### 1. 认证和授权
- 建议添加JWT token验证
- 会话ID验证
- 用户权限控制

### 2. 消息验证
- 输入消息格式验证
- 消息长度限制
- 恶意消息过滤

### 3. 连接管理
- 连接数限制
- 频率限制
- 异常连接检测

## 🎯 最佳实践

1. **前端实现**
   - 实现断线重连机制
   - 添加消息队列缓存
   - 优化用户体验反馈

2. **后端优化**
   - 合理的连接数限制
   - 消息处理异步化
   - 内存使用优化

3. **错误处理**
   - 完善的错误分类
   - 用户友好的错误提示
   - 自动恢复机制

4. **性能优化**
   - 消息压缩
   - 批量消息处理
   - 连接池管理

5. **数据库存储最佳实践**
   - 确保user_id的有效性和唯一性
   - 使用32位UUID作为session_id
   - 定期清理过期的会话数据
   - 实现会话状态的增量更新
   - 使用数据库索引优化查询性能

## 🔐 安全考虑

### 用户身份验证
- **user_id验证**: 确保user_id来自可信的认证系统
- **会话管理**: 实现会话超时和清理机制
- **权限控制**: 基于user_id实现数据访问权限控制

### 数据保护
- **敏感数据加密**: 对存储在数据库中的敏感信息进行加密
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **数据备份**: 定期备份会话和消息数据

### 连接安全
- **WSS协议**: 生产环境使用WSS（WebSocket Secure）
- **CORS配置**: 正确配置跨域资源共享
- **速率限制**: 实现连接和消息的速率限制

---

**注意**: 
- 本文档基于最新的WebSocket实现，包含了user_id必需参数、32位session_id生成和完整的数据库存储架构。
- 请确保客户端实现遵循这些规范。
