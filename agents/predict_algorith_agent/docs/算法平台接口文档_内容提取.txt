

=== 第 1 页 ===
接口列表
下面的表格中给出了查询全部数据集名称、全部算法名称的接口，以及训练
新算法的接口。
接口名称 查询全部算法的名称
接口描述 请求当前项目的全部算法的名称。
接口地址 http://***********:8081/get_all_algorithm
请求方式 GET
返回值类型 Json
返回字段说明
字段名 字段类型默认
值说明
algorithm_nameList 请求的算法名称及其所属的项目号
Msg string 请求结果说明

=== 第 2 页 ===
响应数据示例：
{
"algorithm_name":[
[
"水泵电流预警算法0723",
"285"
],
[
"水泵转速预警算法0723",
"285"
],
[
"水泵电流预警算法0612",
"280"
],
[
"电流预警2",
"280"
],
[
"TEST",
"280"
],
[
"name",
"280"
]
],
"msg":"获取成功"
}
接口名称 查询全部数据集的名称
接口描述 请求当前项目的全部数据集的名称。
接口地址 http://***********:8081/get_all_dataset

=== 第 3 页 ===
请求方式 GET
返回值类型 Json
返回字段说明
字段名 字段类型默认
值说明
msg string
dataset_namelist 全部的数据集名称和项目号，
响应数据示例：
{
"dataset_name":[
[
"水泵数据0606",
"255"
],
[
"水泵数据集0606",
"274"
],
[
"水泵数据集0611",
"275"
],
[
"水泵数据集0609",
"276"
],
[
"水泵数据集0531",
"277"
],
[
"水泵数据集0612",
"280"
],
[
"水泵数据集0723",

=== 第 4 页 ===
"285"
],
[
"测试数据2",
"280"
]
],
"msg":"获取成功"
}
接口名称 训练新算法
接口描述向后端发送新算法的参数，并启动该算法的训练进程，
训练完成后算法名称列表就会新增对应的子数组。
该方法异步执行，启动后可以在前端直接退出，不需要
等待返回值。
接口地址 http://***********:8081/algorithm_parameter
请求方式 POST
返回值类型 Json
发送字段说明
字段名 字段类型默认
值说明
formData FormData存储多个算法参数的表单，前端必须用
append方法追加数据，比如：
constformData=newFormData();
formData.append('parameter10',“0.01”);
返回字段说明
message String
algoname String 启动算法的名称
dataset String 算法用的数据集

=== 第 5 页 ===
请求示例表单：
parameter1="测试算法1"
parameter2="风险预警"
parameter3="水泵数据集0612"
parameter4="5"
parameter5="64"
parameter6="3"
parameter7="0.2"
parameter8="YES"
parameter9="10"
parameter10="0.01"
parameter11="1"
parameter12="Adam"
parameter13="MSE"
parameter14="4"
parameter15="5"
parameter16="P11111124112047702"
project_number="280"
说明：
parameter1#算法名称
parameter2#算法类型
parameter3#数据集
parameter4#输入特征维度,迭代次数
parameter5#LSTM隐藏层的大小
parameter6#lstm层数
parameter7#lstm的dropout比例
parameter8#是否使用双向lstm
parameter9#输出维度的个数
parameter10#学习率大小
parameter11#样本划分比例
parameter12#优化器
parameter13#损失函数
parameter14#标签列
parameter15#数据列
parameter16#字段
project_number#项目编号
响应示例：
{
'message':'启动成功',
'algoname':"测试算法1",
'dataset':"水泵数据集0612"
}

=== 第 6 页 ===
接口名称 查询全部IOT的名称
接口描述 获得特定项目下的全部iot信息
接口地址 http://***********:8081/get_all_iot?project=285
请求方式带参数的get，project是项目号参数，表示前端用户正在查看
的是哪个项目
返回值类型 Json
返回字段说明
字段名 字段类型默认
值说明
iot_name ListIot的数据列表，里面包含多个子列表，每
个子列表代表该项目里面的一个iot对象，
每个子列表包含三个元素，第一个字典元素
存放了该iot数据库里面的所有表名，第二
个字典元素存放了用户定义的字段值及其
物理含义，第三个字符串元素存放了该iot
所属的项目号
msg String 请求结果描述
{
"iot_info":[
[
{
"1":"data_1815320358166839298",
"2":"data_1844651253848231937",
"3":"data_1859849299145101313",
"4":"data_1859850140165967873",
"5":"device_efficiency_today_history_data"
},
{
"name":"电流",
"option":"P11111124112049197"
},
"285"
]
],

=== 第 7 页 ===
"msg":"获取成功"
}
接口名称 启动监控分析服务
接口描述向后端发送参数，并启动算法的监控分析进程，训练完成后
服务名称列表就会新增对应的子数组。该方法异步执行，启
动后可以在前端直接退出，不需要等待返回值。
接口地址 http://***********:8081/implement_parameter
请求方式 POST
返回值类型 Json
发送字段说明
字段名 字段类型默认
值说明
formData FormData存储多个服务生成参数的表单，包括数据库
地址、数据库表名、关键数据的字段、采用
的算法文件、所属的项目号
返回字段说明
Msg string 请求成功的说明
name string 服务名称
示例请求formdata：
db_ip="***********"
db_name="data_1815320358166839298"
list_name="P11111124112047702"
implement_name="TEST"
project_name="280"
示例响应：
{
msg:启动成功,
name:TEST
}

=== 第 8 页 ===
接口名称 查询某个算法的训练信息
接口描述 传算法名称，返回该算法的训练json
接口地址http://***********:8081/get_algorithm_log?name=水泵电流预
警算法0612&project=280
请求方式带参数的get，name是算法名称，表示需要查询哪个算法的
训练信息，不填时会返回默认值
返回值类型 Json
返回字段说明
字段名 字段类型默认
值说明
algorithm_log字典算法训练时的信息日志列表，包括三个元
素，第一个元素是该算法所采用的训练参
数，第二个元素是该算法采用的数据集名
称，第三个元素是训练信息字典，其中训练
信息字典中存放了最新的训练结果和历史
训练结果，后续将扩充这个字典以支持智能
训练
msg String 请求成功的说明
示例响应：
{
"algorithm_log":{
"Algorithm_parameter":"5,64,3,0.2,YES,10,0.01,1,Adam,MSE,4,5",
"Datasetname":"水泵数据集0612",
"MAE":8.01021385192871,
"MAPE":31.9193512201309
},
"msg":"获取成功"
}
接口名称 查询全部服务的名称
接口描述传一个项目号，查询该项目下的所有已生成服务，每个服
务都是独立运行的进程，正在监控某个数据表的特定字段的
数据是否存在异常

=== 第 9 页 ===
接口地址 ***********:8081/get_all_implement?project=280
请求方式 GET
返回值类型 Json
返回字段说明
字段名 字段类型默认
值说明
implement_namelist该项目下已生成服务的数据信息，数组中包
括多个字典，每个字典都是一个服务的信
息，包括所调用的算法名称，分析的字段，
运行状态，分析的数据表名称，用于智能体
获悉有哪些服务是正在运行的
Msg String 请求成功的标志
示例响应：
{
"implement_info":[
{
"algorithm_name":"水泵电流预警算法0612",
"field_value":"P11111124112014448",
"status":"正在运行",
"tablename":"data_1815320358166839298"
}
],
"msg":"获取成功"
}
接口名称 查询某个服务的预测结果
接口描述 传递服务名称和项目号，查询该服务的实时分析结论
接口地址http://***********:8081/get_implement_log?name=电流预警
&project=255
请求方式 GET

=== 第 10 页 ===
返回值类型 Json
返回字段说明
字段名 字段类型默认
值说明
implement_info字典所生成算法的分析结论，后续要扩充该字典
的内容来支持智能分析
msg String 请求成功的标志
{
"implement_info":{
"probability":"96.36",
"riskLevel":"高",
"riskType":"周期性"
},
"msg":"获取成功"
}