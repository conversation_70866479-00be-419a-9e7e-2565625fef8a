# 算法智能体系统完整用户使用指南

## 📋 系统概述

算法智能体系统是一个集成了WebSocket实时聊天和第三方API调用功能的智能算法训练平台。用户可以通过自然语言对话或直接API调用的方式，进行算法训练、参数配置、数据查询等操作。

### 核心功能
- 🤖 **智能对话**：通过自然语言与AI助手交流
- ⚡ **API调用**：直接调用8个第三方算法平台接口
- 🔄 **混合模式**：聊天和API调用无缝切换
- 📊 **实时监控**：查看训练状态和结果
- 📁 **项目管理**：支持多项目切换

## 🚀 快速开始

### 1. 访问系统
打开浏览器，访问系统地址：
```
http://your-server-address:8008/static/html/chat_interface_v3.html
```

### 2. 用户登录
系统启动后会自动弹出登录窗口：
- **用户ID**：输入您的唯一用户标识
- **用户名**：输入您的显示名称
- **登录**：点击登录按钮
- **访客登录**：使用默认访客身份登录

### 3. 界面概览
```
┌─────────────────┬─────────────────────────────────┐
│   左侧边栏      │           聊天区域              │
│ • 用户信息      │                                 │
│ • 项目选择      │                                 │
│ • API功能区     │                                 │
│ • 对话历史      │                                 │
│                 ├─────────────────────────────────┤
│                 │           输入区域              │
└─────────────────┴─────────────────────────────────┘
```

## 💬 聊天对话功能

### 基本对话
1. **连接服务器**：点击右上角"🔗 连接"按钮
2. **发送消息**：在底部输入框输入问题，按Enter发送
3. **查看回复**：AI助手会实时回复

### 对话示例
```
用户：你好，我想了解算法智能体的功能
AI：您好！欢迎使用算法生成智能体！

我可以帮助您进行算法训练！以下是常见的训练流程：

📋 训练步骤：
1. 需求分析 - 确定要解决的问题类型
2. 算法选择 - 选择合适的算法（LSTM、CNN等）
3. 参数配置 - 设置学习率、批次大小等参数
4. 开始训练 - 启动训练任务
5. 监控进度 - 实时查看训练状态
6. 结果分析 - 生成训练报告
```

## ⚡ API调用功能

### 项目选择
使用API功能前，需要先选择项目：
1. 在左侧边栏找到"选择项目"下拉框
2. 选择您要操作的项目（如：项目285）
3. 选择后，相关API按钮将被启用

### 8个核心API接口

#### 查询功能API
1. **📊 查询全部算法名称**
   - 功能：获取系统中所有可用的算法列表
   - 使用场景：了解可用算法，选择合适的算法类型

2. **📁 查询全部数据集名称**
   - 功能：获取系统中所有可用的数据集列表
   - 使用场景：选择训练数据，了解数据集特征

3. **🌐 查询全部IOT名称**
   - 功能：获取指定项目的IOT设备列表
   - 使用场景：物联网数据分析，设备监控

4. **⚙️ 查询全部服务名称**
   - 功能：获取指定项目的运行服务列表
   - 使用场景：服务管理，状态监控

5. **📋 查询算法训练信息**
   - 功能：查看指定算法的训练日志和状态
   - 参数：需要输入算法名称
   - 使用场景：监控训练进度，分析训练结果

6. **🔮 查询服务预测结果**
   - 功能：获取指定服务的实时预测结果
   - 参数：需要输入服务名称
   - 使用场景：实时监控，结果分析

#### 操作功能API
7. **🚀 训练新算法**
   - 功能：启动新的算法训练任务
   - 参数：需要填写详细的训练参数表单
   - 关键参数：
     ```
     • 算法名称：自定义算法名称
     • 算法类型：风险预警/故障诊断/预测维护
     • 数据集名称：选择训练数据集
     • LSTM隐藏层大小：神经元数量（如64）
     • 学习率：优化步长（如0.01）
     • 优化器：Adam/SGD/RMSprop
     ```

8. **📡 启动监控分析服务**
   - 功能：启动实时数据监控和分析服务
   - 参数：需要配置数据库和服务参数
   - 关键参数：
     ```
     • 数据库地址：数据源IP地址
     • 数据库表名：监控数据表
     • 关键数据字段：监控的核心字段
     • 服务名称：自定义服务名称
     ```

## 🔄 混合模式使用

### 场景1：聊天引导 + API执行
1. **对话咨询**：
   ```
   用户：我想训练一个LSTM算法用于设备预测维护
   AI：我来帮您配置LSTM算法。建议的参数配置如下...
   ```
2. **API执行**：根据建议使用"🚀 训练新算法"功能

### 场景2：API查询 + 聊天分析
1. **API查询**：先查询可用资源
   - 点击"📊 查询全部算法名称"
   - 点击"📁 查询全部数据集名称"
2. **聊天分析**：
   ```
   用户：我看到有这些算法和数据集，哪个组合最适合我的预测维护需求？
   AI：根据您的需求，建议使用LSTM算法配合时序数据集...
   ```

## 📊 结果查看与分析

### 实时结果显示
- API调用结果会实时显示在聊天区域
- 成功调用显示绿色边框
- 失败调用显示红色边框，包含错误信息

### 结果格式示例
```json
{
  "API调用": "查询全部算法名称 - 成功",
  "返回数据": {
    "msg": "查询成功",
    "algorithm_name": [
      ["LSTM预测算法", "285"],
      ["CNN分类算法", "286"]
    ]
  }
}
```

## 🔧 高级功能

### 会话管理
- **新建对话**：点击"➕ 新建对话"开始新会话
- **切换对话**：点击历史对话列表中的任意对话
- **对话持久化**：所有对话自动保存到数据库

### 项目切换
- 支持多项目管理
- 不同项目的API调用结果独立
- 项目数据隔离保护

### 错误处理
- **网络断开**：自动检测并提示重连
- **API失败**：显示详细错误信息和建议
- **参数错误**：表单验证和提示

## ❓ 常见问题

### Q1: 无法连接到服务器
**解决方案**：
- 确认服务器地址正确
- 检查网络连接
- 确认服务器已启动
- 检查防火墙设置

### Q2: API调用失败
**可能原因**：
- 第三方API服务器不可达
- 参数格式错误
- 项目权限不足
- 网络超时

### Q3: 聊天无响应
**解决步骤**：
1. 检查WebSocket连接状态
2. 刷新页面重新连接
3. 检查输入内容是否合规
4. 联系管理员检查服务状态

### Q4: 训练参数如何选择
**建议流程**：
1. 先通过聊天咨询AI建议
2. 参考历史成功案例
3. 从保守参数开始尝试
4. 根据结果逐步优化

## 🎯 最佳实践

### 1. 高效使用建议
- **明确需求**：详细描述您的算法需求和数据特征
- **分步操作**：将复杂任务分解为多个简单步骤
- **混合使用**：结合聊天咨询和API调用获得最佳效果
- **保存记录**：重要的参数配置和结果要及时保存

### 2. 安全注意事项
- **数据保护**：不要在聊天中输入敏感数据
- **权限管理**：只在授权项目中进行操作
- **定期备份**：重要的训练结果要定期备份

### 3. 性能优化
- **网络稳定**：确保网络连接稳定
- **浏览器兼容**：推荐使用Chrome或Firefox浏览器
- **资源监控**：关注系统资源使用情况

## 📞 技术支持

### 联系方式
- **技术支持邮箱**：<EMAIL>
- **在线文档**：查看系统帮助文档
- **问题反馈**：通过系统内置反馈功能

### 系统状态监控
- **服务状态**：实时显示在界面顶部
- **API状态**：通过健康检查接口监控
- **维护通知**：重要更新会在系统内通知

---

**提示**：本指南涵盖了系统的主要功能，更多高级功能请参考技术文档或联系技术支持。系统支持WebSocket聊天对话和8个第三方API调用的完美兼容，您可以根据需要灵活选择使用方式。
