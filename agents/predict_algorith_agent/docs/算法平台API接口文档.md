# 算法平台API接口文档

## 概述
本文档描述了部署在龙蜥Linux服务器上的算法平台的8个核心接口，用于算法生成智能体调用。

**服务器地址**: `http://***********:8081`

---

## 1. 查询全部算法名称

### 接口信息
- **接口名称**: 查询全部算法的名称
- **接口地址**: `http://***********:8081/get_all_algorithm`
- **请求方式**: GET
- **返回类型**: JSON

### 接口描述
请求当前项目的全部算法的名称。

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| algorithm_name | List | 算法名称及其所属的项目号 |
| msg | String | 请求结果说明 |

### 响应示例
```json
{
  "algorithm_name": [
    ["水泵电流预警算法0723", "285"],
    ["水泵转速预警算法0723", "285"],
    ["水泵电流预警算法0612", "280"],
    ["电流预警2", "280"],
    ["TEST", "280"],
    ["name", "280"]
  ],
  "msg": "获取成功"
}
```

---

## 2. 查询全部数据集名称

### 接口信息
- **接口名称**: 查询全部数据集的名称
- **接口地址**: `http://***********:8081/get_all_dataset`
- **请求方式**: GET
- **返回类型**: JSON

### 接口描述
请求当前项目的全部数据集的名称。

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| dataset_name | List | 全部的数据集名称和项目号 |
| msg | String | 请求结果说明 |

### 响应示例
```json
{
  "dataset_name": [
    ["水泵数据0606", "255"],
    ["水泵数据集0606", "274"],
    ["水泵数据集0611", "275"],
    ["水泵数据集0609", "276"],
    ["水泵数据集0531", "277"],
    ["水泵数据集0612", "280"],
    ["水泵数据集0723", "285"],
    ["测试数据2", "280"]
  ],
  "msg": "获取成功"
}
```

---

## 3. 训练新算法

### 接口信息
- **接口名称**: 训练新算法
- **接口地址**: `http://***********:8081/algorithm_parameter`
- **请求方式**: POST
- **返回类型**: JSON

### 接口描述
向后端发送新算法的参数，并启动该算法的训练进程。训练完成后算法名称列表就会新增对应的子数组。该方法异步执行，启动后可以在前端直接退出，不需要等待返回值。

### 请求参数
使用FormData格式发送，包含以下参数：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| parameter1 | String | 算法名称 |
| parameter2 | String | 算法类型 |
| parameter3 | String | 数据集 |
| parameter4 | String | 输入特征维度,迭代次数 |
| parameter5 | String | LSTM隐藏层的大小 |
| parameter6 | String | lstm层数 |
| parameter7 | String | lstm的dropout比例 |
| parameter8 | String | 是否使用双向lstm |
| parameter9 | String | 输出维度的个数 |
| parameter10 | String | 学习率大小 |
| parameter11 | String | 样本划分比例 |
| parameter12 | String | 优化器 |
| parameter13 | String | 损失函数 |
| parameter14 | String | 标签列 |
| parameter15 | String | 数据列 |
| parameter16 | String | 字段 |
| project_number | String | 项目编号 |

### 请求示例
```javascript
const formData = new FormData();
formData.append('parameter1', "测试算法1");
formData.append('parameter2', "风险预警");
formData.append('parameter3', "水泵数据集0612");
formData.append('parameter4', "5");
formData.append('parameter5', "64");
formData.append('parameter6', "3");
formData.append('parameter7', "0.2");
formData.append('parameter8', "YES");
formData.append('parameter9', "10");
formData.append('parameter10', "0.01");
formData.append('parameter11', "1");
formData.append('parameter12', "Adam");
formData.append('parameter13', "MSE");
formData.append('parameter14', "4");
formData.append('parameter15', "5");
formData.append('parameter16', "P11111124112047702");
formData.append('project_number', "280");
```

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| message | String | 启动结果说明 |
| algoname | String | 启动算法的名称 |
| dataset | String | 算法用的数据集 |

### 响应示例
```json
{
  "message": "启动成功",
  "algoname": "测试算法1",
  "dataset": "水泵数据集0612"
}
```

---

## 4. 查询全部IOT名称

### 接口信息
- **接口名称**: 查询全部IOT的名称
- **接口地址**: `http://***********:8081/get_all_iot?project={project_id}`
- **请求方式**: GET (带参数)
- **返回类型**: JSON

### 接口描述
获得特定项目下的全部IOT信息。

### 请求参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| project | String | 项目号参数，表示前端用户正在查看的是哪个项目 |

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| iot_info | List | IOT的数据列表，包含多个子列表，每个子列表代表该项目里面的一个IOT对象 |
| msg | String | 请求结果描述 |

### 响应示例
```json
{
  "iot_info": [
    [
      {
        "1": "data_1815320358166839298",
        "2": "data_1844651253848231937",
        "3": "data_1859849299145101313",
        "4": "data_1859850140165967873",
        "5": "device_efficiency_today_history_data"
      },
      {
        "name": "电流",
        "option": "P11111124112049197"
      },
      "285"
    ]
  ],
  "msg": "获取成功"
}
```

---

## 5. 启动监控分析服务

### 接口信息
- **接口名称**: 启动监控分析服务
- **接口地址**: `http://***********:8081/implement_parameter`
- **请求方式**: POST
- **返回类型**: JSON

### 接口描述
向后端发送参数，并启动算法的监控分析进程。训练完成后服务名称列表就会新增对应的子数组。该方法异步执行，启动后可以在前端直接退出，不需要等待返回值。

### 请求参数
使用FormData格式发送：

| 参数名 | 类型 | 说明 |
|--------|------|------|
| db_ip | String | 数据库地址 |
| db_name | String | 数据库表名 |
| list_name | String | 关键数据的字段 |
| implement_name | String | 服务名称 |
| project_name | String | 所属的项目号 |

### 请求示例
```javascript
const formData = new FormData();
formData.append('db_ip', "***********");
formData.append('db_name', "data_1815320358166839298");
formData.append('list_name', "P11111124112047702");
formData.append('implement_name', "TEST");
formData.append('project_name', "280");
```

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| msg | String | 请求成功的说明 |
| name | String | 服务名称 |

### 响应示例
```json
{
  "msg": "启动成功",
  "name": "TEST"
}
```

---

## 6. 查询算法训练信息

### 接口信息
- **接口名称**: 查询某个算法的训练信息
- **接口地址**: `http://***********:8081/get_algorithm_log?name={algorithm_name}&project={project_id}`
- **请求方式**: GET (带参数)
- **返回类型**: JSON

### 接口描述
传算法名称，返回该算法的训练JSON信息。

### 请求参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| name | String | 算法名称，表示需要查询哪个算法的训练信息，不填时会返回默认值 |
| project | String | 项目号 |

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| algorithm_log | Dict | 算法训练时的信息日志，包括训练参数、数据集名称、训练信息字典 |
| msg | String | 请求成功的说明 |

### 响应示例
```json
{
  "algorithm_log": {
    "Algorithm_parameter": "5,64,3,0.2,YES,10,0.01,1,Adam,MSE,4,5",
    "Datasetname": "水泵数据集0612",
    "MAE": 8.01021385192871,
    "MAPE": 31.9193512201309
  },
  "msg": "获取成功"
}
```

---

## 7. 查询全部服务名称

### 接口信息
- **接口名称**: 查询全部服务的名称
- **接口地址**: `http://***********:8081/get_all_implement?project={project_id}`
- **请求方式**: GET (带参数)
- **返回类型**: JSON

### 接口描述
传一个项目号，查询该项目下的所有已生成服务。每个服务都是独立运行的进程，正在监控某个数据表的特定字段的数据是否存在异常。

### 请求参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| project | String | 项目号 |

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| implement_info | List | 该项目下已生成服务的数据信息，数组中包括多个字典，每个字典都是一个服务的信息 |
| msg | String | 请求成功的标志 |

### 响应示例
```json
{
  "implement_info": [
    {
      "algorithm_name": "水泵电流预警算法0612",
      "field_value": "P11111124112014448",
      "status": "正在运行",
      "tablename": "data_1815320358166839298"
    }
  ],
  "msg": "获取成功"
}
```

---

## 8. 查询服务预测结果

### 接口信息
- **接口名称**: 查询某个服务的预测结果
- **接口地址**: `http://***********:8081/get_implement_log?name={service_name}&project={project_id}`
- **请求方式**: GET (带参数)
- **返回类型**: JSON

### 接口描述
传递服务名称和项目号，查询该服务的实时分析结论。

### 请求参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| name | String | 服务名称 |
| project | String | 项目号 |

### 返回字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| implement_info | Dict | 所生成算法的分析结论，后续要扩充该字典的内容来支持智能分析 |
| msg | String | 请求成功的标志 |

### 响应示例
```json
{
  "implement_info": {
    "probability": "96.36",
    "riskLevel": "高",
    "riskType": "周期性"
  },
  "msg": "获取成功"
}
```

---

## 接口调用说明

### 通用说明
1. **服务器地址**: 所有接口都部署在 `http://***********:8081`
2. **异步处理**: 训练新算法和启动监控分析服务为异步接口，调用后可直接退出，无需等待返回值
3. **项目管理**: 大部分接口都需要项目号参数来区分不同项目的数据
4. **数据格式**: 所有接口返回JSON格式数据，包含msg字段说明请求结果

### 调用流程建议
1. 首先调用接口1和2获取可用的算法和数据集列表
2. 使用接口3训练新算法（异步）
3. 使用接口6查询算法训练状态和结果
4. 使用接口4获取IOT信息
5. 使用接口5启动监控分析服务（异步）
6. 使用接口7查询运行中的服务
7. 使用接口8获取服务的实时预测结果

### 错误处理
- 所有接口都会返回msg字段说明请求结果
- 建议在调用前检查必要参数是否完整
- 对于异步接口，需要通过查询接口确认操作是否成功完成

---

## 技术集成分析

### 当前智能体架构分析

基于代码分析，当前智能体具有以下架构特点：

#### 1. 现有API调用模块
- **位置**: `services/history_algorithm_service.py`
- **功能**: 已实现基础的HTTP客户端框架
- **特点**: 支持真实API调用和Mock数据回退机制

#### 2. 网络通信层
- **WebSocket管理**: `network/websocket_manager.py`
- **HTTP路由**: `api/conversation_routes.py`, `api/websocket_routes.py`
- **错误处理**: `utils/error_handler.py`

#### 3. 数据模型层
- **参数推荐模型**: `models/parameter_recommendation_models.py`
- **预测模型**: `models/predictive_models.py`

### 集成建议

#### 1. 创建算法平台API客户端
建议在 `services/` 目录下创建新的服务模块：
- `algorithm_platform_service.py` - 算法平台API客户端
- `algorithm_platform_models.py` - 算法平台数据模型

#### 2. API调用流程设计
```
用户请求 → 智能体分析 → 调用算法平台API → 处理响应 → 返回结果
```

#### 3. 异步处理机制
- 训练新算法：启动后返回任务ID，定期查询状态
- 监控服务：启动后通过WebSocket推送状态更新

#### 4. 错误处理策略
- 网络超时：自动重试机制
- API错误：友好的错误信息转换
- 服务不可用：降级到Mock数据

#### 5. 缓存策略
- 算法列表：缓存30分钟
- 数据集列表：缓存1小时
- 训练状态：实时查询，不缓存

### 实现优先级

#### 高优先级（立即实现）
1. 查询接口（1,2,4,6,7,8）- 用于信息获取
2. 基础错误处理和重试机制
3. 数据模型定义

#### 中优先级（后续实现）
1. 训练新算法接口（3）- 异步处理
2. 启动监控服务接口（5）- 异步处理
3. WebSocket状态推送

#### 低优先级（优化阶段）
1. 缓存机制优化
2. 性能监控
3. 负载均衡

### 配置管理

建议在 `utils/config_manager.py` 中添加算法平台配置：

```python
class AlgorithmPlatformConfig(BaseModel):
    base_url: str = "http://***********:8081"
    timeout: int = 30
    retry_times: int = 3
    retry_delay: float = 1.0
    enable_cache: bool = True
    cache_ttl: int = 1800  # 30分钟
```

### 监控和日志

建议增强现有的日志系统：
- API调用日志：记录请求/响应时间
- 错误日志：详细的错误堆栈信息
- 性能日志：接口响应时间统计

---

## 下一步行动计划

1. **确认API文档**：请确认以上8个接口的文档描述是否准确
2. **创建API客户端**：基于确认的文档创建算法平台API客户端
3. **集成测试**：编写测试用例验证API调用功能
4. **更新智能体逻辑**：将API调用集成到现有的智能体对话流程中
5. **部署验证**：在测试环境验证完整功能
