# 前端WebSocket API快速参考

## 🔗 连接信息
```javascript
const ws = new WebSocket('ws://***********:8008/api/ws/chat?user_id=123&username=张三');
```

## 💬 原有聊天功能（完全兼容）

### 发送聊天消息
```javascript
ws.send(JSON.stringify({
    type: "chat",
    data: { message: "我想用LSTM做预测" },
    user_id: "123"
}));
```

### 接收聊天响应
```javascript
ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    if (response.type === "chat_response") {
        console.log("AI回复:", response.data.message);
    }
};
```

## ⚡ 新增API调用功能

### 1. 查询全部算法名称
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: { action: "get_all_algorithms" },
    user_id: "123"
}));
```

### 2. 查询全部数据集名称
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: { action: "get_all_datasets" },
    user_id: "123"
}));
```

### 3. 查询全部IOT名称
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: { 
        action: "get_all_iot",
        project_id: "285"  // 必需
    },
    user_id: "123"
}));
```

### 4. 查询全部服务名称
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: { 
        action: "get_all_services",
        project_id: "285"  // 必需
    },
    user_id: "123"
}));
```

### 5. 查询算法训练信息
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: {
        action: "get_algorithm_log",
        algorithm_name: "LSTM预测算法"  // 必需
    },
    user_id: "123"
}));
```

### 6. 查询服务预测结果
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: {
        action: "get_service_prediction",
        service_name: "预测服务A"  // 必需
    },
    user_id: "123"
}));
```

### 7. 训练新算法
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: {
        action: "train_algorithm",
        parameter1: "我的LSTM算法",        // 算法名称
        parameter2: "预测维护",           // 算法类型
        parameter3: "时序数据集A",        // 数据集名称
        parameter4: "10",               // 输入维度
        parameter5: "64",               // LSTM隐藏层大小
        parameter6: "3",                // LSTM层数
        parameter7: "0.2",              // Dropout比例
        parameter8: "false",            // 是否双向LSTM
        parameter9: "1",                // 输出维度
        parameter10: "0.01",            // 学习率
        parameter11: "0.8",             // 训练集比例
        parameter12: "Adam",            // 优化器
        parameter13: "MSE",             // 损失函数
        parameter14: "0",               // 标签列索引
        parameter15: "1,2,3,4,5",       // 数据列索引
        parameter16: "temperature"       // 数据字段名称
    },
    user_id: "123"
}));
```

### 8. 启动监控分析服务
```javascript
ws.send(JSON.stringify({
    type: "api_call",
    data: {
        action: "start_monitoring_service",
        db_ip: "*************",
        db_name: "sensor_data",
        list_name: "temperature,pressure",
        implement_name: "设备监控服务"
    },
    user_id: "123"
}));
```

## 📨 API响应处理

### 统一响应处理
```javascript
ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    
    switch (response.type) {
        case "chat_response":
            // 原有聊天响应
            handleChatResponse(response);
            break;
            
        case "api_response":
            // 新增API响应
            handleApiResponse(response);
            break;
            
        case "error":
            // 错误响应
            console.error("错误:", response.data.error_message);
            break;
    }
};

function handleApiResponse(response) {
    const action = response.data.action;
    const result = response.data.result;
    
    switch (action) {
        case "get_all_algorithms":
            console.log("算法列表:", result.algorithm_name);
            break;
            
        case "get_all_datasets":
            console.log("数据集列表:", result.dataset_name);
            break;
            
        case "train_algorithm":
            console.log("训练结果:", result);
            break;
            
        // 其他API处理...
    }
}
```

## 🔄 混合使用示例

### 聊天 + API调用
```javascript
// 1. 聊天咨询
ws.send(JSON.stringify({
    type: "chat",
    data: { message: "我想训练一个LSTM算法" }
}));

// 2. 查询可用算法
ws.send(JSON.stringify({
    type: "api_call",
    data: { action: "get_all_algorithms" }
}));

// 3. 继续聊天
ws.send(JSON.stringify({
    type: "chat",
    data: { message: "这些算法中哪个最适合？" }
}));
```

## 🛡️ 错误处理

### 常见错误类型
```javascript
{
    type: "error",
    data: {
        error_type: "api_error",
        error_message: "算法平台服务器连接失败",
        error_code: "PLATFORM_CONNECTION_ERROR",
        action: "get_all_algorithms"
    }
}
```

### 错误处理示例
```javascript
function handleError(response) {
    const errorType = response.data.error_type;
    const errorMessage = response.data.error_message;
    
    switch (errorType) {
        case "api_error":
            alert("API调用失败: " + errorMessage);
            break;
            
        case "validation_error":
            alert("参数验证失败: " + errorMessage);
            break;
            
        default:
            alert("系统错误: " + errorMessage);
    }
}
```

## 🔧 完整示例

### HTML页面
```html
<!DOCTYPE html>
<html>
<head>
    <title>算法智能体WebSocket客户端</title>
</head>
<body>
    <div id="messages"></div>
    
    <!-- 聊天功能 -->
    <input type="text" id="chatInput" placeholder="输入聊天消息">
    <button onclick="sendChat()">发送聊天</button>
    
    <!-- API功能 -->
    <button onclick="getAlgorithms()">查询算法</button>
    <button onclick="getDatasets()">查询数据集</button>
    <button onclick="getIOT()">查询IOT</button>
    <button onclick="getServices()">查询服务</button>
    
    <script>
        // WebSocket连接
        const ws = new WebSocket('ws://***********:8008/api/ws/chat?user_id=test123&username=测试用户');
        
        ws.onopen = () => console.log('WebSocket连接成功');
        ws.onmessage = handleMessage;
        ws.onerror = (error) => console.error('WebSocket错误:', error);
        
        // 消息处理
        function handleMessage(event) {
            const response = JSON.parse(event.data);
            const messagesDiv = document.getElementById('messages');
            
            if (response.type === 'chat_response') {
                messagesDiv.innerHTML += `<div>AI: ${response.data.message}</div>`;
            } else if (response.type === 'api_response') {
                messagesDiv.innerHTML += `<div>API结果: ${JSON.stringify(response.data.result)}</div>`;
            }
        }
        
        // 发送聊天消息
        function sendChat() {
            const input = document.getElementById('chatInput');
            ws.send(JSON.stringify({
                type: "chat",
                data: { message: input.value },
                user_id: "test123"
            }));
            input.value = '';
        }
        
        // API调用函数
        function getAlgorithms() {
            ws.send(JSON.stringify({
                type: "api_call",
                data: { action: "get_all_algorithms" },
                user_id: "test123"
            }));
        }
        
        function getDatasets() {
            ws.send(JSON.stringify({
                type: "api_call",
                data: { action: "get_all_datasets" },
                user_id: "test123"
            }));
        }
        
        function getIOT() {
            ws.send(JSON.stringify({
                type: "api_call",
                data: { action: "get_all_iot", project_id: "285" },
                user_id: "test123"
            }));
        }
        
        function getServices() {
            ws.send(JSON.stringify({
                type: "api_call",
                data: { action: "get_all_services", project_id: "285" },
                user_id: "test123"
            }));
        }
    </script>
</body>
</html>
```

## 📋 重要提醒

1. **完全向后兼容**：现有聊天功能代码无需任何修改
2. **可选集成**：新增API功能为可选，可逐步集成
3. **统一处理**：聊天和API响应使用相同的WebSocket连接
4. **错误处理**：务必处理网络错误和API调用失败的情况
5. **参数验证**：部分API需要必需参数，请注意参数完整性

---

**技术支持**：如有问题请联系后端开发团队
