#!/usr/bin/env python3
"""
修复数据库中session_id格式的脚本
将所有以"session_"开头的session_id替换为32位UUID格式
"""

import pymysql
import uuid
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET

def get_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset=MYSQL_CHARSET,
        autocommit=True
    )

def generate_uuid_session_id():
    """生成32位UUID session_id"""
    return uuid.uuid4().hex

def check_session_id_format():
    """检查数据库中session_id的格式"""
    print("🔍 检查数据库中session_id格式")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查conversations表中的session_id
            cursor.execute("""
                SELECT session_id, COUNT(*) as count
                FROM conversations 
                WHERE session_id LIKE 'session_%'
                GROUP BY session_id
                ORDER BY count DESC
            """)
            
            old_format_sessions = cursor.fetchall()
            
            if old_format_sessions:
                print(f"📊 找到 {len(old_format_sessions)} 个旧格式的session_id:")
                for session_id, count in old_format_sessions[:10]:  # 只显示前10个
                    print(f"   - {session_id}: {count} 个对话")
                if len(old_format_sessions) > 10:
                    print(f"   ... 还有 {len(old_format_sessions) - 10} 个")
            else:
                print("✅ 没有找到旧格式的session_id")
            
            # 检查新格式的session_id
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM conversations 
                WHERE session_id NOT LIKE 'session_%' AND LENGTH(session_id) = 32
            """)
            
            new_format_count = cursor.fetchone()[0]
            print(f"✅ 新格式(32位UUID)的session_id: {new_format_count} 个")
            
            cursor.close()
            return len(old_format_sessions)
            
    except Exception as e:
        print(f"❌ 检查session_id格式失败: {e}")
        return 0

def fix_session_id_format():
    """修复session_id格式"""
    print("\n🔧 开始修复session_id格式")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取所有需要修复的session_id
            cursor.execute("""
                SELECT DISTINCT session_id
                FROM conversations 
                WHERE session_id LIKE 'session_%'
            """)
            
            old_sessions = cursor.fetchall()
            
            if not old_sessions:
                print("✅ 没有需要修复的session_id")
                return
            
            print(f"📋 需要修复 {len(old_sessions)} 个session_id")
            
            # 创建session_id映射表
            session_mapping = {}
            for (old_session_id,) in old_sessions:
                new_session_id = generate_uuid_session_id()
                session_mapping[old_session_id] = new_session_id
                print(f"   {old_session_id} -> {new_session_id}")
            
            # 更新conversations表
            print("\n📝 更新conversations表...")
            for old_session_id, new_session_id in session_mapping.items():
                cursor.execute("""
                    UPDATE conversations 
                    SET session_id = %s 
                    WHERE session_id = %s
                """, (new_session_id, old_session_id))
                
                affected_rows = cursor.rowcount
                print(f"   ✅ {old_session_id}: 更新了 {affected_rows} 条记录")
            
            # 检查是否有其他表也需要更新session_id
            # 这里可以添加其他表的更新逻辑
            
            cursor.close()
            print(f"\n✅ session_id格式修复完成！")
            
    except Exception as e:
        print(f"❌ 修复session_id格式失败: {e}")

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果")
    print("=" * 60)
    
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查是否还有旧格式的session_id
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM conversations 
                WHERE session_id LIKE 'session_%'
            """)
            
            old_format_count = cursor.fetchone()[0]
            
            # 检查新格式的session_id数量
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM conversations 
                WHERE LENGTH(session_id) = 32 AND session_id NOT LIKE 'session_%'
            """)
            
            new_format_count = cursor.fetchone()[0]
            
            cursor.close()
            
            print(f"📊 验证结果:")
            print(f"   - 旧格式session_id: {old_format_count} 个")
            print(f"   - 新格式session_id: {new_format_count} 个")
            
            if old_format_count == 0:
                print("✅ 所有session_id都已更新为新格式！")
                return True
            else:
                print("❌ 还有旧格式的session_id需要处理")
                return False
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Session ID格式修复工具")
    print("=" * 60)
    
    # 测试数据库连接
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 数据库连接成功，MySQL版本: {version[0]}")
            cursor.close()
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 检查当前session_id格式
    old_count = check_session_id_format()
    
    if old_count == 0:
        print("\n🎉 所有session_id都已经是正确格式，无需修复！")
        return True
    
    # 询问是否执行修复
    print(f"\n⚠️  发现 {old_count} 个旧格式的session_id需要修复")
    response = input("是否继续执行修复？(y/N): ").strip().lower()
    
    if response != 'y':
        print("❌ 用户取消修复操作")
        return False
    
    # 执行修复
    fix_session_id_format()
    
    # 验证修复结果
    success = verify_fix()
    
    if success:
        print("\n🎉 Session ID格式修复完成！")
        print("=" * 60)
        print("📋 修复总结:")
        print("   - ✅ 所有session_id都已更新为32位UUID格式")
        print("   - ✅ 数据库数据完整性保持良好")
        print("   - ✅ 系统现在使用标准化的session_id格式")
    else:
        print("\n❌ 修复过程中出现问题，请检查日志")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
