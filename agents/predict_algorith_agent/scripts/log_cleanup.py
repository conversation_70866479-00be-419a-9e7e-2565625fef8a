#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志清理脚本
用于清理过期的日志文件，防止磁盘空间占用过多
"""

import os
import sys
import glob
import time
import argparse
from datetime import datetime, timedelta
from pathlib import Path

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    except OSError:
        return 0

def get_file_age_days(file_path):
    """获取文件年龄（天数）"""
    try:
        mtime = os.path.getmtime(file_path)
        file_time = datetime.fromtimestamp(mtime)
        age = datetime.now() - file_time
        return age.days
    except OSError:
        return 0

def cleanup_logs(log_dir, max_age_days=7, max_size_mb=1000, dry_run=False):
    """
    清理日志文件
    
    Args:
        log_dir: 日志目录
        max_age_days: 最大保留天数
        max_size_mb: 单个文件最大大小（MB）
        dry_run: 是否为试运行模式
    """
    log_dir = Path(log_dir)
    if not log_dir.exists():
        print(f"日志目录不存在: {log_dir}")
        return
    
    print(f"开始清理日志目录: {log_dir}")
    print(f"清理规则: 文件年龄 > {max_age_days}天 或 文件大小 > {max_size_mb}MB")
    print(f"模式: {'试运行' if dry_run else '实际执行'}")
    print("-" * 60)
    
    # 查找所有日志文件
    log_patterns = ['*.log', '*.log.*', '*.out', '*.err']
    total_files = 0
    total_size_mb = 0
    deleted_files = 0
    deleted_size_mb = 0
    
    for pattern in log_patterns:
        for log_file in log_dir.glob(pattern):
            if log_file.is_file():
                total_files += 1
                file_size_mb = get_file_size_mb(log_file)
                file_age_days = get_file_age_days(log_file)
                total_size_mb += file_size_mb
                
                should_delete = False
                reason = []
                
                # 检查文件年龄
                if file_age_days > max_age_days:
                    should_delete = True
                    reason.append(f"年龄{file_age_days}天")
                
                # 检查文件大小
                if file_size_mb > max_size_mb:
                    should_delete = True
                    reason.append(f"大小{file_size_mb:.1f}MB")
                
                if should_delete:
                    print(f"{'[试运行] ' if dry_run else ''}删除: {log_file.name} "
                          f"({', '.join(reason)})")
                    
                    if not dry_run:
                        try:
                            log_file.unlink()
                            deleted_files += 1
                            deleted_size_mb += file_size_mb
                        except OSError as e:
                            print(f"删除失败: {log_file.name} - {e}")
                    else:
                        deleted_files += 1
                        deleted_size_mb += file_size_mb
                else:
                    print(f"保留: {log_file.name} "
                          f"(年龄{file_age_days}天, 大小{file_size_mb:.1f}MB)")
    
    print("-" * 60)
    print(f"清理完成:")
    print(f"  总文件数: {total_files}")
    print(f"  总大小: {total_size_mb:.1f}MB")
    print(f"  {'预计' if dry_run else '实际'}删除文件数: {deleted_files}")
    print(f"  {'预计' if dry_run else '实际'}释放空间: {deleted_size_mb:.1f}MB")
    
    if dry_run:
        print("\n这是试运行模式，没有实际删除文件。")
        print("要实际执行清理，请移除 --dry-run 参数。")

def cleanup_large_files(log_dir, size_threshold_mb=100, dry_run=False):
    """
    清理超大日志文件
    
    Args:
        log_dir: 日志目录
        size_threshold_mb: 大小阈值（MB）
        dry_run: 是否为试运行模式
    """
    log_dir = Path(log_dir)
    if not log_dir.exists():
        print(f"日志目录不存在: {log_dir}")
        return
    
    print(f"查找超大日志文件 (> {size_threshold_mb}MB):")
    print("-" * 60)
    
    large_files = []
    for log_file in log_dir.rglob('*'):
        if log_file.is_file():
            file_size_mb = get_file_size_mb(log_file)
            if file_size_mb > size_threshold_mb:
                large_files.append((log_file, file_size_mb))
    
    if not large_files:
        print("没有找到超大日志文件。")
        return
    
    # 按大小排序
    large_files.sort(key=lambda x: x[1], reverse=True)
    
    total_size = sum(size for _, size in large_files)
    print(f"找到 {len(large_files)} 个超大文件，总大小: {total_size:.1f}MB")
    print()
    
    for log_file, file_size_mb in large_files:
        file_age_days = get_file_age_days(log_file)
        print(f"文件: {log_file.relative_to(log_dir)}")
        print(f"  大小: {file_size_mb:.1f}MB")
        print(f"  年龄: {file_age_days}天")
        print(f"  修改时间: {datetime.fromtimestamp(log_file.stat().st_mtime)}")
        print()

def get_disk_usage(directory):
    """获取目录磁盘使用情况"""
    directory = Path(directory)
    if not directory.exists():
        return 0, 0
    
    total_size = 0
    file_count = 0
    
    for file_path in directory.rglob('*'):
        if file_path.is_file():
            file_count += 1
            total_size += get_file_size_mb(file_path)
    
    return file_count, total_size

def main():
    parser = argparse.ArgumentParser(description='日志清理工具')
    parser.add_argument('--log-dir', default='logs', 
                       help='日志目录路径 (默认: logs)')
    parser.add_argument('--max-age', type=int, default=7,
                       help='最大保留天数 (默认: 7)')
    parser.add_argument('--max-size', type=float, default=100,
                       help='单个文件最大大小MB (默认: 100)')
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，不实际删除文件')
    parser.add_argument('--check-large', action='store_true',
                       help='只检查超大文件，不删除')
    parser.add_argument('--large-threshold', type=float, default=100,
                       help='超大文件阈值MB (默认: 100)')
    
    args = parser.parse_args()
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    log_dir = project_root / args.log_dir
    
    print("=" * 60)
    print("日志清理工具")
    print("=" * 60)
    print(f"项目根目录: {project_root}")
    print(f"日志目录: {log_dir}")
    print(f"当前时间: {datetime.now()}")
    print()
    
    # 显示当前磁盘使用情况
    file_count, total_size_mb = get_disk_usage(log_dir)
    print(f"当前日志目录状态:")
    print(f"  文件数量: {file_count}")
    print(f"  总大小: {total_size_mb:.1f}MB")
    print()
    
    if args.check_large:
        # 只检查超大文件
        cleanup_large_files(log_dir, args.large_threshold, dry_run=True)
    else:
        # 执行清理
        cleanup_logs(log_dir, args.max_age, args.max_size, args.dry_run)
        
        # 显示清理后的磁盘使用情况
        if not args.dry_run:
            print()
            file_count_after, total_size_mb_after = get_disk_usage(log_dir)
            print(f"清理后日志目录状态:")
            print(f"  文件数量: {file_count_after}")
            print(f"  总大小: {total_size_mb_after:.1f}MB")
            print(f"  节省空间: {total_size_mb - total_size_mb_after:.1f}MB")

if __name__ == "__main__":
    main()
