#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
虚拟环境cryptography包修复脚本
确保在正确的Python环境中安装cryptography
"""

import subprocess
import sys
import os

def run_command(command, description, capture_output=True):
    """运行系统命令"""
    print(f"🔍 {description}...")
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print(f"✅ {description}成功")
                if result.stdout.strip():
                    print(f"   输出: {result.stdout.strip()}")
                return True, result.stdout.strip()
            else:
                print(f"❌ {description}失败")
                if result.stderr.strip():
                    print(f"   错误: {result.stderr.strip()}")
                return False, result.stderr.strip()
        else:
            result = subprocess.run(command, shell=True, timeout=60)
            return result.returncode == 0, ""
    except subprocess.TimeoutExpired:
        print(f"⏰ {description}超时")
        return False, "timeout"
    except Exception as e:
        print(f"❌ {description}异常: {e}")
        return False, str(e)

def check_python_environment():
    """检查Python环境信息"""
    print("🐍 检查Python环境...")
    
    # 当前Python版本和路径
    python_version = sys.version
    python_path = sys.executable
    
    print(f"   Python版本: {python_version}")
    print(f"   Python路径: {python_path}")
    
    # 检查是否在虚拟环境中
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if in_venv:
        print(f"✅ 当前在虚拟环境中")
        print(f"   虚拟环境路径: {sys.prefix}")
    else:
        print(f"⚠️  当前不在虚拟环境中")
    
    return {
        'version': python_version,
        'path': python_path,
        'in_venv': in_venv,
        'prefix': sys.prefix
    }

def check_pip_environment():
    """检查pip环境"""
    print("📦 检查pip环境...")
    
    # 检查pip版本和路径
    success, pip_info = run_command("pip --version", "获取pip信息")
    if success:
        print(f"   pip信息: {pip_info}")
    
    # 检查pip安装路径
    success, pip_path = run_command("which pip", "获取pip路径")
    if success:
        print(f"   pip路径: {pip_path}")
    
    return success

def check_cryptography_status():
    """检查cryptography包状态"""
    print("🔐 检查cryptography包状态...")
    
    try:
        import cryptography
        version = cryptography.__version__
        location = cryptography.__file__
        print(f"✅ cryptography已安装")
        print(f"   版本: {version}")
        print(f"   位置: {location}")
        return True, version, location
    except ImportError:
        print(f"❌ cryptography未安装在当前Python环境中")
        return False, None, None

def install_cryptography():
    """在当前环境中安装cryptography"""
    print("🔧 在当前Python环境中安装cryptography...")
    
    # 使用当前Python的pip安装
    python_executable = sys.executable
    install_command = f"{python_executable} -m pip install cryptography"
    
    print(f"   执行命令: {install_command}")
    success, output = run_command(install_command, "安装cryptography")
    
    if success:
        print("✅ cryptography安装成功")
        # 验证安装
        try:
            import importlib
            importlib.invalidate_caches()
            import cryptography
            print(f"✅ 验证成功，版本: {cryptography.__version__}")
            return True
        except ImportError:
            print("❌ 安装后验证失败")
            return False
    else:
        print("❌ cryptography安装失败")
        return False

def check_virtual_env_activation():
    """检查虚拟环境激活脚本"""
    print("🔍 检查虚拟环境激活...")
    
    # 查找activate_env.sh
    activate_files = [
        "activate_env.sh",
        "../activate_env.sh",
        "../../activate_env.sh",
        "/root/agent/activate_env.sh"
    ]
    
    found_activate = None
    for activate_file in activate_files:
        if os.path.exists(activate_file):
            found_activate = activate_file
            print(f"✅ 找到激活脚本: {activate_file}")
            break
    
    if not found_activate:
        print("⚠️  未找到activate_env.sh脚本")
        print("   请确保在正确的目录中运行此脚本")
    
    return found_activate

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 虚拟环境cryptography包修复工具")
    print("=" * 60)
    
    # 1. 检查Python环境
    python_env = check_python_environment()
    
    # 2. 检查pip环境
    pip_ok = check_pip_environment()
    
    # 3. 检查cryptography状态
    crypto_installed, crypto_version, crypto_location = check_cryptography_status()
    
    # 4. 检查虚拟环境激活
    activate_script = check_virtual_env_activation()
    
    print("\n" + "=" * 60)
    print("📊 环境检查结果:")
    print(f"   Python环境: {'虚拟环境' if python_env['in_venv'] else '系统环境'}")
    print(f"   Python版本: {sys.version.split()[0]}")
    print(f"   pip可用: {'✅' if pip_ok else '❌'}")
    print(f"   cryptography: {'✅' if crypto_installed else '❌'}")
    
    if crypto_installed:
        print(f"   cryptography版本: {crypto_version}")
        print("🎉 cryptography已正确安装在当前环境中!")
        print("\n建议重启算法智能体服务:")
        print("   ./manage_server.sh restart")
        return True
    
    # 如果cryptography未安装，尝试安装
    print("\n🔧 cryptography未安装，尝试修复...")
    
    if not python_env['in_venv']:
        print("⚠️  当前不在虚拟环境中!")
        if activate_script:
            print(f"请先激活虚拟环境:")
            print(f"   source {activate_script}")
            print("然后重新运行此脚本")
        else:
            print("请手动激活虚拟环境后重新运行此脚本")
        return False
    
    # 在虚拟环境中安装cryptography
    if install_cryptography():
        print("\n🎉 修复完成!")
        print("建议重启算法智能体服务:")
        print("   ./manage_server.sh restart")
        return True
    else:
        print("\n❌ 修复失败")
        print("请尝试手动安装:")
        print(f"   {sys.executable} -m pip install cryptography")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
