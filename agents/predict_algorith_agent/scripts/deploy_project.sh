#!/bin/bash
# 项目部署脚本
# 将agents/predict_algorith_agent部署到/data/agent目录

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
SOURCE_DIR="agents/predict_algorith_agent"
BACKUP_DIR="/data/agent/backups"
PYTHON_VERSION="3.10.12"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查源代码目录（已手动拷贝，跳过检查）
check_source_directory() {
    log_info "跳过源代码目录检查（文件已手动拷贝到服务器）..."
    log_info "将直接验证服务器上的项目文件结构"
}

# 检查Python环境
check_python_environment() {
    log_info "检查Python环境..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先运行 install_python310.sh"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/activate_env.sh" ]; then
        log_error "环境激活脚本不存在"
        log_info "请先运行 install_python310.sh"
        exit 1
    fi
    
    log_success "Python环境检查通过"
}

# 备份现有部署
backup_existing_deployment() {
    log_info "备份现有部署..."
    
    if [ -d "$PROJECT_DIR/agents" ]; then
        # 创建备份目录
        mkdir -p $BACKUP_DIR
        
        # 创建备份
        backup_name="agents_backup_$(date +%Y%m%d_%H%M%S)"
        cp -r "$PROJECT_DIR/agents" "$BACKUP_DIR/$backup_name"
        
        log_success "现有部署已备份到: $BACKUP_DIR/$backup_name"
    else
        log_info "没有现有部署需要备份"
    fi
}

# 部署项目文件
deploy_project_files() {
    log_info "验证项目文件部署状态..."

    # 验证必要的目录结构是否存在
    if [ ! -d "$PROJECT_DIR/agents" ]; then
        log_error "agents目录不存在: $PROJECT_DIR/agents"
        log_info "请确保已手动拷贝agents目录到服务器"
        exit 1
    fi

    if [ ! -d "$PROJECT_DIR/agents/predict_algorith_agent" ]; then
        log_error "predict_algorith_agent目录不存在: $PROJECT_DIR/agents/predict_algorith_agent"
        log_info "请确保已手动拷贝完整的agents/predict_algorith_agent目录到服务器"
        exit 1
    fi

    # 验证关键文件是否存在
    if [ ! -f "$PROJECT_DIR/agents/config.py" ]; then
        log_error "config.py文件不存在: $PROJECT_DIR/agents/config.py"
        log_info "请确保已手动拷贝config.py文件到agents目录"
        exit 1
    fi

    if [ ! -f "$PROJECT_DIR/agents/predict_algorith_agent/predict_main.py" ]; then
        log_error "predict_main.py文件不存在: $PROJECT_DIR/agents/predict_algorith_agent/predict_main.py"
        log_info "请确保已手动拷贝predict_main.py文件到agents/predict_algorith_agent目录"
        exit 1
    fi

    # 创建logs目录（如果不存在）
    mkdir -p "$PROJECT_DIR/logs"

    # 设置文件权限
    log_info "设置文件权限..."
    chown -R $USER:$USER "$PROJECT_DIR/agents"
    chmod -R 755 "$PROJECT_DIR/agents"

    if [ -d "$PROJECT_DIR/core" ]; then
        chown -R $USER:$USER "$PROJECT_DIR/core"
        chmod -R 755 "$PROJECT_DIR/core"
    fi

    chown -R $USER:$USER "$PROJECT_DIR/logs"
    chmod -R 755 "$PROJECT_DIR/logs"

    log_success "项目文件验证和权限设置完成"
    log_info "✅ 已验证agents/predict_algorith_agent目录结构"
    log_info "✅ 已验证config.py文件存在"
    log_info "✅ 已验证predict_main.py文件存在"
}

# 安装项目依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    cd $PROJECT_DIR
    
    # 激活Python环境
    source activate_env.sh
    
    # 检查requirements文件
    if [ -f "agents/requirement.txt" ]; then
        log_info "安装agents/requirement.txt中的依赖..."
        pip install -r agents/requirement.txt
    elif [ -f "agents/requirements.txt" ]; then
        log_info "安装agents/requirements.txt中的依赖..."
        pip install -r agents/requirements.txt
    else
        log_warning "未找到requirements文件，安装基础依赖..."
        pip install fastapi uvicorn pydantic pydantic_ai requests jinja2 \
                   pdfkit redis sqlalchemy pymysql python-dotenv coreapi \
                   gunicorn supervisor websockets
    fi
    
    # 安装额外的生产环境依赖
    log_info "安装生产环境依赖..."
    pip install gunicorn supervisor
    
    log_success "依赖安装完成"
}

# 配置项目设置
configure_project() {
    log_info "配置项目设置..."
    
    cd $PROJECT_DIR
    
    # 创建配置文件备份
    if [ -f "agents/config.py" ]; then
        cp agents/config.py agents/config.py.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 创建环境变量文件
    cat > .env << 'EOF'
# 项目环境变量
PROJECT_ROOT=/data/agent
PYTHONPATH=/data/agent
PYTHONIOENCODING=utf-8

# 服务配置
PREDICT_MAIN_HOST=0.0.0.0
PREDICT_MAIN_PORT=8008

# 数据库配置（请根据实际情况修改）
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-password
MYSQL_DB=your-database
MYSQL_CHARSET=utf8mb4

# Redis配置（请根据实际情况修改）
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password
REDIS_DECODE_RESPONSES=True

# 路径配置
WKHTMLTOPDF_PATH=/usr/bin/wkhtmltopdf
REPORT_PDF_DIR=/tmp/reports

# API配置（请根据实际情况修改）
ALGORITHM_API_BASE=http://your-algorithm-api/api
TRAING_API_BASE=http://your-training-api/api
HISTORY_ALGORITHM_API_BASE=http://your-history-api/api
EOF
    
    # 创建必要的目录
    sudo mkdir -p /tmp/reports
    sudo chown $USER:$USER /tmp/reports
    sudo chmod 755 /tmp/reports
    
    log_success "项目配置完成"
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."
    
    cat > $PROJECT_DIR/start_agent.sh << 'EOF'
#!/bin/bash
# 算法智能体启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 切换到项目目录
cd /data/agent

# 激活Python环境
source activate_env.sh

# 设置环境变量
export PROJECT_ROOT=/data/agent
export PYTHONPATH=/data/agent:$PYTHONPATH
export PYTHONIOENCODING=utf-8

log_info "🚀 启动算法智能体服务..."
log_info "📁 项目目录: /data/agent"
log_info "🐍 Python版本: $(python --version)"

# 导入并启动应用
python -c "
import sys
import os
sys.path.insert(0, '/data/agent')

try:
    # 从predict_main.py导入app
    from agents.predict_algorith_agent.predict_main import app
    from agents.config import PREDICT_MAIN_HOST, PREDICT_MAIN_PORT
    import uvicorn

    print(f'🌐 服务将在 http://{PREDICT_MAIN_HOST}:{PREDICT_MAIN_PORT} 启动')
    print(f'📄 使用启动文件: agents/predict_algorith_agent/predict_main.py')

    uvicorn.run(
        app,
        host=PREDICT_MAIN_HOST,
        port=PREDICT_MAIN_PORT,
        log_level='info',
        access_log=True
    )

except Exception as e:
    print(f'❌ 启动失败: {e}')
    print(f'💡 请确保predict_main.py文件存在于: /data/agent/agents/predict_algorith_agent/')
    import traceback
    traceback.print_exc()
    sys.exit(1)
"
EOF
    
    chmod +x $PROJECT_DIR/start_agent.sh
    log_success "启动脚本创建完成: $PROJECT_DIR/start_agent.sh"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    cd $PROJECT_DIR
    
    # 激活环境
    source activate_env.sh
    
    # 测试模块导入
    python -c "
import sys
sys.path.insert(0, '/data/agent')

try:
    # 测试配置文件导入
    from agents.config import PREDICT_MAIN_HOST, PREDICT_MAIN_PORT
    print('✅ agents.config 导入成功')

    # 测试核心响应模块（如果存在）
    try:
        from core.response import Response
        print('✅ core.response 导入成功')
    except ImportError:
        print('⚠️  core.response 不存在，跳过测试')

    # 测试智能体核心模块
    try:
        from agents.predict_algorith_agent.core.predictive_agent import PredictiveAlgorithmAssistant
        print('✅ 智能体核心模块导入成功')
    except ImportError as e:
        print(f'⚠️  智能体核心模块导入失败: {e}')

    # 测试主启动模块
    from agents.predict_algorith_agent.predict_main import app
    print('✅ predict_main.py 导入成功')

    print('🎉 关键模块导入测试通过!')

except ImportError as e:
    print(f'❌ 关键模块导入失败: {e}')
    print(f'💡 请检查文件路径: /data/agent/agents/predict_algorith_agent/predict_main.py')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        log_success "部署测试通过"
    else
        log_error "部署测试失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始项目部署验证和配置..."
    log_info "注意: 项目文件已手动拷贝到服务器"
    log_info "目标目录: $PROJECT_DIR"
    
    # 检查源代码目录
    check_source_directory
    
    # 检查Python环境
    check_python_environment
    
    # 备份现有部署
    backup_existing_deployment
    
    # 部署项目文件
    deploy_project_files
    
    # 安装依赖
    install_dependencies
    
    # 配置项目
    configure_project
    
    # 创建启动脚本
    create_startup_script
    
    # 测试部署
    test_deployment
    
    log_success "项目部署完成！"
    log_info ""
    log_info "使用方法："
    log_info "1. 修改配置: nano $PROJECT_DIR/.env"
    log_info "2. 测试启动: $PROJECT_DIR/start_agent.sh"
    log_info "3. 生产部署: 运行 setup_systemd_service.sh"
    log_info ""
    log_info "项目结构："
    log_info "📁 $PROJECT_DIR/"
    log_info "├── agents/predict_algorith_agent/"
    log_info "├── core/"
    log_info "├── venv/"
    log_info "├── logs/"
    log_info "├── activate_env.sh"
    log_info "├── start_agent.sh"
    log_info "└── .env"
}

# 执行主函数
main "$@"
