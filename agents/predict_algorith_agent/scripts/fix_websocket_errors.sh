#!/bin/bash
# WebSocket错误修复脚本
# 用于清理循环连接和重启服务

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_DIR="/data/agent"
SERVER_PORT="8008"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 强制停止所有相关进程
force_stop_all() {
    log_info "强制停止所有相关进程..."
    
    # 停止Python进程（包含predict_main.py）
    pkill -f "predict_main.py" 2>/dev/null || true
    pkill -f "uvicorn.*predict_main" 2>/dev/null || true
    
    # 停止可能的WebSocket测试进程
    pkill -f "test_websocket" 2>/dev/null || true
    pkill -f "websockets.connect" 2>/dev/null || true
    
    # 停止日志监控进程
    pkill -f "tail.*server.log" 2>/dev/null || true
    
    # 等待进程完全停止
    sleep 3
    
    # 检查端口占用
    if netstat -tlnp | grep ":$SERVER_PORT " > /dev/null; then
        log_warning "端口 $SERVER_PORT 仍被占用，尝试强制释放..."
        
        # 获取占用端口的进程ID
        pid=$(netstat -tlnp | grep ":$SERVER_PORT " | awk '{print $7}' | cut -d'/' -f1 | head -1)
        if [ ! -z "$pid" ] && [ "$pid" != "-" ]; then
            log_info "强制终止进程 PID: $pid"
            kill -9 "$pid" 2>/dev/null || true
            sleep 2
        fi
    fi
    
    log_success "进程清理完成"
}

# 清理PID文件和临时文件
cleanup_files() {
    log_info "清理PID文件和临时文件..."
    
    cd "$PROJECT_DIR" 2>/dev/null || {
        log_error "无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 清理PID文件
    rm -f server.pid
    rm -f log_monitor.pid
    
    # 清理临时测试文件
    rm -f temp_ws_test.py
    rm -f temp_websocket_test.py
    
    # 清理可能的锁文件
    rm -f *.lock
    
    log_success "文件清理完成"
}

# 检查和修复日志文件
fix_log_files() {
    log_info "检查和修复日志文件..."
    
    cd "$PROJECT_DIR" 2>/dev/null || return 1
    
    # 创建日志目录
    mkdir -p logs
    
    # 检查日志文件大小
    if [ -f "logs/server.log" ]; then
        file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
        file_size_mb=$((file_size / 1024 / 1024))
        
        if [ "$file_size_mb" -gt 100 ]; then
            log_warning "日志文件过大 (${file_size_mb}MB)，进行备份和清理..."
            
            # 备份大日志文件
            mv logs/server.log "logs/server.log.backup.$(date +%Y%m%d_%H%M%S)"
            
            # 创建新的日志文件
            touch logs/server.log
            chmod 644 logs/server.log
            
            log_success "日志文件已备份和重置"
        else
            log_info "日志文件大小正常 (${file_size_mb}MB)"
        fi
    else
        # 创建日志文件
        touch logs/server.log
        chmod 644 logs/server.log
        log_info "日志文件已创建"
    fi
}

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    cd "$PROJECT_DIR" 2>/dev/null || return 1
    
    # 激活环境
    if [ -f "activate_env.sh" ]; then
        source activate_env.sh 2>/dev/null || true
    fi
    
    # 检查关键依赖
    python3 -c "
import sys
try:
    import fastapi
    import uvicorn
    import websockets
    print('✅ 关键依赖检查通过')
except ImportError as e:
    print(f'❌ 依赖缺失: {e}')
    sys.exit(1)
" || {
        log_error "Python依赖检查失败"
        return 1
    }
    
    log_success "Python环境检查通过"
}

# 安全重启服务
safe_restart() {
    log_info "安全重启服务..."
    
    cd "$PROJECT_DIR" 2>/dev/null || {
        log_error "无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 使用管理脚本重启
    if [ -f "manage_server.sh" ]; then
        ./manage_server.sh stop 2>/dev/null || true
        sleep 3
        ./manage_server.sh start
    else
        log_error "管理脚本不存在"
        return 1
    fi
}

# 验证修复结果
verify_fix() {
    log_info "验证修复结果..."
    
    cd "$PROJECT_DIR" 2>/dev/null || return 1
    
    # 等待服务启动
    sleep 5
    
    # 检查端口监听
    if netstat -tlnp | grep ":$SERVER_PORT " > /dev/null; then
        log_success "服务端口正在监听"
    else
        log_error "服务端口未监听"
        return 1
    fi
    
    # 检查HTTP健康
    if curl -s "http://localhost:$SERVER_PORT/health" > /dev/null; then
        log_success "HTTP健康检查通过"
    else
        log_error "HTTP健康检查失败"
        return 1
    fi
    
    # 检查WebSocket健康
    if curl -s "http://localhost:$SERVER_PORT/api/ws/health" > /dev/null; then
        log_success "WebSocket健康检查通过"
    else
        log_error "WebSocket健康检查失败"
        return 1
    fi
    
    log_success "修复验证通过"
}

# 主修复流程
main() {
    echo "🔧 WebSocket错误修复工具"
    echo "📍 目标: 修复WebSocket连接循环错误"
    echo "📂 项目目录: $PROJECT_DIR"
    echo "=" * 50
    
    # 1. 强制停止所有进程
    force_stop_all
    
    # 2. 清理文件
    cleanup_files
    
    # 3. 修复日志文件
    fix_log_files
    
    # 4. 检查Python环境
    if ! check_python_env; then
        log_error "Python环境检查失败，请手动修复依赖"
        exit 1
    fi
    
    # 5. 安全重启服务
    if ! safe_restart; then
        log_error "服务重启失败"
        exit 1
    fi
    
    # 6. 验证修复结果
    if ! verify_fix; then
        log_error "修复验证失败"
        exit 1
    fi
    
    echo "=" * 50
    log_success "WebSocket错误修复完成！"
    echo ""
    echo "🎯 修复内容:"
    echo "  ✅ 停止了所有循环连接进程"
    echo "  ✅ 清理了PID文件和临时文件"
    echo "  ✅ 修复了日志文件问题"
    echo "  ✅ 重启了服务"
    echo "  ✅ 验证了服务状态"
    echo ""
    echo "🔍 后续建议:"
    echo "  - 运行 ./manage_server.sh test 测试WebSocket连接"
    echo "  - 运行 ./manage_server.sh logs 查看服务日志"
    echo "  - 避免同时运行多个WebSocket测试"
    echo "=" * 50
}

# 执行修复
main "$@"
