#!/bin/bash

# 算法智能体自动部署脚本
# 适用于Ubuntu 20.04+ / CentOS 7+ / RHEL 8+
# 作者: 算法智能体团队
# 版本: 1.0
# 日期: 2025-07-28

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本！"
        log_info "建议创建专用用户: sudo useradd -m -s /bin/bash algorithm-agent"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统版本"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 2 ]]; then
        log_warn "内存不足2GB，建议至少4GB内存"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_GB -lt 10 ]]; then
        log_error "磁盘空间不足10GB"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 安装系统依赖
install_system_deps() {
    log_step "安装系统依赖..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y python3.10 python3.10-venv python3.10-dev python3-pip \
                           git curl wget build-essential libmysqlclient-dev pkg-config \
                           wkhtmltopdf redis-tools mysql-client
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum update -y
        sudo yum install -y python310 python310-pip python310-devel \
                           git curl wget gcc gcc-c++ make mysql-devel pkgconfig \
                           wkhtmltopdf redis mysql
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_info "系统依赖安装完成"
}

# 创建项目目录结构
create_project_structure() {
    log_step "创建项目目录结构..."
    
    PROJECT_ROOT="$HOME/projects/twinbuilder-agents"
    
    mkdir -p "$PROJECT_ROOT"
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/backups"
    mkdir -p "$PROJECT_ROOT/tmp"
    
    # 创建报告目录
    sudo mkdir -p /tmp/reports
    sudo chown $USER:$USER /tmp/reports
    sudo chmod 755 /tmp/reports
    
    log_info "项目目录结构创建完成: $PROJECT_ROOT"
}

# 设置Python虚拟环境
setup_python_env() {
    log_step "设置Python虚拟环境..."
    
    cd "$PROJECT_ROOT"
    
    # 创建虚拟环境
    python3.10 -m venv venv
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip setuptools wheel
    
    log_info "Python虚拟环境设置完成"
}

# 安装Python依赖
install_python_deps() {
    log_step "安装Python依赖..."
    
    cd "$PROJECT_ROOT"
    source venv/bin/activate
    
    # 安装基础依赖
    pip install fastapi uvicorn pydantic pydantic_ai requests jinja2 pdfkit \
                redis sqlalchemy pymysql python-dotenv coreapi gunicorn
    
    # 验证关键包
    python -c "import fastapi; print('FastAPI:', fastapi.__version__)"
    python -c "import pydantic_ai; print('Pydantic AI: OK')"
    python -c "import pymysql; print('PyMySQL: OK')"
    python -c "import redis; print('Redis: OK')"
    
    log_info "Python依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    # 创建环境变量文件
    cat > ~/.bashrc_algorithm << EOF
# 算法智能体环境变量
export PROJECT_ROOT=$PROJECT_ROOT
export PYTHONPATH=\$PROJECT_ROOT:\$PYTHONPATH
export PYTHONIOENCODING=utf-8
EOF
    
    # 添加到bashrc
    if ! grep -q "source ~/.bashrc_algorithm" ~/.bashrc; then
        echo "source ~/.bashrc_algorithm" >> ~/.bashrc
    fi
    
    # 立即加载环境变量
    source ~/.bashrc_algorithm
    
    log_info "环境变量配置完成"
}

# 创建配置文件模板
create_config_template() {
    log_step "创建配置文件模板..."
    
    cd "$PROJECT_ROOT"
    
    # 创建配置文件模板
    cat > config_template.py << 'EOF'
# 算法智能体配置文件模板
# 请根据实际环境修改以下配置

# 控制是否输出deepseek大模型的思考过程
SHOW_DEEPSEEK_THOUGHT = False

# 算法生成器API Base url
ALGORITHM_API_BASE = "http://your-algorithm-api/api"

# 训练环境API Base url
TRAING_API_BASE = "http://your-training-api/api"

# 历史算法API配置
HISTORY_ALGORITHM_API_BASE = "http://your-history-api/api"
HISTORY_ALGORITHM_API_KEY = None

# predict_main监听配置
PREDICT_MAIN_HOST = "0.0.0.0"
PREDICT_MAIN_PORT = 8008

# MySQL数据库连接配置 - 请修改为实际配置
MYSQL_HOST = "your-mysql-host"
MYSQL_PORT = 3306
MYSQL_USER = "your-mysql-user"
MYSQL_PASSWORD = "your-mysql-password"
MYSQL_DB = "your-database"
MYSQL_CHARSET = "utf8mb4"

# Redis数据库连接配置 - 请修改为实际配置
REDIS_HOST = "your-redis-host"
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = "your-redis-password"
REDIS_DECODE_RESPONSES = True

# Linux环境路径配置
WKHTMLTOPDF_PATH = "/usr/bin/wkhtmltopdf"
REPORT_PDF_DIR = "/tmp/reports"
EOF
    
    log_info "配置文件模板创建完成: $PROJECT_ROOT/config_template.py"
    log_warn "请根据实际环境修改配置文件！"
}

# 创建启动脚本
create_startup_scripts() {
    log_step "创建启动脚本..."
    
    cd "$PROJECT_ROOT"
    
    # 创建主启动脚本
    cat > start_algorithm_agent.py << 'EOF'
#!/usr/bin/env python3
"""
算法智能体启动脚本
解决部署环境下的路径和导入问题
"""
import os
import sys
from pathlib import Path

# 修复Python路径
project_root = Path(__file__).parent.resolve()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['PROJECT_ROOT'] = str(project_root)
os.environ['PYTHONPATH'] = str(project_root)
os.environ['PYTHONIOENCODING'] = 'utf-8'

print(f"🚀 启动算法智能体服务...")
print(f"📁 项目根目录: {project_root}")
print(f"🐍 Python版本: {sys.version}")

# 导入并启动应用
try:
    from agents.predict_algorith_agent.predict_main import app
    from agents.config import PREDICT_MAIN_HOST, PREDICT_MAIN_PORT
    import uvicorn
    
    print(f"🌐 服务将在 http://{PREDICT_MAIN_HOST}:{PREDICT_MAIN_PORT} 启动")
    
    # 启动服务
    uvicorn.run(
        app,
        host=PREDICT_MAIN_HOST,
        port=PREDICT_MAIN_PORT,
        log_level="info",
        access_log=True
    )
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF
    
    # 创建数据库连接测试脚本
    cat > test_db_connection.py << 'EOF'
#!/usr/bin/env python3
import sys
import os

# 设置项目路径
sys.path.insert(0, os.getcwd())

try:
    from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
    import pymysql
    
    print("正在测试MySQL连接...")
    connection = pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset='utf8mb4'
    )
    print("✅ MySQL连接成功!")
    connection.close()
    
except Exception as e:
    print(f"❌ MySQL连接失败: {e}")
    sys.exit(1)

try:
    from agents.config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
    import redis
    
    print("正在测试Redis连接...")
    r = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        decode_responses=True
    )
    r.ping()
    print("✅ Redis连接成功!")
    
except Exception as e:
    print(f"❌ Redis连接失败: {e}")
    sys.exit(1)

print("🎉 所有数据库连接测试通过!")
EOF
    
    # 设置执行权限
    chmod +x start_algorithm_agent.py
    chmod +x test_db_connection.py
    
    log_info "启动脚本创建完成"
}

# 创建Gunicorn配置
create_gunicorn_config() {
    log_step "创建Gunicorn配置..."
    
    cd "$PROJECT_ROOT"
    
    cat > gunicorn.conf.py << 'EOF'
# Gunicorn配置文件
import multiprocessing

# 服务器socket
bind = "0.0.0.0:8008"
backlog = 2048

# Worker进程
workers = min(multiprocessing.cpu_count() * 2 + 1, 8)
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 30
keepalive = 2

# 重启
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# 日志
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# 进程命名
proc_name = "algorithm-agent"
EOF
    
    log_info "Gunicorn配置创建完成"
}

# 创建systemd服务
create_systemd_service() {
    log_step "创建systemd服务..."
    
    sudo tee /etc/systemd/system/algorithm-agent.service << EOF
[Unit]
Description=Algorithm Agent Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=notify
User=$USER
Group=$USER
WorkingDirectory=$PROJECT_ROOT
Environment=PROJECT_ROOT=$PROJECT_ROOT
Environment=PYTHONPATH=$PROJECT_ROOT
Environment=PYTHONIOENCODING=utf-8
ExecStart=$PROJECT_ROOT/venv/bin/gunicorn -c gunicorn.conf.py start_algorithm_agent:app
ExecReload=/bin/kill -s HUP \$MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    sudo systemctl enable algorithm-agent
    
    log_info "systemd服务创建完成"
}

# 创建备份脚本
create_backup_script() {
    log_step "创建备份脚本..."
    
    cd "$PROJECT_ROOT"
    
    cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="$HOME/projects/twinbuilder-agents/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份代码
tar -czf $BACKUP_DIR/code_$DATE.tar.gz -C ~/projects twinbuilder-agents

# 备份配置
if [[ -f agents/config.py ]]; then
    cp agents/config.py $BACKUP_DIR/config_$DATE.py
fi

# 清理旧备份（保留最近7天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "config_*.py" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR"
EOF
    
    chmod +x backup.sh
    
    log_info "备份脚本创建完成"
}

# 显示部署后续步骤
show_next_steps() {
    log_step "部署完成！后续步骤："
    
    echo ""
    echo "📋 接下来需要手动完成以下步骤："
    echo ""
    echo "1. 上传代码文件到项目目录："
    echo "   cd $PROJECT_ROOT"
    echo "   # 使用scp、rsync或git上传agents和core目录"
    echo ""
    echo "2. 配置数据库连接："
    echo "   cp config_template.py agents/config.py"
    echo "   nano agents/config.py"
    echo "   # 修改MySQL和Redis连接配置"
    echo ""
    echo "3. 测试数据库连接："
    echo "   source venv/bin/activate"
    echo "   python test_db_connection.py"
    echo ""
    echo "4. 启动服务："
    echo "   sudo systemctl start algorithm-agent"
    echo "   sudo systemctl status algorithm-agent"
    echo ""
    echo "5. 验证服务："
    echo "   curl http://localhost:8008/health"
    echo ""
    echo "📁 项目目录: $PROJECT_ROOT"
    echo "📝 配置模板: $PROJECT_ROOT/config_template.py"
    echo "🚀 启动脚本: $PROJECT_ROOT/start_algorithm_agent.py"
    echo "🔧 服务管理: sudo systemctl {start|stop|restart|status} algorithm-agent"
    echo ""
}

# 主函数
main() {
    log_info "开始部署算法智能体系统..."
    
    check_root
    detect_os
    check_requirements
    install_system_deps
    create_project_structure
    setup_python_env
    install_python_deps
    setup_environment
    create_config_template
    create_startup_scripts
    create_gunicorn_config
    create_systemd_service
    create_backup_script
    show_next_steps
    
    log_info "自动部署脚本执行完成！"
}

# 执行主函数
main "$@"
