#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的DatabaseManager
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_database_manager():
    """测试DatabaseManager类"""
    print("🔍 测试修复后的DatabaseManager...")
    
    try:
        from agents.predict_algorith_agent.database.database_manager import DatabaseManager
        
        # 创建实例
        db_manager = DatabaseManager()
        print("✅ DatabaseManager实例创建成功")
        
        # 测试连接配置
        print(f"   主配置: {db_manager.connection_config}")
        print(f"   备用配置: {db_manager.fallback_config}")
        
        # 测试连接
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                print(f"✅ 数据库连接测试成功: {result}")
                return True
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入DatabaseManager失败: {e}")
        return False
    except Exception as e:
        print(f"❌ DatabaseManager测试异常: {e}")
        return False

def test_syntax():
    """测试语法是否正确"""
    print("🔍 测试Python语法...")
    
    try:
        import py_compile
        py_compile.compile('agents/predict_algorith_agent/database/database_manager.py', doraise=True)
        print("✅ Python语法检查通过")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Python语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 语法检查异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 DatabaseManager修复验证")
    print("=" * 50)
    
    # 1. 语法检查
    syntax_ok = test_syntax()
    
    # 2. 功能测试
    function_ok = test_database_manager()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   语法检查: {'✅' if syntax_ok else '❌'}")
    print(f"   功能测试: {'✅' if function_ok else '❌'}")
    
    if syntax_ok and function_ok:
        print("\n🎉 DatabaseManager修复成功!")
    elif syntax_ok:
        print("\n⚠️  语法正确，但数据库连接仍有问题")
    else:
        print("\n❌ 代码存在语法错误，需要进一步修复")
    
    return syntax_ok and function_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
