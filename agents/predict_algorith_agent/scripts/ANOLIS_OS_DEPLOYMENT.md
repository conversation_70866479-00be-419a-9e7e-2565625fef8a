# Anolis OS 8.6 专用部署指南

## 📋 概述

本指南专门针对**Anolis OS 8.6（龙蜥操作系统）**进行优化，提供在远程服务器 *********** 上部署算法智能体的完整解决方案。

## 🎯 系统信息

- **目标服务器**: ***********
- **操作系统**: Anolis OS 8.6（龙蜥操作系统）
- **系统架构**: 兼容RHEL/CentOS生态
- **包管理器**: dnf（主要）/ yum（兼容）
- **系统Python**: 3.6.8（保持不变）
- **项目Python**: 3.10.12（通过pyenv安装）

## 🚀 快速部署

### 一键部署（推荐）
```bash
# 1. 上传脚本到Anolis OS服务器
scp -r agents/predict_algorith_agent/scripts/ username@***********:/tmp/

# 2. 在服务器上执行
ssh username@***********
cd /tmp/scripts
chmod +x *.sh
./one_click_deploy.sh
```

### 分步骤部署
```bash
# 1. 环境准备（安装pyenv和依赖）
./setup_environment.sh
source ~/.bashrc

# 2. 安装Python 3.10
./install_python310.sh

# 3. 部署项目代码
./deploy_project.sh

# 4. 配置系统服务
./setup_systemd_service.sh
```

## 🔧 Anolis OS 特定配置

### 包管理器使用
```bash
# Anolis OS 8.6 使用dnf作为主要包管理器
sudo dnf update
sudo dnf groupinstall "Development Tools"
sudo dnf install gcc openssl-devel bzip2-devel libffi-devel

# 也可以使用yum（兼容命令）
sudo yum update
sudo yum groupinstall "Development Tools"
```

### 系统服务管理
```bash
# Anolis OS使用systemd管理服务
sudo systemctl enable algorithm-agent
sudo systemctl start algorithm-agent
sudo systemctl status algorithm-agent
```

### 防火墙配置
```bash
# Anolis OS使用firewalld
sudo firewall-cmd --permanent --add-port=8008/tcp
sudo firewall-cmd --reload
sudo firewall-cmd --list-ports
```

## 📦 依赖包安装

### 编译依赖
```bash
# Python编译所需的依赖包
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y gcc openssl-devel bzip2-devel libffi-devel \
    zlib-devel readline-devel sqlite-devel wget curl llvm \
    ncurses-devel xz-devel tk-devel git
```

### 应用依赖
```bash
# PDF生成工具
sudo dnf install wkhtmltopdf -y

# 数据库客户端
sudo dnf install mysql-devel -y

# 其他工具
sudo dnf install htop net-tools -y
```

## 🐍 Python环境管理

### pyenv安装验证
```bash
# 验证pyenv安装
pyenv --version
pyenv versions

# 查看可用的Python版本
pyenv install --list | grep "3.10"
```

### Python 3.10安装验证
```bash
# 验证Python 3.10安装
cd /data/agent
source activate_env.sh
python --version  # 应该显示 Python 3.10.12
pip --version
```

## 🌐 网络配置

### 端口开放
```bash
# 开放应用端口
sudo firewall-cmd --permanent --add-port=8008/tcp
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
netstat -tlnp | grep 8008
```

### SELinux配置
```bash
# 检查SELinux状态
sestatus

# 如果启用了SELinux，可能需要配置策略
sudo setsebool -P httpd_can_network_connect 1
```

## 📁 目录结构

### Anolis OS上的标准部署结构
```
/data/agent/                    # 项目根目录
├── agents/                     # 项目代码
│   ├── config.py              # 配置文件
│   ├── requirement.txt        # 依赖列表
│   └── predict_algorith_agent/ # 智能体代码
├── core/                      # 核心模块
├── venv/                      # Python虚拟环境
├── logs/                      # 日志目录
├── backups/                   # 备份目录
├── .env                       # 环境变量
├── activate_env.sh            # 环境激活脚本
├── start_agent.sh             # 启动脚本
├── manage_service.sh          # 服务管理脚本
├── gunicorn.conf.py           # Gunicorn配置
└── start_gunicorn.py          # Gunicorn启动脚本
```

## 🔍 验证部署

### 系统验证
```bash
# 验证操作系统
cat /etc/os-release | grep "Anolis"

# 验证Python环境
cd /data/agent
source activate_env.sh
python --version
pip list | grep fastapi
```

### 服务验证
```bash
# 验证服务状态
sudo systemctl status algorithm-agent

# 验证端口监听
sudo netstat -tlnp | grep 8008

# 验证API响应
curl http://localhost:8008/health
```

### 功能验证
```bash
# 验证API文档
curl -I http://localhost:8008/docs

# 验证前端页面
curl -I http://localhost:8008/static/html/chat_interface_v2.html

# 验证数据库连接
cd /data/agent
source activate_env.sh
python test_db_connection.py
```

## 🛠️ 故障排除

### 常见问题

#### 1. dnf命令失败
```bash
# 检查网络连接
ping -c 3 mirrors.openanolis.cn

# 清理dnf缓存
sudo dnf clean all
sudo dnf makecache
```

#### 2. Python编译失败
```bash
# 检查编译依赖
sudo dnf groupinstall "Development Tools"
sudo dnf install gcc openssl-devel

# 重新安装Python
pyenv uninstall 3.10.12
pyenv install 3.10.12
```

#### 3. 服务启动失败
```bash
# 查看详细日志
sudo journalctl -u algorithm-agent -f

# 检查配置文件
cd /data/agent
source activate_env.sh
python -c "from agents.config import *; print('配置加载成功')"
```

#### 4. 防火墙问题
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 临时关闭防火墙（仅用于测试）
sudo systemctl stop firewalld

# 重新配置防火墙
sudo firewall-cmd --permanent --add-port=8008/tcp
sudo firewall-cmd --reload
```

## 📞 技术支持

### 日志位置
- **应用日志**: `/data/agent/logs/`
- **系统日志**: `sudo journalctl -u algorithm-agent`
- **系统消息**: `/var/log/messages`

### 配置文件
- **环境变量**: `/data/agent/.env`
- **服务配置**: `/etc/systemd/system/algorithm-agent.service`
- **Gunicorn配置**: `/data/agent/gunicorn.conf.py`

### 有用的命令
```bash
# 查看系统信息
uname -a
cat /etc/os-release

# 查看资源使用
htop
df -h
free -h

# 查看网络状态
ss -tlnp
netstat -an | grep 8008

# 查看进程
ps aux | grep python
ps aux | grep gunicorn
```

## ✅ 部署完成确认

部署完成后，确认以下项目：

- [ ] Anolis OS 8.6系统确认
- [ ] Python 3.10.12安装成功
- [ ] 项目代码部署到/data/agent
- [ ] 虚拟环境创建成功
- [ ] 依赖包安装完成
- [ ] 系统服务配置完成
- [ ] 服务启动成功
- [ ] 端口8008监听正常
- [ ] API接口响应正常
- [ ] 前端页面可访问
- [ ] 数据库连接正常

---

**专为Anolis OS 8.6（龙蜥操作系统）优化**  
**服务器地址**: ***********  
**部署路径**: /data/agent
