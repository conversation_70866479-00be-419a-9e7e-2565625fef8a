#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket连接测试脚本 - 适用于远程Linux服务器***********
修复user_id参数缺失问题
"""

import asyncio
import websockets
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WebSocketTester:
    """WebSocket连接测试器"""
    
    def __init__(self, host: str = "***********", port: int = 8008):
        self.host = host
        self.port = port
        self.websocket = None
        self.connected = False
        
    def get_websocket_url(self, user_id: str = "test_user", username: str = "测试用户") -> str:
        """构建正确的WebSocket连接URL，包含必需的user_id参数"""
        session_id = f"test_session_{int(time.time())}"
        return f"ws://{self.host}:{self.port}/api/ws/chat?session_id={session_id}&user_id={user_id}&username={username}"
    
    async def connect(self, user_id: str = "test_user", username: str = "测试用户") -> bool:
        """建立WebSocket连接"""
        try:
            url = self.get_websocket_url(user_id, username)
            logger.info(f"🔗 连接WebSocket: {url}")
            
            self.websocket = await websockets.connect(url)
            self.connected = True
            
            logger.info("✅ WebSocket连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ WebSocket连接失败: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket and self.connected:
            await self.websocket.close()
            self.connected = False
            logger.info("🔌 WebSocket连接已断开")
    
    async def send_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送消息并等待响应"""
        if not self.connected or not self.websocket:
            logger.error("❌ WebSocket未连接")
            return None
        
        try:
            # 发送消息
            message_json = json.dumps(message, ensure_ascii=False)
            logger.info(f"📤 发送消息: {message_json}")
            
            await self.websocket.send(message_json)
            
            # 等待响应
            response_text = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            response = json.loads(response_text)
            
            logger.info(f"📥 收到响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return response
            
        except asyncio.TimeoutError:
            logger.error("❌ 响应超时")
            return None
        except Exception as e:
            logger.error(f"❌ 发送消息失败: {e}")
            return None
    
    async def test_welcome_message(self) -> bool:
        """测试欢迎消息接收"""
        logger.info("🧪 测试欢迎消息接收...")

        try:
            # 连接后应该自动收到欢迎消息
            welcome_text = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            welcome = json.loads(welcome_text)

            if welcome.get("type") == "welcome":
                logger.info("✅ 欢迎消息接收成功")
                logger.info(f"📋 欢迎内容: {welcome.get('data', {}).get('message', '')[:100]}...")
                return True
            else:
                logger.warning(f"⚠️ 收到非欢迎消息: {welcome.get('type')}")
                # 可能是其他类型的消息，不算失败
                return True

        except asyncio.TimeoutError:
            logger.warning("⚠️ 欢迎消息接收超时，可能服务器没有发送欢迎消息")
            return True  # 不算失败，继续测试
        except Exception as e:
            logger.error(f"❌ 欢迎消息接收失败: {e}")
            return False
    
    async def test_ping_pong(self) -> bool:
        """测试心跳机制"""
        logger.info("🧪 测试心跳机制...")
        
        ping_message = {
            "type": "ping",
            "data": {"timestamp": datetime.now().isoformat()}
        }
        
        response = await self.send_message(ping_message)
        
        if response and response.get("type") == "pong":
            logger.info("✅ 心跳测试成功")
            return True
        else:
            logger.error("❌ 心跳测试失败")
            return False
    
    async def test_chat_message(self) -> bool:
        """测试聊天消息"""
        logger.info("🧪 测试聊天消息...")
        
        chat_message = {
            "type": "chat",
            "data": {
                "message": "你好，我想测试WebSocket连接",
                "project_id": "285"
            }
        }
        
        response = await self.send_message(chat_message)
        
        if response and response.get("type") == "chat_response":
            logger.info("✅ 聊天消息测试成功")
            return True
        else:
            logger.error("❌ 聊天消息测试失败")
            return False
    
    async def test_api_call(self) -> bool:
        """测试API调用"""
        logger.info("🧪 测试API调用...")
        
        api_message = {
            "type": "api_call",
            "data": {
                "action": "get_all_algorithms"
            }
        }
        
        response = await self.send_message(api_message)
        
        if response and response.get("type") == "api_response":
            logger.info("✅ API调用测试成功")
            return True
        else:
            logger.error("❌ API调用测试失败")
            return False
    
    async def run_comprehensive_test(self) -> Dict[str, bool]:
        """运行综合测试"""
        logger.info("🚀 开始WebSocket综合测试")
        logger.info(f"📍 目标服务器: {self.host}:{self.port}")
        
        results = {
            "connection": False,
            "welcome": False,
            "ping_pong": False,
            "chat": False,
            "api_call": False
        }
        
        try:
            # 1. 测试连接
            if await self.connect():
                results["connection"] = True
                
                # 2. 测试欢迎消息
                results["welcome"] = await self.test_welcome_message()
                await asyncio.sleep(1)
                
                # 3. 测试心跳
                results["ping_pong"] = await self.test_ping_pong()
                await asyncio.sleep(1)
                
                # 4. 测试聊天
                results["chat"] = await self.test_chat_message()
                await asyncio.sleep(1)
                
                # 5. 测试API调用
                results["api_call"] = await self.test_api_call()
                
        except Exception as e:
            logger.error(f"❌ 测试过程异常: {e}")
        finally:
            await self.disconnect()
        
        return results

def print_test_results(results: Dict[str, bool]):
    """打印测试结果"""
    print("\n" + "=" * 60)
    print("📊 WebSocket测试结果总结")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        test_desc = {
            "connection": "WebSocket连接",
            "welcome": "欢迎消息接收",
            "ping_pong": "心跳机制",
            "chat": "聊天消息",
            "api_call": "API调用"
        }.get(test_name, test_name)
        
        print(f"  {test_desc}: {status}")
    
    print("=" * 60)
    
    # 总体评估
    success_count = sum(results.values())
    total_count = len(results)
    
    if success_count == total_count:
        print("🎉 所有测试通过！WebSocket服务运行正常")
    elif success_count > 0:
        print(f"⚠️ 部分测试通过 ({success_count}/{total_count})，需要检查失败项")
    else:
        print("❌ 所有测试失败，WebSocket服务存在问题")
    
    print("=" * 60)

async def main():
    """主测试函数"""
    print("🧪 WebSocket连接测试工具")
    print("📍 适用于远程Linux服务器 ***********")
    print("🔧 修复user_id参数缺失问题")
    print("=" * 60)
    
    tester = WebSocketTester()
    results = await tester.run_comprehensive_test()
    
    print_test_results(results)
    
    # 根据结果给出建议
    if not results["connection"]:
        print("\n💡 连接失败排查建议:")
        print("  1. 检查服务器是否启动: ./manage_server.sh status")
        print("  2. 检查端口是否监听: netstat -tlnp | grep :8008")
        print("  3. 检查防火墙设置")
        print("  4. 查看服务器日志: ./manage_server.sh logs")
    elif not all(results.values()):
        print("\n💡 功能测试失败排查建议:")
        print("  1. 查看服务器日志: ./manage_server.sh logs")
        print("  2. 检查数据库连接")
        print("  3. 检查大模型配置")
        print("  4. 重启服务: ./manage_server.sh restart")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        traceback.print_exc()
