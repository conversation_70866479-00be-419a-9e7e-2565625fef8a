@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ==========================================
REM 241服务器WebSocket修复部署脚本 (Windows版本)
REM ==========================================

echo ==========================================
echo 241服务器WebSocket修复部署脚本
echo ==========================================

REM 配置变量
set SERVER_IP=*************
set SERVER_USER=root
set PROJECT_NAME=twinbuilder-agents
set REMOTE_PROJECT_PATH=/opt/%PROJECT_NAME%
set SERVICE_NAME=predict-algorithm-agent
set SERVICE_PORT=8008

echo 服务器IP: %SERVER_IP%
echo 项目路径: %REMOTE_PROJECT_PATH%
echo 服务名称: %SERVICE_NAME%
echo 服务端口: %SERVICE_PORT%
echo.

REM 检查必要工具
echo [步骤] 检查必要工具...
where ssh >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到SSH工具，请安装OpenSSH或Git Bash
    pause
    exit /b 1
)

where scp >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到SCP工具，请安装OpenSSH或Git Bash
    pause
    exit /b 1
)

echo [成功] 工具检查完成
echo.

REM 确认部署
set /p CONFIRM="确认要部署到 %SERVER_IP% 吗? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo 部署已取消
    pause
    exit /b 0
)

echo.
echo [步骤] 开始部署流程...
echo.

REM 检查SSH连接
echo [步骤] 检查SSH连接...
ssh -o ConnectTimeout=10 %SERVER_USER%@%SERVER_IP% "echo 'SSH连接成功'" >nul 2>&1
if errorlevel 1 (
    echo [错误] SSH连接失败，请检查网络和认证
    echo 请确保：
    echo 1. 服务器IP地址正确: %SERVER_IP%
    echo 2. SSH密钥已配置或可以密码登录
    echo 3. 网络连接正常
    pause
    exit /b 1
)
echo [成功] SSH连接正常
echo.

REM 备份现有服务
echo [步骤] 备份现有服务...
ssh %SERVER_USER%@%SERVER_IP% "BACKUP_DIR=/opt/backup/$(date +%%Y%%m%%d_%%H%%M%%S); mkdir -p ${BACKUP_DIR}; if systemctl is-active --quiet predict-algorithm-agent; then systemctl stop predict-algorithm-agent; fi; if [ -d '/opt/twinbuilder-agents' ]; then cp -r /opt/twinbuilder-agents ${BACKUP_DIR}/; echo '备份完成: ${BACKUP_DIR}/twinbuilder-agents'; fi"
if errorlevel 1 (
    echo [错误] 备份失败
    pause
    exit /b 1
)
echo [成功] 备份完成
echo.

REM 创建代码压缩包
echo [步骤] 创建代码压缩包...
set TEMP_ARCHIVE=%TEMP%\%PROJECT_NAME%_fixed_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.tar.gz
set TEMP_ARCHIVE=%TEMP_ARCHIVE: =0%

REM 使用tar命令创建压缩包（需要Windows 10 1803+或Git Bash）
cd /d "%~dp0..\..\..\"
tar -czf "%TEMP_ARCHIVE%" --exclude=".git" --exclude="__pycache__" --exclude="*.pyc" --exclude=".venv" --exclude="logs/*" --exclude="*.log" agents/
if errorlevel 1 (
    echo [错误] 创建压缩包失败，请确保安装了tar工具或使用Git Bash
    pause
    exit /b 1
)
echo [成功] 压缩包创建完成
echo.

REM 上传代码
echo [步骤] 上传代码到服务器...
scp "%TEMP_ARCHIVE%" %SERVER_USER%@%SERVER_IP%:/tmp/
if errorlevel 1 (
    echo [错误] 代码上传失败
    del "%TEMP_ARCHIVE%" >nul 2>&1
    pause
    exit /b 1
)

REM 在服务器上解压和配置
echo [步骤] 在服务器上解压和配置...
ssh %SERVER_USER%@%SERVER_IP% "mkdir -p %REMOTE_PROJECT_PATH%; cd %REMOTE_PROJECT_PATH%; tar -xzf /tmp/$(basename '%TEMP_ARCHIVE%'); rm /tmp/$(basename '%TEMP_ARCHIVE%'); chown -R %SERVER_USER%:%SERVER_USER% %REMOTE_PROJECT_PATH%; chmod +x %REMOTE_PROJECT_PATH%/agents/predict_algorith_agent/scripts/*.sh; echo '代码解压完成'"
if errorlevel 1 (
    echo [错误] 服务器端配置失败
    del "%TEMP_ARCHIVE%" >nul 2>&1
    pause
    exit /b 1
)

REM 清理本地临时文件
del "%TEMP_ARCHIVE%" >nul 2>&1
echo [成功] 代码上传完成
echo.

REM 安装依赖和配置环境
echo [步骤] 安装依赖和配置环境...
ssh %SERVER_USER%@%SERVER_IP% "cd %REMOTE_PROJECT_PATH%; pip3 install fastapi uvicorn websockets pymysql python-dotenv pydantic; cd %REMOTE_PROJECT_PATH%/agents/predict_algorith_agent; if [ ! -f '.env.production' ]; then cat > .env.production << 'EOF'
# 生产环境配置文件
PRODUCTION_MODE=true
LOG_LEVEL=WARNING
ENABLE_FILE_LOGGING=true
LOG_FILE_PATH=logs/agent_production.log
SERVER_HOST=0.0.0.0
SERVER_PORT=8008
DB_HOST=***********
DB_PORT=3306
DB_NAME=indusaio_agent
DB_USER=root
DB_PASSWORD=Spsm2021+
EOF
fi; mkdir -p logs; chmod 600 .env.production; echo '环境配置完成'"
if errorlevel 1 (
    echo [错误] 依赖安装或环境配置失败
    pause
    exit /b 1
)
echo [成功] 依赖安装和环境配置完成
echo.

REM 创建systemd服务
echo [步骤] 创建systemd服务...
ssh %SERVER_USER%@%SERVER_IP% "cat > /etc/systemd/system/%SERVICE_NAME%.service << 'EOF'
[Unit]
Description=Predict Algorithm Agent WebSocket Service
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=%REMOTE_PROJECT_PATH%/agents/predict_algorith_agent
Environment=PYTHONPATH=%REMOTE_PROJECT_PATH%
Environment=PYTHONUNBUFFERED=1
ExecStart=/usr/bin/python3 predict_main.py
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
systemctl daemon-reload; systemctl enable %SERVICE_NAME%; echo 'systemd服务创建完成'"
if errorlevel 1 (
    echo [错误] systemd服务创建失败
    pause
    exit /b 1
)
echo [成功] systemd服务创建完成
echo.

REM 启动服务
echo [步骤] 启动服务...
ssh %SERVER_USER%@%SERVER_IP% "systemctl start %SERVICE_NAME%; sleep 5; if systemctl is-active --quiet %SERVICE_NAME%; then echo '服务启动成功'; systemctl status %SERVICE_NAME% --no-pager -l; else echo '服务启动失败'; systemctl status %SERVICE_NAME% --no-pager -l; journalctl -u %SERVICE_NAME% --no-pager -l -n 20; exit 1; fi"
if errorlevel 1 (
    echo [错误] 服务启动失败
    pause
    exit /b 1
)
echo [成功] 服务启动成功
echo.

REM 运行验证测试
echo [步骤] 运行验证测试...
ssh %SERVER_USER%@%SERVER_IP% "cd %REMOTE_PROJECT_PATH%/agents/predict_algorith_agent/scripts; python3 test_database_connection.py; sleep 10; if netstat -tlnp | grep :%SERVICE_PORT%; then echo '服务端口 %SERVICE_PORT% 正在监听'; else echo '警告: 服务端口 %SERVICE_PORT% 未监听'; fi; if pgrep -f 'predict_main.py'; then echo '服务进程运行正常'; else echo '警告: 未找到服务进程'; fi"
echo [成功] 验证测试完成
echo.

REM 显示部署结果
echo [步骤] 获取部署结果...
ssh %SERVER_USER%@%SERVER_IP% "echo '==========================================='; echo '部署结果总结'; echo '==========================================='; echo '服务状态:'; systemctl is-active %SERVICE_NAME% && echo '  ✅ 服务运行中' || echo '  ❌ 服务未运行'; echo '端口监听:'; netstat -tlnp | grep :%SERVICE_PORT% && echo '  ✅ 端口 %SERVICE_PORT% 正在监听' || echo '  ❌ 端口未监听'; echo '日志文件:'; if [ -f '%REMOTE_PROJECT_PATH%/agents/predict_algorith_agent/logs/agent_production.log' ]; then echo '  ✅ 日志文件存在'; echo '  最新日志:'; tail -5 %REMOTE_PROJECT_PATH%/agents/predict_algorith_agent/logs/agent_production.log; else echo '  ⚠️ 日志文件不存在'; fi; echo ''; echo 'WebSocket连接地址: ws://%SERVER_IP%:%SERVICE_PORT%/ws'; echo '服务管理命令:'; echo '  启动: systemctl start %SERVICE_NAME%'; echo '  停止: systemctl stop %SERVICE_NAME%'; echo '  重启: systemctl restart %SERVICE_NAME%'; echo '  状态: systemctl status %SERVICE_NAME%'; echo '  日志: journalctl -u %SERVICE_NAME% -f'"

echo.
echo ===========================================
echo 部署完成！
echo ===========================================
echo WebSocket服务地址: ws://%SERVER_IP%:%SERVICE_PORT%/ws
echo 服务管理: systemctl [start^|stop^|restart^|status] %SERVICE_NAME%
echo 查看日志: journalctl -u %SERVICE_NAME% -f
echo.
echo 建议接下来：
echo 1. 测试WebSocket连接
echo 2. 监控服务日志
echo 3. 进行功能验证
echo 4. 如有问题，可使用备份快速回滚
echo.

REM 询问是否运行WebSocket测试
set /p TEST_WS="是否运行WebSocket连接测试? (y/N): "
if /i "%TEST_WS%"=="y" (
    echo.
    echo [步骤] 运行WebSocket连接测试...
    python "%~dp0test_websocket_241_deployment.py" %SERVER_IP%
    if errorlevel 1 (
        echo [警告] WebSocket测试未完全通过，请检查服务状态
    ) else (
        echo [成功] WebSocket测试通过
    )
)

echo.
echo 部署脚本执行完成！
pause
