#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库表结构差异检查脚本
用于对比本地数据库和远程241服务器数据库的表结构差异
"""

import pymysql
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSchemaChecker:
    """数据库表结构检查器"""
    
    def __init__(self):
        # 本地数据库配置
        self.local_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'your_password',  # 请替换为实际密码
            'database': 'indusaio_agent',
            'charset': 'utf8mb4'
        }
        
        # 远程241服务器数据库配置
        self.remote_config = {
            'host': '*************',  # 请替换为实际IP
            'port': 3306,
            'user': 'your_user',      # 请替换为实际用户名
            'password': 'your_password',  # 请替换为实际密码
            'database': 'indusaio_agent',
            'charset': 'utf8mb4'
        }
        
        # 需要检查的核心表
        self.core_tables = [
            'conversations',
            'conversation_messages', 
            'session_states',
            'algorithm_tasks',
            'task_parameters',
            'training_logs',
            'task_results',
            'context_summaries',
            'agent_session'  # 可能存在的旧表
        ]
    
    def get_connection(self, config: Dict) -> pymysql.Connection:
        """获取数据库连接"""
        try:
            return pymysql.connect(**config)
        except Exception as e:
            logger.error(f"数据库连接失败: {config['host']}:{config['port']} - {e}")
            raise
    
    def get_table_structure(self, connection: pymysql.Connection, table_name: str) -> Optional[Dict]:
        """获取表结构信息"""
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 检查表是否存在
                cursor.execute("SHOW TABLES LIKE %s", (table_name,))
                if not cursor.fetchone():
                    return None
                
                # 获取表结构
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                # 获取索引信息
                cursor.execute(f"SHOW INDEX FROM {table_name}")
                indexes = cursor.fetchall()
                
                # 获取建表语句
                cursor.execute(f"SHOW CREATE TABLE {table_name}")
                create_table = cursor.fetchone()
                
                return {
                    'columns': columns,
                    'indexes': indexes,
                    'create_table': create_table['Create Table'] if create_table else None
                }
        except Exception as e:
            logger.error(f"获取表结构失败 {table_name}: {e}")
            return None
    
    def get_all_tables(self, connection: pymysql.Connection) -> List[str]:
        """获取数据库中所有表名"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SHOW TABLES")
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取表列表失败: {e}")
            return []
    
    def compare_columns(self, local_cols: List[Dict], remote_cols: List[Dict]) -> Dict:
        """对比表字段差异"""
        local_col_dict = {col['Field']: col for col in local_cols}
        remote_col_dict = {col['Field']: col for col in remote_cols}
        
        differences = {
            'missing_in_local': [],      # 远程有但本地没有的字段
            'missing_in_remote': [],     # 本地有但远程没有的字段
            'type_differences': [],      # 字段类型不同
            'null_differences': [],      # 是否允许NULL不同
            'default_differences': []    # 默认值不同
        }
        
        # 检查缺失的字段
        for field_name in remote_col_dict:
            if field_name not in local_col_dict:
                differences['missing_in_local'].append({
                    'field': field_name,
                    'remote_definition': remote_col_dict[field_name]
                })
        
        for field_name in local_col_dict:
            if field_name not in remote_col_dict:
                differences['missing_in_remote'].append({
                    'field': field_name,
                    'local_definition': local_col_dict[field_name]
                })
        
        # 检查字段差异
        for field_name in set(local_col_dict.keys()) & set(remote_col_dict.keys()):
            local_col = local_col_dict[field_name]
            remote_col = remote_col_dict[field_name]
            
            if local_col['Type'] != remote_col['Type']:
                differences['type_differences'].append({
                    'field': field_name,
                    'local_type': local_col['Type'],
                    'remote_type': remote_col['Type']
                })
            
            if local_col['Null'] != remote_col['Null']:
                differences['null_differences'].append({
                    'field': field_name,
                    'local_null': local_col['Null'],
                    'remote_null': remote_col['Null']
                })
            
            if local_col['Default'] != remote_col['Default']:
                differences['default_differences'].append({
                    'field': field_name,
                    'local_default': local_col['Default'],
                    'remote_default': remote_col['Default']
                })
        
        return differences
    
    def check_schema_differences(self) -> Dict:
        """检查数据库表结构差异"""
        logger.info("开始检查数据库表结构差异...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'local_config': f"{self.local_config['host']}:{self.local_config['port']}/{self.local_config['database']}",
            'remote_config': f"{self.remote_config['host']}:{self.remote_config['port']}/{self.remote_config['database']}",
            'table_differences': {},
            'connection_status': {
                'local': False,
                'remote': False
            },
            'summary': {
                'total_tables_checked': 0,
                'tables_with_differences': 0,
                'missing_tables': [],
                'extra_tables': []
            }
        }
        
        try:
            # 连接本地数据库
            logger.info("连接本地数据库...")
            local_conn = self.get_connection(self.local_config)
            results['connection_status']['local'] = True
            logger.info("本地数据库连接成功")
            
            # 连接远程数据库
            logger.info("连接远程数据库...")
            remote_conn = self.get_connection(self.remote_config)
            results['connection_status']['remote'] = True
            logger.info("远程数据库连接成功")
            
            # 获取所有表列表
            local_tables = set(self.get_all_tables(local_conn))
            remote_tables = set(self.get_all_tables(remote_conn))
            
            logger.info(f"本地数据库表数量: {len(local_tables)}")
            logger.info(f"远程数据库表数量: {len(remote_tables)}")
            
            # 检查表差异
            results['summary']['missing_tables'] = list(remote_tables - local_tables)
            results['summary']['extra_tables'] = list(local_tables - remote_tables)
            
            # 检查核心表结构
            for table_name in self.core_tables:
                if table_name in local_tables and table_name in remote_tables:
                    logger.info(f"检查表结构: {table_name}")
                    
                    local_structure = self.get_table_structure(local_conn, table_name)
                    remote_structure = self.get_table_structure(remote_conn, table_name)
                    
                    if local_structure and remote_structure:
                        # 对比字段差异
                        column_diff = self.compare_columns(
                            local_structure['columns'],
                            remote_structure['columns']
                        )
                        
                        if any(column_diff.values()):
                            results['table_differences'][table_name] = {
                                'column_differences': column_diff,
                                'local_columns_count': len(local_structure['columns']),
                                'remote_columns_count': len(remote_structure['columns'])
                            }
                            results['summary']['tables_with_differences'] += 1
                            logger.warning(f"表 {table_name} 存在结构差异")
                        else:
                            logger.info(f"表 {table_name} 结构一致")
                    
                    results['summary']['total_tables_checked'] += 1
                
                elif table_name in local_tables:
                    logger.warning(f"表 {table_name} 仅存在于本地数据库")
                elif table_name in remote_tables:
                    logger.warning(f"表 {table_name} 仅存在于远程数据库")
                else:
                    logger.warning(f"表 {table_name} 在两个数据库中都不存在")
            
            # 关闭连接
            local_conn.close()
            remote_conn.close()
            
        except Exception as e:
            logger.error(f"检查过程中发生错误: {e}")
            results['error'] = str(e)
        
        return results
    
    def generate_report(self, results: Dict) -> str:
        """生成差异报告"""
        report = []
        report.append("=" * 80)
        report.append("数据库表结构差异检查报告")
        report.append("=" * 80)
        report.append(f"检查时间: {results['timestamp']}")
        report.append(f"本地数据库: {results['local_config']}")
        report.append(f"远程数据库: {results['remote_config']}")
        report.append("")
        
        # 连接状态
        report.append("连接状态:")
        report.append(f"  本地数据库: {'✓ 成功' if results['connection_status']['local'] else '✗ 失败'}")
        report.append(f"  远程数据库: {'✓ 成功' if results['connection_status']['remote'] else '✗ 失败'}")
        report.append("")
        
        # 汇总信息
        summary = results['summary']
        report.append("检查汇总:")
        report.append(f"  检查表数量: {summary['total_tables_checked']}")
        report.append(f"  存在差异的表: {summary['tables_with_differences']}")
        report.append(f"  缺失的表: {len(summary['missing_tables'])}")
        report.append(f"  多余的表: {len(summary['extra_tables'])}")
        report.append("")
        
        # 缺失和多余的表
        if summary['missing_tables']:
            report.append("⚠️  本地缺失的表:")
            for table in summary['missing_tables']:
                report.append(f"    - {table}")
            report.append("")
        
        if summary['extra_tables']:
            report.append("ℹ️  本地多余的表:")
            for table in summary['extra_tables']:
                report.append(f"    - {table}")
            report.append("")
        
        # 表结构差异详情
        if results['table_differences']:
            report.append("🔍 表结构差异详情:")
            report.append("")
            
            for table_name, diff in results['table_differences'].items():
                report.append(f"📋 表: {table_name}")
                col_diff = diff['column_differences']
                
                if col_diff['missing_in_local']:
                    report.append("  ❌ 本地缺失的字段:")
                    for field in col_diff['missing_in_local']:
                        report.append(f"    - {field['field']}: {field['remote_definition']['Type']}")
                
                if col_diff['missing_in_remote']:
                    report.append("  ➕ 本地多余的字段:")
                    for field in col_diff['missing_in_remote']:
                        report.append(f"    - {field['field']}: {field['local_definition']['Type']}")
                
                if col_diff['type_differences']:
                    report.append("  🔄 字段类型差异:")
                    for field in col_diff['type_differences']:
                        report.append(f"    - {field['field']}: 本地({field['local_type']}) vs 远程({field['remote_type']})")
                
                report.append("")
        
        if not results['table_differences']:
            report.append("✅ 所有检查的表结构都一致!")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("数据库表结构差异检查工具")
    print("=" * 50)
    
    # 提示用户配置数据库连接
    print("⚠️  请先在脚本中配置正确的数据库连接信息:")
    print("   - 本地数据库连接信息")
    print("   - 远程241服务器数据库连接信息")
    print("")
    
    confirm = input("是否已配置数据库连接信息? (y/N): ").strip().lower()
    if confirm != 'y':
        print("请先配置数据库连接信息后再运行此脚本")
        return
    
    checker = DatabaseSchemaChecker()
    
    try:
        # 执行检查
        results = checker.check_schema_differences()
        
        # 生成报告
        report = checker.generate_report(results)
        print(report)
        
        # 保存报告到文件
        report_file = f"database_schema_diff_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 保存JSON格式的详细结果
        json_file = f"database_schema_diff_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"📊 详细数据已保存到: {json_file}")
        
    except Exception as e:
        logger.error(f"检查失败: {e}")
        print(f"\n❌ 检查失败: {e}")

if __name__ == "__main__":
    main()
