#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志优化效果演示脚本
展示优化前后的日志输出差异
"""

import os
import sys
import time
import tempfile
import shutil
from pathlib import Path

def demo_before_optimization():
    """演示优化前的日志输出（大量DEBUG日志）"""
    print("🔴 优化前的日志输出（模拟）:")
    print("-" * 60)
    
    # 模拟大量DEBUG日志
    debug_logs = [
        "2025-08-04 09:00:01,123 - agents.predict_algorith_agent.core.predictive_agent - DEBUG - 🔍 [调试] 进入新对话处理 - 用户输入: 我想训练LSTM算法",
        "2025-08-04 09:00:01,124 - agents.predict_algorith_agent.core.predictive_agent - DEBUG - 🔍 [调试] 匹配预设问题，返回预设回答",
        "2025-08-04 09:00:01,125 - agents.predict_algorith_agent.core.predictive_agent - INFO - 🔧 [参数提取Agent] 🌐 正在向LLM发送请求...",
        "2025-08-04 09:00:01,126 - agents.predict_algorith_agent.core.predictive_agent - INFO - 🔧 [参数提取Agent] ✅ LLM请求完成",
        "2025-08-04 09:00:01,127 - agents.predict_algorith_agent.core.predictive_agent - INFO - 🔧 [参数提取Agent] 📊 LLM返回结果类型: <class 'dict'>",
        "2025-08-04 09:00:01,128 - agents.predict_algorith_agent.core.predictive_agent - INFO - 🔧 [参数提取Agent] 📊 提取到的参数: {'learning_rate': 0.01, 'batch_size': 32}",
        "2025-08-04 09:00:01,129 - agents.predict_algorith_agent.core.predictive_agent - INFO - 🔧 [参数提取Agent] 📊 缺失的参数: ['epochs', 'hidden_size']",
        "2025-08-04 09:00:01,130 - agents.predict_algorith_agent.core.predictive_agent - INFO - 🔧 [参数提取Agent] ✅ 参数提取微调模型调用成功",
        "2025-08-04 09:00:01,131 - agents.predict_algorith_agent.api.conversation_routes - DEBUG - === 对话管理路由模块加载 ===",
        "2025-08-04 09:00:01,132 - agents.predict_algorith_agent.api.conversation_routes - DEBUG - 路由器创建完成，准备注册路由...",
        "2025-08-04 09:00:01,133 - agents.predict_algorith_agent.network.websocket_manager - DEBUG - WebSocket连接建立: session_123",
        "2025-08-04 09:00:01,134 - agents.predict_algorith_agent.network.websocket_manager - DEBUG - 处理消息: {'type': 'chat', 'data': {...}}",
        "2025-08-04 09:00:01,135 - uvicorn.access - INFO - 127.0.0.1:12345 - \"GET /api/ws/chat HTTP/1.1\" 101 -",
        "2025-08-04 09:00:01,136 - asyncio - DEBUG - Using selector: SelectSelector",
        "2025-08-04 09:00:01,137 - httpx - DEBUG - HTTP Request: POST https://api.example.com/v1/chat/completions",
    ]
    
    for log in debug_logs:
        print(log)
        time.sleep(0.05)  # 模拟实时输出
    
    print()
    print(f"📊 优化前统计:")
    print(f"   - 日志级别: DEBUG")
    print(f"   - 每分钟日志行数: ~500-1000行")
    print(f"   - 每小时日志大小: ~50-100MB")
    print(f"   - 每天日志大小: ~1-2GB")
    print(f"   - 一周累积: ~7-14GB")
    print()

def demo_after_optimization():
    """演示优化后的日志输出（只有重要信息）"""
    print("🟢 优化后的日志输出:")
    print("-" * 60)
    
    # 模拟优化后的简洁日志
    optimized_logs = [
        "2025-08-04 09:00:01,123 - [MAIN] - INFO - 主应用启动完成，总路由数量: 25",
        "2025-08-04 09:00:01,124 - [WS] - WARNING - WebSocket连接异常重试: session_123",
        "2025-08-04 09:00:01,125 - [AGENT] - ERROR - 🔧 [参数提取Agent] ❌ 参数提取失败: Connection timeout",
        "2025-08-04 09:00:01,126 - [DB] - ERROR - 数据库连接失败: Connection refused",
    ]
    
    for log in optimized_logs:
        print(log)
        time.sleep(0.1)
    
    print()
    print(f"📊 优化后统计:")
    print(f"   - 日志级别: WARNING/ERROR")
    print(f"   - 每分钟日志行数: ~5-20行")
    print(f"   - 每小时日志大小: ~1-5MB")
    print(f"   - 每天日志大小: ~20-100MB")
    print(f"   - 一周累积: ~150-700MB")
    print()

def demo_file_rotation():
    """演示文件轮转功能"""
    print("🔄 文件轮转功能演示:")
    print("-" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 模拟日志文件
        log_files = [
            "agent_production.log"
        ]
        
        sizes = [45, 50, 50, 50]  # MB
        
        for i, (filename, size) in enumerate(zip(log_files, sizes)):
            file_path = Path(temp_dir) / filename
            # 创建模拟文件
            with open(file_path, 'w') as f:
                f.write("模拟日志内容" * (size * 1024 * 100))  # 近似大小
            
            actual_size = file_path.stat().st_size / (1024 * 1024)
            status = "🟢 当前" if i == 0 else "📦 备份"
            print(f"   {status} {filename}: {actual_size:.1f}MB")
        
        print()
        print("📋 轮转规则:")
        print("   - 当前文件超过50MB时自动轮转")
        print("   - 保留3个备份文件")
        print("   - 最老的备份文件自动删除")
        print("   - 总磁盘占用限制在200MB以内")
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    print()

def demo_environment_configs():
    """演示不同环境配置"""
    print("⚙️ 环境配置对比:")
    print("-" * 60)
    
    configs = {
        "开发环境": {
            "LOG_LEVEL": "DEBUG",
            "ENABLE_FILE_LOGGING": "false",
            "ENABLE_COLOR_LOGGING": "true",
            "DEBUG_MODE": "true",
            "预期日志量": "大量（便于调试）"
        },
        "测试环境": {
            "LOG_LEVEL": "INFO",
            "ENABLE_FILE_LOGGING": "true",
            "ENABLE_COLOR_LOGGING": "false",
            "DEBUG_MODE": "false",
            "预期日志量": "中等（记录关键信息）"
        },
        "生产环境": {
            "LOG_LEVEL": "WARNING",
            "ENABLE_FILE_LOGGING": "true",
            "ENABLE_COLOR_LOGGING": "false",
            "DEBUG_MODE": "false",
            "预期日志量": "最少（只记录问题）"
        }
    }
    
    for env_name, config in configs.items():
        print(f"📁 {env_name}:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        print()

def demo_monitoring_tools():
    """演示监控工具"""
    print("🔍 监控和维护工具:")
    print("-" * 60)
    
    tools = [
        {
            "工具": "log_monitor.py",
            "功能": "实时监控日志文件大小和增长速度",
            "用法": "python scripts/log_monitor.py --watch 60",
            "输出": "文件大小、增长速度、预警信息"
        },
        {
            "工具": "log_cleanup.py", 
            "功能": "自动清理过期和超大日志文件",
            "用法": "python scripts/log_cleanup.py --max-age 7",
            "输出": "清理统计、释放空间大小"
        },
        {
            "工具": "setup_logging.py",
            "功能": "快速配置不同环境的日志设置",
            "用法": "python scripts/setup_logging.py --env production",
            "输出": "配置文件、定时任务、服务文件"
        }
    ]
    
    for tool in tools:
        print(f"🛠️ {tool['工具']}:")
        print(f"   功能: {tool['功能']}")
        print(f"   用法: {tool['用法']}")
        print(f"   输出: {tool['输出']}")
        print()

def main():
    """主演示函数"""
    print("=" * 80)
    print("🚀 算法智能体日志优化效果演示")
    print("=" * 80)
    print()
    
    print("📋 优化目标:")
    print("   - 解决生产环境日志文件过大问题（从36GB降到几百MB）")
    print("   - 提供灵活的环境配置管理")
    print("   - 增加自动化监控和清理工具")
    print("   - 保持向后兼容性")
    print()
    
    # 演示各个方面
    demo_before_optimization()
    demo_after_optimization()
    demo_file_rotation()
    demo_environment_configs()
    demo_monitoring_tools()
    
    print("=" * 80)
    print("✅ 优化效果总结:")
    print("   📉 日志量减少: 95%+ (从GB级别降到MB级别)")
    print("   💾 磁盘占用: 从36GB降到<500MB")
    print("   ⚡ 性能提升: 减少I/O开销，提高响应速度")
    print("   🔧 运维友好: 自动轮转、监控、清理")
    print("   🎯 问题定位: 保留关键错误和警告信息")
    print("=" * 80)
    print()
    
    print("🎉 立即使用优化配置:")
    print("   2. 启动服务: python scripts/start_server.py --env production")
    print("   3. 监控日志: python scripts/log_monitor.py --watch 300")
    print("   4. 定期清理: python scripts/log_cleanup.py --max-age 7")

if __name__ == "__main__":
    main()
