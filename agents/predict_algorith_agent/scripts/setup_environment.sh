#!/bin/bash
# 环境准备脚本 - 安装pyenv和相关依赖
# 适用于Anolis OS 8.6（龙蜥操作系统）远程Linux服务器 10.0.42.241
# 目标：在Python 3.6.8环境下安装Python 3.10

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi

    log_info "检测到操作系统: $OS $VER"

    # 验证是否为Anolis OS
    if [[ "$OS" != *"Anolis"* ]]; then
        log_warning "此脚本专为Anolis OS 8.6优化，当前系统: $OS $VER"
        log_warning "继续执行，但可能需要手动调整"
    fi
}

# 安装系统依赖
install_dependencies() {
    log_info "安装Anolis OS 8.6系统依赖包..."

    # Anolis OS 8.6 使用dnf包管理器（兼容RHEL/CentOS生态）
    sudo dnf groupinstall -y "Development Tools"
    sudo dnf install -y gcc openssl-devel bzip2-devel libffi-devel \
        zlib-devel readline-devel sqlite-devel wget curl llvm \
        ncurses-devel xz-devel tk-devel git

    log_success "Anolis OS系统依赖安装完成"
}

# 安装pyenv
install_pyenv() {
    log_info "安装pyenv..."
    
    # 检查是否已安装pyenv
    if command -v pyenv >/dev/null 2>&1; then
        log_warning "pyenv已安装，跳过安装步骤"
        return 0
    fi
    
    # 下载并安装pyenv
    curl https://pyenv.run | bash
    
    # 配置环境变量
    log_info "配置pyenv环境变量..."
    
    # 备份现有的bashrc
    cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
    
    # 添加pyenv配置到bashrc
    cat >> ~/.bashrc << 'EOF'

# Pyenv configuration
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$PATH"
if command -v pyenv 1>/dev/null 2>&1; then
    eval "$(pyenv init -)"
    eval "$(pyenv virtualenv-init -)"
fi
EOF
    
    # 重新加载bashrc
    source ~/.bashrc
    
    # 验证安装
    if command -v pyenv >/dev/null 2>&1; then
        log_success "pyenv安装成功"
        pyenv --version
    else
        log_error "pyenv安装失败"
        exit 1
    fi
}

# 配置pyenv环境
configure_pyenv() {
    log_info "配置pyenv环境..."
    
    # 设置环境变量
    export PYENV_ROOT="$HOME/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    
    # 初始化pyenv
    if command -v pyenv 1>/dev/null 2>&1; then
        eval "$(pyenv init -)"
        eval "$(pyenv virtualenv-init -)"
    fi
    
    log_success "pyenv环境配置完成"
}

# 主函数
main() {
    log_info "开始Anolis OS 8.6环境准备..."
    log_info "目标：为Python 3.10项目准备环境"
    log_info "当前系统Python版本: $(python3 --version 2>/dev/null || echo '未找到python3')"
    log_info "服务器地址: 10.0.42.241"

    # 检测操作系统
    detect_os

    # 安装系统依赖
    install_dependencies

    # 安装pyenv
    install_pyenv

    # 配置pyenv环境
    configure_pyenv

    log_success "Anolis OS环境准备完成！"
    log_info "请运行以下命令重新加载环境变量："
    log_info "source ~/.bashrc"
    log_info ""
    log_info "然后运行 install_python310.sh 安装Python 3.10"
}

# 执行主函数
main "$@"
