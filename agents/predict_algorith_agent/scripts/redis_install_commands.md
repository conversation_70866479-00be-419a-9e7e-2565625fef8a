# Redis 6.2 Docker安装命令

## 快速安装命令

在服务器 *********** 上执行以下命令安装Redis 6.2：

### 1. 一键安装脚本
```bash
# 上传并执行安装脚本
chmod +x install_redis_docker.sh
./install_redis_docker.sh
```

### 2. 手动安装命令

#### 创建数据目录
```bash
sudo mkdir -p /data/redis
sudo chown $USER:$USER /data/redis
```

#### 创建Redis配置文件
```bash
cat > /data/redis/redis.conf << 'EOF'
bind 0.0.0.0
port 6379
protected-mode yes
requirepass Sygy@2025
save 900 1
save 300 10
save 60 10000
dbfilename dump.rdb
dir /data
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
maxmemory 1gb
maxmemory-policy allkeys-lru
loglevel notice
logfile /data/redis.log
timeout 300
tcp-keepalive 300
databases 16
EOF
```

#### 拉取并启动Redis容器
```bash
# 拉取Redis 6.2镜像
docker pull redis:6.2

# 停止并删除现有容器（如果存在）
docker stop redis-server 2>/dev/null || true
docker rm redis-server 2>/dev/null || true

# 启动Redis容器
docker run -d \
  --name redis-server \
  --restart unless-stopped \
  -p 6379:6379 \
  -v /data/redis:/data \
  -v /data/redis/redis.conf:/usr/local/etc/redis/redis.conf \
  redis:6.2 \
  redis-server /usr/local/etc/redis/redis.conf
```

#### 配置防火墙
```bash
sudo firewall-cmd --permanent --add-port=6379/tcp
sudo firewall-cmd --reload
```

#### 测试连接
```bash
# 测试Redis连接
docker exec redis-server redis-cli -a Sygy@2025 ping

# 测试读写操作
docker exec redis-server redis-cli -a Sygy@2025 set test "hello"
docker exec redis-server redis-cli -a Sygy@2025 get test
```

## 连接信息

- **主机**: ***********
- **端口**: 6379
- **密码**: Sygy@2025
- **数据库**: 0

## 管理命令

```bash
# 查看容器状态
docker ps | grep redis-server

# 查看容器日志
docker logs redis-server

# 进入Redis CLI
docker exec -it redis-server redis-cli -a Sygy@2025

# 重启容器
docker restart redis-server

# 停止容器
docker stop redis-server

# 启动容器
docker start redis-server
```

## 配置验证

安装完成后，使用以下Python代码测试连接：

```python
import redis

# 连接Redis
r = redis.Redis(
    host='***********',
    port=6379,
    password='Sygy@2025',
    db=0,
    decode_responses=True
)

# 测试连接
try:
    r.ping()
    print("✅ Redis连接成功")
    
    # 测试读写
    r.set('test_key', 'test_value')
    value = r.get('test_key')
    print(f"✅ 读写测试成功: {value}")
    
    r.delete('test_key')
    print("✅ Redis测试完成")
    
except Exception as e:
    print(f"❌ Redis连接失败: {e}")
```
