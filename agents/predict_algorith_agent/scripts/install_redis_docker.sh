#!/bin/bash
# Redis 6.2 Docker安装脚本
# 适用于远程服务器 ***********（Anolis OS 8.6）

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
REDIS_VERSION="6.2"
REDIS_CONTAINER_NAME="redis-server"
REDIS_PORT="6379"
REDIS_PASSWORD="Sygy@2025"
REDIS_DATA_DIR="/data/redis"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    log_info "检查Docker安装状态..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，开始安装Docker..."
        install_docker
    else
        log_success "Docker已安装: $(docker --version)"
    fi
    
    # 检查Docker服务状态
    if ! systemctl is-active --quiet docker; then
        log_info "启动Docker服务..."
        sudo systemctl start docker
        sudo systemctl enable docker
    fi
    
    log_success "Docker服务运行正常"
}

# 安装Docker（适用于Anolis OS 8.6）
install_docker() {
    log_info "在Anolis OS 8.6上安装Docker..."
    
    # 更新系统
    sudo dnf update -y
    
    # 安装必要的包
    sudo dnf install -y dnf-plugins-core
    
    # 添加Docker官方仓库
    sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    
    # 安装Docker CE
    sudo dnf install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # 启动并启用Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    log_success "Docker安装完成"
    log_warning "请重新登录以使docker组权限生效，或运行: newgrp docker"
}

# 创建Redis数据目录
create_redis_directories() {
    log_info "创建Redis数据目录..."
    
    sudo mkdir -p $REDIS_DATA_DIR
    sudo chown $USER:$USER $REDIS_DATA_DIR
    sudo chmod 755 $REDIS_DATA_DIR
    
    log_success "Redis数据目录创建完成: $REDIS_DATA_DIR"
}

# 创建Redis配置文件
create_redis_config() {
    log_info "创建Redis配置文件..."
    
    cat > $REDIS_DATA_DIR/redis.conf << EOF
# Redis 6.2 配置文件
# 适用于生产环境

# 网络配置
bind 0.0.0.0
port 6379
protected-mode yes

# 认证配置
requirepass $REDIS_PASSWORD

# 持久化配置
save 900 1
save 300 10
save 60 10000

# RDB文件配置
dbfilename dump.rdb
dir /data

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 内存配置
maxmemory 1gb
maxmemory-policy allkeys-lru

# 日志配置
loglevel notice
logfile /data/redis.log

# 其他配置
timeout 300
tcp-keepalive 300
databases 16

# 安全配置
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_$REDIS_PASSWORD"
EOF
    
    log_success "Redis配置文件创建完成: $REDIS_DATA_DIR/redis.conf"
}

# 停止并删除现有Redis容器
remove_existing_redis() {
    log_info "检查现有Redis容器..."
    
    if docker ps -a | grep -q $REDIS_CONTAINER_NAME; then
        log_warning "发现现有Redis容器，正在停止并删除..."
        docker stop $REDIS_CONTAINER_NAME 2>/dev/null || true
        docker rm $REDIS_CONTAINER_NAME 2>/dev/null || true
        log_success "现有Redis容器已删除"
    else
        log_info "没有发现现有Redis容器"
    fi
}

# 拉取Redis镜像
pull_redis_image() {
    log_info "拉取Redis $REDIS_VERSION镜像..."
    
    docker pull redis:$REDIS_VERSION
    
    log_success "Redis镜像拉取完成"
}

# 启动Redis容器
start_redis_container() {
    log_info "启动Redis容器..."
    
    docker run -d \
        --name $REDIS_CONTAINER_NAME \
        --restart unless-stopped \
        -p $REDIS_PORT:6379 \
        -v $REDIS_DATA_DIR:/data \
        -v $REDIS_DATA_DIR/redis.conf:/usr/local/etc/redis/redis.conf \
        redis:$REDIS_VERSION \
        redis-server /usr/local/etc/redis/redis.conf
    
    # 等待容器启动
    sleep 5
    
    # 检查容器状态
    if docker ps | grep -q $REDIS_CONTAINER_NAME; then
        log_success "Redis容器启动成功"
    else
        log_error "Redis容器启动失败"
        docker logs $REDIS_CONTAINER_NAME
        exit 1
    fi
}

# 测试Redis连接
test_redis_connection() {
    log_info "测试Redis连接..."
    
    # 测试连接
    if docker exec $REDIS_CONTAINER_NAME redis-cli -a $REDIS_PASSWORD ping | grep -q "PONG"; then
        log_success "Redis连接测试成功"
    else
        log_error "Redis连接测试失败"
        exit 1
    fi
    
    # 测试基本操作
    docker exec $REDIS_CONTAINER_NAME redis-cli -a $REDIS_PASSWORD set test_key "test_value"
    test_result=$(docker exec $REDIS_CONTAINER_NAME redis-cli -a $REDIS_PASSWORD get test_key)
    
    if [ "$test_result" = "test_value" ]; then
        log_success "Redis读写测试成功"
        docker exec $REDIS_CONTAINER_NAME redis-cli -a $REDIS_PASSWORD del test_key
    else
        log_error "Redis读写测试失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 检查防火墙状态
    if systemctl is-active --quiet firewalld; then
        sudo firewall-cmd --permanent --add-port=$REDIS_PORT/tcp
        sudo firewall-cmd --reload
        log_success "防火墙配置完成，已开放端口 $REDIS_PORT"
    else
        log_warning "防火墙未运行，跳过配置"
    fi
}

# 创建Redis管理脚本
create_management_script() {
    log_info "创建Redis管理脚本..."
    
    cat > /tmp/redis_manager.sh << EOF
#!/bin/bash
# Redis管理脚本

CONTAINER_NAME="$REDIS_CONTAINER_NAME"
REDIS_PASSWORD="$REDIS_PASSWORD"

case "\$1" in
    start)
        echo "启动Redis容器..."
        docker start \$CONTAINER_NAME
        ;;
    stop)
        echo "停止Redis容器..."
        docker stop \$CONTAINER_NAME
        ;;
    restart)
        echo "重启Redis容器..."
        docker restart \$CONTAINER_NAME
        ;;
    status)
        echo "Redis容器状态:"
        docker ps | grep \$CONTAINER_NAME || echo "容器未运行"
        ;;
    logs)
        echo "Redis容器日志:"
        docker logs \$CONTAINER_NAME
        ;;
    cli)
        echo "连接Redis CLI..."
        docker exec -it \$CONTAINER_NAME redis-cli -a \$REDIS_PASSWORD
        ;;
    info)
        echo "Redis信息:"
        docker exec \$CONTAINER_NAME redis-cli -a \$REDIS_PASSWORD info
        ;;
    *)
        echo "用法: \$0 {start|stop|restart|status|logs|cli|info}"
        exit 1
        ;;
esac
EOF
    
    chmod +x /tmp/redis_manager.sh
    sudo mv /tmp/redis_manager.sh /usr/local/bin/redis-manager
    
    log_success "Redis管理脚本创建完成: /usr/local/bin/redis-manager"
}

# 主函数
main() {
    log_info "开始在Anolis OS 8.6上安装Redis 6.2..."
    log_info "服务器: ***********"
    log_info "Redis版本: $REDIS_VERSION"
    log_info "容器名称: $REDIS_CONTAINER_NAME"
    log_info "端口: $REDIS_PORT"
    log_info "数据目录: $REDIS_DATA_DIR"
    
    # 检查Docker
    # check_docker
    
    # 创建目录
    # create_redis_directories
    
    # 创建配置文件
    # create_redis_config
    
    # 删除现有容器
    # remove_existing_redis
    
    # 拉取镜像
    # pull_redis_image
    
    # 启动容器
    start_redis_container
    
    # 测试连接
    test_redis_connection
    
    # 配置防火墙
    configure_firewall
    
    # 创建管理脚本
    create_management_script
    
    log_success "Redis 6.2安装完成！"
    log_info ""
    log_info "连接信息："
    log_info "主机: ***********"
    log_info "端口: $REDIS_PORT"
    log_info "密码: $REDIS_PASSWORD"
    log_info ""
    log_info "管理命令："
    log_info "启动: redis-manager start"
    log_info "停止: redis-manager stop"
    log_info "重启: redis-manager restart"
    log_info "状态: redis-manager status"
    log_info "日志: redis-manager logs"
    log_info "CLI: redis-manager cli"
    log_info "信息: redis-manager info"
    log_info ""
    log_info "容器管理："
    log_info "查看状态: docker ps | grep redis"
    log_info "查看日志: docker logs $REDIS_CONTAINER_NAME"
    log_info "进入容器: docker exec -it $REDIS_CONTAINER_NAME bash"
}

# 执行主函数
main "$@"
