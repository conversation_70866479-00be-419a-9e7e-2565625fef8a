#!/bin/bash
# 后台启动算法智能体服务器脚本
# 适用于远程龙蜥Linux服务器 ***********

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
SERVER_HOST="0.0.0.0"
SERVER_PORT="8008"
PYTHON_VERSION="3.10.12"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目目录
check_project_directory() {
    log_info "检查项目目录..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/predict_algorith_agent/predict_main.py" ]; then
        log_error "主启动文件不存在: $PROJECT_DIR/agents/predict_algorith_agent/predict_main.py"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/config.py" ]; then
        log_error "配置文件不存在: $PROJECT_DIR/agents/config.py"
        exit 1
    fi
    
    log_success "项目目录检查通过"
}

# 检查Python环境
check_python_environment() {
    log_info "检查Python环境..."
    
    cd $PROJECT_DIR
    
    # 检查激活脚本
    if [ ! -f "activate_env.sh" ]; then
        log_error "环境激活脚本不存在: $PROJECT_DIR/activate_env.sh"
        exit 1
    fi
    
    log_success "Python环境检查通过"
}

# 停止现有服务
stop_existing_server() {
    log_info "检查并停止现有服务..."
    
    cd $PROJECT_DIR
    
    # 检查PID文件
    if [ -f "server.pid" ]; then
        OLD_PID=$(cat server.pid)
        if kill -0 $OLD_PID 2>/dev/null; then
            log_warning "发现运行中的服务 (PID: $OLD_PID)，正在停止..."
            kill $OLD_PID
            sleep 3
            
            # 强制停止
            if kill -0 $OLD_PID 2>/dev/null; then
                log_warning "强制停止服务..."
                kill -9 $OLD_PID
                sleep 1
            fi
            
            log_success "现有服务已停止"
        fi
        rm -f server.pid
    fi
    
    # 检查端口占用
    if netstat -tlnp | grep ":$SERVER_PORT " > /dev/null; then
        log_warning "端口 $SERVER_PORT 仍被占用，尝试停止占用进程..."
        pid=$(netstat -tlnp | grep ":$SERVER_PORT " | awk '{print $7}' | cut -d'/' -f1 | head -1)
        if [ ! -z "$pid" ] && [ "$pid" != "-" ]; then
            log_info "停止占用端口的进程 PID: $pid"
            kill -9 $pid 2>/dev/null || true
            sleep 2
        fi
    fi
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."
    
    cd $PROJECT_DIR
    
    cat > start_server.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import signal
import time
from datetime import datetime

# 设置项目路径
sys.path.insert(0, '/data/agent')

def signal_handler(signum, frame):
    print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 收到信号 {signum}，正在关闭服务器...")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

try:
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 正在启动算法智能体服务器...")
    
    from agents.predict_algorith_agent.predict_main import app
    from agents.config import PREDICT_MAIN_HOST, PREDICT_MAIN_PORT
    import uvicorn
    
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🌐 服务将在 http://{PREDICT_MAIN_HOST}:{PREDICT_MAIN_PORT} 启动")
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 📄 使用启动文件: agents/predict_algorith_agent/predict_main.py")
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 🔌 WebSocket端点: ws://{PREDICT_MAIN_HOST}:{PREDICT_MAIN_PORT}/api/ws/chat")
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 📋 进程PID: {os.getpid()}")
    print("")
    
    uvicorn.run(
        app,
        host=PREDICT_MAIN_HOST,
        port=PREDICT_MAIN_PORT,
        log_level='info',
        access_log=True,
        reload=False
    )
    
except Exception as e:
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] ❌ 启动失败: {e}")
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 💡 请确保predict_main.py文件存在于: /data/agent/agents/predict_algorith_agent/")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF
    
    chmod +x start_server.py
    log_success "启动脚本创建完成"
}

# 检查并管理日志文件大小
manage_log_file() {
    log_file="$PROJECT_DIR/logs/server.log"
    max_size_gb=2
    max_size_bytes=$((max_size_gb * 1024 * 1024 * 1024))  # 2GB in bytes

    log_info "检查日志文件大小..."

    # 创建日志目录
    mkdir -p logs

    # 如果日志文件存在，检查大小
    if [ -f "$log_file" ]; then
        # 获取文件大小（字节）
        file_size=$(stat -c%s "$log_file" 2>/dev/null || echo "0")
        file_size_mb=$((file_size / 1024 / 1024))

        log_info "当前日志文件大小: ${file_size_mb}MB"

        # 如果文件大小超过2GB
        if [ "$file_size" -gt "$max_size_bytes" ]; then
            log_warning "日志文件超过${max_size_gb}GB，正在进行日志轮转..."

            # 备份当前日志文件（保留最后100MB的内容）
            backup_file="$PROJECT_DIR/logs/server_$(date +%Y%m%d_%H%M%S).log.old"
            temp_file="$PROJECT_DIR/logs/server_temp.log"

            # 保留最后100MB的日志内容
            tail -c 104857600 "$log_file" > "$temp_file"  # 100MB = 104857600 bytes

            # 移动原文件为备份
            mv "$log_file" "$backup_file"

            # 将临时文件作为新的日志文件
            mv "$temp_file" "$log_file"

            log_success "日志轮转完成："
            log_info "  - 旧日志备份: $backup_file"
            log_info "  - 新日志文件: $log_file (保留最新100MB内容)"

            # 删除超过7天的旧日志备份文件
            find "$PROJECT_DIR/logs" -name "server_*.log.old" -mtime +7 -delete 2>/dev/null || true
            log_info "  - 已清理7天前的旧日志备份"
        else
            log_success "日志文件大小正常 (${file_size_mb}MB < ${max_size_gb}GB)"
        fi
    else
        log_info "日志文件不存在，将创建新文件"
        touch "$log_file"
    fi
}

# 后台启动服务器
start_server_background() {
    log_info "后台启动算法智能体服务器..."

    cd $PROJECT_DIR
    source $PROJECT_DIR/activate_env.sh

    # 设置环境变量
    export PROJECT_ROOT=/data/agent
    export PYTHONPATH=/data/agent:$PYTHONPATH
    export PYTHONIOENCODING=utf-8

    # 管理日志文件大小
    manage_log_file

    # 启动动态日志监控
    log_info "🔍 启动动态日志监控..."
    nohup python agents/predict_algorith_agent/scripts/dynamic_log_monitor.py > logs/log_monitor.log 2>&1 &
    MONITOR_PID=$!
    echo $MONITOR_PID > log_monitor.pid

    # 等待监控启动
    sleep 2

    # 后台启动服务器
    log_info "🚀 后台启动服务器..."
    nohup python agents/predict_algorith_agent/predict_main.py > logs/server.log 2>&1 &
    SERVER_PID=$!

    # 保存PID
    echo $SERVER_PID > server.pid

    log_info "📍 项目目录: $PROJECT_DIR"
    log_info "🌐 服务地址: http://$SERVER_HOST:$SERVER_PORT"
    log_info "🔌 WebSocket地址: ws://$SERVER_HOST:$SERVER_PORT/api/ws/chat"
    log_info "📋 服务器PID: $SERVER_PID"
    log_info "🔍 日志监控PID: $MONITOR_PID"
    log_info "📄 服务器日志: $PROJECT_DIR/logs/server.log (动态管理，最大2GB)"
    log_info "📄 监控日志: $PROJECT_DIR/logs/log_monitor.log"
    log_info "📄 PID文件: $PROJECT_DIR/server.pid, $PROJECT_DIR/log_monitor.pid"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 5
    
    # 检查服务是否启动成功
    if kill -0 $SERVER_PID 2>/dev/null; then
        # 检查端口是否监听
        if netstat -tlnp | grep ":$SERVER_PORT " > /dev/null; then
            log_success "✅ 服务器后台启动成功！"
            log_info ""
            log_info "🎯 服务信息："
            log_info "   - 服务地址: http://***********:$SERVER_PORT"
            log_info "   - WebSocket: ws://***********:$SERVER_PORT/api/ws/chat"
            log_info "   - API文档: http://***********:$SERVER_PORT/docs"
            log_info ""
            log_info "📋 管理命令："
            log_info "   - 查看日志: tail -f $PROJECT_DIR/logs/server.log"
            log_info "   - 查看状态: ps aux | grep $SERVER_PID"
            log_info "   - 停止服务: kill $SERVER_PID"
            log_info "   - 或者使用: kill \$(cat $PROJECT_DIR/server.pid)"
            log_info ""
            log_info "🧪 测试命令："
            log_info "   - 健康检查: curl http://***********:$SERVER_PORT/api/ws/health"
            log_info "   - WebSocket测试: python $PROJECT_DIR/agents/predict_algorith_agent/scripts/test_websocket.py"
        else
            log_error "❌ 服务启动失败：端口未监听"
            log_info "查看错误日志: cat $PROJECT_DIR/logs/server.log"
            exit 1
        fi
    else
        log_error "❌ 服务器启动失败"
        log_info "查看错误日志: cat $PROJECT_DIR/logs/server.log"
        exit 1
    fi
}

# 创建服务管理脚本
create_management_script() {
    log_info "创建服务管理脚本..."
    
    cat > $PROJECT_DIR/manage_server.sh << 'EOF'
#!/bin/bash
# 服务器管理脚本

PROJECT_DIR="/data/agent"
SERVER_PORT="8008"

case "$1" in
    start)
        echo "启动服务器..."
        cd $PROJECT_DIR
        if [ -f "server.pid" ]; then
            PID=$(cat server.pid)
            if kill -0 $PID 2>/dev/null; then
                echo "服务器已在运行 (PID: $PID)"
                exit 0
            fi
        fi
        ./agents/predict_algorith_agent/scripts/start_server_background.sh
        ;;
    stop)
        echo "停止服务器..."
        cd $PROJECT_DIR

        # 停止服务器
        if [ -f "server.pid" ]; then
            PID=$(cat server.pid)
            if kill -0 $PID 2>/dev/null; then
                echo "停止服务器 (PID: $PID)"
                kill $PID
                sleep 3
                if kill -0 $PID 2>/dev/null; then
                    echo "强制停止服务器"
                    kill -9 $PID
                fi
                rm -f server.pid
                echo "服务器已停止"
            else
                echo "服务器未运行"
                rm -f server.pid
            fi
        else
            echo "服务器未运行"
        fi

        # 停止日志监控
        if [ -f "log_monitor.pid" ]; then
            MONITOR_PID=$(cat log_monitor.pid)
            if kill -0 $MONITOR_PID 2>/dev/null; then
                echo "停止日志监控 (PID: $MONITOR_PID)"
                kill $MONITOR_PID
                sleep 2
                if kill -0 $MONITOR_PID 2>/dev/null; then
                    echo "强制停止日志监控"
                    kill -9 $MONITOR_PID
                fi
                rm -f log_monitor.pid
                echo "日志监控已停止"
            else
                echo "日志监控未运行"
                rm -f log_monitor.pid
            fi
        fi
        ;;
    restart)
        echo "重启服务器..."
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        echo "检查服务器状态..."
        cd $PROJECT_DIR

        # 检查服务器状态
        if [ -f "server.pid" ]; then
            PID=$(cat server.pid)
            if kill -0 $PID 2>/dev/null; then
                echo "✅ 服务器运行中 (PID: $PID)"
                if netstat -tlnp | grep ":$SERVER_PORT " > /dev/null; then
                    echo "✅ 端口 $SERVER_PORT 正在监听"
                else
                    echo "❌ 端口 $SERVER_PORT 未监听"
                fi
            else
                echo "❌ 服务器未运行"
                rm -f server.pid
            fi
        else
            echo "❌ 服务器未运行"
        fi

        # 检查日志监控状态
        if [ -f "log_monitor.pid" ]; then
            MONITOR_PID=$(cat log_monitor.pid)
            if kill -0 $MONITOR_PID 2>/dev/null; then
                echo "✅ 日志监控运行中 (PID: $MONITOR_PID)"
                # 检查日志文件大小
                if [ -f "logs/server.log" ]; then
                    file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
                    file_size_mb=$((file_size / 1024 / 1024))
                    echo "📊 当前日志大小: ${file_size_mb}MB / 2048MB"
                fi
            else
                echo "❌ 日志监控未运行"
                rm -f log_monitor.pid
            fi
        else
            echo "❌ 日志监控未运行"
        fi
        ;;
    logs)
        echo "查看服务器日志..."
        cd $PROJECT_DIR
        if [ -f "logs/server.log" ]; then
            # 显示日志文件信息
            file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
            file_size_mb=$((file_size / 1024 / 1024))
            echo "📄 日志文件大小: ${file_size_mb}MB"
            echo "📄 日志文件路径: $PROJECT_DIR/logs/server.log"
            echo "📄 开始显示日志内容 (Ctrl+C 退出)..."
            echo "----------------------------------------"
            tail -f logs/server.log
        else
            echo "❌ 日志文件不存在: $PROJECT_DIR/logs/server.log"
        fi
        ;;
    test)
        echo "测试WebSocket连接..."
        cd $PROJECT_DIR
        source $PROJECT_DIR/activate_env.sh
        python agents/predict_algorith_agent/scripts/test_websocket.py basic
        ;;
    clean-logs)
        echo "清理日志文件..."
        cd $PROJECT_DIR
        if [ -f "logs/server.log" ]; then
            file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
            file_size_mb=$((file_size / 1024 / 1024))
            echo "📄 当前日志文件大小: ${file_size_mb}MB"

            if [ "$file_size_mb" -gt 100 ]; then
                echo "🔄 正在清理日志文件..."
                backup_file="logs/server_$(date +%Y%m%d_%H%M%S).log.old"
                temp_file="logs/server_temp.log"

                # 保留最后50MB的日志内容
                tail -c 52428800 "logs/server.log" > "$temp_file"  # 50MB = 52428800 bytes
                mv "logs/server.log" "$backup_file"
                mv "$temp_file" "logs/server.log"

                echo "✅ 日志清理完成："
                echo "   - 旧日志备份: $backup_file"
                echo "   - 新日志文件: logs/server.log (保留最新50MB内容)"
            else
                echo "✅ 日志文件大小正常，无需清理"
            fi
        else
            echo "❌ 日志文件不存在"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|test|clean-logs}"
        echo ""
        echo "命令说明:"
        echo "  start      - 启动服务器"
        echo "  stop       - 停止服务器"
        echo "  restart    - 重启服务器"
        echo "  status     - 查看服务器状态"
        echo "  logs       - 查看服务器日志"
        echo "  test       - 测试WebSocket连接"
        echo "  clean-logs - 手动清理日志文件"
        exit 1
        ;;
esac
EOF
    
    chmod +x $PROJECT_DIR/manage_server.sh
    log_success "服务管理脚本创建完成: $PROJECT_DIR/manage_server.sh"
}

# 主函数
main() {
    log_info "开始后台启动算法智能体服务器..."
    log_info "服务器: ***********"
    log_info "项目目录: $PROJECT_DIR"
    log_info "监听地址: $SERVER_HOST:$SERVER_PORT"
    
    # 检查项目目录
    check_project_directory
    
    # 检查Python环境
    check_python_environment
    
    # 停止现有服务
    stop_existing_server
    
    # 创建启动脚本
    create_startup_script
    
    # 后台启动服务器
    start_server_background
    
    # 创建管理脚本
    create_management_script
    
    log_success "🎉 后台启动完成！"
}

# 执行主函数
main "$@"
