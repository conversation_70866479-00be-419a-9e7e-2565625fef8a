#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
服务器启动脚本
支持不同环境配置和日志级别
"""

import os
import sys
import argparse
import shutil
from pathlib import Path

def setup_environment(env_type: str, project_root: Path):
    """设置环境配置"""
    env_files = {
        'development': '.env.example',
        'production': '.env.production',
        'testing': '.env.example'
    }
    
    source_env = project_root / env_files.get(env_type, '.env.example')
    target_env = project_root / '.env'
    
    if source_env.exists():
        print(f"使用 {env_type} 环境配置: {source_env}")
        shutil.copy2(source_env, target_env)
        
        # 如果是生产环境，额外设置一些环境变量
        if env_type == 'production':
            print("应用生产环境优化配置...")
            # 可以在这里添加额外的生产环境配置
    else:
        print(f"警告: 环境配置文件不存在: {source_env}")
        print("将使用默认配置")

def check_dependencies():
    """检查依赖"""
    required_modules = [
        'fastapi',
        'uvicorn',
        'pydantic_ai',
        'pymysql'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("错误: 缺少必要的依赖模块:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请安装缺少的依赖:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def create_log_directory(project_root: Path):
    """创建日志目录"""
    log_dir = project_root / 'logs'
    log_dir.mkdir(exist_ok=True)
    print(f"日志目录: {log_dir}")

def print_startup_info(env_type: str, host: str, port: int, log_level: str):
    """打印启动信息"""
    print("=" * 60)
    print("算法智能体服务器启动")
    print("=" * 60)
    print(f"环境类型: {env_type}")
    print(f"服务地址: http://{host}:{port}")
    print(f"WebSocket: ws://{host}:{port}/api/ws/chat")
    print(f"日志级别: {log_level}")
    print(f"静态文件: http://{host}:{port}/static/")
    print(f"健康检查: http://{host}:{port}/health")
    print("=" * 60)

def main():
    parser = argparse.ArgumentParser(description='算法智能体服务器启动脚本')
    parser.add_argument('--env', choices=['development', 'production', 'testing'],
                       default='development', help='环境类型 (默认: development)')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8008, help='服务器端口 (默认: 8008)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (覆盖环境配置)')
    parser.add_argument('--reload', action='store_true', help='启用自动重载 (仅开发环境)')
    parser.add_argument('--workers', type=int, default=1, help='工作进程数 (默认: 1)')
    parser.add_argument('--check-only', action='store_true', help='只检查环境，不启动服务器')
    
    args = parser.parse_args()
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print(f"项目根目录: {project_root}")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置环境配置
    setup_environment(args.env, project_root)
    
    # 创建日志目录
    create_log_directory(project_root)
    
    # 设置环境变量
    if args.log_level:
        os.environ['LOG_LEVEL'] = args.log_level
    
    # 生产环境特殊处理
    if args.env == 'production':
        if args.reload:
            print("警告: 生产环境不建议使用 --reload 参数")
            args.reload = False
        
        # 生产环境默认使用WARNING日志级别
        if not args.log_level:
            os.environ['LOG_LEVEL'] = 'WARNING'
    
    # 开发环境特殊处理
    if args.env == 'development':
        if not args.log_level:
            os.environ['LOG_LEVEL'] = 'DEBUG'
    
    # 获取最终的日志级别
    final_log_level = os.environ.get('LOG_LEVEL', 'INFO')
    
    # 打印启动信息
    print_startup_info(args.env, args.host, args.port, final_log_level)
    
    if args.check_only:
        print("环境检查完成，退出。")
        return
    
    # 添加项目根目录到Python路径
    sys.path.insert(0, str(project_root))
    
    try:
        # 导入并启动服务器
        import uvicorn
        from agents.predict_algorith_agent.predict_main import app
        
        # 配置uvicorn参数
        uvicorn_config = {
            'app': app,
            'host': args.host,
            'port': args.port,
            'log_level': 'warning' if args.env == 'production' else 'info',
            'access_log': args.env != 'production',  # 生产环境关闭访问日志
        }
        
        # 开发环境特殊配置
        if args.env == 'development':
            uvicorn_config.update({
                'reload': args.reload,
                'reload_dirs': [str(project_root / 'agents')],
            })
        
        # 生产环境特殊配置
        if args.env == 'production':
            uvicorn_config.update({
                'workers': args.workers,
            })
        
        print("正在启动服务器...")
        print("按 Ctrl+C 停止服务器")
        print()
        
        uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except ImportError as e:
        print(f"错误: 无法导入必要模块: {e}")
        print("请确保在正确的Python环境中运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
