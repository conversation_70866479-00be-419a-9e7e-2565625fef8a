#!/bin/bash
# 系统服务配置脚本
# 为算法智能体创建systemd服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
SERVICE_NAME="algorithm-agent"
SERVICE_USER="$USER"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目部署
check_deployment() {
    log_info "检查项目部署状态..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先运行 deploy_project.sh"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_DIR/start_agent.sh" ]; then
        log_error "启动脚本不存在"
        log_info "请先运行 deploy_project.sh"
        exit 1
    fi
    
    log_success "项目部署检查通过"
}

# 创建Gunicorn配置文件
create_gunicorn_config() {
    log_info "创建Gunicorn配置文件..."
    
    cat > $PROJECT_DIR/gunicorn.conf.py << 'EOF'
# Gunicorn配置文件
import multiprocessing
import os

# 服务器socket
bind = "0.0.0.0:8008"
backlog = 2048

# Worker进程
workers = min(multiprocessing.cpu_count() * 2 + 1, 8)
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 30
keepalive = 2

# 重启
max_requests = 1000
max_requests_jitter = 50
preload_app = True

# 日志
accesslog = "/data/agent/logs/access.log"
errorlog = "/data/agent/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# 进程命名
proc_name = "algorithm-agent"

# 用户权限
user = os.getenv('USER', 'root')
group = os.getenv('USER', 'root')

# 环境变量
raw_env = [
    'PROJECT_ROOT=/data/agent',
    'PYTHONPATH=/data/agent',
    'PYTHONIOENCODING=utf-8'
]
EOF
    
    log_success "Gunicorn配置文件创建完成"
}

# 创建Gunicorn启动脚本
create_gunicorn_startup() {
    log_info "创建Gunicorn启动脚本..."
    
    cat > $PROJECT_DIR/start_gunicorn.py << 'EOF'
#!/usr/bin/env python3
"""
Gunicorn启动脚本
"""
import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path("/data/agent").resolve()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['PROJECT_ROOT'] = str(project_root)
os.environ['PYTHONPATH'] = str(project_root)
os.environ['PYTHONIOENCODING'] = 'utf-8'

# 导入应用
try:
    from agents.predict_algorith_agent.predict_main import app
    print(f"✅ 应用导入成功，项目根目录: {project_root}")
except Exception as e:
    print(f"❌ 应用导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 导出应用对象供Gunicorn使用
application = app

if __name__ == "__main__":
    print("此脚本用于Gunicorn启动，不应直接运行")
    print("请使用: gunicorn -c gunicorn.conf.py start_gunicorn:application")
EOF
    
    chmod +x $PROJECT_DIR/start_gunicorn.py
    log_success "Gunicorn启动脚本创建完成"
}

# 创建systemd服务文件
create_systemd_service() {
    log_info "创建systemd服务文件..."
    
    sudo tee /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Algorithm Agent Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=notify
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$PROJECT_DIR
Environment=PROJECT_ROOT=$PROJECT_DIR
Environment=PYTHONPATH=$PROJECT_DIR
Environment=PYTHONIOENCODING=utf-8
Environment=PYENV_ROOT=$HOME/.pyenv
Environment=PATH=$HOME/.pyenv/bin:$HOME/.pyenv/shims:/usr/local/bin:/usr/bin:/bin
ExecStartPre=/bin/bash -c 'source $PROJECT_DIR/activate_env.sh'
ExecStart=$PROJECT_DIR/venv/bin/gunicorn -c gunicorn.conf.py start_gunicorn:application
ExecReload=/bin/kill -s HUP \$MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "systemd服务文件创建完成"
}

# 配置服务
configure_service() {
    log_info "配置systemd服务..."
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable $SERVICE_NAME
    
    log_success "服务配置完成"
}

# 创建服务管理脚本
create_service_management_script() {
    log_info "创建服务管理脚本..."
    
    cat > $PROJECT_DIR/manage_service.sh << EOF
#!/bin/bash
# 服务管理脚本

SERVICE_NAME="$SERVICE_NAME"

case "\$1" in
    start)
        echo "启动服务..."
        sudo systemctl start \$SERVICE_NAME
        ;;
    stop)
        echo "停止服务..."
        sudo systemctl stop \$SERVICE_NAME
        ;;
    restart)
        echo "重启服务..."
        sudo systemctl restart \$SERVICE_NAME
        ;;
    status)
        echo "查看服务状态..."
        sudo systemctl status \$SERVICE_NAME
        ;;
    logs)
        echo "查看服务日志..."
        sudo journalctl -u \$SERVICE_NAME -f
        ;;
    enable)
        echo "启用服务..."
        sudo systemctl enable \$SERVICE_NAME
        ;;
    disable)
        echo "禁用服务..."
        sudo systemctl disable \$SERVICE_NAME
        ;;
    *)
        echo "用法: \$0 {start|stop|restart|status|logs|enable|disable}"
        exit 1
        ;;
esac
EOF
    
    chmod +x $PROJECT_DIR/manage_service.sh
    log_success "服务管理脚本创建完成: $PROJECT_DIR/manage_service.sh"
}

# 测试服务
test_service() {
    log_info "测试服务..."
    
    # 启动服务
    sudo systemctl start $SERVICE_NAME
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if sudo systemctl is-active --quiet $SERVICE_NAME; then
        log_success "服务启动成功"
        
        # 测试API
        if curl -s http://localhost:8008/health >/dev/null; then
            log_success "API响应正常"
        else
            log_warning "API响应异常，请检查日志"
        fi
    else
        log_error "服务启动失败"
        log_info "查看日志: sudo journalctl -u $SERVICE_NAME -n 50"
        exit 1
    fi
}

# 创建Nginx配置（可选）
create_nginx_config() {
    log_info "创建Nginx配置文件（可选）..."
    
    cat > $PROJECT_DIR/nginx.conf << 'EOF'
# Nginx配置文件
# 复制到 /etc/nginx/sites-available/algorithm-agent

server {
    listen 80;
    server_name your-domain.com;  # 替换为实际域名或IP

    client_max_body_size 100M;
    
    location / {
        proxy_pass http://127.0.0.1:8008;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态文件直接服务
    location /static/ {
        alias /data/agent/agents/predict_algorith_agent/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    log_info "Nginx配置文件已创建: $PROJECT_DIR/nginx.conf"
    log_info "如需使用，请复制到 /etc/nginx/sites-available/ 并启用"
}

# 主函数
main() {
    log_info "开始配置系统服务..."
    log_info "服务名称: $SERVICE_NAME"
    log_info "项目目录: $PROJECT_DIR"
    log_info "运行用户: $SERVICE_USER"
    
    # 检查项目部署
    check_deployment
    
    # 创建Gunicorn配置
    create_gunicorn_config
    
    # 创建Gunicorn启动脚本
    create_gunicorn_startup
    
    # 创建systemd服务
    create_systemd_service
    
    # 配置服务
    configure_service
    
    # 创建服务管理脚本
    create_service_management_script
    
    # 创建Nginx配置
    create_nginx_config
    
    # 测试服务
    test_service
    
    log_success "系统服务配置完成！"
    log_info ""
    log_info "服务管理命令："
    log_info "启动服务: sudo systemctl start $SERVICE_NAME"
    log_info "停止服务: sudo systemctl stop $SERVICE_NAME"
    log_info "重启服务: sudo systemctl restart $SERVICE_NAME"
    log_info "查看状态: sudo systemctl status $SERVICE_NAME"
    log_info "查看日志: sudo journalctl -u $SERVICE_NAME -f"
    log_info ""
    log_info "或使用管理脚本: $PROJECT_DIR/manage_service.sh {start|stop|restart|status|logs}"
    log_info ""
    log_info "API访问地址: http://服务器IP:8008"
    log_info "健康检查: http://服务器IP:8008/health"
}

# 执行主函数
main "$@"
