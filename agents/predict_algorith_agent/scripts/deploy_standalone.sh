#!/bin/bash

# 独立智能体服务自动部署脚本
# 专注于Agent和WebSocket功能，不包含Nginx等额外组件
# 适用于Ubuntu 20.04+ / CentOS 7+ / RHEL 8+

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本！"
        log_info "建议使用普通用户执行部署"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统版本"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 2 ]]; then
        log_warn "内存不足2GB，建议至少2GB内存"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df -BG . | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_GB -lt 5 ]]; then
        log_error "磁盘空间不足5GB"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 安装系统依赖
install_system_deps() {
    log_step "安装系统依赖..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt update
        sudo apt install -y python3.10 python3.10-venv python3.10-dev python3-pip \
                           git curl libmysqlclient-dev pkg-config
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum update -y
        sudo yum install -y python310 python310-pip python310-devel \
                           git curl mysql-devel pkgconfig
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_info "系统依赖安装完成"
}

# 创建项目目录
create_project_structure() {
    log_step "创建项目目录..."
    
    PROJECT_ROOT="$HOME/algorithm-agent"
    
    mkdir -p "$PROJECT_ROOT"
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/backups"
    
    log_info "项目目录创建完成: $PROJECT_ROOT"
}

# 设置Python环境
setup_python_env() {
    log_step "设置Python虚拟环境..."
    
    cd "$PROJECT_ROOT"
    
    # 创建虚拟环境
    python3.10 -m venv venv
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip setuptools wheel
    
    log_info "Python虚拟环境设置完成"
}

# 安装Python依赖
install_python_deps() {
    log_step "安装Python依赖..."
    
    cd "$PROJECT_ROOT"
    source venv/bin/activate
    
    # 安装智能体服务核心依赖
    pip install fastapi uvicorn pydantic pydantic_ai requests \
                pymysql redis sqlalchemy python-dotenv websockets
    
    # 验证关键包
    python -c "import fastapi; print('FastAPI:', fastapi.__version__)"
    python -c "import pydantic_ai; print('Pydantic AI: OK')"
    python -c "import pymysql; print('PyMySQL: OK')"
    python -c "import redis; print('Redis: OK')"
    
    log_info "Python依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    # 创建环境变量文件
    cat > ~/.bashrc_algorithm << EOF
# 独立智能体服务环境变量
export PROJECT_ROOT=$PROJECT_ROOT
export PYTHONPATH=\$PROJECT_ROOT:\$PYTHONPATH
export PYTHONIOENCODING=utf-8
EOF
    
    # 添加到bashrc
    if ! grep -q "source ~/.bashrc_algorithm" ~/.bashrc; then
        echo "source ~/.bashrc_algorithm" >> ~/.bashrc
    fi
    
    # 立即加载环境变量
    source ~/.bashrc_algorithm
    
    log_info "环境变量配置完成"
}

# 创建配置文件模板
create_config_template() {
    log_step "创建配置文件模板..."
    
    cd "$PROJECT_ROOT"
    
    cat > config_template.py << 'EOF'
# 独立智能体服务配置文件模板

# 服务监听配置
PREDICT_MAIN_HOST = "0.0.0.0"
PREDICT_MAIN_PORT = 8008

# MySQL数据库连接配置 - 请修改为实际配置
MYSQL_HOST = "your-mysql-host"
MYSQL_PORT = 3306
MYSQL_USER = "your-mysql-user"
MYSQL_PASSWORD = "your-mysql-password"
MYSQL_DB = "your-database"
MYSQL_CHARSET = "utf8mb4"

# Redis数据库连接配置 - 请修改为实际配置
REDIS_HOST = "your-redis-host"
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = "your-redis-password"
REDIS_DECODE_RESPONSES = True

# API配置（可选）
ALGORITHM_API_BASE = "http://your-algorithm-api/api"
TRAING_API_BASE = "http://your-training-api/api"
HISTORY_ALGORITHM_API_BASE = "http://your-history-api/api"

# 控制是否输出deepseek大模型的思考过程
SHOW_DEEPSEEK_THOUGHT = False
EOF
    
    log_info "配置文件模板创建完成: $PROJECT_ROOT/config_template.py"
}

# 创建启动脚本
create_startup_scripts() {
    log_step "创建启动脚本..."
    
    cd "$PROJECT_ROOT"
    
    # 创建独立服务启动脚本
    cat > start_standalone.py << 'EOF'
#!/usr/bin/env python3
"""
独立智能体服务启动脚本
专注于Agent和WebSocket功能
"""
import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent.resolve()
sys.path.insert(0, str(project_root))
os.environ['PROJECT_ROOT'] = str(project_root)
os.environ['PYTHONPATH'] = str(project_root)

print("🚀 启动独立智能体服务...")
print(f"📁 项目目录: {project_root}")
print(f"🌐 服务地址: http://0.0.0.0:8008")

try:
    from agents.predict_algorith_agent.predict_main import app
    import uvicorn
    
    # 启动独立服务
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8008,
        log_level="info",
        access_log=True,
        reload=False
    )
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
EOF
    
    # 创建数据库连接测试脚本
    cat > test_connections.py << 'EOF'
#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.getcwd())

def test_mysql():
    try:
        from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB
        import pymysql
        
        print("🔍 测试MySQL连接...")
        connection = pymysql.connect(
            host=MYSQL_HOST,
            port=MYSQL_PORT,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=MYSQL_DB,
            charset='utf8mb4'
        )
        print("✅ MySQL连接成功!")
        connection.close()
        return True
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_redis():
    try:
        from agents.config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD
        import redis
        
        print("🔍 测试Redis连接...")
        r = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        r.ping()
        print("✅ Redis连接成功!")
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

if __name__ == "__main__":
    mysql_ok = test_mysql()
    redis_ok = test_redis()
    
    if mysql_ok and redis_ok:
        print("🎉 所有数据库连接测试通过!")
        sys.exit(0)
    else:
        print("💥 数据库连接测试失败，请检查配置!")
        sys.exit(1)
EOF
    
    # 设置执行权限
    chmod +x start_standalone.py
    chmod +x test_connections.py
    
    log_info "启动脚本创建完成"
}

# 创建systemd服务
create_systemd_service() {
    log_step "创建systemd服务..."
    
    sudo tee /etc/systemd/system/algorithm-agent.service << EOF
[Unit]
Description=Algorithm Agent Standalone Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_ROOT
Environment=PROJECT_ROOT=$PROJECT_ROOT
Environment=PYTHONPATH=$PROJECT_ROOT
ExecStart=$PROJECT_ROOT/venv/bin/python start_standalone.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    sudo systemctl enable algorithm-agent
    
    log_info "systemd服务创建完成"
}

# 创建备份脚本
create_backup_script() {
    log_step "创建备份脚本..."
    
    cd "$PROJECT_ROOT"
    
    cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="$HOME/algorithm-agent/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份代码
tar -czf $BACKUP_DIR/code_$DATE.tar.gz agents/ core/

# 备份配置
if [[ -f agents/config.py ]]; then
    cp agents/config.py $BACKUP_DIR/config_$DATE.py
fi

# 清理旧备份（保留最近7天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "config_*.py" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR"
EOF
    
    chmod +x backup.sh
    
    log_info "备份脚本创建完成"
}

# 显示部署后续步骤
show_next_steps() {
    log_step "独立智能体服务部署完成！"
    
    echo ""
    echo "📋 接下来需要手动完成以下步骤："
    echo ""
    echo "1. 上传代码文件到项目目录："
    echo "   cd $PROJECT_ROOT"
    echo "   # 使用upload_code.sh/bat上传agents和core目录"
    echo ""
    echo "2. 配置数据库连接："
    echo "   cp config_template.py agents/config.py"
    echo "   nano agents/config.py"
    echo "   # 修改MySQL和Redis连接配置"
    echo ""
    echo "3. 测试数据库连接："
    echo "   source venv/bin/activate"
    echo "   python test_connections.py"
    echo ""
    echo "4. 启动独立服务："
    echo "   # 前台启动（测试）："
    echo "   python start_standalone.py"
    echo "   # 或后台启动（生产）："
    echo "   sudo systemctl start algorithm-agent"
    echo ""
    echo "5. 验证服务："
    echo "   curl http://localhost:8008/health"
    echo ""
    echo "📁 项目目录: $PROJECT_ROOT"
    echo "📝 配置模板: $PROJECT_ROOT/config_template.py"
    echo "🚀 启动脚本: $PROJECT_ROOT/start_standalone.py"
    echo "🔧 服务管理: sudo systemctl {start|stop|restart|status} algorithm-agent"
    echo ""
    echo "🌐 服务访问地址："
    echo "   健康检查: http://your-server-ip:8008/health"
    echo "   前端界面: http://your-server-ip:8008/static/html/chat_interface_v2.html"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "独立智能体服务自动部署脚本"
    echo "专注于Agent和WebSocket功能"
    echo "========================================"
    echo ""
    
    check_root
    detect_os
    check_requirements
    install_system_deps
    create_project_structure
    setup_python_env
    install_python_deps
    setup_environment
    create_config_template
    create_startup_scripts
    create_systemd_service
    create_backup_script
    show_next_steps
    
    log_info "独立智能体服务自动部署脚本执行完成！"
}

# 执行主函数
main "$@"
