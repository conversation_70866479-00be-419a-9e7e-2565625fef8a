#!/bin/bash

# ==========================================
# 241服务器WebSocket修复部署脚本
# ==========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVER_IP="***********"    # 241服务器IP地址
SERVER_USER="root"         # 请根据实际用户修改
PROJECT_NAME="twinbuilder-agents"
REMOTE_PROJECT_PATH="/opt/${PROJECT_NAME}"
SERVICE_NAME="predict-algorithm-agent"
SERVICE_PORT="8008"

# 本地项目路径
LOCAL_PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../" && pwd)"

echo -e "${BLUE}===========================================${NC}"
echo -e "${BLUE}241服务器WebSocket修复部署脚本${NC}"
echo -e "${BLUE}===========================================${NC}"
echo -e "服务器IP: ${GREEN}${SERVER_IP}${NC}"
echo -e "项目路径: ${GREEN}${REMOTE_PROJECT_PATH}${NC}"
echo -e "服务名称: ${GREEN}${SERVICE_NAME}${NC}"
echo -e "服务端口: ${GREEN}${SERVICE_PORT}${NC}"
echo ""

# 函数：打印步骤
print_step() {
    echo -e "${YELLOW}[步骤] $1${NC}"
}

# 函数：打印成功
print_success() {
    echo -e "${GREEN}[成功] $1${NC}"
}

# 函数：打印错误
print_error() {
    echo -e "${RED}[错误] $1${NC}"
}

# 函数：检查SSH连接
check_ssh_connection() {
    print_step "检查SSH连接..."
    if ssh -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_IP} "echo 'SSH连接成功'" > /dev/null 2>&1; then
        print_success "SSH连接正常"
    else
        print_error "SSH连接失败，请检查网络和认证"
        echo "请确保："
        echo "1. 服务器IP地址正确: ${SERVER_IP}"
        echo "2. SSH密钥已配置或可以密码登录"
        echo "3. 网络连接正常"
        exit 1
    fi
}

# 函数：备份现有服务
backup_existing_service() {
    print_step "备份现有服务..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << 'EOF'
        # 创建备份目录
        BACKUP_DIR="/opt/backup/$(date +%Y%m%d_%H%M%S)"
        mkdir -p ${BACKUP_DIR}
        
        # 停止现有服务
        if systemctl is-active --quiet predict-algorithm-agent; then
            echo "停止现有服务..."
            systemctl stop predict-algorithm-agent
        fi
        
        # 备份现有代码
        if [ -d "/opt/twinbuilder-agents" ]; then
            echo "备份现有代码到 ${BACKUP_DIR}..."
            cp -r /opt/twinbuilder-agents ${BACKUP_DIR}/
            echo "备份完成: ${BACKUP_DIR}/twinbuilder-agents"
        fi
        
        # 备份服务配置
        if [ -f "/etc/systemd/system/predict-algorithm-agent.service" ]; then
            cp /etc/systemd/system/predict-algorithm-agent.service ${BACKUP_DIR}/
            echo "服务配置已备份"
        fi
EOF
    
    print_success "备份完成"
}

# 函数：上传修复后的代码
upload_fixed_code() {
    print_step "上传修复后的代码..."
    
    # 创建临时压缩包
    TEMP_ARCHIVE="/tmp/${PROJECT_NAME}_fixed_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    echo "创建代码压缩包..."
    cd "${LOCAL_PROJECT_PATH}"
    tar -czf "${TEMP_ARCHIVE}" \
        --exclude='.git' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='.venv' \
        --exclude='logs/*' \
        --exclude='*.log' \
        agents/
    
    echo "上传代码到服务器..."
    scp "${TEMP_ARCHIVE}" ${SERVER_USER}@${SERVER_IP}:/tmp/
    
    # 在服务器上解压
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        # 创建项目目录
        mkdir -p ${REMOTE_PROJECT_PATH}
        
        # 解压代码
        cd ${REMOTE_PROJECT_PATH}
        tar -xzf /tmp/$(basename ${TEMP_ARCHIVE})
        
        # 清理临时文件
        rm /tmp/$(basename ${TEMP_ARCHIVE})
        
        # 设置权限
        chown -R ${SERVER_USER}:${SERVER_USER} ${REMOTE_PROJECT_PATH}
        chmod +x ${REMOTE_PROJECT_PATH}/agents/predict_algorith_agent/scripts/*.sh
        
        echo "代码上传完成"
EOF
    
    # 清理本地临时文件
    rm "${TEMP_ARCHIVE}"
    
    print_success "代码上传完成"
}

# 函数：安装依赖
install_dependencies() {
    print_step "安装Python依赖..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        cd ${REMOTE_PROJECT_PATH}
        
        # 检查Python版本
        python3 --version
        
        # 安装pip依赖
        if [ -f "requirements.txt" ]; then
            pip3 install -r requirements.txt
        fi
        
        # 安装核心依赖
        pip3 install fastapi uvicorn websockets pymysql python-dotenv pydantic pydantic-ai redis
        
        echo "依赖安装完成"
EOF
    
    print_success "依赖安装完成"
}

# 函数：配置环境变量
configure_environment() {
    print_step "配置环境变量..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        cd ${REMOTE_PROJECT_PATH}/agents/predict_algorith_agent
        
        # 确保.env.production文件存在且配置正确
        if [ ! -f ".env.production" ]; then
            echo "创建.env.production文件..."
            cat > .env.production << 'ENVEOF'
# 生产环境配置文件
PRODUCTION_MODE=true

# 日志配置
LOG_LEVEL=WARNING
ENABLE_FILE_LOGGING=true
LOG_FILE_PATH=logs/agent_production.log

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8008

# 数据库配置
DB_HOST=***********
DB_PORT=3306
DB_NAME=indusaio_agent
DB_USER=root
DB_PASSWORD=Spsm2021+

# Redis配置
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=Sygy@2025

# WebSocket欢迎消息配置（默认关闭，由前端提供引导词）
ENABLE_WELCOME_MESSAGE=false

# 大模型配置
LLM_PROVIDER=qwen
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2000
LLM_TIMEOUT=30
ENVEOF
        fi
        
        # 创建日志目录
        mkdir -p logs
        
        # 设置权限
        chmod 600 .env.production
        
        echo "环境配置完成"
EOF
    
    print_success "环境配置完成"
}

# 函数：创建systemd服务
create_systemd_service() {
    print_step "创建systemd服务..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        # 创建服务配置文件
        cat > /etc/systemd/system/${SERVICE_NAME}.service << 'SERVICEEOF'
[Unit]
Description=Predict Algorithm Agent WebSocket Service
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=${REMOTE_PROJECT_PATH}/agents/predict_algorith_agent
Environment=PYTHONPATH=${REMOTE_PROJECT_PATH}
Environment=PYTHONUNBUFFERED=1
ExecStart=/usr/bin/python3 predict_main.py
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
SERVICEEOF

        # 重新加载systemd配置
        systemctl daemon-reload
        
        # 启用服务
        systemctl enable ${SERVICE_NAME}
        
        echo "systemd服务创建完成"
EOF
    
    print_success "systemd服务创建完成"
}

# 函数：启动服务
start_service() {
    print_step "启动服务..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        # 启动服务
        systemctl start ${SERVICE_NAME}
        
        # 等待服务启动
        sleep 5
        
        # 检查服务状态
        if systemctl is-active --quiet ${SERVICE_NAME}; then
            echo "服务启动成功"
            systemctl status ${SERVICE_NAME} --no-pager -l
        else
            echo "服务启动失败"
            systemctl status ${SERVICE_NAME} --no-pager -l
            journalctl -u ${SERVICE_NAME} --no-pager -l -n 20
            exit 1
        fi
EOF
    
    print_success "服务启动成功"
}

# 函数：运行验证测试
run_verification_tests() {
    print_step "运行验证测试..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        cd ${REMOTE_PROJECT_PATH}/agents/predict_algorith_agent/scripts
        
        # 运行数据库连接测试
        echo "运行数据库连接测试..."
        python3 test_database_connection.py
        
        # 等待服务完全启动
        sleep 10
        
        # 检查端口是否监听
        if netstat -tlnp | grep :${SERVICE_PORT}; then
            echo "服务端口 ${SERVICE_PORT} 正在监听"
        else
            echo "警告: 服务端口 ${SERVICE_PORT} 未监听"
        fi
        
        # 检查进程
        if pgrep -f "predict_main.py"; then
            echo "服务进程运行正常"
        else
            echo "警告: 未找到服务进程"
        fi
EOF
    
    print_success "验证测试完成"
}

# 函数：显示部署结果
show_deployment_result() {
    print_step "获取部署结果..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        echo "==========================================="
        echo "部署结果总结"
        echo "==========================================="
        
        # 服务状态
        echo "服务状态:"
        systemctl is-active ${SERVICE_NAME} && echo "  ✅ 服务运行中" || echo "  ❌ 服务未运行"
        
        # 端口监听
        echo "端口监听:"
        netstat -tlnp | grep :${SERVICE_PORT} && echo "  ✅ 端口 ${SERVICE_PORT} 正在监听" || echo "  ❌ 端口未监听"
        
        # 日志文件
        echo "日志文件:"
        if [ -f "${REMOTE_PROJECT_PATH}/agents/predict_algorith_agent/logs/agent_production.log" ]; then
            echo "  ✅ 日志文件存在"
            echo "  最新日志:"
            tail -5 ${REMOTE_PROJECT_PATH}/agents/predict_algorith_agent/logs/agent_production.log
        else
            echo "  ⚠️ 日志文件不存在"
        fi
        
        echo ""
        echo "WebSocket连接地址: ws://${SERVER_IP}:${SERVICE_PORT}/ws"
        echo "服务管理命令:"
        echo "  启动: systemctl start ${SERVICE_NAME}"
        echo "  停止: systemctl stop ${SERVICE_NAME}"
        echo "  重启: systemctl restart ${SERVICE_NAME}"
        echo "  状态: systemctl status ${SERVICE_NAME}"
        echo "  日志: journalctl -u ${SERVICE_NAME} -f"
EOF
}

# 主执行流程
main() {
    echo -e "${BLUE}开始部署流程...${NC}"
    echo ""
    
    # 确认部署
    read -p "确认要部署到 ${SERVER_IP} 吗? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_ssh_connection
    backup_existing_service
    upload_fixed_code
    install_dependencies
    configure_environment
    create_systemd_service
    start_service
    run_verification_tests
    show_deployment_result
    
    echo ""
    echo -e "${GREEN}===========================================${NC}"
    echo -e "${GREEN}部署完成！${NC}"
    echo -e "${GREEN}===========================================${NC}"
    echo -e "WebSocket服务地址: ${GREEN}ws://${SERVER_IP}:${SERVICE_PORT}/ws${NC}"
    echo -e "服务管理: ${YELLOW}systemctl [start|stop|restart|status] ${SERVICE_NAME}${NC}"
    echo -e "查看日志: ${YELLOW}journalctl -u ${SERVICE_NAME} -f${NC}"
    echo ""
    echo -e "${YELLOW}建议接下来：${NC}"
    echo "1. 测试WebSocket连接"
    echo "2. 监控服务日志"
    echo "3. 进行功能验证"
    echo "4. 如有问题，可使用备份快速回滚"
}

# 执行主函数
main "$@"
