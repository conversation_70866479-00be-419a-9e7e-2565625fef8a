#!/bin/bash
# Agent服务状态检查脚本
# 用于检查算法智能体服务的运行状态

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
SERVER_PORT="8008"

echo -e "${BLUE}🔍 检查算法智能体服务状态${NC}"
echo "=================================="

# 1. 检查PID文件
echo -e "${BLUE}📋 检查PID文件...${NC}"
if [ -f "$PROJECT_DIR/server.pid" ]; then
    PID=$(cat $PROJECT_DIR/server.pid)
    echo "PID文件存在: $PID"
    
    # 检查进程是否真实存在
    if kill -0 $PID 2>/dev/null; then
        echo -e "${GREEN}✅ 进程存在 (PID: $PID)${NC}"
        PROCESS_EXISTS=true
    else
        echo -e "${RED}❌ 进程不存在 (PID: $PID)${NC}"
        PROCESS_EXISTS=false
    fi
else
    echo -e "${YELLOW}⚠️  PID文件不存在${NC}"
    PID=""
    PROCESS_EXISTS=false
fi

echo ""

# 2. 通过进程名查找
echo -e "${BLUE}🔍 通过进程名查找...${NC}"
echo "查找包含 'predict_main' 的进程:"
PREDICT_PROCESSES=$(ps aux | grep predict_main | grep -v grep)
if [ ! -z "$PREDICT_PROCESSES" ]; then
    echo -e "${GREEN}找到predict_main进程:${NC}"
    echo "$PREDICT_PROCESSES"
else
    echo -e "${YELLOW}未找到predict_main进程${NC}"
fi

echo ""
echo "查找包含 'uvicorn' 的进程:"
UVICORN_PROCESSES=$(ps aux | grep uvicorn | grep -v grep)
if [ ! -z "$UVICORN_PROCESSES" ]; then
    echo -e "${GREEN}找到uvicorn进程:${NC}"
    echo "$UVICORN_PROCESSES"
else
    echo -e "${YELLOW}未找到uvicorn进程${NC}"
fi

echo ""
echo "查找包含 'start_server.py' 的进程:"
START_SERVER_PROCESSES=$(ps aux | grep start_server.py | grep -v grep)
if [ ! -z "$START_SERVER_PROCESSES" ]; then
    echo -e "${GREEN}找到start_server.py进程:${NC}"
    echo "$START_SERVER_PROCESSES"
else
    echo -e "${YELLOW}未找到start_server.py进程${NC}"
fi

echo ""

# 3. 检查端口监听
echo -e "${BLUE}🌐 检查端口监听...${NC}"
PORT_LISTEN=$(netstat -tlnp 2>/dev/null | grep ":$SERVER_PORT ")
if [ ! -z "$PORT_LISTEN" ]; then
    echo -e "${GREEN}✅ 端口 $SERVER_PORT 正在监听:${NC}"
    echo "$PORT_LISTEN"
    
    # 提取监听进程的PID
    LISTEN_PID=$(echo "$PORT_LISTEN" | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$LISTEN_PID" ] && [ "$LISTEN_PID" != "-" ]; then
        echo "监听进程PID: $LISTEN_PID"
    fi
else
    echo -e "${RED}❌ 端口 $SERVER_PORT 未被监听${NC}"
fi

echo ""

# 4. 检查Python进程
echo -e "${BLUE}🐍 检查Python进程...${NC}"
PYTHON_PROCESSES=$(ps aux | grep python | grep -E "(agent|predict|uvicorn)" | grep -v grep)
if [ ! -z "$PYTHON_PROCESSES" ]; then
    echo -e "${GREEN}找到相关Python进程:${NC}"
    echo "$PYTHON_PROCESSES"
else
    echo -e "${YELLOW}未找到相关Python进程${NC}"
fi

echo ""

# 5. 检查项目目录中的进程
echo -e "${BLUE}📁 检查项目目录相关进程...${NC}"
PROJECT_PROCESSES=$(ps aux | grep "$PROJECT_DIR" | grep -v grep)
if [ ! -z "$PROJECT_PROCESSES" ]; then
    echo -e "${GREEN}找到项目目录相关进程:${NC}"
    echo "$PROJECT_PROCESSES"
else
    echo -e "${YELLOW}未找到项目目录相关进程${NC}"
fi

echo ""

# 6. 测试HTTP连接
echo -e "${BLUE}🌐 测试HTTP连接...${NC}"
if command -v curl >/dev/null 2>&1; then
    HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$SERVER_PORT/api/ws/health 2>/dev/null)
    if [ "$HTTP_RESPONSE" = "200" ]; then
        echo -e "${GREEN}✅ HTTP服务响应正常 (状态码: $HTTP_RESPONSE)${NC}"
    else
        echo -e "${RED}❌ HTTP服务无响应 (状态码: $HTTP_RESPONSE)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  curl命令不可用，跳过HTTP测试${NC}"
fi

echo ""

# 7. 检查日志文件
echo -e "${BLUE}📄 检查日志文件...${NC}"
if [ -f "$PROJECT_DIR/logs/server.log" ]; then
    echo -e "${GREEN}日志文件存在: $PROJECT_DIR/logs/server.log${NC}"
    echo "最后10行日志:"
    echo "----------------------------------------"
    tail -10 "$PROJECT_DIR/logs/server.log"
    echo "----------------------------------------"
else
    echo -e "${YELLOW}⚠️  日志文件不存在${NC}"
fi

echo ""

# 8. 总结状态
echo -e "${BLUE}📊 服务状态总结${NC}"
echo "=================================="

if [ "$PROCESS_EXISTS" = true ] && [ ! -z "$PORT_LISTEN" ]; then
    echo -e "${GREEN}✅ 服务运行正常${NC}"
    echo "   - 进程PID: $PID"
    echo "   - 端口监听: $SERVER_PORT"
    echo "   - 服务地址: http://***********:$SERVER_PORT"
    echo "   - WebSocket: ws://***********:$SERVER_PORT/api/ws/chat"
elif [ ! -z "$PORT_LISTEN" ]; then
    echo -e "${YELLOW}⚠️  服务可能运行中，但PID文件异常${NC}"
    echo "   - 端口监听: $SERVER_PORT"
    echo "   - 建议检查进程: $LISTEN_PID"
elif [ "$PROCESS_EXISTS" = true ]; then
    echo -e "${YELLOW}⚠️  进程存在但端口未监听${NC}"
    echo "   - 进程PID: $PID"
    echo "   - 建议检查日志文件"
else
    echo -e "${RED}❌ 服务未运行${NC}"
    echo "   - 进程不存在"
    echo "   - 端口未监听"
fi

echo ""

# 9. 提供管理建议
echo -e "${BLUE}💡 管理建议${NC}"
echo "=================================="
echo "启动服务: cd $PROJECT_DIR && ./agents/predict_algorith_agent/scripts/quick_start.sh"
echo "查看日志: tail -f $PROJECT_DIR/logs/server.log"
echo "停止服务: kill \$(cat $PROJECT_DIR/server.pid) 2>/dev/null || pkill -f predict_main"
echo "检查端口: netstat -tlnp | grep :$SERVER_PORT"
echo "测试连接: curl http://***********:$SERVER_PORT/api/ws/health"
