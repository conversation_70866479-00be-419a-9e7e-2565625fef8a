#!/bin/bash
# WebSocket连接问题诊断脚本
# 适用于远程Linux服务器***********

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_DIR="/data/agent"
SERVER_PORT="8008"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查基础环境
check_basic_environment() {
    log_info "检查基础环境..."
    echo "=" * 50
    
    # 检查项目目录
    if [ -d "$PROJECT_DIR" ]; then
        log_success "项目目录存在: $PROJECT_DIR"
    else
        log_error "项目目录不存在: $PROJECT_DIR"
        return 1
    fi
    
    # 检查关键文件
    cd "$PROJECT_DIR"
    
    if [ -f "agents/predict_algorith_agent/predict_main.py" ]; then
        log_success "主启动文件存在"
    else
        log_error "主启动文件不存在: predict_main.py"
    fi
    
    if [ -f "agents/config.py" ]; then
        log_success "配置文件存在"
    else
        log_error "配置文件不存在: config.py"
    fi
    
    if [ -f "activate_env.sh" ]; then
        log_success "环境激活脚本存在"
    else
        log_warning "环境激活脚本不存在"
    fi
    
    echo ""
}

# 检查进程状态
check_process_status() {
    log_info "检查进程状态..."
    echo "=" * 50
    
    cd "$PROJECT_DIR"
    
    # 检查服务器进程
    if [ -f "server.pid" ]; then
        server_pid=$(cat server.pid)
        if kill -0 "$server_pid" 2>/dev/null; then
            log_success "服务器进程运行中 (PID: $server_pid)"
            
            # 显示进程详情
            echo "📋 进程详情:"
            ps aux | grep "$server_pid" | grep -v grep || log_warning "无法获取进程详情"
        else
            log_error "服务器进程未运行 (PID文件存在但进程不存在)"
            rm -f server.pid
        fi
    else
        log_error "服务器PID文件不存在"
    fi
    
    # 检查日志监控进程
    if [ -f "log_monitor.pid" ]; then
        monitor_pid=$(cat log_monitor.pid)
        if kill -0 "$monitor_pid" 2>/dev/null; then
            log_success "日志监控进程运行中 (PID: $monitor_pid)"
        else
            log_warning "日志监控进程未运行"
            rm -f log_monitor.pid
        fi
    else
        log_warning "日志监控PID文件不存在"
    fi
    
    echo ""
}

# 检查网络状态
check_network_status() {
    log_info "检查网络状态..."
    echo "=" * 50
    
    # 检查端口监听
    if netstat -tlnp | grep ":$SERVER_PORT " > /dev/null; then
        log_success "端口 $SERVER_PORT 正在监听"
        
        # 显示端口详情
        echo "📋 端口详情:"
        netstat -tlnp | grep ":$SERVER_PORT "
    else
        log_error "端口 $SERVER_PORT 未监听"
    fi
    
    # 检查所有Python进程
    echo ""
    log_info "所有Python进程:"
    ps aux | grep python | grep -v grep || log_warning "没有找到Python进程"
    
    echo ""
}

# 检查日志文件
check_log_files() {
    log_info "检查日志文件..."
    echo "=" * 50
    
    cd "$PROJECT_DIR"
    
    # 检查服务器日志
    if [ -f "logs/server.log" ]; then
        file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
        file_size_mb=$((file_size / 1024 / 1024))
        log_success "服务器日志存在 (${file_size_mb}MB)"
        
        echo "📄 最近的日志内容:"
        echo "----------------------------------------"
        tail -20 logs/server.log
        echo "----------------------------------------"
    else
        log_error "服务器日志文件不存在"
    fi
    
    echo ""
}

# 检查Python环境
check_python_environment() {
    log_info "检查Python环境..."
    echo "=" * 50
    
    cd "$PROJECT_DIR"
    
    # 激活环境
    if [ -f "activate_env.sh" ]; then
        source activate_env.sh
    fi
    
    # 检查Python版本
    python_version=$(python3 --version 2>/dev/null || echo "未安装")
    log_info "Python版本: $python_version"
    
    # 检查关键依赖
    echo "📦 关键依赖检查:"
    
    python3 -c "import fastapi; print('✅ fastapi')" 2>/dev/null || echo "❌ fastapi"
    python3 -c "import uvicorn; print('✅ uvicorn')" 2>/dev/null || echo "❌ uvicorn"
    python3 -c "import websockets; print('✅ websockets')" 2>/dev/null || echo "❌ websockets"
    python3 -c "import pydantic_ai; print('✅ pydantic_ai')" 2>/dev/null || echo "❌ pydantic_ai"
    python3 -c "import mysql.connector; print('✅ mysql-connector')" 2>/dev/null || echo "❌ mysql-connector"
    python3 -c "import redis; print('✅ redis')" 2>/dev/null || echo "❌ redis"
    
    echo ""
}

# 测试HTTP端点
test_http_endpoints() {
    log_info "测试HTTP端点..."
    echo "=" * 50
    
    # 测试根路径
    log_info "测试根路径..."
    if curl -s "http://localhost:$SERVER_PORT/" > /dev/null; then
        log_success "根路径访问正常"
    else
        log_error "根路径访问失败"
    fi
    
    # 测试健康检查
    log_info "测试健康检查..."
    if curl -s "http://localhost:$SERVER_PORT/health" > /dev/null; then
        log_success "健康检查正常"
    else
        log_error "健康检查失败"
    fi
    
    # 测试WebSocket健康检查
    log_info "测试WebSocket健康检查..."
    if curl -s "http://localhost:$SERVER_PORT/api/ws/health" > /dev/null; then
        log_success "WebSocket健康检查正常"
        
        # 显示详细响应
        echo "📋 WebSocket健康检查响应:"
        curl -s "http://localhost:$SERVER_PORT/api/ws/health" | python3 -m json.tool 2>/dev/null || echo "响应格式异常"
    else
        log_error "WebSocket健康检查失败"
    fi
    
    # 测试API文档
    log_info "测试API文档..."
    if curl -s "http://localhost:$SERVER_PORT/docs" > /dev/null; then
        log_success "API文档访问正常"
    else
        log_warning "API文档访问失败"
    fi
    
    echo ""
}

# 运行完整诊断
run_full_diagnosis() {
    echo "🔍 WebSocket连接问题诊断工具"
    echo "📍 目标服务器: ***********"
    echo "📂 项目目录: $PROJECT_DIR"
    echo "🔌 服务端口: $SERVER_PORT"
    echo "=" * 60
    
    # 1. 检查基础环境
    check_basic_environment
    
    # 2. 检查进程状态
    check_process_status
    
    # 3. 检查网络状态
    check_network_status
    
    # 4. 检查Python环境
    check_python_environment
    
    # 5. 测试HTTP端点
    test_http_endpoints
    
    # 6. 检查日志文件
    check_log_files
    
    echo "🎯 诊断完成！"
    echo "=" * 60
    
    # 给出建议
    echo "💡 问题排查建议:"
    echo "  1. 如果进程未运行: ./manage_server.sh start"
    echo "  2. 如果端口未监听: 检查启动日志中的错误信息"
    echo "  3. 如果依赖缺失: 安装缺失的Python包"
    echo "  4. 如果WebSocket连接失败: 确保user_id参数正确传递"
    echo "  5. 运行WebSocket测试: python agents/predict_algorith_agent/scripts/test_websocket_241.py"
    echo ""
    echo "🔧 常用修复命令:"
    echo "  ./manage_server.sh restart  # 重启服务"
    echo "  ./manage_server.sh logs     # 查看详细日志"
    echo "  ./manage_server.sh test     # 运行WebSocket测试"
    echo "=" * 60
}

# 主函数
case "$1" in
    full|"")
        run_full_diagnosis
        ;;
    env)
        check_python_environment
        ;;
    process)
        check_process_status
        ;;
    network)
        check_network_status
        ;;
    logs)
        check_log_files
        ;;
    http)
        test_http_endpoints
        ;;
    help|--help|-h)
        echo "🔍 WebSocket诊断工具"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  full     - 运行完整诊断 (默认)"
        echo "  env      - 检查Python环境"
        echo "  process  - 检查进程状态"
        echo "  network  - 检查网络状态"
        echo "  logs     - 检查日志文件"
        echo "  http     - 测试HTTP端点"
        echo "  help     - 显示帮助信息"
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 $0 help 查看帮助"
        exit 1
        ;;
esac
