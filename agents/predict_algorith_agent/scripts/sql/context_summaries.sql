/*
 Navicat Premium Data Transfer

 Source Server         : 交大仿真_indusaio_agent
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : *********:23306
 Source Schema         : indusaio_agent

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 30/07/2025 13:37:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for context_summaries
-- ----------------------------
DROP TABLE IF EXISTS `context_summaries`;
CREATE TABLE `context_summaries`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '摘要唯一标识',
  `conversation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对话ID',
  `summary_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '摘要内容',
  `key_points` json NULL COMMENT '关键要点',
  `message_count` int(11) NULL DEFAULT 0 COMMENT '摘要的消息数量',
  `token_count` int(11) NULL DEFAULT 0 COMMENT '摘要token数量',
  `summary_type` enum('auto','manual','periodic') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'auto' COMMENT '摘要类型',
  `summary_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '摘要生成时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id`) USING BTREE,
  INDEX `idx_summary_time`(`summary_time`) USING BTREE,
  INDEX `idx_summary_type`(`summary_type`) USING BTREE,
  CONSTRAINT `context_summaries_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`conversation_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '对话摘要表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
