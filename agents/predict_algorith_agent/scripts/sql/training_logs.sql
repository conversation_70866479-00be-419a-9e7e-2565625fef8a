/*
 Navicat Premium Data Transfer

 Source Server         : 交大仿真_indusaio_agent
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : *********:23306
 Source Schema         : indusaio_agent

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 30/07/2025 13:39:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for training_logs
-- ----------------------------
DROP TABLE IF EXISTS `training_logs`;
CREATE TABLE `training_logs`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志唯一标识',
  `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `log_level` enum('DEBUG','INFO','WARNING','ERROR','CRITICAL') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'INFO' COMMENT '日志级别',
  `log_type` enum('training','validation','system','error') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'training' COMMENT '日志类型',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志消息',
  `metrics` json NULL COMMENT '训练指标数据(loss, accuracy等)',
  `epoch` int(11) NULL DEFAULT NULL COMMENT '训练轮次',
  `step` int(11) NULL DEFAULT NULL COMMENT '训练步数',
  `timestamp` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '日志时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_log_level`(`log_level`) USING BTREE,
  INDEX `idx_log_type`(`log_type`) USING BTREE,
  INDEX `idx_timestamp`(`timestamp`) USING BTREE,
  INDEX `idx_epoch`(`epoch`) USING BTREE,
  CONSTRAINT `training_logs_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `algorithm_tasks` (`task_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '训练日志表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
