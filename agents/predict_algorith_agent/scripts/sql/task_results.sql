/*
 Navicat Premium Data Transfer

 Source Server         : 交大仿真_indusaio_agent
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : *********:23306
 Source Schema         : indusaio_agent

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 30/07/2025 13:39:22
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for task_results
-- ----------------------------
DROP TABLE IF EXISTS `task_results`;
CREATE TABLE `task_results`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '结果唯一标识',
  `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `result_type` enum('training_result','evaluation_result','prediction_result') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'training_result' COMMENT '结果类型',
  `accuracy` decimal(8, 6) NULL DEFAULT NULL COMMENT '准确率',
  `loss` decimal(10, 6) NULL DEFAULT NULL COMMENT '损失值',
  `metrics` json NULL COMMENT '详细评估指标',
  `model_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型文件路径',
  `model_size` bigint(20) NULL DEFAULT NULL COMMENT '模型文件大小(字节)',
  `evaluation_report` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评估报告',
  `gb_standard_score` decimal(5, 2) NULL DEFAULT NULL COMMENT 'GB标准评分',
  `gb_standard_level` enum('优秀','良好','合格','不合格') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'GB标准等级',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `task_id`(`task_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_result_type`(`result_type`) USING BTREE,
  INDEX `idx_accuracy`(`accuracy`) USING BTREE,
  INDEX `idx_gb_standard_level`(`gb_standard_level`) USING BTREE,
  CONSTRAINT `task_results_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `algorithm_tasks` (`task_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '任务结果表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
