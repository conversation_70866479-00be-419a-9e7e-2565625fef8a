/*
 Navicat Premium Data Transfer

 Source Server         : 交大仿真_indusaio_agent
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : *********:23306
 Source Schema         : indusaio_agent

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 30/07/2025 13:38:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for conversations
-- ----------------------------
DROP TABLE IF EXISTS `conversations`;
CREATE TABLE `conversations`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '对话唯一标识',
  `conversation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对话ID（业务主键）',
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '新建算法训练任务' COMMENT '对话标题',
  `conversation_type` enum('training','query','report','guide') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'training' COMMENT '对话类型',
  `status` enum('active','completed','failed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '对话状态',
  `current_stage` enum('welcome','guided','param_extraction','param_confirmation','training','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'welcome' COMMENT '当前阶段',
  `progress` decimal(5, 2) NULL COMMENT '进度百分比(0-100)',
  `task_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '任务摘要描述',
  `algorithm_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '算法类型(LSTM/CNN/等)',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `completed_at` timestamp(0) NULL DEFAULT NULL COMMENT '完成时间',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'WebSocket会话ID',
  `last_message_at` timestamp(0) NULL DEFAULT NULL COMMENT '最后消息时间',
  `context_token_count` int(11) NULL DEFAULT 0 COMMENT '上下文token数量',
  `auto_title` tinyint(1) NULL DEFAULT 1 COMMENT '是否自动生成标题',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `conversation_id`(`conversation_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_conversation_id`(`conversation_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_conversation_type`(`conversation_type`) USING BTREE,
  INDEX `idx_session_id`(`session_id`) USING BTREE,
  INDEX `idx_last_message_at`(`last_message_at`) USING BTREE,
  INDEX `idx_user_last_message`(`user_id`, `last_message_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 152 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '对话管理表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
