/*
 Navicat Premium Data Transfer

 Source Server         : 交大仿真_indusaio_agent
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : *********:23306
 Source Schema         : indusaio_agent

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 30/07/2025 13:39:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for task_parameters
-- ----------------------------
DROP TABLE IF EXISTS `task_parameters`;
CREATE TABLE `task_parameters`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参数记录唯一标识',
  `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `parameter_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参数名称',
  `parameter_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '参数值',
  `parameter_type` enum('int','float','string','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'string' COMMENT '参数类型',
  `is_required` tinyint(1) NULL DEFAULT 0 COMMENT '是否必需参数',
  `default_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '默认值',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '参数描述',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_task_parameter`(`task_id`, `parameter_name`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_parameter_name`(`parameter_name`) USING BTREE,
  CONSTRAINT `task_parameters_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `algorithm_tasks` (`task_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '任务参数表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
