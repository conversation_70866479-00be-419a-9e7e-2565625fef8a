/*
 Navicat Premium Data Transfer

 Source Server         : 交大仿真_indusaio_agent
 Source Server Type    : MySQL
 Source Server Version : 50734
 Source Host           : *********:23306
 Source Schema         : indusaio_agent

 Target Server Type    : MySQL
 Target Server Version : 50734
 File Encoding         : 65001

 Date: 30/07/2025 13:36:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for agent_session
-- ----------------------------
DROP TABLE IF EXISTS `agent_session`;
CREATE TABLE `agent_session`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户唯一标识',
  `session_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会话唯一标识',
  `task_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三方任务ID',
  `state` json NOT NULL COMMENT 'Agent内部状态(JSON序列化)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '逻辑删除标志,0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_user_session`(`user_id`, `session_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 202 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Agent会话与任务状态表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
