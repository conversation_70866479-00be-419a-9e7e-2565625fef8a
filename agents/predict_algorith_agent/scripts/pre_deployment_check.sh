#!/bin/bash
# 部署前检查脚本
# 确保新版本可以正确启动，WebSocket请求使用INFO级别日志，server.log文件大小控制在2GB

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
LOG_FILE="$PROJECT_DIR/logs/server.log"
ENV_FILE="$PROJECT_DIR/agents/predict_algorith_agent/.env"
LOGGING_CONFIG="$PROJECT_DIR/agents/predict_algorith_agent/utils/logging_config.py"
START_SCRIPT="$PROJECT_DIR/agents/predict_algorith_agent/scripts/start_server_background.sh"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    local required_files=(
        "$PROJECT_DIR/agents/predict_algorith_agent/predict_main.py"
        "$PROJECT_DIR/agents/predict_algorith_agent/.env"
        "$PROJECT_DIR/agents/predict_algorith_agent/utils/logging_config.py"
        "$PROJECT_DIR/agents/predict_algorith_agent/scripts/start_server_background.sh"
        "$PROJECT_DIR/agents/predict_algorith_agent/scripts/dynamic_log_monitor.py"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -eq 0 ]; then
        log_success "所有必需文件存在"
    else
        log_error "缺少以下文件:"
        for file in "${missing_files[@]}"; do
            log_error "  - $file"
        done
        return 1
    fi
}

# 检查日志级别配置
check_log_level_config() {
    log_info "检查日志级别配置..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_error ".env文件不存在: $ENV_FILE"
        return 1
    fi
    
    # 检查全局日志级别
    local log_level=$(grep "^LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2)
    if [ "$log_level" = "INFO" ]; then
        log_success "全局日志级别: INFO ✓"
    else
        log_warning "全局日志级别: $log_level (建议使用INFO)"
    fi
    
    # 检查模块日志级别
    local main_log_level=$(grep "^MAIN_LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2)
    local agent_log_level=$(grep "^AGENT_LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2)
    
    if [ "$main_log_level" = "INFO" ]; then
        log_success "主应用日志级别: INFO ✓"
    else
        log_warning "主应用日志级别: $main_log_level"
    fi
    
    if [ "$agent_log_level" = "INFO" ]; then
        log_success "算法智能体日志级别: INFO ✓"
    else
        log_warning "算法智能体日志级别: $agent_log_level"
    fi
    
    # 检查WebSocket日志级别
    local websocket_log_level=$(grep "^WEBSOCKET_LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2 || echo "INFO")
    if [ "$websocket_log_level" = "INFO" ] || [ -z "$websocket_log_level" ]; then
        log_success "WebSocket日志级别: INFO ✓"
    else
        log_warning "WebSocket日志级别: $websocket_log_level"
    fi
}

# 检查启动脚本配置
check_start_script() {
    log_info "检查启动脚本配置..."
    
    if [ ! -f "$START_SCRIPT" ]; then
        log_error "启动脚本不存在: $START_SCRIPT"
        return 1
    fi
    
    # 检查是否包含日志管理功能
    if grep -q "manage_log_file" "$START_SCRIPT"; then
        log_success "启动脚本包含日志管理功能 ✓"
    else
        log_error "启动脚本缺少日志管理功能"
        return 1
    fi
    
    # 检查是否包含动态日志监控
    if grep -q "dynamic_log_monitor.py" "$START_SCRIPT"; then
        log_success "启动脚本包含动态日志监控 ✓"
    else
        log_error "启动脚本缺少动态日志监控"
        return 1
    fi
    
    # 检查是否使用正确的启动文件
    if grep -q "predict_main.py" "$START_SCRIPT"; then
        log_success "启动脚本使用正确的主文件 ✓"
    else
        log_error "启动脚本未使用predict_main.py"
        return 1
    fi
    
    # 检查2GB限制配置
    if grep -q "max_size_gb=2" "$START_SCRIPT"; then
        log_success "日志文件2GB限制配置正确 ✓"
    else
        log_warning "未找到2GB限制配置"
    fi
}

# 检查动态日志监控脚本
check_log_monitor() {
    log_info "检查动态日志监控脚本..."
    
    local monitor_script="$PROJECT_DIR/agents/predict_algorith_agent/scripts/dynamic_log_monitor.py"
    
    if [ ! -f "$monitor_script" ]; then
        log_error "动态日志监控脚本不存在: $monitor_script"
        return 1
    fi
    
    # 检查脚本是否可执行
    if [ -x "$monitor_script" ]; then
        log_success "动态日志监控脚本可执行 ✓"
    else
        log_warning "动态日志监控脚本不可执行，正在设置权限..."
        chmod +x "$monitor_script"
    fi
    
    # 检查Python语法
    if python3 -m py_compile "$monitor_script" 2>/dev/null; then
        log_success "动态日志监控脚本语法正确 ✓"
    else
        log_error "动态日志监控脚本语法错误"
        return 1
    fi
}

# 检查日志目录和权限
check_log_directory() {
    log_info "检查日志目录和权限..."
    
    local log_dir="$PROJECT_DIR/logs"
    
    # 创建日志目录
    if [ ! -d "$log_dir" ]; then
        log_info "创建日志目录: $log_dir"
        mkdir -p "$log_dir"
    fi
    
    # 检查目录权限
    if [ -w "$log_dir" ]; then
        log_success "日志目录可写 ✓"
    else
        log_error "日志目录不可写: $log_dir"
        return 1
    fi
    
    # 检查现有日志文件大小
    if [ -f "$LOG_FILE" ]; then
        local file_size=$(stat -c%s "$LOG_FILE" 2>/dev/null || echo "0")
        local file_size_gb=$((file_size / 1024 / 1024 / 1024))
        local file_size_mb=$((file_size / 1024 / 1024))
        
        log_info "当前日志文件大小: ${file_size_mb}MB"
        
        if [ "$file_size_gb" -ge 2 ]; then
            log_warning "当前日志文件超过2GB，启动时将自动轮转"
        else
            log_success "当前日志文件大小正常 ✓"
        fi
    else
        log_info "日志文件不存在，将在启动时创建"
    fi
}

# 检查Python环境
check_python_environment() {
    log_info "检查Python环境..."
    
    # 检查Python版本
    if command -v python3 &> /dev/null; then
        local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
        log_success "Python版本: $python_version ✓"
    else
        log_error "Python3未安装"
        return 1
    fi
    
    # 检查必需的Python模块
    local required_modules=("fastapi" "uvicorn" "websockets" "aiohttp")
    local missing_modules=()
    
    for module in "${required_modules[@]}"; do
        if ! python3 -c "import $module" 2>/dev/null; then
            missing_modules+=("$module")
        fi
    done
    
    if [ ${#missing_modules[@]} -eq 0 ]; then
        log_success "所有必需的Python模块已安装 ✓"
    else
        log_error "缺少以下Python模块:"
        for module in "${missing_modules[@]}"; do
            log_error "  - $module"
        done
        return 1
    fi
}

# 检查端口可用性
check_port_availability() {
    log_info "检查端口可用性..."
    
    local port=8008
    
    if netstat -tlnp | grep ":$port " > /dev/null; then
        log_warning "端口 $port 已被占用"
        local pid=$(netstat -tlnp | grep ":$port " | awk '{print $7}' | cut -d'/' -f1)
        log_info "占用进程PID: $pid"
        
        # 检查是否是我们的服务
        if [ -f "$PROJECT_DIR/server.pid" ]; then
            local our_pid=$(cat "$PROJECT_DIR/server.pid")
            if [ "$pid" = "$our_pid" ]; then
                log_info "端口被我们的服务占用，这是正常的"
            else
                log_warning "端口被其他进程占用"
            fi
        fi
    else
        log_success "端口 $port 可用 ✓"
    fi
}

# 生成部署报告
generate_deployment_report() {
    log_info "生成部署检查报告..."
    
    local report_file="$PROJECT_DIR/deployment_check_report.txt"
    
    cat > "$report_file" << EOF
算法智能体系统部署前检查报告
生成时间: $(date '+%Y-%m-%d %H:%M:%S')

=== 配置检查结果 ===

1. 日志级别配置:
   - 全局日志级别: $(grep "^LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2)
   - 主应用日志级别: $(grep "^MAIN_LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2)
   - 算法智能体日志级别: $(grep "^AGENT_LOG_LEVEL=" "$ENV_FILE" | cut -d'=' -f2)

2. 日志文件管理:
   - 最大文件大小: 2GB
   - 轮转保留大小: 100MB
   - 备份保留天数: 7天
   - 动态监控: 启用

3. 启动配置:
   - 主启动文件: agents/predict_algorith_agent/predict_main.py
   - 日志输出: logs/server.log
   - 监控日志: logs/log_monitor.log

4. WebSocket配置:
   - 服务地址: ws://0.0.0.0:8008/api/ws/chat
   - 日志级别: INFO
   - 兼容性: 支持聊天对话和API调用

=== 部署建议 ===

1. 确保所有检查项都通过
2. 备份现有配置和数据
3. 使用启动脚本进行部署
4. 监控日志输出和系统性能
5. 验证WebSocket功能正常

=== 启动命令 ===

cd $PROJECT_DIR
./agents/predict_algorith_agent/scripts/start_server_background.sh

=== 管理命令 ===

查看状态: ./manage_server.sh status
查看日志: ./manage_server.sh logs
停止服务: ./manage_server.sh stop
重启服务: ./manage_server.sh restart

EOF

    log_success "部署检查报告已生成: $report_file"
}

# 主函数
main() {
    echo "🚀 算法智能体系统部署前检查"
    echo "=" * 60
    
    local check_passed=true
    
    # 执行各项检查
    check_project_structure || check_passed=false
    echo ""
    
    check_log_level_config || check_passed=false
    echo ""
    
    check_start_script || check_passed=false
    echo ""
    
    check_log_monitor || check_passed=false
    echo ""
    
    check_log_directory || check_passed=false
    echo ""
    
    check_python_environment || check_passed=false
    echo ""
    
    check_port_availability || check_passed=false
    echo ""
    
    # 生成报告
    generate_deployment_report
    echo ""
    
    # 显示最终结果
    if [ "$check_passed" = true ]; then
        log_success "🎉 所有检查通过，系统可以部署！"
        echo ""
        log_info "📋 部署步骤："
        log_info "1. cd $PROJECT_DIR"
        log_info "2. ./agents/predict_algorith_agent/scripts/start_server_background.sh"
        log_info "3. ./manage_server.sh status  # 检查启动状态"
        log_info "4. tail -f logs/server.log    # 监控日志输出"
        echo ""
        log_info "✅ 系统将自动管理日志文件大小，确保不超过2GB"
        log_info "✅ WebSocket请求将使用INFO级别日志"
        log_info "✅ 支持聊天对话和API调用的兼容性"
    else
        log_error "❌ 部分检查未通过，请修复问题后重新检查"
        echo ""
        log_info "📄 详细信息请查看: $PROJECT_DIR/deployment_check_report.txt"
    fi
    
    return $check_passed
}

# 执行主函数
main "$@"
