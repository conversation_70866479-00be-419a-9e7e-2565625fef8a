#!/bin/bash
# 日志管理脚本
# 用于自动管理算法智能体服务器的日志文件大小
# 适用于远程龙蜥Linux服务器 10.0.42.241

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/data/agent"
LOG_FILE="$PROJECT_DIR/logs/server.log"
MAX_SIZE_GB=2
MAX_SIZE_BYTES=$((MAX_SIZE_GB * 1024 * 1024 * 1024))  # 2GB in bytes
KEEP_SIZE_MB=100
KEEP_SIZE_BYTES=$((KEEP_SIZE_MB * 1024 * 1024))  # 100MB in bytes
BACKUP_RETENTION_DAYS=7

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 获取文件大小（字节）
get_file_size() {
    local file="$1"
    if [ -f "$file" ]; then
        stat -c%s "$file" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# 格式化文件大小显示
format_size() {
    local size_bytes="$1"
    local size_mb=$((size_bytes / 1024 / 1024))
    local size_gb=$((size_bytes / 1024 / 1024 / 1024))
    
    if [ "$size_gb" -gt 0 ]; then
        echo "${size_gb}GB (${size_mb}MB)"
    else
        echo "${size_mb}MB"
    fi
}

# 检查日志文件状态
check_log_status() {
    log_info "检查日志文件状态..."
    
    if [ ! -d "$PROJECT_DIR/logs" ]; then
        log_warning "日志目录不存在，创建目录: $PROJECT_DIR/logs"
        mkdir -p "$PROJECT_DIR/logs"
    fi
    
    if [ -f "$LOG_FILE" ]; then
        local file_size=$(get_file_size "$LOG_FILE")
        local formatted_size=$(format_size "$file_size")
        local max_formatted_size=$(format_size "$MAX_SIZE_BYTES")
        
        log_info "日志文件: $LOG_FILE"
        log_info "当前大小: $formatted_size"
        log_info "最大限制: $max_formatted_size"
        
        if [ "$file_size" -gt "$MAX_SIZE_BYTES" ]; then
            log_warning "日志文件超过大小限制！"
            return 1
        else
            log_success "日志文件大小正常"
            return 0
        fi
    else
        log_info "日志文件不存在: $LOG_FILE"
        return 0
    fi
}

# 执行日志轮转
rotate_log() {
    log_info "开始执行日志轮转..."
    
    if [ ! -f "$LOG_FILE" ]; then
        log_error "日志文件不存在，无法执行轮转"
        return 1
    fi
    
    local file_size=$(get_file_size "$LOG_FILE")
    local backup_file="$PROJECT_DIR/logs/server_$(date +%Y%m%d_%H%M%S).log.old"
    local temp_file="$PROJECT_DIR/logs/server_temp_$$.log"
    
    log_info "创建备份文件: $backup_file"
    log_info "保留最新内容: $(format_size $KEEP_SIZE_BYTES)"
    
    # 保留最后指定大小的日志内容
    if ! tail -c "$KEEP_SIZE_BYTES" "$LOG_FILE" > "$temp_file"; then
        log_error "创建临时文件失败"
        rm -f "$temp_file"
        return 1
    fi
    
    # 移动原文件为备份
    if ! mv "$LOG_FILE" "$backup_file"; then
        log_error "创建备份文件失败"
        rm -f "$temp_file"
        return 1
    fi
    
    # 将临时文件作为新的日志文件
    if ! mv "$temp_file" "$LOG_FILE"; then
        log_error "创建新日志文件失败"
        # 尝试恢复原文件
        mv "$backup_file" "$LOG_FILE" 2>/dev/null || true
        return 1
    fi
    
    # 设置正确的文件权限
    chmod 644 "$LOG_FILE" 2>/dev/null || true
    
    local new_size=$(get_file_size "$LOG_FILE")
    local backup_size=$(get_file_size "$backup_file")
    
    log_success "日志轮转完成："
    log_info "  - 原文件大小: $(format_size $file_size)"
    log_info "  - 新文件大小: $(format_size $new_size)"
    log_info "  - 备份文件: $backup_file ($(format_size $backup_size))"
    
    return 0
}

# 清理旧的备份文件
cleanup_old_backups() {
    log_info "清理旧的备份文件..."
    
    local logs_dir="$PROJECT_DIR/logs"
    local deleted_count=0
    
    # 查找并删除超过指定天数的备份文件
    if [ -d "$logs_dir" ]; then
        # 使用find命令查找旧文件
        local old_files=$(find "$logs_dir" -name "server_*.log.old" -mtime +$BACKUP_RETENTION_DAYS 2>/dev/null || true)
        
        if [ -n "$old_files" ]; then
            for file in $old_files; do
                if [ -f "$file" ]; then
                    local file_size=$(get_file_size "$file")
                    log_info "删除旧备份: $file ($(format_size $file_size))"
                    rm -f "$file"
                    deleted_count=$((deleted_count + 1))
                fi
            done
        fi
    fi
    
    if [ "$deleted_count" -gt 0 ]; then
        log_success "已删除 $deleted_count 个旧备份文件"
    else
        log_info "没有需要清理的旧备份文件"
    fi
}

# 显示日志统计信息
show_log_stats() {
    log_info "日志文件统计信息："
    
    local logs_dir="$PROJECT_DIR/logs"
    
    if [ -d "$logs_dir" ]; then
        # 当前日志文件
        if [ -f "$LOG_FILE" ]; then
            local current_size=$(get_file_size "$LOG_FILE")
            log_info "  📄 当前日志: server.log ($(format_size $current_size))"
        fi
        
        # 备份文件统计
        local backup_files=$(find "$logs_dir" -name "server_*.log.old" 2>/dev/null | wc -l)
        local total_backup_size=0
        
        if [ "$backup_files" -gt 0 ]; then
            for file in $(find "$logs_dir" -name "server_*.log.old" 2>/dev/null); do
                local size=$(get_file_size "$file")
                total_backup_size=$((total_backup_size + size))
            done
            
            log_info "  📦 备份文件: $backup_files 个 ($(format_size $total_backup_size))"
        else
            log_info "  📦 备份文件: 无"
        fi
        
        # 总大小
        local total_size=$(($(get_file_size "$LOG_FILE") + total_backup_size))
        log_info "  📊 总大小: $(format_size $total_size)"
    else
        log_warning "日志目录不存在: $logs_dir"
    fi
}

# 主函数
main() {
    case "${1:-check}" in
        check)
            log_info "检查日志文件状态..."
            check_log_status
            show_log_stats
            ;;
        rotate)
            log_info "手动执行日志轮转..."
            if check_log_status; then
                log_info "日志文件大小正常，是否仍要执行轮转？(y/N)"
                read -r response
                if [[ "$response" =~ ^[Yy]$ ]]; then
                    rotate_log
                    cleanup_old_backups
                else
                    log_info "取消日志轮转"
                fi
            else
                rotate_log
                cleanup_old_backups
            fi
            show_log_stats
            ;;
        auto)
            log_info "自动日志管理..."
            if ! check_log_status; then
                log_warning "日志文件超过大小限制，自动执行轮转"
                rotate_log
            fi
            cleanup_old_backups
            show_log_stats
            ;;
        cleanup)
            log_info "清理旧备份文件..."
            cleanup_old_backups
            show_log_stats
            ;;
        stats)
            show_log_stats
            ;;
        *)
            echo "用法: $0 {check|rotate|auto|cleanup|stats}"
            echo ""
            echo "命令说明:"
            echo "  check   - 检查日志文件状态（默认）"
            echo "  rotate  - 手动执行日志轮转"
            echo "  auto    - 自动日志管理（超过限制时自动轮转）"
            echo "  cleanup - 清理旧备份文件"
            echo "  stats   - 显示日志统计信息"
            echo ""
            echo "配置信息:"
            echo "  - 最大日志大小: ${MAX_SIZE_GB}GB"
            echo "  - 轮转后保留: ${KEEP_SIZE_MB}MB"
            echo "  - 备份保留天数: ${BACKUP_RETENTION_DAYS}天"
            echo "  - 日志文件路径: $LOG_FILE"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
