#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志监控脚本
监控日志文件大小和增长速度，提供预警
"""

import os
import sys
import time
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple

class LogMonitor:
    def __init__(self, log_dir: str, config_file: str = None):
        self.log_dir = Path(log_dir)
        self.config_file = config_file
        self.config = self.load_config()
        self.history_file = self.log_dir / '.log_monitor_history.json'
        self.history = self.load_history()
    
    def load_config(self) -> Dict:
        """加载配置"""
        default_config = {
            'size_warning_mb': 500,      # 文件大小警告阈值 (MB)
            'size_critical_mb': 1000,    # 文件大小严重阈值 (MB)
            'growth_warning_mb_per_hour': 50,   # 增长速度警告阈值 (MB/小时)
            'growth_critical_mb_per_hour': 100, # 增长速度严重阈值 (MB/小时)
            'check_patterns': ['*.log', '*.log.*', '*.out', '*.err'],
            'exclude_patterns': ['*.gz', '*.zip', '*.bak'],
            'max_history_days': 30       # 历史记录保留天数
        }
        
        if self.config_file and Path(self.config_file).exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                default_config.update(user_config)
            except Exception as e:
                print(f"警告: 无法加载配置文件 {self.config_file}: {e}")
        
        return default_config
    
    def load_history(self) -> Dict:
        """加载历史记录"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"警告: 无法加载历史记录: {e}")
        
        return {}
    
    def save_history(self):
        """保存历史记录"""
        try:
            # 清理过期记录
            cutoff_time = datetime.now() - timedelta(days=self.config['max_history_days'])
            cutoff_timestamp = cutoff_time.timestamp()
            
            for file_path in list(self.history.keys()):
                self.history[file_path] = [
                    record for record in self.history[file_path]
                    if record['timestamp'] > cutoff_timestamp
                ]
                if not self.history[file_path]:
                    del self.history[file_path]
            
            # 保存到文件
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, indent=2)
        except Exception as e:
            print(f"警告: 无法保存历史记录: {e}")
    
    def get_file_size_mb(self, file_path: Path) -> float:
        """获取文件大小（MB）"""
        try:
            return file_path.stat().st_size / (1024 * 1024)
        except OSError:
            return 0
    
    def get_log_files(self) -> List[Path]:
        """获取所有日志文件"""
        log_files = []
        
        for pattern in self.config['check_patterns']:
            for file_path in self.log_dir.glob(pattern):
                if file_path.is_file():
                    # 检查是否在排除列表中
                    excluded = False
                    for exclude_pattern in self.config['exclude_patterns']:
                        if file_path.match(exclude_pattern):
                            excluded = True
                            break
                    
                    if not excluded:
                        log_files.append(file_path)
        
        return log_files
    
    def calculate_growth_rate(self, file_path: Path, current_size: float) -> Tuple[float, str]:
        """计算文件增长速度"""
        file_key = str(file_path.relative_to(self.log_dir))
        current_time = datetime.now().timestamp()
        
        if file_key not in self.history:
            self.history[file_key] = []
        
        # 添加当前记录
        self.history[file_key].append({
            'timestamp': current_time,
            'size_mb': current_size
        })
        
        # 计算增长速度（基于最近1小时的数据）
        one_hour_ago = current_time - 3600
        recent_records = [
            record for record in self.history[file_key]
            if record['timestamp'] > one_hour_ago
        ]
        
        if len(recent_records) < 2:
            return 0, "数据不足"
        
        # 计算平均增长速度
        oldest_record = min(recent_records, key=lambda x: x['timestamp'])
        time_diff_hours = (current_time - oldest_record['timestamp']) / 3600
        size_diff_mb = current_size - oldest_record['size_mb']
        
        if time_diff_hours > 0:
            growth_rate = size_diff_mb / time_diff_hours
            return growth_rate, f"{growth_rate:.2f} MB/小时"
        
        return 0, "无增长"
    
    def check_file(self, file_path: Path) -> Dict:
        """检查单个文件"""
        size_mb = self.get_file_size_mb(file_path)
        growth_rate, growth_desc = self.calculate_growth_rate(file_path, size_mb)
        
        # 判断警告级别
        size_level = "正常"
        growth_level = "正常"
        
        if size_mb >= self.config['size_critical_mb']:
            size_level = "严重"
        elif size_mb >= self.config['size_warning_mb']:
            size_level = "警告"
        
        if growth_rate >= self.config['growth_critical_mb_per_hour']:
            growth_level = "严重"
        elif growth_rate >= self.config['growth_warning_mb_per_hour']:
            growth_level = "警告"
        
        overall_level = "严重" if (size_level == "严重" or growth_level == "严重") else \
                       "警告" if (size_level == "警告" or growth_level == "警告") else "正常"
        
        return {
            'file_path': file_path,
            'relative_path': file_path.relative_to(self.log_dir),
            'size_mb': size_mb,
            'size_level': size_level,
            'growth_rate': growth_rate,
            'growth_desc': growth_desc,
            'growth_level': growth_level,
            'overall_level': overall_level,
            'last_modified': datetime.fromtimestamp(file_path.stat().st_mtime)
        }
    
    def monitor(self) -> Dict:
        """执行监控"""
        if not self.log_dir.exists():
            return {
                'error': f"日志目录不存在: {self.log_dir}",
                'files': [],
                'summary': {}
            }
        
        log_files = self.get_log_files()
        results = []
        
        for file_path in log_files:
            try:
                result = self.check_file(file_path)
                results.append(result)
            except Exception as e:
                print(f"检查文件失败 {file_path}: {e}")
        
        # 生成摘要
        summary = {
            'total_files': len(results),
            'total_size_mb': sum(r['size_mb'] for r in results),
            'normal_files': len([r for r in results if r['overall_level'] == '正常']),
            'warning_files': len([r for r in results if r['overall_level'] == '警告']),
            'critical_files': len([r for r in results if r['overall_level'] == '严重']),
            'check_time': datetime.now()
        }
        
        # 保存历史记录
        self.save_history()
        
        return {
            'files': results,
            'summary': summary,
            'config': self.config
        }
    
    def print_report(self, results: Dict, verbose: bool = False):
        """打印监控报告"""
        print("=" * 80)
        print("日志监控报告")
        print("=" * 80)
        print(f"检查时间: {results['summary']['check_time']}")
        print(f"日志目录: {self.log_dir}")
        print()
        
        summary = results['summary']
        print("摘要:")
        print(f"  总文件数: {summary['total_files']}")
        print(f"  总大小: {summary['total_size_mb']:.1f} MB")
        print(f"  正常文件: {summary['normal_files']}")
        print(f"  警告文件: {summary['warning_files']}")
        print(f"  严重文件: {summary['critical_files']}")
        print()
        
        # 按级别分组显示
        files_by_level = {
            '严重': [f for f in results['files'] if f['overall_level'] == '严重'],
            '警告': [f for f in results['files'] if f['overall_level'] == '警告'],
            '正常': [f for f in results['files'] if f['overall_level'] == '正常']
        }
        
        for level in ['严重', '警告', '正常']:
            files = files_by_level[level]
            if not files:
                continue
            
            if level != '正常' or verbose:
                print(f"{level}文件 ({len(files)}个):")
                print("-" * 60)
                
                for file_info in sorted(files, key=lambda x: x['size_mb'], reverse=True):
                    print(f"文件: {file_info['relative_path']}")
                    print(f"  大小: {file_info['size_mb']:.1f} MB ({file_info['size_level']})")
                    print(f"  增长: {file_info['growth_desc']} ({file_info['growth_level']})")
                    print(f"  修改: {file_info['last_modified']}")
                    print()

def main():
    parser = argparse.ArgumentParser(description='日志监控工具')
    parser.add_argument('--log-dir', default='logs',
                       help='日志目录路径 (默认: logs)')
    parser.add_argument('--config', 
                       help='配置文件路径')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细信息（包括正常文件）')
    parser.add_argument('--json', action='store_true',
                       help='以JSON格式输出结果')
    parser.add_argument('--watch', type=int, metavar='SECONDS',
                       help='持续监控模式，指定检查间隔（秒）')
    
    args = parser.parse_args()
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    log_dir = project_root / args.log_dir
    
    monitor = LogMonitor(log_dir, args.config)
    
    if args.watch:
        print(f"开始持续监控，检查间隔: {args.watch}秒")
        print("按 Ctrl+C 停止监控")
        print()
        
        try:
            while True:
                results = monitor.monitor()
                
                if args.json:
                    # 转换datetime对象为字符串
                    results_copy = json.loads(json.dumps(results, default=str))
                    print(json.dumps(results_copy, indent=2, ensure_ascii=False))
                else:
                    monitor.print_report(results, args.verbose)
                
                time.sleep(args.watch)
                print("\n" + "="*80 + "\n")
                
        except KeyboardInterrupt:
            print("\n监控已停止")
    else:
        results = monitor.monitor()
        
        if args.json:
            # 转换datetime对象为字符串
            results_copy = json.loads(json.dumps(results, default=str))
            print(json.dumps(results_copy, indent=2, ensure_ascii=False))
        else:
            monitor.print_report(results, args.verbose)

if __name__ == "__main__":
    main()
