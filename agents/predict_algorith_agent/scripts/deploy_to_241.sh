#!/bin/bash
# 部署agents/predict_algorith_agent到远程Linux服务器***********
# 完整覆盖部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
REMOTE_HOST="***********"
REMOTE_USER="root"  # 根据实际情况修改
REMOTE_PROJECT_DIR="/data/agent"
LOCAL_PROJECT_DIR="agents/predict_algorith_agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查本地项目目录
check_local_project() {
    log_info "检查本地项目目录..."
    
    if [ ! -d "$LOCAL_PROJECT_DIR" ]; then
        log_error "本地项目目录不存在: $LOCAL_PROJECT_DIR"
        return 1
    fi
    
    if [ ! -f "$LOCAL_PROJECT_DIR/predict_main.py" ]; then
        log_error "主启动文件不存在: $LOCAL_PROJECT_DIR/predict_main.py"
        return 1
    fi
    
    if [ ! -f "agents/config.py" ]; then
        log_error "配置文件不存在: agents/config.py"
        return 1
    fi
    
    log_success "本地项目检查通过"
    return 0
}

# 检查远程连接
check_remote_connection() {
    log_info "检查远程服务器连接..."
    
    if ssh -o ConnectTimeout=10 "$REMOTE_USER@$REMOTE_HOST" "echo '连接测试成功'" > /dev/null 2>&1; then
        log_success "远程服务器连接正常"
        return 0
    else
        log_error "无法连接到远程服务器: $REMOTE_USER@$REMOTE_HOST"
        log_info "请检查:"
        log_info "  - SSH密钥配置"
        log_info "  - 网络连接"
        log_info "  - 服务器地址和用户名"
        return 1
    fi
}

# 停止远程服务
stop_remote_service() {
    log_info "停止远程服务器上的现有服务..."
    
    ssh "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
        cd /data/agent 2>/dev/null || exit 0
        
        # 停止服务器
        if [ -f "server.pid" ]; then
            PID=$(cat server.pid)
            if kill -0 $PID 2>/dev/null; then
                echo "停止服务器 (PID: $PID)"
                kill $PID
                sleep 3
                if kill -0 $PID 2>/dev/null; then
                    echo "强制停止服务器"
                    kill -9 $PID
                fi
            fi
            rm -f server.pid
        fi
        
        # 停止日志监控
        if [ -f "log_monitor.pid" ]; then
            MONITOR_PID=$(cat log_monitor.pid)
            if kill -0 $MONITOR_PID 2>/dev/null; then
                echo "停止日志监控 (PID: $MONITOR_PID)"
                kill $MONITOR_PID
                sleep 2
                if kill -0 $MONITOR_PID 2>/dev/null; then
                    echo "强制停止日志监控"
                    kill -9 $MONITOR_PID
                fi
            fi
            rm -f log_monitor.pid
        fi
        
        echo "现有服务已停止"
EOF
    
    log_success "远程服务停止完成"
}

# 备份远程项目
backup_remote_project() {
    log_info "备份远程项目..."
    
    ssh "$REMOTE_USER@$REMOTE_HOST" << EOF
        if [ -d "$REMOTE_PROJECT_DIR" ]; then
            backup_dir="$REMOTE_PROJECT_DIR.backup.\$(date +%Y%m%d_%H%M%S)"
            echo "创建备份: \$backup_dir"
            cp -r "$REMOTE_PROJECT_DIR" "\$backup_dir"
            echo "备份完成: \$backup_dir"
        else
            echo "远程项目目录不存在，跳过备份"
        fi
EOF
    
    log_success "远程项目备份完成"
}

# 同步项目文件
sync_project_files() {
    log_info "同步项目文件到远程服务器..."
    
    # 创建远程目录
    ssh "$REMOTE_USER@$REMOTE_HOST" "mkdir -p $REMOTE_PROJECT_DIR"
    
    # 同步agents目录
    log_info "同步agents目录..."
    rsync -avz --delete \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='*.log' \
        --exclude='.git' \
        --exclude='node_modules' \
        agents/ "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PROJECT_DIR/agents/"
    
    log_success "项目文件同步完成"
}

# 设置远程环境
setup_remote_environment() {
    log_info "设置远程环境..."
    
    ssh "$REMOTE_USER@$REMOTE_HOST" << EOF
        cd $REMOTE_PROJECT_DIR
        
        # 设置脚本执行权限
        chmod +x agents/predict_algorith_agent/scripts/*.sh

        # 复制manage_server.sh到根目录，方便使用
        if [ -f "agents/predict_algorith_agent/scripts/manage_server.sh" ]; then
            cp agents/predict_algorith_agent/scripts/manage_server.sh ./manage_server.sh
            chmod +x manage_server.sh
            echo "✅ manage_server.sh已复制到根目录"
        fi

        # 复制其他常用脚本到根目录
        if [ -f "agents/predict_algorith_agent/scripts/diagnose_websocket.sh" ]; then
            cp agents/predict_algorith_agent/scripts/diagnose_websocket.sh ./diagnose_websocket.sh
            chmod +x diagnose_websocket.sh
            echo "✅ diagnose_websocket.sh已复制到根目录"
        fi

        # 创建日志目录
        mkdir -p logs
        
        # 创建环境激活脚本（如果不存在）
        if [ ! -f "activate_env.sh" ]; then
            cat > activate_env.sh << 'ENVEOF'
#!/bin/bash
# Python环境激活脚本
export PROJECT_ROOT=/data/agent
export PYTHONPATH=/data/agent:\$PYTHONPATH
export PYTHONIOENCODING=utf-8

# 如果有虚拟环境，在这里激活
# source /path/to/venv/bin/activate

echo "Python环境已激活"
echo "项目根目录: \$PROJECT_ROOT"
echo "Python路径: \$PYTHONPATH"
ENVEOF
            chmod +x activate_env.sh
            echo "环境激活脚本已创建"
        fi
        
        echo "远程环境设置完成"
EOF
    
    log_success "远程环境设置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    ssh "$REMOTE_USER@$REMOTE_HOST" << EOF
        cd $REMOTE_PROJECT_DIR
        
        echo "📂 项目目录结构:"
        ls -la agents/predict_algorith_agent/ | head -10
        
        echo ""
        echo "🔧 关键文件检查:"
        
        if [ -f "agents/predict_algorith_agent/predict_main.py" ]; then
            echo "✅ predict_main.py"
        else
            echo "❌ predict_main.py"
        fi
        
        if [ -f "agents/config.py" ]; then
            echo "✅ config.py"
        else
            echo "❌ config.py"
        fi
        
        if [ -f "manage_server.sh" ]; then
            echo "✅ manage_server.sh (根目录)"
        else
            echo "❌ manage_server.sh (根目录)"
        fi

        if [ -f "diagnose_websocket.sh" ]; then
            echo "✅ diagnose_websocket.sh (根目录)"
        else
            echo "❌ diagnose_websocket.sh (根目录)"
        fi
        
        if [ -f "agents/predict_algorith_agent/scripts/start_server_background.sh" ]; then
            echo "✅ start_server_background.sh"
        else
            echo "❌ start_server_background.sh"
        fi
        
        echo ""
        echo "🐍 Python环境检查:"
        python3 --version || echo "❌ Python3未安装"
        
        echo ""
        echo "📦 依赖包检查:"
        python3 -c "import fastapi; print('✅ fastapi')" 2>/dev/null || echo "❌ fastapi"
        python3 -c "import uvicorn; print('✅ uvicorn')" 2>/dev/null || echo "❌ uvicorn"
        python3 -c "import websockets; print('✅ websockets')" 2>/dev/null || echo "❌ websockets"
EOF
    
    log_success "部署验证完成"
}

# 显示部署后的使用说明
show_usage_instructions() {
    log_success "🎉 部署完成！"
    echo ""
    log_info "📋 远程服务器使用说明:"
    echo "=" * 60
    echo ""
    echo "🔗 连接到远程服务器:"
    echo "   ssh $REMOTE_USER@$REMOTE_HOST"
    echo ""
    echo "📂 进入项目目录:"
    echo "   cd $REMOTE_PROJECT_DIR"
    echo ""
    echo "🚀 服务管理命令:"
    echo "   ./manage_server.sh start    # 启动服务"
    echo "   ./manage_server.sh stop     # 停止服务"
    echo "   ./manage_server.sh restart  # 重启服务"
    echo "   ./manage_server.sh status   # 查看状态"
    echo "   ./manage_server.sh logs     # 查看日志"
    echo "   ./manage_server.sh test     # 测试连接"
    echo ""
    echo "🔧 大模型配置管理:"
    echo "   python agents/predict_algorith_agent/switch_llm_config.py show    # 查看配置"
    echo "   python agents/predict_algorith_agent/switch_llm_config.py switch deepseek  # 切换模型"
    echo "   python agents/predict_algorith_agent/switch_llm_config.py update qwen sk-new-key  # 更新Key"
    echo ""
    echo "🌐 服务访问地址:"
    echo "   - HTTP: http://***********:8008"
    echo "   - WebSocket: ws://***********:8008/api/ws/chat"
    echo "   - API文档: http://***********:8008/docs"
    echo ""
    echo "=" * 60
}

# 主执行流程
main() {
    log_info "开始部署agents/predict_algorith_agent到远程Linux服务器"
    log_info "目标服务器: $REMOTE_HOST"
    log_info "项目目录: $REMOTE_PROJECT_DIR"
    echo "=" * 60
    
    # 1. 检查本地项目
    if ! check_local_project; then
        exit 1
    fi
    
    # 2. 检查远程连接
    if ! check_remote_connection; then
        exit 1
    fi
    
    # 3. 停止远程服务
    stop_remote_service
    
    # 4. 备份远程项目
    backup_remote_project
    
    # 5. 同步项目文件
    sync_project_files
    
    # 6. 设置远程环境
    setup_remote_environment
    
    # 7. 验证部署
    verify_deployment
    
    # 8. 显示使用说明
    show_usage_instructions
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "🚀 agents/predict_algorith_agent 远程部署脚本"
    echo ""
    echo "用法: $0"
    echo ""
    echo "功能:"
    echo "  - 完整覆盖部署agents/predict_algorith_agent到***********"
    echo "  - 自动停止现有服务"
    echo "  - 备份现有项目"
    echo "  - 同步所有文件"
    echo "  - 设置执行权限"
    echo "  - 验证部署结果"
    echo ""
    echo "前置条件:"
    echo "  - SSH密钥已配置"
    echo "  - 远程服务器可访问"
    echo "  - 本地项目目录完整"
    exit 0
fi

# 执行部署
main "$@"
