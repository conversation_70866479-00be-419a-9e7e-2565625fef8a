#!/bin/bash
# 一键部署脚本 - 专为Anolis OS 8.6优化
# 自动完成从环境准备到服务启动的全过程
# 适用于远程服务器 ***********（龙蜥操作系统）

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
TARGET_DIR="/data/agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_banner() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

# 显示欢迎信息
show_welcome() {
    clear
    log_banner "算法智能体一键部署脚本 - Anolis OS 8.6"
    echo ""
    log_info "目标服务器: ***********（龙蜥操作系统）"
    log_info "此脚本将自动完成以下操作："
    echo "  1. 安装pyenv和Python 3.10"
    echo "  2. 创建虚拟环境"
    echo "  3. 部署项目代码到 $TARGET_DIR"
    echo "  4. 安装项目依赖"
    echo "  5. 配置系统服务"
    echo "  6. 启动服务"
    echo ""
    log_warning "注意：此脚本专为Anolis OS 8.6优化，需要sudo权限"
    echo ""
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 检查系统要求
check_requirements() {
    log_step "检查Anolis OS 8.6系统要求..."

    # 检查操作系统
    if [ ! -f /etc/os-release ]; then
        log_error "无法检测操作系统"
        exit 1
    fi

    # 验证是否为Anolis OS
    . /etc/os-release
    if [[ "$NAME" != *"Anolis"* ]]; then
        log_warning "检测到非Anolis OS系统: $NAME"
        log_warning "此脚本专为Anolis OS 8.6优化，继续执行但可能需要手动调整"
    else
        log_info "确认运行在Anolis OS $VERSION_ID"
    fi

    # 检查sudo权限
    if ! sudo -n true 2>/dev/null; then
        log_error "需要sudo权限"
        exit 1
    fi

    # 检查网络连接
    if ! ping -c 1 google.com >/dev/null 2>&1; then
        log_warning "网络连接可能有问题，但继续执行"
    fi

    # 检查磁盘空间
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 2097152 ]; then  # 2GB in KB
        log_warning "磁盘空间不足2GB，可能影响安装"
    fi

    log_success "Anolis OS系统要求检查完成"
}

# 步骤1：环境准备
step1_setup_environment() {
    log_step "步骤1: 环境准备"
    
    cd "$SCRIPT_DIR"
    
    if [ -f "setup_environment.sh" ]; then
        chmod +x setup_environment.sh
        ./setup_environment.sh
    else
        log_error "setup_environment.sh 不存在"
        exit 1
    fi
    
    # 重新加载环境变量
    source ~/.bashrc
    
    log_success "步骤1完成：环境准备"
}

# 步骤2：安装Python 3.10
step2_install_python() {
    log_step "步骤2: 安装Python 3.10"
    
    cd "$SCRIPT_DIR"
    
    if [ -f "install_python310.sh" ]; then
        chmod +x install_python310.sh
        ./install_python310.sh
    else
        log_error "install_python310.sh 不存在"
        exit 1
    fi
    
    log_success "步骤2完成：Python 3.10安装"
}

# 步骤3：部署项目
step3_deploy_project() {
    log_step "步骤3: 部署项目"
    
    cd "$PROJECT_ROOT"
    
    if [ -f "$SCRIPT_DIR/deploy_project.sh" ]; then
        chmod +x "$SCRIPT_DIR/deploy_project.sh"
        "$SCRIPT_DIR/deploy_project.sh"
    else
        log_error "deploy_project.sh 不存在"
        exit 1
    fi
    
    log_success "步骤3完成：项目部署"
}

# 步骤4：配置系统服务
step4_setup_service() {
    log_step "步骤4: 配置系统服务"
    
    cd "$SCRIPT_DIR"
    
    if [ -f "setup_systemd_service.sh" ]; then
        chmod +x setup_systemd_service.sh
        ./setup_systemd_service.sh
    else
        log_error "setup_systemd_service.sh 不存在"
        exit 1
    fi
    
    log_success "步骤4完成：系统服务配置"
}

# 步骤5：验证部署
step5_verify_deployment() {
    log_step "步骤5: 验证部署"
    
    # 检查服务状态
    if sudo systemctl is-active --quiet algorithm-agent; then
        log_success "服务运行正常"
    else
        log_error "服务未运行"
        return 1
    fi
    
    # 检查端口监听
    if netstat -tlnp 2>/dev/null | grep -q ":8008"; then
        log_success "端口8008监听正常"
    else
        log_warning "端口8008未监听"
    fi
    
    # 测试API
    sleep 3
    if curl -s http://localhost:8008/health >/dev/null 2>&1; then
        log_success "API响应正常"
    else
        log_warning "API响应异常"
    fi
    
    log_success "步骤5完成：部署验证"
}

# 显示部署结果
show_deployment_result() {
    log_banner "部署完成"
    
    echo ""
    log_success "🎉 算法智能体部署成功！"
    echo ""
    
    # 获取服务器IP
    server_ip=$(hostname -I | awk '{print $1}')
    
    log_info "📋 部署信息："
    echo "  项目目录: $TARGET_DIR"
    echo "  服务名称: algorithm-agent"
    echo "  监听端口: 8008"
    echo "  服务器IP: $server_ip"
    echo ""
    
    log_info "🌐 访问地址："
    echo "  API文档: http://$server_ip:8008/docs"
    echo "  健康检查: http://$server_ip:8008/health"
    echo "  前端界面: http://$server_ip:8008/static/html/chat_interface_v2.html"
    echo ""
    
    log_info "🔧 管理命令："
    echo "  查看状态: sudo systemctl status algorithm-agent"
    echo "  查看日志: sudo journalctl -u algorithm-agent -f"
    echo "  重启服务: sudo systemctl restart algorithm-agent"
    echo "  停止服务: sudo systemctl stop algorithm-agent"
    echo ""
    
    log_info "📁 重要文件："
    echo "  配置文件: $TARGET_DIR/.env"
    echo "  启动脚本: $TARGET_DIR/start_agent.sh"
    echo "  管理脚本: $TARGET_DIR/manage_service.sh"
    echo "  日志目录: $TARGET_DIR/logs/"
    echo ""
    
    log_warning "⚠️  下一步操作："
    echo "  1. 修改配置文件: nano $TARGET_DIR/.env"
    echo "  2. 配置数据库连接信息"
    echo "  3. 重启服务: sudo systemctl restart algorithm-agent"
    echo ""
}

# 错误处理
handle_error() {
    log_error "部署过程中发生错误"
    log_info "请检查以下内容："
    echo "  1. 网络连接是否正常"
    echo "  2. 磁盘空间是否充足"
    echo "  3. 是否有sudo权限"
    echo "  4. 系统是否支持"
    echo ""
    log_info "查看详细错误信息："
    echo "  journalctl -xe"
    echo "  tail -f /var/log/syslog"
    exit 1
}

# 主函数
main() {
    # 设置错误处理
    trap handle_error ERR
    
    # 显示欢迎信息
    show_welcome
    
    # 检查系统要求
    check_requirements
    
    # 执行部署步骤
    step1_setup_environment
    step2_install_python
    step3_deploy_project
    step4_setup_service
    step5_verify_deployment
    
    # 显示部署结果
    show_deployment_result
}

# 执行主函数
main "$@"
