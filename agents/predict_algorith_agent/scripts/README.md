# 算法智能体部署脚本说明

## 📋 概述

本目录包含了在远程Linux服务器上部署算法智能体的完整自动化脚本。这些脚本专门解决了在Python 3.6.8环境下部署Python 3.10项目的问题。

## 🎯 适用场景

- **服务器环境**: 远程Linux服务器 10.0.42.241
- **操作系统**: Anolis OS 8.6（龙蜥操作系统，兼容RHEL/CentOS生态）
- **系统Python**: 3.6.8（保持不变）
- **项目Python**: 3.10.12（通过pyenv安装）
- **部署路径**: `/data/agent`
- **源代码路径**: `agents/predict_algorith_agent/`

## 📦 脚本文件说明

### 1. setup_environment.sh
**功能**: 环境准备和pyenv安装
- 专为Anolis OS 8.6优化
- 安装编译依赖包（使用dnf包管理器）
- 下载并安装pyenv
- 配置环境变量

**使用方法**:
```bash
chmod +x setup_environment.sh
./setup_environment.sh
source ~/.bashrc
```

### 2. install_python310.sh
**功能**: 安装Python 3.10和创建虚拟环境
- 使用pyenv安装Python 3.10.12
- 创建项目目录 `/data/agent`
- 创建虚拟环境
- 生成环境激活脚本

**使用方法**:
```bash
chmod +x install_python310.sh
./install_python310.sh
```

### 3. deploy_project.sh
**功能**: 部署项目代码和配置
- 备份现有部署
- 复制项目文件到 `/data/agent`
- 安装项目依赖
- 创建配置文件和启动脚本

**使用方法**:
```bash
chmod +x deploy_project.sh
./deploy_project.sh
```

### 4. setup_systemd_service.sh
**功能**: 配置生产环境服务
- 创建Gunicorn配置文件
- 配置systemd服务
- 创建服务管理脚本
- 配置Nginx（可选）

**使用方法**:
```bash
chmod +x setup_systemd_service.sh
./setup_systemd_service.sh
```

### 5. one_click_deploy.sh
**功能**: 一键完整部署
- 自动执行所有部署步骤
- 友好的用户界面
- 错误处理和验证
- 显示部署结果

**使用方法**:
```bash
chmod +x one_click_deploy.sh
./one_click_deploy.sh
```

## 🚀 快速开始

### 方案A: 一键部署（推荐）
```bash
# 1. 进入脚本目录
cd agents/predict_algorith_agent/scripts

# 2. 执行一键部署
chmod +x one_click_deploy.sh
./one_click_deploy.sh

# 3. 按提示操作，等待部署完成
```

### 方案B: 分步骤部署
```bash
# 1. 环境准备
chmod +x setup_environment.sh
./setup_environment.sh
source ~/.bashrc

# 2. 安装Python 3.10
chmod +x install_python310.sh
./install_python310.sh

# 3. 部署项目
chmod +x deploy_project.sh
./deploy_project.sh

# 4. 配置系统服务
chmod +x setup_systemd_service.sh
./setup_systemd_service.sh
```

## ⚙️ 部署后配置

### 1. 修改配置文件
```bash
# 编辑环境变量
nano /data/agent/.env

# 主要需要修改的配置：
# - 数据库连接信息
# - Redis连接信息
# - API端点配置
```

### 2. 重启服务
```bash
sudo systemctl restart algorithm-agent
```

### 3. 验证部署
```bash
# 检查服务状态
sudo systemctl status algorithm-agent

# 测试API
curl http://localhost:8008/health

# 查看日志
sudo journalctl -u algorithm-agent -f
```

## 🔧 服务管理

### 使用systemctl命令
```bash
# 启动服务
sudo systemctl start algorithm-agent

# 停止服务
sudo systemctl stop algorithm-agent

# 重启服务
sudo systemctl restart algorithm-agent

# 查看状态
sudo systemctl status algorithm-agent

# 查看日志
sudo journalctl -u algorithm-agent -f
```

### 使用管理脚本
```bash
# 使用便捷的管理脚本
/data/agent/manage_service.sh start
/data/agent/manage_service.sh stop
/data/agent/manage_service.sh restart
/data/agent/manage_service.sh status
/data/agent/manage_service.sh logs
```

## 📁 部署后目录结构

```
/data/agent/
├── agents/                    # 项目代码
│   ├── config.py             # 配置文件
│   ├── requirement.txt       # 依赖列表
│   └── predict_algorith_agent/
├── core/                     # 核心模块
├── venv/                     # Python虚拟环境
├── logs/                     # 日志目录
├── backups/                  # 备份目录
├── .env                      # 环境变量
├── activate_env.sh           # 环境激活脚本
├── start_agent.sh            # 启动脚本
├── manage_service.sh         # 服务管理脚本
├── gunicorn.conf.py          # Gunicorn配置
├── start_gunicorn.py         # Gunicorn启动脚本
└── nginx.conf                # Nginx配置模板
```

## 🌐 访问地址

部署完成后，可以通过以下地址访问：

- **API文档**: http://服务器IP:8008/docs
- **健康检查**: http://服务器IP:8008/health
- **前端界面**: http://服务器IP:8008/static/html/chat_interface_v2.html

## 🛠️ 故障排除

### 常见问题

1. **pyenv安装失败**
   ```bash
   # 检查网络连接
   curl -I https://github.com
   
   # 手动下载安装
   git clone https://github.com/pyenv/pyenv.git ~/.pyenv
   ```

2. **Python编译失败**
   ```bash
   # 检查系统依赖
   sudo apt install -y build-essential libssl-dev zlib1g-dev
   
   # 清理后重试
   pyenv uninstall 3.10.12
   pyenv install 3.10.12
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   sudo journalctl -u algorithm-agent -n 50
   
   # 检查端口占用
   sudo netstat -tlnp | grep 8008
   ```

4. **模块导入错误**
   ```bash
   # 检查Python路径
   cd /data/agent
   source activate_env.sh
   python -c "import sys; print(sys.path)"
   ```

### 获取帮助

如果遇到问题，请：
1. 查看脚本输出的错误信息
2. 检查系统日志：`sudo journalctl -xe`
3. 查看服务日志：`sudo journalctl -u algorithm-agent -f`
4. 确认网络连接和权限设置

## 📞 技术支持

- 详细部署文档：`docs/Linux服务器部署指南.md`
- 项目文档：`docs/`目录下的其他文档
- 问题反馈：请提供详细的错误日志和系统信息
