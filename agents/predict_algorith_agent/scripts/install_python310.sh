#!/bin/bash
# Python 3.10安装脚本
# 使用pyenv在Anolis OS 8.6服务器上安装Python 3.10
# 适用于远程服务器 ***********（龙蜥操作系统）

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PYTHON_VERSION="3.10.12"  # 推荐的稳定版本
PROJECT_DIR="/data/agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查pyenv是否安装
check_pyenv() {
    log_info "检查pyenv安装状态..."
    
    # 设置pyenv环境变量
    export PYENV_ROOT="$HOME/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    
    # 初始化pyenv
    if command -v pyenv 1>/dev/null 2>&1; then
        eval "$(pyenv init -)"
        eval "$(pyenv virtualenv-init -)"
        log_success "pyenv已安装: $(pyenv --version)"
    else
        log_error "pyenv未安装，请先运行 setup_environment.sh"
        exit 1
    fi
}

# 列出可用的Python版本
list_available_versions() {
    log_info "检查可用的Python版本..."
    
    # 更新pyenv
    pyenv update 2>/dev/null || log_warning "无法更新pyenv，继续使用当前版本"
    
    # 显示可用的Python 3.10版本
    log_info "可用的Python 3.10版本："
    pyenv install --list | grep "  3\.10\." | head -10
}

# 安装Python 3.10
install_python310() {
    log_info "开始安装Python $PYTHON_VERSION..."
    
    # 检查是否已安装
    if pyenv versions | grep -q "$PYTHON_VERSION"; then
        log_warning "Python $PYTHON_VERSION 已安装，跳过安装步骤"
        return 0
    fi
    
    # 设置编译优化选项
    export PYTHON_CONFIGURE_OPTS="--enable-optimizations --with-lto"
    export PYTHON_CFLAGS="-march=native -mtune=native"
    
    # 安装Python
    log_info "正在编译安装Python $PYTHON_VERSION（这可能需要几分钟）..."
    pyenv install $PYTHON_VERSION
    
    if [ $? -eq 0 ]; then
        log_success "Python $PYTHON_VERSION 安装成功"
    else
        log_error "Python $PYTHON_VERSION 安装失败"
        exit 1
    fi
}

# 验证Python安装
verify_python() {
    log_info "验证Python安装..."
    
    # 设置为全局版本（可选）
    # pyenv global $PYTHON_VERSION
    
    # 设置为本地版本
    pyenv local $PYTHON_VERSION
    
    # 验证版本
    python_version=$(pyenv exec python --version)
    log_success "当前Python版本: $python_version"
    
    # 验证pip
    pip_version=$(pyenv exec pip --version)
    log_success "当前pip版本: $pip_version"
    
    # 升级pip
    log_info "升级pip到最新版本..."
    pyenv exec pip install --upgrade pip setuptools wheel
}

# 创建项目目录
create_project_directory() {
    log_info "创建项目目录..."
    
    # 创建目录
    sudo mkdir -p $PROJECT_DIR
    
    # 设置权限
    sudo chown $USER:$USER $PROJECT_DIR
    sudo chmod 755 $PROJECT_DIR
    
    log_success "项目目录创建完成: $PROJECT_DIR"
}

# 创建虚拟环境
create_virtual_environment() {
    log_info "创建虚拟环境..."
    
    cd $PROJECT_DIR
    
    # 设置Python版本
    pyenv local $PYTHON_VERSION
    
    # 创建虚拟环境
    pyenv exec python -m venv venv
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip setuptools wheel
    
    # 验证虚拟环境
    python_path=$(which python)
    python_version=$(python --version)
    
    log_success "虚拟环境创建成功"
    log_info "Python路径: $python_path"
    log_info "Python版本: $python_version"
    
    deactivate
}

# 创建环境激活脚本
create_activation_script() {
    log_info "创建环境激活脚本..."
    
    cat > $PROJECT_DIR/activate_env.sh << EOF
#!/bin/bash
# 激活Python 3.10环境脚本

# 设置pyenv环境
export PYENV_ROOT="\$HOME/.pyenv"
export PATH="\$PYENV_ROOT/bin:\$PATH"

# 初始化pyenv
if command -v pyenv 1>/dev/null 2>&1; then
    eval "\$(pyenv init -)"
    eval "\$(pyenv virtualenv-init -)"
fi

# 切换到项目目录
cd $PROJECT_DIR

# 设置Python版本
pyenv local $PYTHON_VERSION

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PROJECT_ROOT=$PROJECT_DIR
export PYTHONPATH=$PROJECT_DIR:\$PYTHONPATH
export PYTHONIOENCODING=utf-8

echo "✅ Python 3.10环境已激活"
echo "📁 项目目录: $PROJECT_DIR"
echo "🐍 Python版本: \$(python --version)"
echo "📦 Pip版本: \$(pip --version)"
EOF
    
    chmod +x $PROJECT_DIR/activate_env.sh
    log_success "环境激活脚本创建完成: $PROJECT_DIR/activate_env.sh"
}

# 主函数
main() {
    log_info "开始在Anolis OS 8.6上安装Python 3.10..."
    log_info "目标版本: $PYTHON_VERSION"
    log_info "项目目录: $PROJECT_DIR"
    log_info "服务器: ***********（龙蜥操作系统）"

    # 检查pyenv
    check_pyenv

    # 列出可用版本
    list_available_versions

    # 安装Python 3.10
    install_python310

    # 验证安装
    verify_python

    # 创建项目目录
    create_project_directory

    # 创建虚拟环境
    create_virtual_environment

    # 创建激活脚本
    create_activation_script

    log_success "Python 3.10在Anolis OS上安装完成！"
    log_info ""
    log_info "使用方法："
    log_info "1. 激活环境: source $PROJECT_DIR/activate_env.sh"
    log_info "2. 验证版本: python --version"
    log_info "3. 安装依赖: pip install -r requirements.txt"
    log_info ""
    log_info "下一步请运行 deploy_project.sh 部署项目代码"
}

# 执行主函数
main "$@"
