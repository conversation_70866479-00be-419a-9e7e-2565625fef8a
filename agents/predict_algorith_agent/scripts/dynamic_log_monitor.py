#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态日志监控脚本
在服务器运行时持续监控日志文件大小，确保不超过2GB
当接近2GB时自动进行日志轮转
"""

import os
import sys
import time
import signal
import logging
import threading
from pathlib import Path
from datetime import datetime

class DynamicLogMonitor:
    """动态日志监控器"""
    
    def __init__(self, log_file_path="/data/agent/logs/server.log"):
        self.log_file_path = Path(log_file_path)
        self.max_size_bytes = 2 * 1024 * 1024 * 1024  # 2GB
        self.warning_size_bytes = int(self.max_size_bytes * 0.9)  # 1.8GB时开始警告
        self.keep_size_bytes = 100 * 1024 * 1024  # 保留100MB
        self.check_interval = 60  # 每60秒检查一次
        self.running = False
        self.monitor_thread = None
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/data/agent/logs/log_monitor.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('LogMonitor')
    
    def get_file_size(self):
        """获取日志文件大小"""
        try:
            if self.log_file_path.exists():
                return self.log_file_path.stat().st_size
            return 0
        except Exception as e:
            self.logger.error(f"获取文件大小失败: {e}")
            return 0
    
    def format_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes >= 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f}GB"
        elif size_bytes >= 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f}MB"
        else:
            return f"{size_bytes / 1024:.1f}KB"
    
    def rotate_log_file(self):
        """执行日志文件轮转"""
        try:
            self.logger.warning("开始执行日志轮转...")
            
            # 创建备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.log_file_path.parent / f"server_{timestamp}.log.old"
            temp_file = self.log_file_path.parent / f"server_temp_{os.getpid()}.log"
            
            # 获取当前文件大小
            current_size = self.get_file_size()
            self.logger.info(f"当前日志文件大小: {self.format_size(current_size)}")
            
            # 保留最后100MB的内容
            with open(self.log_file_path, 'rb') as src:
                # 移动到文件末尾前100MB的位置
                if current_size > self.keep_size_bytes:
                    src.seek(-self.keep_size_bytes, 2)
                else:
                    src.seek(0)
                
                # 读取并写入临时文件
                with open(temp_file, 'wb') as dst:
                    dst.write(src.read())
            
            # 移动原文件为备份
            self.log_file_path.rename(backup_file)
            
            # 将临时文件作为新的日志文件
            temp_file.rename(self.log_file_path)
            
            # 设置正确的文件权限
            os.chmod(self.log_file_path, 0o644)
            
            new_size = self.get_file_size()
            backup_size = backup_file.stat().st_size
            
            self.logger.info(f"日志轮转完成:")
            self.logger.info(f"  - 原文件大小: {self.format_size(current_size)}")
            self.logger.info(f"  - 新文件大小: {self.format_size(new_size)}")
            self.logger.info(f"  - 备份文件: {backup_file} ({self.format_size(backup_size)})")
            
            # 清理7天前的旧备份
            self.cleanup_old_backups()
            
            return True
            
        except Exception as e:
            self.logger.error(f"日志轮转失败: {e}")
            # 如果轮转失败，尝试清理临时文件
            try:
                if temp_file.exists():
                    temp_file.unlink()
            except:
                pass
            return False
    
    def cleanup_old_backups(self):
        """清理7天前的旧备份文件"""
        try:
            import time
            current_time = time.time()
            seven_days_ago = current_time - (7 * 24 * 60 * 60)
            
            backup_pattern = "server_*.log.old"
            deleted_count = 0
            
            for backup_file in self.log_file_path.parent.glob(backup_pattern):
                try:
                    if backup_file.stat().st_mtime < seven_days_ago:
                        file_size = backup_file.stat().st_size
                        backup_file.unlink()
                        self.logger.info(f"删除旧备份: {backup_file.name} ({self.format_size(file_size)})")
                        deleted_count += 1
                except Exception as e:
                    self.logger.warning(f"删除备份文件失败 {backup_file}: {e}")
            
            if deleted_count > 0:
                self.logger.info(f"已清理 {deleted_count} 个旧备份文件")
            
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")
    
    def monitor_loop(self):
        """监控循环"""
        self.logger.info("开始监控日志文件大小...")
        
        while self.running:
            try:
                current_size = self.get_file_size()
                
                if current_size >= self.max_size_bytes:
                    self.logger.warning(f"日志文件达到最大限制 {self.format_size(current_size)}，立即执行轮转")
                    self.rotate_log_file()
                elif current_size >= self.warning_size_bytes:
                    self.logger.warning(f"日志文件接近限制 {self.format_size(current_size)}，准备轮转")
                else:
                    # 每10分钟记录一次状态
                    if int(time.time()) % 600 == 0:
                        self.logger.info(f"日志文件大小正常: {self.format_size(current_size)}")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(self.check_interval)
    
    def start(self):
        """启动监控"""
        if self.running:
            self.logger.warning("监控已在运行")
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("动态日志监控已启动")
        self.logger.info(f"监控文件: {self.log_file_path}")
        self.logger.info(f"最大大小: {self.format_size(self.max_size_bytes)}")
        self.logger.info(f"检查间隔: {self.check_interval}秒")
    
    def stop(self):
        """停止监控"""
        if not self.running:
            return
        
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("动态日志监控已停止")
    
    def status(self):
        """获取监控状态"""
        current_size = self.get_file_size()
        return {
            'running': self.running,
            'log_file': str(self.log_file_path),
            'current_size': current_size,
            'current_size_formatted': self.format_size(current_size),
            'max_size': self.max_size_bytes,
            'max_size_formatted': self.format_size(self.max_size_bytes),
            'usage_percentage': (current_size / self.max_size_bytes) * 100 if self.max_size_bytes > 0 else 0
        }


def signal_handler(signum, frame):
    """信号处理器"""
    global monitor
    print(f"\n收到信号 {signum}，正在停止监控...")
    if monitor:
        monitor.stop()
    sys.exit(0)


def main():
    """主函数"""
    global monitor
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析命令行参数
    log_file = "/data/agent/logs/server.log"
    if len(sys.argv) > 1:
        log_file = sys.argv[1]
    
    # 创建监控器
    monitor = DynamicLogMonitor(log_file)
    
    # 根据命令行参数执行不同操作
    if len(sys.argv) > 2:
        command = sys.argv[2]
        
        if command == "status":
            status = monitor.status()
            print(f"监控状态: {'运行中' if status['running'] else '已停止'}")
            print(f"日志文件: {status['log_file']}")
            print(f"当前大小: {status['current_size_formatted']}")
            print(f"最大限制: {status['max_size_formatted']}")
            print(f"使用率: {status['usage_percentage']:.1f}%")
            return
        
        elif command == "rotate":
            print("手动执行日志轮转...")
            if monitor.rotate_log_file():
                print("日志轮转成功")
            else:
                print("日志轮转失败")
            return
        
        elif command == "cleanup":
            print("清理旧备份文件...")
            monitor.cleanup_old_backups()
            return
    
    # 默认启动监控
    try:
        monitor.start()
        
        print("动态日志监控已启动")
        print("按 Ctrl+C 停止监控")
        
        # 保持主线程运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n用户中断，停止监控...")
    except Exception as e:
        print(f"监控异常: {e}")
    finally:
        monitor.stop()


if __name__ == "__main__":
    monitor = None
    main()
