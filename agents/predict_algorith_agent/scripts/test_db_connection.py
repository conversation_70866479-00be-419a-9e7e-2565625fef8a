#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接测试脚本
用于诊断和修复MySQL连接问题
"""

import sys
import os
import pymysql
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from agents.config import (
    MYSQL_HOST, MYSQL_PORT, MYSQL_USER,
    MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET
)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_connection():
    """测试基本数据库连接"""
    print("🔍 测试基本数据库连接...")
    print(f"   主机: {MYSQL_HOST}:{MYSQL_PORT}")
    print(f"   用户: {MYSQL_USER}")
    print(f"   数据库: {MYSQL_DB}")
    
    try:
        # 基本连接配置
        config = {
            'host': MYSQL_HOST,
            'port': MYSQL_PORT,
            'user': MYSQL_USER,
            'password': MYSQL_PASSWORD,
            'database': MYSQL_DB,
            'charset': MYSQL_CHARSET,
            'connect_timeout': 10
        }
        
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        # 测试查询
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ 连接成功! MySQL版本: {version[0]}")
        
        # 测试认证插件
        cursor.execute("SELECT plugin FROM mysql.user WHERE user = %s", (MYSQL_USER,))
        plugin_result = cursor.fetchone()
        if plugin_result:
            print(f"   认证插件: {plugin_result[0]}")
        
        cursor.close()
        connection.close()
        return True
        
    except pymysql.err.OperationalError as e:
        error_code = e.args[0] if e.args else 0
        error_msg = e.args[1] if len(e.args) > 1 else str(e)
        
        print(f"❌ 连接失败 (错误代码: {error_code}): {error_msg}")
        
        if error_code == 2059:
            print("🔧 认证插件问题解决方案:")
            print("   1. 确保安装cryptography: pip install cryptography")
            print("   2. 或修改MySQL用户认证方式:")
            print(f"      ALTER USER '{MYSQL_USER}'@'%' IDENTIFIED WITH mysql_native_password BY '{MYSQL_PASSWORD}';")
            print("      FLUSH PRIVILEGES;")
        elif error_code == 1045:
            print("🔧 访问被拒绝解决方案:")
            print("   1. 检查用户名和密码是否正确")
            print("   2. 确认用户有访问权限")
        elif error_code == 2003:
            print("🔧 无法连接服务器解决方案:")
            print("   1. 检查MySQL服务是否运行")
            print("   2. 检查网络连接和防火墙设置")
            print("   3. 确认服务器地址和端口正确")
        
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_ssl_disabled_connection():
    """测试禁用SSL的连接"""
    print("\n🔍 测试禁用SSL的连接...")
    
    try:
        config = {
            'host': MYSQL_HOST,
            'port': MYSQL_PORT,
            'user': MYSQL_USER,
            'password': MYSQL_PASSWORD,
            'database': MYSQL_DB,
            'charset': MYSQL_CHARSET,
            'ssl_disabled': True,
            'connect_timeout': 10
        }
        
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        print(f"✅ SSL禁用连接成功! 测试查询结果: {result[0]}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ SSL禁用连接失败: {e}")
        return False

def test_database_manager():
    """测试DatabaseManager类"""
    print("\n🔍 测试DatabaseManager类...")
    
    try:
        from agents.predict_algorith_agent.database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM conversations")
            count = cursor.fetchone()
            print(f"✅ DatabaseManager连接成功! conversations表记录数: {count[0]}")
            cursor.close()
        
        return True
        
    except Exception as e:
        print(f"❌ DatabaseManager测试失败: {e}")
        return False

def check_cryptography():
    """检查cryptography包"""
    print("\n🔍 检查cryptography包...")
    
    try:
        import cryptography
        print(f"✅ cryptography包已安装，版本: {cryptography.__version__}")
        return True
    except ImportError:
        print("❌ cryptography包未安装")
        print("🔧 解决方案: pip install cryptography")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔧 MySQL数据库连接诊断工具")
    print("=" * 60)
    
    # 检查cryptography包
    crypto_ok = check_cryptography()
    
    # 测试基本连接
    basic_ok = test_basic_connection()
    
    # 测试SSL禁用连接
    ssl_ok = test_ssl_disabled_connection()
    
    # 测试DatabaseManager
    manager_ok = test_database_manager()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   Cryptography包: {'✅' if crypto_ok else '❌'}")
    print(f"   基本连接: {'✅' if basic_ok else '❌'}")
    print(f"   SSL禁用连接: {'✅' if ssl_ok else '❌'}")
    print(f"   DatabaseManager: {'✅' if manager_ok else '❌'}")
    
    if all([crypto_ok, basic_ok, ssl_ok, manager_ok]):
        print("\n🎉 所有测试通过! 数据库连接正常")
        return True
    else:
        print("\n⚠️  部分测试失败，请根据上述建议进行修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
