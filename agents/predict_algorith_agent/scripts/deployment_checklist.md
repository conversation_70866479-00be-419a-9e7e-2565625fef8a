# 算法智能体部署检查清单

## 📋 部署前检查

### 服务器环境检查
- [ ] 服务器可以正常SSH连接
- [ ] 服务器有sudo权限
- [ ] 服务器可以访问外网（用于下载依赖）
- [ ] 磁盘空间充足（至少2GB可用空间）
- [ ] 内存充足（至少2GB RAM）

### 网络环境检查
- [ ] 服务器可以访问GitHub（下载pyenv）
- [ ] 服务器可以访问PyPI（下载Python包）
- [ ] MySQL服务器可以连接
- [ ] Redis服务器可以连接
- [ ] 防火墙允许8008端口访问

### 代码准备检查
- [ ] 源代码已准备完毕
- [ ] agents/predict_algorith_agent目录完整
- [ ] core目录存在
- [ ] 配置文件agents/config.py存在
- [ ] 依赖文件agents/requirement.txt存在

## 🚀 部署步骤检查

### 步骤1: 环境准备
- [ ] 执行setup_environment.sh成功
- [ ] pyenv安装成功
- [ ] 环境变量配置正确
- [ ] 重新加载bashrc成功

**验证命令**:
```bash
pyenv --version
echo $PYENV_ROOT
```

### 步骤2: Python 3.10安装
- [ ] 执行install_python310.sh成功
- [ ] Python 3.10.12安装成功
- [ ] /data/agent目录创建成功
- [ ] 虚拟环境创建成功
- [ ] activate_env.sh脚本创建成功

**验证命令**:
```bash
pyenv versions | grep 3.10.12
ls -la /data/agent/
source /data/agent/activate_env.sh
python --version
```

### 步骤3: 项目部署
- [ ] 执行deploy_project.sh成功
- [ ] 项目文件复制完成
- [ ] 依赖包安装成功
- [ ] 配置文件创建成功
- [ ] 启动脚本创建成功

**验证命令**:
```bash
ls -la /data/agent/agents/
ls -la /data/agent/core/
source /data/agent/activate_env.sh
python -c "import fastapi; print('FastAPI OK')"
python -c "import pydantic_ai; print('Pydantic AI OK')"
```

### 步骤4: 系统服务配置
- [ ] 执行setup_systemd_service.sh成功
- [ ] Gunicorn配置文件创建成功
- [ ] systemd服务文件创建成功
- [ ] 服务启用成功
- [ ] 服务启动成功

**验证命令**:
```bash
sudo systemctl status algorithm-agent
sudo systemctl is-enabled algorithm-agent
netstat -tlnp | grep 8008
```

## 🔍 部署后验证

### 服务状态检查
- [ ] algorithm-agent服务运行正常
- [ ] 端口8008正在监听
- [ ] 进程运行正常
- [ ] 日志无错误信息

**验证命令**:
```bash
sudo systemctl status algorithm-agent
sudo netstat -tlnp | grep 8008
ps aux | grep gunicorn
sudo journalctl -u algorithm-agent -n 20
```

### API功能检查
- [ ] 健康检查接口响应正常
- [ ] API文档可以访问
- [ ] WebSocket连接正常
- [ ] 静态文件可以访问

**验证命令**:
```bash
curl http://localhost:8008/health
curl -I http://localhost:8008/docs
curl -I http://localhost:8008/static/html/chat_interface_v2.html
```

### 数据库连接检查
- [ ] MySQL连接正常
- [ ] Redis连接正常
- [ ] 数据库权限正确

**验证命令**:
```bash
cd /data/agent
source activate_env.sh
python test_db_connection.py
```

### 日志检查
- [ ] 应用日志正常
- [ ] 访问日志正常
- [ ] 错误日志无异常
- [ ] 系统日志无异常

**验证命令**:
```bash
tail -f /data/agent/logs/access.log
tail -f /data/agent/logs/error.log
sudo journalctl -u algorithm-agent -f
```

## 🌐 外部访问检查

### 网络访问检查
- [ ] 从外部可以访问服务器IP:8008
- [ ] API接口响应正常
- [ ] 前端页面可以正常加载
- [ ] WebSocket连接正常

**验证命令**:
```bash
# 在其他机器上执行
curl http://服务器IP:8008/health
curl http://服务器IP:8008/docs
```

### 防火墙检查
- [ ] 服务器防火墙允许8008端口
- [ ] 网络防火墙允许8008端口
- [ ] SELinux配置正确（如果启用）

**验证命令**:
```bash
sudo ufw status
sudo firewall-cmd --list-all
sestatus
```

## 🔧 配置检查

### 环境变量检查
- [ ] PROJECT_ROOT设置正确
- [ ] PYTHONPATH设置正确
- [ ] 数据库连接信息正确
- [ ] API端点配置正确

**验证方法**:
```bash
cd /data/agent
source activate_env.sh
echo $PROJECT_ROOT
echo $PYTHONPATH
cat .env
```

### 文件权限检查
- [ ] /data/agent目录权限正确
- [ ] 脚本文件有执行权限
- [ ] 日志目录可写
- [ ] 临时目录可写

**验证命令**:
```bash
ls -la /data/agent/
ls -la /data/agent/*.sh
ls -la /data/agent/logs/
ls -la /tmp/reports/
```

## 📊 性能检查

### 资源使用检查
- [ ] CPU使用率正常
- [ ] 内存使用率正常
- [ ] 磁盘使用率正常
- [ ] 网络连接正常

**验证命令**:
```bash
top
free -h
df -h
netstat -an | grep 8008
```

### 响应时间检查
- [ ] API响应时间正常（<1秒）
- [ ] 页面加载时间正常
- [ ] WebSocket连接时间正常

**验证方法**:
```bash
time curl http://localhost:8008/health
```

## 🛠️ 故障排除检查清单

### 如果服务无法启动
- [ ] 检查Python环境是否正确
- [ ] 检查依赖包是否安装完整
- [ ] 检查配置文件是否正确
- [ ] 检查端口是否被占用
- [ ] 检查文件权限是否正确

### 如果API无法访问
- [ ] 检查服务是否运行
- [ ] 检查端口是否监听
- [ ] 检查防火墙设置
- [ ] 检查网络连接
- [ ] 检查Nginx配置（如果使用）

### 如果数据库连接失败
- [ ] 检查数据库服务是否运行
- [ ] 检查网络连接
- [ ] 检查用户名密码
- [ ] 检查数据库权限
- [ ] 检查防火墙设置

## ✅ 部署完成确认

当所有检查项都通过时，部署完成！

### 最终确认
- [ ] 所有服务正常运行
- [ ] 所有功能正常工作
- [ ] 性能指标正常
- [ ] 监控告警正常
- [ ] 备份策略已实施

### 交付文档
- [ ] 部署文档已更新
- [ ] 运维手册已提供
- [ ] 故障排除指南已提供
- [ ] 联系方式已提供

---

**部署完成时间**: ___________  
**部署人员**: ___________  
**验收人员**: ___________  
**备注**: ___________
