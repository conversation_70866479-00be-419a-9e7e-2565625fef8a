#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的前后端自测脚本
验证修复后的WebSocket服务和数据库配置
"""

import os
import sys
import json
import asyncio
import websockets
import pymysql
import logging
import time
import subprocess
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteSelfTester:
    """完整自测器"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'config_tests': {},
            'database_tests': {},
            'backend_tests': {},
            'websocket_tests': {},
            'integration_tests': {},
            'summary': {}
        }
        
        self.server_process = None
        self.server_port = 8008
        self.websocket_url = f"ws://localhost:{self.server_port}/ws"
        
        # 测试用户信息
        self.test_users = [
            {"user_id": "test_user_001", "username": "测试用户1"},
            {"user_id": "test_user_002", "username": "测试用户2"},
            {"user_id": "test_user_003", "username": "测试用户3"}
        ]
    
    def test_config_loading(self) -> Dict[str, Any]:
        """测试配置加载"""
        logger.info("🔍 测试配置文件加载...")
        
        result = {
            'config_import_success': False,
            'database_config_valid': False,
            'algorithm_platform_config_valid': False,
            'llm_config_valid': False,
            'config_values': {},
            'errors': []
        }
        
        try:
            # 测试配置导入
            from agents.config import (
                MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB,
                ALGORITHM_PLATFORM_BASE_URL, CURRENT_LLM_PROVIDER,
                get_current_llm_config, get_algorithm_platform_url
            )
            
            result['config_import_success'] = True
            logger.info("✅ 配置文件导入成功")
            
            # 验证数据库配置
            result['config_values']['database'] = {
                'host': MYSQL_HOST,
                'port': MYSQL_PORT,
                'user': MYSQL_USER,
                'database': MYSQL_DB,
                'password_set': bool(MYSQL_PASSWORD)
            }
            
            if MYSQL_HOST and MYSQL_PORT and MYSQL_USER and MYSQL_PASSWORD and MYSQL_DB:
                result['database_config_valid'] = True
                logger.info("✅ 数据库配置有效")
            else:
                result['errors'].append("数据库配置不完整")
            
            # 验证算法平台配置
            result['config_values']['algorithm_platform'] = {
                'base_url': ALGORITHM_PLATFORM_BASE_URL
            }
            
            if ALGORITHM_PLATFORM_BASE_URL:
                result['algorithm_platform_config_valid'] = True
                logger.info("✅ 算法平台配置有效")
            else:
                result['errors'].append("算法平台配置缺失")
            
            # 验证LLM配置
            try:
                llm_config = get_current_llm_config()
                result['config_values']['llm'] = {
                    'provider': CURRENT_LLM_PROVIDER,
                    'model_name': llm_config.get('model_name'),
                    'api_key_set': bool(llm_config.get('api_key'))
                }
                result['llm_config_valid'] = True
                logger.info("✅ LLM配置有效")
            except Exception as e:
                result['errors'].append(f"LLM配置错误: {e}")
        
        except Exception as e:
            result['errors'].append(f"配置导入失败: {e}")
            logger.error(f"❌ 配置导入失败: {e}")
        
        return result
    
    def test_database_connection(self) -> Dict[str, Any]:
        """测试数据库连接"""
        logger.info("🔍 测试数据库连接...")
        
        result = {
            'connection_success': False,
            'database_exists': False,
            'tables_check': {},
            'crud_operations': False,
            'error': None
        }
        
        try:
            from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET
            
            # 测试数据库连接
            connection = pymysql.connect(
                host=MYSQL_HOST,
                port=MYSQL_PORT,
                user=MYSQL_USER,
                password=MYSQL_PASSWORD,
                database=MYSQL_DB,
                charset=MYSQL_CHARSET,
                connect_timeout=10
            )
            
            result['connection_success'] = True
            logger.info("✅ 数据库连接成功")
            
            with connection.cursor() as cursor:
                # 检查数据库
                cursor.execute("SELECT DATABASE()")
                current_db = cursor.fetchone()[0]
                result['database_exists'] = current_db == MYSQL_DB
                
                # 检查关键表
                key_tables = ['conversations', 'conversation_messages', 'session_states', 'algorithm_tasks']
                for table in key_tables:
                    cursor.execute(f"SHOW TABLES LIKE '{table}'")
                    exists = cursor.fetchone() is not None
                    result['tables_check'][table] = exists
                    
                    if exists:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        result['tables_check'][f'{table}_count'] = count
                
                # 测试CRUD操作
                try:
                    test_session_id = f"test_session_{int(time.time())}"
                    
                    # 插入测试数据
                    cursor.execute("""
                        INSERT INTO session_states (user_id, session_id, conversation_id, task_id, state_data, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                    """, ('test_user', test_session_id, '', '', '{"test": true}'))
                    
                    # 查询测试数据
                    cursor.execute("SELECT * FROM session_states WHERE session_id = %s", (test_session_id,))
                    test_data = cursor.fetchone()
                    
                    # 删除测试数据
                    cursor.execute("DELETE FROM session_states WHERE session_id = %s", (test_session_id,))
                    
                    if test_data:
                        result['crud_operations'] = True
                        logger.info("✅ 数据库CRUD操作测试成功")
                    
                except Exception as e:
                    logger.warning(f"⚠️ CRUD操作测试失败: {e}")
            
            connection.close()
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ 数据库连接失败: {e}")
        
        return result
    
    def start_backend_server(self) -> Dict[str, Any]:
        """启动后端服务器"""
        logger.info("🚀 启动后端服务器...")
        
        result = {
            'server_started': False,
            'process_id': None,
            'startup_time': 0,
            'error': None
        }
        
        try:
            # 切换到正确的工作目录
            work_dir = Path(__file__).parent.parent
            
            start_time = time.time()
            
            # 启动服务器进程
            self.server_process = subprocess.Popen(
                [sys.executable, 'predict_main.py'],
                cwd=work_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            result['process_id'] = self.server_process.pid
            
            # 等待服务器启动
            max_wait = 30  # 最多等待30秒
            while time.time() - start_time < max_wait:
                if self.server_process.poll() is not None:
                    # 进程已退出
                    stdout, stderr = self.server_process.communicate()
                    result['error'] = f"服务器启动失败: {stderr}"
                    break
                
                # 检查端口是否开始监听
                try:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result_code = sock.connect_ex(('localhost', self.server_port))
                    sock.close()
                    
                    if result_code == 0:
                        result['server_started'] = True
                        result['startup_time'] = time.time() - start_time
                        logger.info(f"✅ 后端服务器启动成功，耗时: {result['startup_time']:.2f}秒")
                        break
                        
                except Exception:
                    pass
                
                time.sleep(1)
            
            if not result['server_started'] and not result['error']:
                result['error'] = "服务器启动超时"
        
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ 后端服务器启动失败: {e}")
        
        return result
    
    async def test_websocket_functionality(self) -> Dict[str, Any]:
        """测试WebSocket功能"""
        logger.info("🔍 测试WebSocket功能...")
        
        result = {
            'basic_connection': False,
            'welcome_message': False,
            'user_validation': False,
            'message_exchange': False,
            'multiple_users': False,
            'connection_stability': False,
            'test_details': {},
            'errors': []
        }
        
        try:
            # 1. 测试基本连接
            user = self.test_users[0]
            url = f"{self.websocket_url}?user_id={user['user_id']}&username={user['username']}"
            
            async with websockets.connect(url, timeout=10) as websocket:
                result['basic_connection'] = True
                logger.info("✅ WebSocket基本连接成功")
                
                # 2. 测试欢迎消息
                try:
                    welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5)
                    welcome_data = json.loads(welcome_msg)
                    
                    if welcome_data.get('type') == 'welcome':
                        result['welcome_message'] = True
                        result['test_details']['welcome_message'] = welcome_data
                        logger.info("✅ 欢迎消息接收成功")
                    
                except asyncio.TimeoutError:
                    result['errors'].append("未收到欢迎消息")
                
                # 3. 测试消息交换
                test_message = {
                    "type": "user_message",
                    "data": {
                        "message": "你好，这是一个测试消息"
                    }
                }
                
                await websocket.send(json.dumps(test_message, ensure_ascii=False))
                
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=15)
                    response_data = json.loads(response)
                    
                    if response_data.get('type') in ['agent_response', 'error']:
                        result['message_exchange'] = True
                        result['test_details']['message_response'] = response_data
                        logger.info("✅ 消息交换测试成功")
                    
                except asyncio.TimeoutError:
                    result['errors'].append("消息响应超时")
            
            # 4. 测试用户验证（无user_id的连接应该被拒绝）
            try:
                invalid_url = f"{self.websocket_url}?username=无效用户"
                async with websockets.connect(invalid_url, timeout=5) as websocket:
                    await asyncio.wait_for(websocket.recv(), timeout=2)
                    result['errors'].append("用户验证失败：应该拒绝无user_id的连接")
            except (websockets.exceptions.ConnectionClosedError, asyncio.TimeoutError):
                result['user_validation'] = True
                logger.info("✅ 用户验证测试成功")
            
            # 5. 测试多用户连接
            try:
                connections = []
                for user in self.test_users[:2]:  # 测试2个用户
                    url = f"{self.websocket_url}?user_id={user['user_id']}&username={user['username']}"
                    conn = await websockets.connect(url, timeout=10)
                    connections.append(conn)
                    
                    # 等待欢迎消息
                    await asyncio.wait_for(conn.recv(), timeout=5)
                
                if len(connections) == 2:
                    result['multiple_users'] = True
                    logger.info("✅ 多用户连接测试成功")
                
                # 关闭连接
                for conn in connections:
                    await conn.close()
                    
            except Exception as e:
                result['errors'].append(f"多用户连接测试失败: {e}")
            
            # 6. 测试连接稳定性
            try:
                user = self.test_users[0]
                url = f"{self.websocket_url}?user_id={user['user_id']}&username={user['username']}"
                
                async with websockets.connect(url, timeout=10) as websocket:
                    await asyncio.wait_for(websocket.recv(), timeout=5)  # 欢迎消息
                    
                    # 保持连接10秒
                    await asyncio.sleep(10)
                    
                    if not websocket.closed:
                        result['connection_stability'] = True
                        logger.info("✅ 连接稳定性测试成功")
                    
            except Exception as e:
                result['errors'].append(f"连接稳定性测试失败: {e}")
        
        except Exception as e:
            result['errors'].append(f"WebSocket测试失败: {e}")
            logger.error(f"❌ WebSocket测试失败: {e}")
        
        return result
    
    def stop_backend_server(self):
        """停止后端服务器"""
        if self.server_process:
            logger.info("🛑 停止后端服务器...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                self.server_process.wait()
            logger.info("✅ 后端服务器已停止")
    
    def generate_summary(self):
        """生成测试总结"""
        config_ok = self.test_results['config_tests'].get('config_import_success', False)
        db_ok = self.test_results['database_tests'].get('connection_success', False)
        backend_ok = self.test_results['backend_tests'].get('server_started', False)
        ws_basic_ok = self.test_results['websocket_tests'].get('basic_connection', False)
        ws_msg_ok = self.test_results['websocket_tests'].get('message_exchange', False)
        
        self.test_results['summary'] = {
            'overall_success': config_ok and db_ok and backend_ok and ws_basic_ok,
            'config_fixed': config_ok,
            'database_working': db_ok,
            'backend_working': backend_ok,
            'websocket_working': ws_basic_ok and ws_msg_ok,
            'ready_for_deployment': config_ok and db_ok and backend_ok and ws_basic_ok and ws_msg_ok
        }
    
    async def run_complete_test(self):
        """运行完整测试"""
        logger.info("开始完整的前后端自测...")
        
        try:
            # 1. 配置测试
            self.test_results['config_tests'] = self.test_config_loading()
            
            # 2. 数据库测试
            self.test_results['database_tests'] = self.test_database_connection()
            
            # 3. 启动后端服务器
            self.test_results['backend_tests'] = self.start_backend_server()
            
            if self.test_results['backend_tests']['server_started']:
                # 等待服务器完全启动
                await asyncio.sleep(3)
                
                # 4. WebSocket功能测试
                self.test_results['websocket_tests'] = await self.test_websocket_functionality()
            else:
                logger.error("❌ 后端服务器启动失败，跳过WebSocket测试")
                self.test_results['websocket_tests'] = {'error': '后端服务器未启动'}
            
            # 5. 生成总结
            self.generate_summary()
            
        finally:
            # 确保停止服务器
            self.stop_backend_server()
        
        return self.test_results
    
    def generate_report(self) -> str:
        """生成测试报告"""
        report = []
        report.append("=" * 80)
        report.append("完整前后端自测报告")
        report.append("=" * 80)
        report.append(f"测试时间: {self.test_results['timestamp']}")
        report.append("")
        
        # 总结
        summary = self.test_results['summary']
        report.append("📊 自测结果总结:")
        report.append(f"  整体测试通过: {'✅ 是' if summary['overall_success'] else '❌ 否'}")
        report.append(f"  配置文件修复: {'✅ 成功' if summary['config_fixed'] else '❌ 失败'}")
        report.append(f"  数据库连接正常: {'✅ 是' if summary['database_working'] else '❌ 否'}")
        report.append(f"  后端服务正常: {'✅ 是' if summary['backend_working'] else '❌ 否'}")
        report.append(f"  WebSocket功能正常: {'✅ 是' if summary['websocket_working'] else '❌ 否'}")
        report.append(f"  可部署到生产环境: {'✅ 是' if summary['ready_for_deployment'] else '❌ 否'}")
        report.append("")
        
        # 详细结果
        for test_name, test_result in self.test_results.items():
            if test_name in ['timestamp', 'summary']:
                continue
                
            report.append(f"🔍 {test_name.replace('_', ' ').title()}:")
            
            if isinstance(test_result, dict):
                for key, value in test_result.items():
                    if key == 'errors' and value:
                        report.append(f"  ❌ 错误: {', '.join(value)}")
                    elif key == 'error' and value:
                        report.append(f"  ❌ 错误: {value}")
                    elif isinstance(value, bool):
                        status = "✅" if value else "❌"
                        report.append(f"  {status} {key.replace('_', ' ')}: {value}")
                    elif isinstance(value, (int, float, str)) and not key.endswith('_details'):
                        report.append(f"  📊 {key.replace('_', ' ')}: {value}")
            
            report.append("")
        
        # 建议
        if summary['ready_for_deployment']:
            report.append("🎉 恭喜！所有测试通过，系统可以部署到生产环境！")
        else:
            report.append("⚠️ 部分测试未通过，建议修复以下问题后再部署：")
            if not summary['config_fixed']:
                report.append("  - 修复配置文件问题")
            if not summary['database_working']:
                report.append("  - 修复数据库连接问题")
            if not summary['backend_working']:
                report.append("  - 修复后端服务启动问题")
            if not summary['websocket_working']:
                report.append("  - 修复WebSocket功能问题")
        
        return "\n".join(report)

async def main():
    """主函数"""
    print("完整前后端自测工具")
    print("=" * 50)
    
    tester = CompleteSelfTester()
    
    try:
        # 运行完整测试
        results = await tester.run_complete_test()
        
        # 生成报告
        report = tester.generate_report()
        print("\n" + report)
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"complete_self_test_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 保存详细数据
        json_file = f"complete_self_test_data_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"📊 详细数据已保存到: {json_file}")
        
        # 返回测试结果
        return results['summary']['ready_for_deployment']
        
    except Exception as e:
        logger.error(f"自测失败: {e}")
        print(f"\n❌ 自测失败: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
