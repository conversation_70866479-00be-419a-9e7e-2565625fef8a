"""
参数推荐服务
提供智能的算法参数推荐功能，支持多LLM对比和优化建议
"""

import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import statistics
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    ParameterRecommendationRequest, ParameterRecommendationResponse,
    ParameterRecommendation, MultiLLMRecommendationResponse, LLMProvider,
    DataScale, PerformanceObjective, ParameterOptimizationSuggestion,
    ParameterRecommendationHistory, ALGORITHM_PARAMETER_TEMPLATES
)
from agents.predict_algorith_agent.utils.llm_provider_manager import llm_provider_manager

logger = logging.getLogger(__name__)


class ParameterRecommendationService:
    """参数推荐服务"""
    
    def __init__(self):
        """初始化参数推荐服务"""
        self.llm_manager = llm_provider_manager
        self.recommendation_history: List[ParameterRecommendationHistory] = []
    
    async def get_parameter_recommendation(
        self,
        algorithm_type: str,
        data_scale: DataScale = DataScale.MEDIUM,
        data_features: Optional[Dict[str, Any]] = None,
        performance_objective: PerformanceObjective = PerformanceObjective.BALANCED,
        constraints: Optional[Dict[str, Any]] = None,
        current_params: Optional[Dict[str, Any]] = None,
        user_preferences: Optional[Dict[str, Any]] = None,
        context: str = "",
        provider: LLMProvider = LLMProvider.QWEN,
        user_id: Optional[str] = None
    ) -> ParameterRecommendationResponse:
        """获取单个LLM的参数推荐"""
        
        # 构建推荐请求
        request = ParameterRecommendationRequest(
            algorithm_type=algorithm_type,
            data_scale=data_scale,
            data_features=data_features or {},
            performance_objective=performance_objective,
            constraints=constraints or {},
            current_params=current_params or {},
            user_preferences=user_preferences or {},
            context=context
        )
        
        try:
            # 调用LLM获取推荐
            response = await self.llm_manager.get_parameter_recommendation(request, provider)
            
            # 后处理：验证和优化推荐结果
            response = self._post_process_recommendation(response, request)
            
            # 保存推荐历史
            if user_id:
                self._save_recommendation_history(request, response, user_id)
            
            logger.info(f"成功生成参数推荐 - 算法: {algorithm_type}, 提供商: {provider.value}")
            return response
            
        except Exception as e:
            logger.error(f"参数推荐失败 - 算法: {algorithm_type}, 提供商: {provider.value}, 错误: {e}")
            raise
    
    async def get_multi_llm_recommendation(
        self,
        algorithm_type: str,
        data_scale: DataScale = DataScale.MEDIUM,
        data_features: Optional[Dict[str, Any]] = None,
        performance_objective: PerformanceObjective = PerformanceObjective.BALANCED,
        constraints: Optional[Dict[str, Any]] = None,
        current_params: Optional[Dict[str, Any]] = None,
        user_preferences: Optional[Dict[str, Any]] = None,
        context: str = "",
        providers: Optional[List[LLMProvider]] = None,
        user_id: Optional[str] = None
    ) -> MultiLLMRecommendationResponse:
        """获取多个LLM的参数推荐并进行对比分析"""
        
        # 构建推荐请求
        request = ParameterRecommendationRequest(
            algorithm_type=algorithm_type,
            data_scale=data_scale,
            data_features=data_features or {},
            performance_objective=performance_objective,
            constraints=constraints or {},
            current_params=current_params or {},
            user_preferences=user_preferences or {},
            context=context
        )
        
        try:
            # 获取多个LLM的推荐结果
            if providers is None:
                providers = self.llm_manager.get_available_providers()
            
            recommendations = await self.llm_manager.get_multi_llm_recommendations(request, providers)
            
            if not recommendations:
                raise ValueError("未能获取任何有效的参数推荐")
            
            # 分析和对比推荐结果
            multi_response = self._analyze_multi_llm_recommendations(algorithm_type, recommendations)
            
            logger.info(f"成功生成多LLM参数推荐 - 算法: {algorithm_type}, 模型数: {len(recommendations)}")
            return multi_response
            
        except Exception as e:
            logger.error(f"多LLM参数推荐失败 - 算法: {algorithm_type}, 错误: {e}")
            raise
    
    def _post_process_recommendation(
        self, 
        response: ParameterRecommendationResponse, 
        request: ParameterRecommendationRequest
    ) -> ParameterRecommendationResponse:
        """后处理推荐结果，验证和优化"""
        
        # 获取算法模板
        template = ALGORITHM_PARAMETER_TEMPLATES.get(request.algorithm_type)
        if not template:
            return response
        
        # 验证推荐参数的合理性
        validated_params = []
        for param_rec in response.recommended_parameters:
            param_name = param_rec.parameter_name
            
            # 检查参数是否在模板中
            if param_name in template.required_parameters or param_name in template.optional_parameters:
                # 检查参数值是否在合理范围内
                if param_name in template.parameter_ranges:
                    param_range = template.parameter_ranges[param_name]
                    validated_value = self._validate_parameter_value(
                        param_rec.recommended_value, param_range
                    )
                    if validated_value != param_rec.recommended_value:
                        param_rec.recommended_value = validated_value
                        param_rec.reasoning += f" (已调整到合理范围内)"
                
                validated_params.append(param_rec)
        
        response.recommended_parameters = validated_params
        
        # 添加缺失的必需参数
        recommended_param_names = {p.parameter_name for p in validated_params}
        for required_param in template.required_parameters:
            if required_param not in recommended_param_names:
                default_value = template.parameter_defaults.get(required_param)
                if default_value is not None:
                    missing_param = ParameterRecommendation(
                        parameter_name=required_param,
                        recommended_value=default_value,
                        confidence=0.6,
                        reasoning=f"必需参数，使用默认值",
                        impact_description=template.parameter_descriptions.get(
                            required_param, f"{required_param}参数"
                        )
                    )
                    response.recommended_parameters.append(missing_param)
        
        return response
    
    def _validate_parameter_value(self, value: Any, param_range: Dict[str, Any]) -> Any:
        """验证参数值是否在合理范围内"""
        try:
            if isinstance(value, (int, float)):
                if "min" in param_range and value < param_range["min"]:
                    return param_range["min"]
                if "max" in param_range and value > param_range["max"]:
                    return param_range["max"]
            return value
        except:
            return value
    
    def _analyze_multi_llm_recommendations(
        self, 
        algorithm_type: str, 
        recommendations: List[ParameterRecommendationResponse]
    ) -> MultiLLMRecommendationResponse:
        """分析多个LLM的推荐结果"""
        
        # 收集所有参数推荐
        all_params = {}
        for rec in recommendations:
            for param_rec in rec.recommended_parameters:
                param_name = param_rec.parameter_name
                if param_name not in all_params:
                    all_params[param_name] = []
                all_params[param_name].append({
                    "value": param_rec.recommended_value,
                    "confidence": param_rec.confidence,
                    "provider": rec.llm_provider.value,
                    "reasoning": param_rec.reasoning
                })
        
        # 分析一致性和分歧
        consensus_parameters = {}
        divergent_parameters = {}
        
        for param_name, param_values in all_params.items():
            values = [p["value"] for p in param_values]
            
            # 检查数值参数的一致性
            if all(isinstance(v, (int, float)) for v in values):
                mean_val = statistics.mean(values)
                std_val = statistics.stdev(values) if len(values) > 1 else 0
                
                # 如果标准差相对较小，认为是一致的
                if std_val / mean_val < 0.2 if mean_val != 0 else std_val < 0.1:
                    consensus_parameters[param_name] = mean_val
                else:
                    divergent_parameters[param_name] = values
            else:
                # 非数值参数，检查是否完全一致
                unique_values = list(set(values))
                if len(unique_values) == 1:
                    consensus_parameters[param_name] = unique_values[0]
                else:
                    divergent_parameters[param_name] = values
        
        # 生成最终推荐（优先使用一致性参数，分歧参数使用置信度最高的）
        final_recommendation = consensus_parameters.copy()
        for param_name, param_values in divergent_parameters.items():
            # 选择置信度最高的推荐
            best_param = max(all_params[param_name], key=lambda x: x["confidence"])
            final_recommendation[param_name] = best_param["value"]
        
        # 置信度分析
        confidence_analysis = {}
        for rec in recommendations:
            provider_name = rec.llm_provider.value
            confidence_analysis[provider_name] = rec.overall_confidence
        
        # 模型对比
        model_comparison = {}
        for rec in recommendations:
            provider_name = rec.llm_provider.value
            model_comparison[provider_name] = {
                "model_name": rec.model_name,
                "generation_time": rec.generation_time,
                "confidence": rec.overall_confidence,
                "param_count": len(rec.recommended_parameters),
                "optimization_suggestions": len(rec.optimization_suggestions)
            }
        
        return MultiLLMRecommendationResponse(
            algorithm_type=algorithm_type,
            recommendations=recommendations,
            consensus_parameters=consensus_parameters,
            divergent_parameters=divergent_parameters,
            final_recommendation=final_recommendation,
            confidence_analysis=confidence_analysis,
            model_comparison=model_comparison
        )
    
    def _save_recommendation_history(
        self, 
        request: ParameterRecommendationRequest,
        response: ParameterRecommendationResponse,
        user_id: str
    ):
        """保存推荐历史记录"""
        try:
            history = ParameterRecommendationHistory(
                request_id=str(uuid.uuid4()),
                user_id=user_id,
                algorithm_type=request.algorithm_type,
                request_data=request,
                response_data=response
            )
            self.recommendation_history.append(history)
            
            # 限制历史记录数量
            if len(self.recommendation_history) > 1000:
                self.recommendation_history = self.recommendation_history[-1000:]
                
        except Exception as e:
            logger.error(f"保存推荐历史失败: {e}")
    
    def get_optimization_suggestions(
        self, 
        current_params: Dict[str, Any],
        algorithm_type: str,
        performance_feedback: Optional[Dict[str, float]] = None
    ) -> List[ParameterOptimizationSuggestion]:
        """获取参数优化建议"""
        
        suggestions = []
        template = ALGORITHM_PARAMETER_TEMPLATES.get(algorithm_type)
        
        if not template:
            return suggestions
        
        # 基于模板和当前参数生成优化建议
        for param_name in template.tuning_priorities:
            if param_name in current_params:
                current_value = current_params[param_name]
                
                # 生成基础优化建议
                suggestion = self._generate_parameter_suggestion(
                    param_name, current_value, template, performance_feedback
                )
                if suggestion:
                    suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_parameter_suggestion(
        self,
        param_name: str,
        current_value: Any,
        template,
        performance_feedback: Optional[Dict[str, float]] = None
    ) -> Optional[ParameterOptimizationSuggestion]:
        """生成单个参数的优化建议"""
        
        # 这里可以实现更复杂的优化逻辑
        # 目前提供基础的建议
        
        if param_name == "learning_rate" and isinstance(current_value, (int, float)):
            if performance_feedback and performance_feedback.get("loss_trend") == "increasing":
                return ParameterOptimizationSuggestion(
                    parameter_name=param_name,
                    current_value=current_value,
                    suggested_value=current_value * 0.5,
                    optimization_direction="decrease",
                    expected_improvement="降低学习率可能改善训练稳定性",
                    risk_assessment="低风险，可能需要更多训练轮数",
                    priority=8
                )
        
        return None


# 全局参数推荐服务实例
parameter_recommendation_service = ParameterRecommendationService()
