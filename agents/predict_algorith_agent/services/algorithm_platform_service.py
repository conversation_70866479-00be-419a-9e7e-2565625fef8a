#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法平台API服务
提供与算法平台交互的所有功能
"""

import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any
from urllib.parse import urljoin

# 引用配置文件
from agents.config import (
    ALGORITHM_PLATFORM_BASE_URL,
    ALGORITHM_PLATFORM_TIMEOUT,
    ALGORITHM_PLATFORM_RETRY_TIMES,
    ALGORITHM_PLATFORM_RETRY_DELAY,
    get_algorithm_platform_url
)

from ..models.algorithm_platform_models import (
    GetAllAlgorithmResponse,
    GetAllDatasetResponse,
    AlgorithmTrainingRequest,
    AlgorithmTrainingResponse,
    GetAllIOTResponse,
    MonitoringServiceRequest,
    MonitoringServiceResponse,
    GetAlgorithmLogResponse,
    GetAllImplementResponse,
    GetImplementLogResponse,
    AlgorithmPlatformError,
    NetworkError,
    TimeoutError,
    APIError
)

logger = logging.getLogger(__name__)


class AlgorithmPlatformService:
    """算法平台API服务类"""

    def __init__(self):
        """
        初始化算法平台服务
        使用配置文件中的设置
        """
        self.base_url = ALGORITHM_PLATFORM_BASE_URL
        self.timeout = ALGORITHM_PLATFORM_TIMEOUT
        self.retry_times = ALGORITHM_PLATFORM_RETRY_TIMES
        self.retry_delay = ALGORITHM_PLATFORM_RETRY_DELAY
        self._session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        
    async def _ensure_session(self):
        """确保HTTP会话已创建"""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(timeout=timeout)
            
    async def close(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        form_data: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        发起HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            params: URL参数
            data: JSON数据
            form_data: 表单数据
            
        Returns:
            响应JSON数据
            
        Raises:
            AlgorithmPlatformError: API调用错误
        """
        await self._ensure_session()
        
        # 使用配置文件中的URL构建函数
        if endpoint.startswith('/'):
            url = f"{self.base_url}{endpoint}"
        else:
            url = get_algorithm_platform_url(endpoint)

        for attempt in range(self.retry_times):
            try:
                logger.info(f"发起API请求: {method} {url}, 尝试 {attempt + 1}/{self.retry_times}")

                # 准备请求参数
                kwargs = {}
                if params:
                    kwargs['params'] = params
                if data:
                    kwargs['json'] = data
                if form_data:
                    # 使用FormData
                    form = aiohttp.FormData()
                    for key, value in form_data.items():
                        form.add_field(key, value)
                    kwargs['data'] = form
                
                async with self._session.request(method, url, **kwargs) as response:
                    response_text = await response.text()
                    
                    # 检查HTTP状态码
                    if response.status >= 400:
                        logger.error(f"API请求失败: {response.status} {response_text}")
                        raise APIError(
                            f"API请求失败: HTTP {response.status}",
                            status_code=response.status,
                            response_data={"text": response_text}
                        )
                    
                    # 解析JSON响应
                    try:
                        response_data = await response.json()
                        logger.info(f"API请求成功: {url}")
                        return response_data
                    except Exception as e:
                        logger.error(f"JSON解析失败: {e}, 响应内容: {response_text}")
                        raise APIError(f"响应JSON解析失败: {e}")
                        
            except asyncio.TimeoutError:
                logger.warning(f"API请求超时: {url}, 尝试 {attempt + 1}/{self.retry_times}")
                if attempt == self.retry_times - 1:
                    raise TimeoutError(f"API请求超时: {url}")
                await asyncio.sleep(self.retry_delay)

            except aiohttp.ClientError as e:
                logger.warning(f"网络连接错误: {e}, 尝试 {attempt + 1}/{self.retry_times}")
                if attempt == self.retry_times - 1:
                    raise NetworkError(f"网络连接失败: {e}")
                await asyncio.sleep(self.retry_delay)
                
            except (APIError, AlgorithmPlatformError):
                # 这些错误不需要重试
                raise
                
            except Exception as e:
                logger.error(f"未知错误: {e}, 尝试 {attempt + 1}/{self.retry_times}")
                if attempt == self.retry_times - 1:
                    raise AlgorithmPlatformError(f"API调用失败: {e}")
                await asyncio.sleep(self.retry_delay)
    
    # ==================== API接口方法 ====================
    
    async def get_all_algorithms(self) -> GetAllAlgorithmResponse:
        """
        查询全部算法名称

        Returns:
            算法列表响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常，不使用Mock数据
        """
        try:
            response_data = await self._make_request("GET", "get_all_algorithm")
            return GetAllAlgorithmResponse(**response_data)
        except Exception as e:
            logger.error(f"查询算法列表失败: {e}")
            # 直接抛出异常，不使用Mock数据
            raise AlgorithmPlatformError(f"无法获取算法列表: {e}")

    async def get_all_datasets(self) -> GetAllDatasetResponse:
        """
        查询全部数据集名称

        Returns:
            数据集列表响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常，不使用Mock数据
        """
        try:
            response_data = await self._make_request("GET", "get_all_dataset")
            return GetAllDatasetResponse(**response_data)
        except Exception as e:
            logger.error(f"查询数据集列表失败: {e}")
            # 直接抛出异常，不使用Mock数据
            raise AlgorithmPlatformError(f"无法获取数据集列表: {e}")
    
    async def train_algorithm(self, request: AlgorithmTrainingRequest) -> AlgorithmTrainingResponse:
        """
        训练新算法（异步）

        Args:
            request: 训练请求参数

        Returns:
            训练响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常
        """
        try:
            form_data = request.to_form_data()
            response_data = await self._make_request("POST", "algorithm_parameter", form_data=form_data)
            return AlgorithmTrainingResponse(**response_data)
        except Exception as e:
            logger.error(f"启动算法训练失败: {e}")
            raise AlgorithmPlatformError(f"无法启动算法训练: {e}")
    
    async def get_all_iot(self, project_id: str) -> GetAllIOTResponse:
        """
        查询全部IOT名称

        Args:
            project_id: 项目ID（必传）

        Returns:
            IOT列表响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常
        """
        try:
            params = {"project": project_id}
            response_data = await self._make_request("GET", "get_all_iot", params=params)
            return GetAllIOTResponse(**response_data)
        except Exception as e:
            logger.error(f"查询IOT列表失败: {e}")
            raise AlgorithmPlatformError(f"无法获取IOT列表: {e}")
    
    async def start_monitoring_service(self, request: MonitoringServiceRequest) -> MonitoringServiceResponse:
        """
        启动监控分析服务（异步）

        Args:
            request: 监控服务请求参数

        Returns:
            监控服务响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常
        """
        try:
            form_data = request.to_form_data()
            response_data = await self._make_request("POST", "implement_parameter", form_data=form_data)
            return MonitoringServiceResponse(**response_data)
        except Exception as e:
            logger.error(f"启动监控服务失败: {e}")
            raise AlgorithmPlatformError(f"无法启动监控服务: {e}")

    async def get_algorithm_log(self, algorithm_name: str, project_id: str) -> GetAlgorithmLogResponse:
        """
        查询算法训练信息

        Args:
            algorithm_name: 算法名称
            project_id: 项目ID（必传）

        Returns:
            算法训练信息响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常
        """
        try:
            params = {"name": algorithm_name, "project": project_id}
            response_data = await self._make_request("GET", "get_algorithm_log", params=params)
            return GetAlgorithmLogResponse(**response_data)
        except Exception as e:
            logger.error(f"查询算法训练信息失败: {e}")
            raise AlgorithmPlatformError(f"无法获取算法训练信息: {e}")
    
    async def get_all_services(self, project_id: str) -> GetAllImplementResponse:
        """
        查询全部服务名称

        Args:
            project_id: 项目ID（必传）

        Returns:
            服务列表响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常
        """
        try:
            params = {"project": project_id}
            response_data = await self._make_request("GET", "get_all_implement", params=params)
            return GetAllImplementResponse(**response_data)
        except Exception as e:
            logger.error(f"查询服务列表失败: {e}")
            raise AlgorithmPlatformError(f"无法获取服务列表: {e}")

    async def get_service_prediction(self, service_name: str, project_id: str) -> GetImplementLogResponse:
        """
        查询服务预测结果

        Args:
            service_name: 服务名称
            project_id: 项目ID（必传）

        Returns:
            预测结果响应

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常
        """
        try:
            params = {"name": service_name, "project": project_id}
            response_data = await self._make_request("GET", "get_implement_log", params=params)
            return GetImplementLogResponse(**response_data)
        except Exception as e:
            logger.error(f"查询服务预测结果失败: {e}")
            raise AlgorithmPlatformError(f"无法获取服务预测结果: {e}")


# ==================== 便捷函数 ====================

async def create_algorithm_platform_service() -> AlgorithmPlatformService:
    """创建算法平台服务实例"""
    return AlgorithmPlatformService()


# ==================== 全局服务实例 ====================

# 可以在应用启动时初始化全局实例
_global_service: Optional[AlgorithmPlatformService] = None


async def get_algorithm_platform_service() -> AlgorithmPlatformService:
    """获取全局算法平台服务实例"""
    global _global_service
    if _global_service is None:
        _global_service = AlgorithmPlatformService()
    return _global_service
