#!/usr/bin/env python3
"""
兜底回答系统 - 当LLM返回空响应时提供智能兜底回答
"""

import re
from typing import Dict, List, Optional, Any
from datetime import datetime

class FallbackResponseManager:
    """兜底回答管理器"""
    
    def __init__(self):
        self.concept_keywords = {
            "机器学习": ["机器学习", "machine learning", "ml", "算法", "模型", "训练"],
            "深度学习": ["深度学习", "deep learning", "神经网络", "neural network"],
            "LSTM": ["lstm", "长短期记忆", "时序", "序列", "预测"],
            "CNN": ["cnn", "卷积", "图像", "视觉", "识别"],
            "参数": ["参数", "parameter", "配置", "设置", "调优"],
            "训练": ["训练", "training", "学习", "拟合"],
            "预测": ["预测", "prediction", "预报", "forecast"],
            "数据": ["数据", "data", "dataset", "样本"]
        }
        
        self.concept_responses = {
            "机器学习": self._get_ml_concept_response(),
            "深度学习": self._get_dl_concept_response(),
            "LSTM": self._get_lstm_concept_response(),
            "CNN": self._get_cnn_concept_response(),
            "参数": self._get_parameter_concept_response(),
            "训练": self._get_training_concept_response(),
            "预测": self._get_prediction_concept_response(),
            "数据": self._get_data_concept_response()
        }
    
    def get_fallback_response(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        根据用户输入生成智能兜底回答

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            兜底回答字典
        """
        # 分析用户输入类型
        input_type = self._analyze_input_type(user_input)

        # 根据输入类型生成相应的兜底回答
        if input_type == "error_handling":
            return self._get_error_handling_response(user_input)
        elif input_type == "context_memory":
            return self._get_context_memory_response(user_input, context)
        elif input_type == "concept_explanation":
            return self._get_concept_explanation_response(user_input)
        elif input_type == "special_function":
            return self._get_special_function_response(user_input)
        elif input_type == "parameter_question":
            return self._get_parameter_question_response(user_input)
        elif input_type == "algorithm_recommendation":
            return self._get_algorithm_recommendation_response(user_input)
        elif input_type == "algorithm_training":
            return self._get_algorithm_training_response(user_input)
        elif input_type == "general_help":
            return self._get_general_help_response()
        else:
            return self._get_intelligent_default_response(user_input)
    
    def _analyze_input_type(self, user_input: str) -> str:
        """智能分析用户输入类型"""
        user_input_lower = user_input.lower()

        # 优先级1: 错误处理类 - 检测不存在的算法或错误请求
        error_indicators = ["不存在", "找不到", "没有", "错误", "xyz", "abc", "test123"]
        if any(indicator in user_input_lower for indicator in error_indicators):
            return "error_handling"

        # 优先级2: 上下文记忆类 - 检测对历史对话的引用
        context_indicators = ["记得", "刚才", "之前", "刚刚", "前面", "上次", "还记得"]
        if any(indicator in user_input_lower for indicator in context_indicators):
            return "context_memory"

        # 优先级3: 概念解释类 - 明确的解释请求
        concept_indicators = ["解释", "什么是", "介绍一下", "概念", "原理", "explain", "what is"]
        if any(indicator in user_input_lower for indicator in concept_indicators):
            return "concept_explanation"

        # 优先级4: 特殊功能类 - 系统功能操作
        special_indicators = ["清除", "删除", "重置", "查看历史", "历史记录", "总结"]
        if any(indicator in user_input_lower for indicator in special_indicators):
            return "special_function"

        # 优先级5: 参数配置类 - 具体的参数请求
        if "参数" in user_input_lower and ("配置" in user_input_lower or "设置" in user_input_lower):
            return "parameter_question"

        # 优先级6: 算法推荐类 - 推荐请求
        if "推荐" in user_input_lower and "算法" in user_input_lower:
            return "algorithm_recommendation"

        # 优先级7: 一般帮助类
        help_indicators = ["帮助", "help", "怎么", "如何", "能做什么", "功能"]
        if any(indicator in user_input_lower for indicator in help_indicators):
            return "general_help"

        # 优先级8: 算法训练类 - 最后检查，避免过度匹配
        if ("训练" in user_input_lower or "模型" in user_input_lower) and len(user_input) > 10:
            return "algorithm_training"

        return "unknown"

    def _get_error_handling_response(self, user_input: str) -> Dict[str, Any]:
        """处理错误请求的响应"""
        # 提取可能的算法名称
        words = user_input.split()
        potential_algorithm = None
        for word in words:
            if word.upper() not in ["请", "帮", "我", "配置", "一个", "的", "算法"] and len(word) > 2:
                potential_algorithm = word
                break

        if potential_algorithm:
            response = f"""❌ **算法不存在**

抱歉，我无法找到名为 "{potential_algorithm}" 的算法。

🔍 **可能的原因**：
- 算法名称拼写错误
- 该算法不在我们的支持列表中
- 可能是自定义或实验性算法

✅ **我支持的算法类型**：
- **LSTM** - 长短期记忆网络，适用于时序预测
- **CNN** - 卷积神经网络，适用于图像识别
- **RNN** - 循环神经网络，适用于序列数据
- **线性回归** - 适用于简单预测任务
- **随机森林** - 适用于分类和回归
- **SVM** - 支持向量机，适用于分类

💡 **建议**：
1. 检查算法名称是否正确
2. 描述您的具体需求，我可以推荐合适的算法
3. 如果需要特定算法，请提供更多详细信息

请重新描述您的需求，我会为您推荐最适合的算法！"""
        else:
            response = """❌ **请求无法处理**

抱歉，我无法理解您的请求。

💡 **建议**：
- 请描述您的具体需求
- 说明要处理的数据类型
- 告诉我想要实现的目标

我会为您提供专业的算法建议！"""

        return {
            "msg": response,
            "type": "error_handling",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }

    def _get_context_memory_response(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理上下文记忆请求的响应"""
        response = """🧠 **上下文记忆**

我理解您在询问之前的对话内容。

📝 **关于对话记忆**：
- 我可以在当前会话中记住我们的对话
- 我会尽力回忆相关的讨论内容
- 如果涉及具体的项目或算法，我会提供相关建议

🔄 **继续对话**：
如果您想继续之前的话题，请：
1. 重新描述一下具体需求
2. 提及关键的项目信息
3. 说明当前遇到的问题

这样我就能为您提供更准确的帮助！

💡 **提示**：您可以说"我有一个销售预测项目，需要算法建议"这样的具体描述。"""

        return {
            "msg": response,
            "type": "context_memory",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }

    def _get_special_function_response(self, user_input: str) -> Dict[str, Any]:
        """处理特殊功能请求的响应"""
        user_input_lower = user_input.lower()

        if "清除" in user_input_lower or "删除" in user_input_lower:
            response = """🗑️ **会话清除**

我理解您想要清除当前会话记录。

⚠️ **注意事项**：
- 清除会话将删除当前对话历史
- 之前的算法推荐和配置信息将丢失
- 您需要重新开始对话

🔄 **替代方案**：
- 开始新的话题讨论
- 重新描述您的需求
- 继续在当前会话中提问

💡 **建议**：如果您想开始新的算法项目，直接告诉我新的需求即可，无需清除历史记录。"""

        elif "历史" in user_input_lower:
            response = """📋 **历史记录查看**

我可以帮您回顾我们的对话历史。

📊 **可查看的信息**：
- 之前讨论的算法类型
- 推荐的参数配置
- 项目需求分析
- 给出的建议和方案

🔍 **查看方式**：
请具体说明您想了解什么，比如：
- "回顾一下推荐的算法"
- "之前的参数配置是什么"
- "总结一下我们的讨论"

💡 **提示**：我会根据您的具体请求提供相关的历史信息总结。"""

        else:
            response = """⚙️ **系统功能**

我理解您在询问系统功能操作。

🛠️ **可用功能**：
- 算法推荐和配置
- 参数优化建议
- 训练指导和监控
- 结果分析和报告

💬 **使用方式**：
请直接描述您的具体需求，我会提供相应的帮助和指导。

💡 **示例**：
- "推荐时序预测算法"
- "配置LSTM参数"
- "分析训练结果"""

        return {
            "msg": response,
            "type": "special_function",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }

    def _get_algorithm_recommendation_response(self, user_input: str) -> Dict[str, Any]:
        """处理算法推荐请求的响应"""
        response = """🎯 **算法推荐服务**

我很乐意为您推荐合适的算法！

📊 **推荐流程**：
1. **需求分析** - 了解您的数据和目标
2. **算法匹配** - 根据场景推荐最适合的算法
3. **参数建议** - 提供初始参数配置
4. **实施指导** - 给出具体的实现建议

🔍 **为了给出精准推荐，请告诉我**：
- **数据类型**：时序数据、图像、文本、表格数据？
- **数据规模**：大概多少条记录？
- **目标任务**：预测、分类、聚类、异常检测？
- **性能要求**：准确性、速度、资源消耗？

💡 **示例描述**：
"我有1万条销售历史数据，想预测未来3个月的销售趋势"

请提供更多细节，我会为您推荐最合适的算法方案！"""

        return {
            "msg": response,
            "type": "algorithm_recommendation",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }

    def _get_concept_explanation_response(self, user_input: str) -> Dict[str, Any]:
        """生成概念解释类回答"""
        # 识别用户询问的具体概念
        concept = self._identify_concept(user_input)
        
        if concept and concept in self.concept_responses:
            response_content = self.concept_responses[concept]
        else:
            response_content = self._get_general_concept_response()
        
        return {
            "msg": response_content,
            "type": "concept_explanation",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_algorithm_training_response(self, user_input: str) -> Dict[str, Any]:
        """生成算法训练类回答"""
        response = """🤖 **算法训练指导**

我可以帮助您进行算法训练！以下是常见的训练流程：

📋 **训练步骤**：
1. **需求分析** - 确定要解决的问题类型
2. **算法选择** - 选择合适的算法（LSTM、CNN等）
3. **参数配置** - 设置学习率、批次大小等参数
4. **开始训练** - 启动训练任务
5. **监控进度** - 实时查看训练状态
6. **结果分析** - 生成训练报告

🎯 **支持的算法类型**：
- **LSTM** - 适用于时序数据预测
- **CNN** - 适用于图像识别任务
- **RNN** - 适用于序列数据处理

💡 **建议**：请详细描述您的具体需求，比如：
- 要处理什么类型的数据？
- 想要实现什么功能？
- 有什么特殊要求？

这样我就能为您提供更精准的帮助！"""

        return {
            "msg": response,
            "type": "algorithm_training_guide",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_parameter_question_response(self, user_input: str) -> Dict[str, Any]:
        """生成参数问题类回答"""
        response = """⚙️ **算法参数配置指导**

算法参数对训练效果至关重要！以下是常见参数的说明：

🔧 **核心参数**：
- **学习率 (Learning Rate)** - 控制模型学习速度，通常在0.001-0.1之间
- **批次大小 (Batch Size)** - 每次训练的样本数量，常用32、64、128
- **训练轮数 (Epochs)** - 完整训练数据集的遍历次数
- **隐藏层单元数** - 神经网络的复杂度控制

📊 **LSTM特有参数**：
- **序列长度** - 输入时间序列的长度
- **隐藏单元数** - LSTM层的神经元数量
- **Dropout率** - 防止过拟合的正则化参数

🎯 **CNN特有参数**：
- **卷积核大小** - 特征提取窗口大小
- **池化大小** - 降维操作的窗口大小
- **卷积层数** - 网络深度控制

💡 **调优建议**：
1. 从默认参数开始
2. 逐步调整关键参数
3. 观察训练效果变化
4. 使用验证集评估性能

需要具体的参数配置建议吗？请告诉我您的算法类型和数据特点！"""

        return {
            "msg": response,
            "type": "parameter_question",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_general_help_response(self) -> Dict[str, Any]:
        """生成一般帮助类回答"""
        response = """👋 **欢迎使用算法智能体！**

我是您的专业算法训练助手，可以为您提供以下服务：

🔧 **核心功能**：
1. **智能算法推荐** - 根据您的需求推荐最适合的算法
2. **参数自动配置** - 智能推荐最优的训练参数
3. **实时训练监控** - 跟踪训练进度和性能指标
4. **专业报告生成** - 自动生成详细的分析报告

📚 **知识服务**：
- 机器学习概念解释
- 算法原理介绍
- 参数调优指导
- 最佳实践建议

🚀 **使用方式**：
- 直接描述您的需求，如"我想用LSTM做股票预测"
- 询问概念，如"什么是深度学习"
- 寻求帮助，如"如何调整学习率"

💬 **示例对话**：
- "我有销售数据，想做预测分析"
- "解释一下卷积神经网络的原理"
- "LSTM的参数应该怎么设置"

现在就开始吧！请告诉我您想要了解或实现什么？"""

        return {
            "msg": response,
            "type": "general_help",
            "fallback": True,
            "timestamp": datetime.now().isoformat()
        }
    
    def _get_intelligent_default_response(self, user_input: str) -> Dict[str, Any]:
        """生成智能默认兜底回答"""
        # 分析用户输入的关键词，尝试理解意图
        user_input_lower = user_input.lower()

        # 检测可能的意图
        if any(word in user_input_lower for word in ["数据", "预测", "分析"]):
            intent = "data_analysis"
        elif any(word in user_input_lower for word in ["学习", "了解", "知道"]):
            intent = "learning"
        elif any(word in user_input_lower for word in ["项目", "任务", "工作"]):
            intent = "project"
        else:
            intent = "general"

        # 根据意图生成不同的回答
        if intent == "data_analysis":
            response = f"""📊 **数据分析需求理解**

我注意到您提到了数据分析相关的内容："{user_input}"

🎯 **我可以帮您**：
- **数据预处理** - 清洗、标准化、特征工程
- **算法选择** - 根据数据特点推荐最适合的算法
- **模型训练** - 配置参数、监控训练过程
- **结果分析** - 生成报告、性能评估

💡 **请详细描述**：
- 您的数据是什么类型？（时序、图像、文本、表格）
- 数据规模大概多少？
- 想要实现什么目标？（预测、分类、聚类等）

这样我就能为您提供精准的算法建议！"""

        elif intent == "learning":
            response = f"""📚 **学习需求理解**

我理解您想要学习相关知识："{user_input}"

🧠 **我可以解释**：
- **机器学习基础概念** - 监督学习、无监督学习、强化学习
- **算法原理详解** - LSTM、CNN、RNN等算法工作原理
- **参数调优技巧** - 如何选择和调整算法参数
- **实践应用案例** - 真实场景中的算法应用

💡 **学习建议**：
请告诉我您想了解的具体概念或算法，我会提供详细的解释和实例。

例如："解释一下LSTM的工作原理"或"什么是深度学习"。"""

        elif intent == "project":
            response = f"""🚀 **项目需求理解**

我理解您在讨论项目相关内容："{user_input}"

📋 **项目支持服务**：
- **需求分析** - 理解项目目标和约束条件
- **技术方案** - 推荐合适的算法和架构
- **实施计划** - 提供分步骤的实施指导
- **风险评估** - 识别潜在问题和解决方案

💡 **项目咨询流程**：
1. 描述项目背景和目标
2. 说明可用的数据资源
3. 明确性能和时间要求
4. 获得定制化的技术方案

请详细描述您的项目需求，我会提供专业的技术建议！"""

        else:
            response = f"""🤖 **智能助手理解**

我正在努力理解您的需求："{user_input}"

🎯 **我的专长领域**：
- **算法推荐** - 根据需求推荐最适合的机器学习算法
- **参数配置** - 提供最优的算法参数设置
- **技术咨询** - 解答机器学习相关的技术问题
- **项目指导** - 提供端到端的项目实施建议

💬 **更好的交流方式**：
- 描述具体的业务场景
- 说明要处理的数据类型
- 明确想要达成的目标
- 提及遇到的具体问题

💡 **示例**：
"我想用机器学习预测客户流失"
"如何选择合适的深度学习算法"
"LSTM参数应该怎么设置"

请重新描述您的需求，我会提供更精准的帮助！"""

        return {
            "msg": response,
            "type": "intelligent_default",
            "fallback": True,
            "user_input": user_input,
            "detected_intent": intent,
            "timestamp": datetime.now().isoformat()
        }

    def _get_default_response(self, user_input: str) -> Dict[str, Any]:
        """生成默认兜底回答"""
        response = f"""🤔 **理解您的需求**

抱歉，我暂时无法完全理解您的输入："{user_input}"

不过别担心！我可以帮助您：

🎯 **明确需求**：
- 如果您想训练算法，请告诉我：
  - 数据类型（时序数据、图像、文本等）
  - 目标任务（预测、分类、识别等）
  - 具体需求（如"用LSTM预测股价"）

📚 **学习概念**：
- 询问"什么是机器学习"
- 了解"LSTM和CNN的区别"
- 学习"如何选择算法参数"

🔧 **获取帮助**：
- 输入"帮助"查看完整功能
- 说"我想训练一个算法"开始流程
- 问"你能做什么"了解能力

💡 **建议**：请用更具体的描述重新提问，我会尽力为您提供专业的帮助！"""

        return {
            "msg": response,
            "type": "clarification_request",
            "fallback": True,
            "timestamp": datetime.now().isoformat(),
            "original_input": user_input
        }
    
    def _identify_concept(self, user_input: str) -> Optional[str]:
        """识别用户询问的概念"""
        user_input_lower = user_input.lower()
        
        for concept, keywords in self.concept_keywords.items():
            if any(keyword in user_input_lower for keyword in keywords):
                return concept
        
        return None
    
    def _get_ml_concept_response(self) -> str:
        """机器学习概念回答"""
        return """🧠 **机器学习 (Machine Learning)**

机器学习是人工智能的一个分支，让计算机能够从数据中自动学习和改进，而无需明确编程。

🔍 **核心概念**：
- **监督学习** - 使用标记数据训练模型
- **无监督学习** - 从未标记数据中发现模式
- **强化学习** - 通过奖励机制学习最优策略

📊 **常见应用**：
- 图像识别、语音识别
- 推荐系统、搜索引擎
- 金融风控、医疗诊断
- 自动驾驶、智能客服

🛠️ **主要算法**：
- **线性回归** - 预测连续值
- **决策树** - 分类和回归
- **神经网络** - 复杂模式识别
- **支持向量机** - 分类和回归

想了解更多特定算法或开始训练任务吗？"""
    
    def _get_dl_concept_response(self) -> str:
        """深度学习概念回答"""
        return """🧠 **深度学习 (Deep Learning)**

深度学习是机器学习的一个子领域，使用多层神经网络来学习数据的复杂表示。

🏗️ **网络结构**：
- **输入层** - 接收原始数据
- **隐藏层** - 提取特征（多层）
- **输出层** - 产生最终结果

🎯 **主要类型**：
- **CNN (卷积神经网络)** - 图像处理专家
- **RNN/LSTM** - 序列数据处理
- **Transformer** - 自然语言处理
- **GAN** - 生成对抗网络

💪 **优势特点**：
- 自动特征提取
- 处理复杂非线性关系
- 在大数据上表现优异
- 端到端学习能力

🚀 **应用领域**：
- 计算机视觉、自然语言处理
- 语音识别、机器翻译
- 游戏AI、自动驾驶

需要了解具体的深度学习算法吗？"""
    
    def _get_lstm_concept_response(self) -> str:
        """LSTM概念回答"""
        return """🔄 **LSTM (长短期记忆网络)**

LSTM是一种特殊的循环神经网络，专门设计用来解决长序列依赖问题。

🧠 **核心机制**：
- **遗忘门** - 决定丢弃哪些信息
- **输入门** - 决定存储哪些新信息
- **输出门** - 控制输出哪些信息
- **细胞状态** - 长期记忆载体

📈 **适用场景**：
- **时间序列预测** - 股价、天气、销量
- **自然语言处理** - 文本生成、翻译
- **语音识别** - 语音到文本转换
- **异常检测** - 设备故障预警

⚙️ **关键参数**：
- **序列长度** - 输入时间步数
- **隐藏单元数** - 网络容量
- **学习率** - 训练速度控制
- **Dropout** - 防止过拟合

🎯 **优势**：
- 处理长序列依赖
- 避免梯度消失问题
- 记忆重要信息
- 忘记无关信息

想开始LSTM训练任务吗？"""
    
    def _get_cnn_concept_response(self) -> str:
        """CNN概念回答"""
        return """🖼️ **CNN (卷积神经网络)**

CNN是专门用于处理网格状数据（如图像）的深度学习架构。

🔍 **核心组件**：
- **卷积层** - 特征提取，检测边缘、纹理
- **池化层** - 降维，减少计算量
- **全连接层** - 分类决策
- **激活函数** - 引入非线性

🎯 **工作原理**：
1. **特征提取** - 卷积核扫描图像
2. **特征映射** - 生成特征图
3. **池化操作** - 降低分辨率
4. **分类决策** - 全连接层输出

📊 **应用领域**：
- **图像分类** - 识别图像内容
- **目标检测** - 定位物体位置
- **人脸识别** - 身份验证
- **医学影像** - 疾病诊断

⚙️ **关键参数**：
- **卷积核大小** - 特征检测窗口
- **步长** - 卷积移动距离
- **填充** - 边界处理方式
- **通道数** - 特征图数量

🚀 **优势**：
- 平移不变性
- 参数共享
- 局部连接
- 层次特征学习

需要配置CNN训练任务吗？"""
    
    def _get_parameter_concept_response(self) -> str:
        """参数概念回答"""
        return self._get_parameter_question_response("")["msg"]
    
    def _get_training_concept_response(self) -> str:
        """训练概念回答"""
        return """🏋️ **模型训练 (Model Training)**

模型训练是机器学习的核心过程，通过数据让算法学习规律和模式。

📚 **训练流程**：
1. **数据准备** - 清洗、标注、分割数据
2. **模型初始化** - 设置网络结构和参数
3. **前向传播** - 计算预测结果
4. **损失计算** - 评估预测误差
5. **反向传播** - 更新模型参数
6. **迭代优化** - 重复直到收敛

📊 **关键概念**：
- **训练集** - 用于学习的数据
- **验证集** - 用于调参的数据
- **测试集** - 用于最终评估
- **过拟合** - 模型过度学习训练数据
- **欠拟合** - 模型学习不充分

⚙️ **训练参数**：
- **学习率** - 参数更新步长
- **批次大小** - 每次训练样本数
- **训练轮数** - 完整数据集遍历次数
- **优化器** - 参数更新策略

📈 **监控指标**：
- **损失函数** - 训练误差
- **准确率** - 预测正确率
- **收敛速度** - 学习效率
- **泛化能力** - 新数据表现

想开始训练您的模型吗？"""
    
    def _get_prediction_concept_response(self) -> str:
        """预测概念回答"""
        return """🔮 **预测分析 (Prediction Analysis)**

预测分析使用历史数据和统计算法来预测未来事件或趋势。

🎯 **预测类型**：
- **时间序列预测** - 基于时间的数据预测
- **分类预测** - 预测类别标签
- **回归预测** - 预测连续数值
- **异常预测** - 识别异常模式

📊 **常用算法**：
- **LSTM** - 长序列时间预测
- **ARIMA** - 传统时间序列
- **随机森林** - 集成学习预测
- **支持向量机** - 非线性预测

🔍 **应用场景**：
- **金融预测** - 股价、汇率预测
- **销售预测** - 需求量、收入预测
- **天气预测** - 温度、降雨预测
- **设备预测** - 故障、维护预测

📈 **评估指标**：
- **MAE** - 平均绝对误差
- **RMSE** - 均方根误差
- **MAPE** - 平均绝对百分比误差
- **R²** - 决定系数

💡 **成功要素**：
- 高质量历史数据
- 合适的算法选择
- 正确的特征工程
- 持续的模型更新

需要建立预测模型吗？"""
    
    def _get_data_concept_response(self) -> str:
        """数据概念回答"""
        return """📊 **数据处理 (Data Processing)**

数据是机器学习的基础，高质量的数据决定了模型的上限。

🗂️ **数据类型**：
- **结构化数据** - 表格、数据库数据
- **非结构化数据** - 文本、图像、音频
- **时间序列数据** - 按时间排序的数据
- **流数据** - 实时产生的数据

🔧 **数据预处理**：
- **数据清洗** - 处理缺失值、异常值
- **数据转换** - 标准化、归一化
- **特征工程** - 创建新特征
- **数据增强** - 扩充训练数据

📈 **数据质量**：
- **完整性** - 数据是否完整
- **准确性** - 数据是否正确
- **一致性** - 数据格式统一
- **时效性** - 数据是否最新

🎯 **数据分割**：
- **训练集 (70%)** - 模型学习
- **验证集 (15%)** - 参数调优
- **测试集 (15%)** - 性能评估

💡 **最佳实践**：
- 充分了解数据来源
- 进行探索性数据分析
- 处理数据不平衡问题
- 保护数据隐私安全

需要帮助处理您的数据吗？"""
    
    def _get_general_concept_response(self) -> str:
        """通用概念回答"""
        return """🤖 **人工智能与机器学习**

我可以为您解释各种AI和机器学习概念！

📚 **核心概念**：
- **人工智能** - 让机器具备智能的技术
- **机器学习** - 从数据中自动学习的方法
- **深度学习** - 使用多层神经网络的技术
- **算法** - 解决问题的计算方法

🧠 **学习类型**：
- **监督学习** - 有标签数据学习
- **无监督学习** - 无标签数据学习
- **强化学习** - 通过奖励学习

🔧 **常用算法**：
- **神经网络** - 模拟大脑神经元
- **决策树** - 基于规则的决策
- **支持向量机** - 寻找最优分界面
- **集成学习** - 多个模型组合

💡 **想了解什么**：
- 具体算法原理？
- 应用场景介绍？
- 参数配置方法？
- 实际项目案例？

请告诉我您感兴趣的具体概念，我会详细为您解释！"""


# 全局兜底回答管理器实例
fallback_manager = FallbackResponseManager()
