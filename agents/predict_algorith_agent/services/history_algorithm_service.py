"""
历史算法检索服务
负责调用三方系统API获取历史算法列表，并提供相关数据结构
"""

import requests
import json
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class HistoryAlgorithm(BaseModel):
    """历史算法数据模型"""
    algorithm_id: str = Field(..., description="算法ID")
    algorithm_name: str = Field(..., description="算法名称")
    algorithm_type: str = Field(..., description="算法类型，如LSTM、CNN、RandomForest等")
    description: str = Field(..., description="算法描述")
    data_type: str = Field(..., description="适用数据类型，如时序数据、图像数据等")
    use_case: str = Field(..., description="使用场景，如预测性维护、异常检测等")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="算法参数配置")
    performance_metrics: Dict[str, float] = Field(default_factory=dict, description="性能指标")
    created_by: str = Field(..., description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")
    usage_count: int = Field(default=0, description="使用次数")
    tags: List[str] = Field(default_factory=list, description="标签")


class HistoryAlgorithmListResponse(BaseModel):
    """历史算法列表响应"""
    algorithms: List[HistoryAlgorithm] = Field(..., description="历史算法列表")
    total_count: int = Field(..., description="总算法数量")
    fetch_time_ms: float = Field(..., description="获取耗时（毫秒）")


class HistoryAlgorithmService:
    """历史算法检索服务"""
    
    def __init__(self, api_base_url: str = None, api_key: str = None):
        """
        初始化历史算法检索服务

        Args:
            api_base_url: 三方系统API基础URL
            api_key: API密钥
        """
        # 导入配置 - 使用算法平台配置
        try:
            from agents.config import ALGORITHM_PLATFORM_BASE_URL
            self.api_base_url = api_base_url or ALGORITHM_PLATFORM_BASE_URL
            self.api_key = api_key
        except ImportError:
            self.api_base_url = api_base_url or "http://***********:8081"  # 算法平台地址
            self.api_key = api_key

        self.timeout = 30  # 请求超时时间

    async def get_history_algorithms(self, project_id: str) -> HistoryAlgorithmListResponse:
        """
        获取历史算法列表 - 使用真实API，项目ID必传

        Args:
            project_id: 项目ID（必传）

        Returns:
            历史算法列表

        Raises:
            AlgorithmPlatformError: API调用失败时抛出异常，不使用Mock数据
        """
        try:
            # 调用算法平台API获取真实算法列表
            from .algorithm_platform_service import AlgorithmPlatformService
            platform_service = AlgorithmPlatformService()
            algorithms_response = await platform_service.get_all_algorithms()

            # 转换为HistoryAlgorithm格式
            history_algorithms = self._convert_to_history_algorithms(algorithms_response.algorithm_name, project_id)

            return HistoryAlgorithmListResponse(
                algorithms=history_algorithms,
                total_count=len(history_algorithms),
                fetch_time_ms=50.0
            )
        except Exception as e:
            logger.error(f"算法平台API调用失败: {e}")
            # 直接抛出异常，不使用Mock数据
            from ..models.algorithm_platform_models import AlgorithmPlatformError
            raise AlgorithmPlatformError(f"无法获取算法列表: {e}")
    
    async def _get_algorithms_real_api(self) -> HistoryAlgorithmListResponse:
        """
        调用真实的三方系统API获取历史算法列表

        Returns:
            历史算法列表
        """
        import asyncio
        import aiohttp
        import time

        start_time = time.time()

        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        url = f"{self.api_base_url}/algorithms"

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(url, headers=headers) as response:
                    response.raise_for_status()
                    data = await response.json()

                    # 计算请求耗时
                    fetch_time_ms = (time.time() - start_time) * 1000

                    # 验证响应数据格式
                    if isinstance(data, dict):
                        # 如果返回的是标准格式
                        if 'algorithms' in data:
                            return HistoryAlgorithmListResponse(**data)
                        else:
                            # 如果直接返回算法列表
                            algorithms = [HistoryAlgorithm(**algo) for algo in data.get('data', data)]
                            return HistoryAlgorithmListResponse(
                                algorithms=algorithms,
                                total_count=len(algorithms),
                                fetch_time_ms=fetch_time_ms
                            )
                    elif isinstance(data, list):
                        # 如果直接返回算法列表数组
                        algorithms = [HistoryAlgorithm(**algo) for algo in data]
                        return HistoryAlgorithmListResponse(
                            algorithms=algorithms,
                            total_count=len(algorithms),
                            fetch_time_ms=fetch_time_ms
                        )
                    else:
                        raise ValueError(f"不支持的响应格式: {type(data)}")

        except aiohttp.ClientError as e:
            logger.error(f"调用三方API失败: {e}")
            raise Exception(f"三方系统API调用失败: {str(e)}")
        except Exception as e:
            logger.error(f"处理API响应失败: {e}")
            raise Exception(f"处理API响应失败: {str(e)}")
    
    def _get_algorithms_mock(self) -> HistoryAlgorithmListResponse:
        """
        Mock数据实现，用于开发和测试

        Returns:
            Mock历史算法列表
        """
        # Mock历史算法数据
        mock_algorithms = [
            HistoryAlgorithm(
                algorithm_id="algo_001",
                algorithm_name="设备预测性维护LSTM模型",
                algorithm_type="LSTM",
                description="基于传感器数据的设备故障预测模型，适用于温度、振动等时序数据分析",
                data_type="时序数据",
                use_case="预测性维护",
                parameters={
                    "sequence_length": 60,
                    "hidden_units": 128,
                    "learning_rate": 0.001,
                    "batch_size": 32,
                    "epochs": 100,
                    "dropout": 0.2
                },
                performance_metrics={
                    "accuracy": 0.92,
                    "precision": 0.89,
                    "recall": 0.94,
                    "f1_score": 0.91
                },
                created_by="张工程师",
                created_at=datetime(2024, 1, 15, 10, 30),
                last_used=datetime(2024, 12, 20, 14, 20),
                usage_count=15,
                tags=["预测性维护", "LSTM", "传感器数据", "故障预测"]
            ),
            HistoryAlgorithm(
                algorithm_id="algo_002", 
                algorithm_name="温度异常检测CNN模型",
                algorithm_type="CNN",
                description="基于卷积神经网络的温度异常检测模型，适用于工业设备温度监控",
                data_type="时序数据",
                use_case="异常检测",
                parameters={
                    "input_shape": [100, 1],
                    "num_filters": 64,
                    "kernel_size": 3,
                    "pool_size": 2,
                    "dense_units": 50,
                    "learning_rate": 0.0005,
                    "batch_size": 64,
                    "epochs": 80
                },
                performance_metrics={
                    "accuracy": 0.88,
                    "precision": 0.85,
                    "recall": 0.90,
                    "f1_score": 0.87
                },
                created_by="李工程师",
                created_at=datetime(2024, 2, 10, 9, 15),
                last_used=datetime(2024, 12, 18, 16, 45),
                usage_count=8,
                tags=["异常检测", "CNN", "温度监控", "工业设备"]
            ),
            HistoryAlgorithm(
                algorithm_id="algo_003",
                algorithm_name="振动信号分析LSTM模型", 
                algorithm_type="LSTM",
                description="专门用于分析设备振动信号的LSTM模型，可预测设备健康状态",
                data_type="时序数据",
                use_case="健康状态监控",
                parameters={
                    "sequence_length": 120,
                    "hidden_units": 256,
                    "learning_rate": 0.002,
                    "batch_size": 16,
                    "epochs": 150,
                    "dropout": 0.3
                },
                performance_metrics={
                    "accuracy": 0.95,
                    "precision": 0.93,
                    "recall": 0.96,
                    "f1_score": 0.94
                },
                created_by="王工程师",
                created_at=datetime(2024, 3, 5, 11, 20),
                last_used=datetime(2024, 12, 22, 10, 30),
                usage_count=22,
                tags=["振动分析", "LSTM", "健康监控", "预测性维护"]
            )
        ]
        
        # 直接返回所有Mock算法（简化版实现）

        return HistoryAlgorithmListResponse(
            algorithms=mock_algorithms,
            total_count=len(mock_algorithms),
            fetch_time_ms=20.0  # Mock获取时间
        )

    def _convert_to_history_algorithms(self, algorithm_data: List[List[str]], current_project_id: str) -> List[HistoryAlgorithm]:
        """
        将算法平台数据转换为历史算法格式

        Args:
            algorithm_data: 算法平台返回的算法数据
            current_project_id: 当前项目ID

        Returns:
            转换后的历史算法列表
        """
        history_algorithms = []
        for algo_info in algorithm_data:
            algorithm_name, algo_project_id = algo_info
            # 只返回当前项目的算法
            if algo_project_id == current_project_id:
                history_algorithm = HistoryAlgorithm(
                    algorithm_id=f"platform_{hash(algorithm_name)}",
                    algorithm_name=algorithm_name,
                    algorithm_type="LSTM",  # 默认类型，可通过其他API获取详细信息
                    description=f"来自算法平台的算法：{algorithm_name}",
                    data_type="时序数据",
                    use_case="预测分析",
                    parameters={},  # 可通过get_algorithm_log获取详细参数
                    performance_metrics={},
                    created_by="算法平台",
                    created_at=datetime.now(),
                    last_used=datetime.now(),
                    usage_count=0,
                    tags=["算法平台", "LSTM"],
                    project_id=algo_project_id
                )
                history_algorithms.append(history_algorithm)
        return history_algorithms


# 创建全局服务实例
history_algorithm_service = HistoryAlgorithmService()
    



# 全局历史算法服务实例
history_algorithm_service = HistoryAlgorithmService()
