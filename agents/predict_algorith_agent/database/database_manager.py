#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库管理器
提供对话管理、任务管理等数据库操作的统一接口
"""

import pymysql
import json
import uuid
import logging
import time
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from contextlib import contextmanager

from agents.config import (
    MYSQL_HOST, MYSQL_PORT, MYSQL_USER,
    MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET
)

# 设置日志
logger = logging.getLogger(__name__)

class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        # 基础连接配置
        self.connection_config = {
            'host': MYSQL_HOST,
            'port': MYSQL_PORT,
            'user': MYSQL_USER,
            'password': MYSQL_PASSWORD,
            'database': MYSQL_DB,
            'charset': MYSQL_CHARSET,
            'autocommit': True,
            # SSL和安全配置
            'ssl_disabled': True,  # 禁用SSL以避免认证问题
            'ssl_verify_cert': False,
            'ssl_verify_identity': False,
            # 连接超时设置
            'connect_timeout': 15,
            'read_timeout': 30,
            'write_timeout': 30,
            # 连接池设置
            'max_allowed_packet': 16777216,  # 16MB
            # 字符集设置
            'use_unicode': True,
            'sql_mode': 'TRADITIONAL',
        }

        # 备用连接配置（不指定数据库）
        self.fallback_config = {
            'host': MYSQL_HOST,
            'port': MYSQL_PORT,
            'user': MYSQL_USER,
            'password': MYSQL_PASSWORD,
            'charset': MYSQL_CHARSET,
            'ssl_disabled': True,
            'connect_timeout': 15,
        }

        # 本地连接配置（使用localhost）
        self.local_config = {
            'host': 'localhost',
            'port': MYSQL_PORT,
            'user': MYSQL_USER,
            'password': MYSQL_PASSWORD,
            'database': MYSQL_DB,
            'charset': MYSQL_CHARSET,
            'ssl_disabled': True,
            'connect_timeout': 15,
        }
    
    @contextmanager
    def get_connection(self, max_retries=3, retry_delay=2):
        """获取数据库连接的上下文管理器，支持重试和备用配置"""
        connection = None
        last_exception = None

        # 尝试多种配置：主配置 -> 本地配置 -> 备用配置
        configs_to_try = [
            ("主配置", self.connection_config),
            ("本地配置", self.local_config),
            ("备用配置(无数据库)", self.fallback_config)
        ]

        for config_name, config in configs_to_try:
            for attempt in range(max_retries):
                connection = None
                try:
                    logger.debug(f"尝试{config_name} (第{attempt + 1}次)")
                    connection = pymysql.connect(**config)

                    # 如果使用备用配置成功连接，尝试选择数据库
                    if config_name == "备用配置(无数据库)":
                        cursor = connection.cursor()
                        cursor.execute(f"USE {MYSQL_DB}")
                        cursor.close()
                        logger.info(f"使用备用配置成功连接并选择数据库: {MYSQL_DB}")
                    else:
                        logger.debug(f"使用{config_name}成功连接")

                    # 成功连接，yield并返回
                    yield connection
                    return  # 成功连接，退出所有循环

                except pymysql.err.OperationalError as e:
                    last_exception = e
                    error_code = e.args[0] if e.args else 0
                    error_msg = e.args[1] if len(e.args) > 1 else str(e)

                    if attempt < max_retries - 1:  # 不是最后一次尝试
                        logger.warning(f"{config_name}连接失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        # 当前配置的最后一次尝试失败，记录错误但继续尝试下一个配置
                        logger.error(f"{config_name}所有尝试都失败: {error_msg}")
                        if error_code == 2059:  # Authentication plugin error
                            logger.error("MySQL认证插件错误，建议安装cryptography包或修改认证方式")
                        elif error_code == 1045:  # Access denied
                            logger.error("MySQL访问被拒绝，请检查用户名和密码")
                        elif error_code == 2003:  # Can't connect to server
                            logger.error("无法连接到MySQL服务器，请检查服务器地址和端口")
                        elif error_code == 2013:  # Lost connection
                            logger.error("MySQL连接中断，请检查服务器状态和网络连接")
                        break  # 跳出当前配置的重试循环，尝试下一个配置

                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"{config_name}连接异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        logger.error(f"{config_name}连接异常: {e}")
                        break  # 跳出当前配置的重试循环，尝试下一个配置

                finally:
                    # 只有在连接失败时才关闭连接，成功时由上下文管理器处理
                    if connection and last_exception:
                        try:
                            connection.close()
                        except:
                            pass

        # 如果所有配置都失败了
        logger.error("所有数据库连接配置都失败了")
        if last_exception:
            raise last_exception
        else:
            raise Exception("无法建立数据库连接")
    
    def generate_id(self, prefix: str = "") -> str:
        """生成唯一ID"""
        return f"{prefix}{uuid.uuid4().hex[:16]}" if prefix else uuid.uuid4().hex[:16]

    def generate_session_id(self) -> str:
        """生成32位Session ID - 纯UUID，不带前缀"""
        return uuid.uuid4().hex
    
    # =====================================================
    # 对话管理相关方法
    # =====================================================
    
    def create_conversation(self, user_id: str, session_id: str = None,
                          title: str = "新建算法训练任务",
                          conversation_type: str = "training") -> str:
        """
        创建新对话

        Args:
            user_id: 用户ID
            session_id: WebSocket会话ID
            title: 对话标题
            conversation_type: 对话类型

        Returns:
            str: 对话ID
        """
        conversation_id = self.generate_id("conv_")

        with self.get_connection() as conn:
            cursor = conn.cursor()
            sql = """
                INSERT INTO conversations
                (conversation_id, user_id, session_id, title, conversation_type, status, current_stage)
                VALUES (%s, %s, %s, %s, %s, 'active', 'welcome')
            """
            cursor.execute(sql, (conversation_id, user_id, session_id, title, conversation_type))
            cursor.close()

        return conversation_id

    def create_or_get_conversation(self, session_id: str, user_id: str, title: str = "新建算法训练任务") -> str:
        """
        创建或获取对话记录

        Args:
            session_id: WebSocket会话ID
            user_id: 用户ID
            title: 对话标题

        Returns:
            str: 对话ID
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否已存在活跃对话
            cursor.execute(
                "SELECT conversation_id FROM conversations WHERE session_id = %s AND status = 'active'",
                (session_id,)
            )
            result = cursor.fetchone()

            if result:
                cursor.close()
                return result[0]

            # 创建新对话
            conversation_id = self.generate_id("conv_")
            cursor.execute("""
                INSERT INTO conversations
                (conversation_id, user_id, session_id, title, status, current_stage)
                VALUES (%s, %s, %s, %s, 'active', 'welcome')
            """, (conversation_id, user_id, session_id, title))
            cursor.close()

            return conversation_id
    
    def get_conversations_by_user(self, user_id: str, page: int = 1, size: int = 10) -> Tuple[List[Dict], int]:
        """
        获取用户的对话列表
        
        Args:
            user_id: 用户ID
            page: 页码
            size: 每页大小
            
        Returns:
            Tuple[List[Dict], int]: (对话列表, 总数)
        """
        offset = (page - 1) * size
        
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 查询总数
            count_sql = "SELECT COUNT(*) as total FROM conversations WHERE user_id = %s"
            cursor.execute(count_sql, (user_id,))
            total = cursor.fetchone()['total']
            
            # 查询列表
            list_sql = """
                SELECT conversation_id, title, conversation_type, status, current_stage, 
                       progress, algorithm_type, created_at, updated_at
                FROM conversations 
                WHERE user_id = %s 
                ORDER BY updated_at DESC 
                LIMIT %s OFFSET %s
            """
            cursor.execute(list_sql, (user_id, size, offset))
            conversations = cursor.fetchall()
            
            cursor.close()
        
        return conversations, total
    
    def get_conversation_detail(self, conversation_id: str) -> Optional[Dict]:
        """
        获取对话详情
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            Optional[Dict]: 对话详情
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            sql = """
                SELECT * FROM conversations WHERE conversation_id = %s
            """
            cursor.execute(sql, (conversation_id,))
            conversation = cursor.fetchone()
            
            cursor.close()
        
        return conversation
    
    def update_conversation(self, conversation_id: str, **kwargs) -> bool:
        """
        更新对话信息
        
        Args:
            conversation_id: 对话ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 是否更新成功
        """
        if not kwargs:
            return True
        
        # 构建更新SQL
        set_clauses = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['title', 'status', 'current_stage', 'progress', 'task_summary', 'algorithm_type']:
                set_clauses.append(f"{key} = %s")
                values.append(value)
        
        if not set_clauses:
            return True
        
        values.append(conversation_id)
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            sql = f"UPDATE conversations SET {', '.join(set_clauses)} WHERE conversation_id = %s"
            cursor.execute(sql, values)
            cursor.close()
        
        return True
    
    def delete_conversation(self, conversation_id: str) -> bool:
        """
        删除对话（级联删除相关数据）
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            bool: 是否删除成功
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            sql = "DELETE FROM conversations WHERE conversation_id = %s"
            cursor.execute(sql, (conversation_id,))
            cursor.close()
        
        return True
    
    # =====================================================
    # 对话消息相关方法
    # =====================================================
    
    def get_next_message_sequence(self, conversation_id: str) -> int:
        """
        获取下一个消息序号

        Args:
            conversation_id: 对话ID

        Returns:
            int: 下一个消息序号
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COALESCE(MAX(message_sequence), 0) + 1
                FROM conversation_messages
                WHERE conversation_id = %s
            """, (conversation_id,))
            result = cursor.fetchone()
            cursor.close()
            return result[0] if result else 1

    def add_conversation_message(self, conversation_id: str, message_type: str,
                               content: str, message_data: Dict = None,
                               interaction_type: str = None, key_info: Dict = None,
                               user_id: str = None) -> bool:
        """
        添加对话消息

        Args:
            conversation_id: 对话ID
            message_type: 消息类型 (user/assistant/system)
            content: 消息内容
            message_data: 消息附加数据
            interaction_type: 交互类型
            key_info: 关键信息提取

        Returns:
            bool: 是否添加成功
        """
        # 获取消息序号
        sequence = self.get_next_message_sequence(conversation_id)

        # 计算token数量（简单估算）
        token_count = len(content.split()) * 1.3  # 粗略估算

        with self.get_connection() as conn:
            cursor = conn.cursor()
            sql = """
                INSERT INTO conversation_messages
                (conversation_id, message_type, content, message_data, interaction_type,
                 message_sequence, key_info, token_count, user_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                conversation_id, message_type, content,
                json.dumps(message_data, cls=DateTimeEncoder) if message_data else None,
                interaction_type, sequence,
                json.dumps(key_info, cls=DateTimeEncoder) if key_info else None,
                int(token_count), user_id or ""
            ))
            cursor.close()

        # 更新对话最后消息时间
        self.update_conversation_last_activity(conversation_id)

        return True

    def update_conversation_last_activity(self, conversation_id: str):
        """
        更新对话最后活动时间

        Args:
            conversation_id: 对话ID
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE conversations
                SET last_message_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE conversation_id = %s
            """, (conversation_id,))
            cursor.close()
    
    def get_conversation_messages(self, conversation_id: str, limit: int = 100) -> List[Dict]:
        """
        获取对话消息列表
        
        Args:
            conversation_id: 对话ID
            limit: 消息数量限制
            
        Returns:
            List[Dict]: 消息列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            sql = """
                SELECT * FROM conversation_messages 
                WHERE conversation_id = %s 
                ORDER BY timestamp ASC 
                LIMIT %s
            """
            cursor.execute(sql, (conversation_id, limit))
            messages = cursor.fetchall()
            cursor.close()
        
        # 解析JSON数据
        for message in messages:
            if message['message_data']:
                try:
                    message['message_data'] = json.loads(message['message_data'])
                except:
                    message['message_data'] = None
        
        return messages
    
    # =====================================================
    # 算法任务相关方法
    # =====================================================
    
    def create_algorithm_task(self, conversation_id: str, user_id: str, 
                            algorithm_type: str, task_name: str = None,
                            task_description: str = None) -> str:
        """
        创建算法任务
        
        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            algorithm_type: 算法类型
            task_name: 任务名称
            task_description: 任务描述
            
        Returns:
            str: 任务ID
        """
        task_id = self.generate_id("task_")
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            sql = """
                INSERT INTO algorithm_tasks 
                (task_id, conversation_id, user_id, task_name, algorithm_type, task_description, status)
                VALUES (%s, %s, %s, %s, %s, %s, 'created')
            """
            cursor.execute(sql, (task_id, conversation_id, user_id, task_name, algorithm_type, task_description))
            cursor.close()
        
        return task_id
    
    def get_task_by_id(self, task_id: str) -> Optional[Dict]:
        """
        根据任务ID获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务信息
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            sql = "SELECT * FROM algorithm_tasks WHERE task_id = %s"
            cursor.execute(sql, (task_id,))
            task = cursor.fetchone()
            cursor.close()
        
        return task
    
    def update_task_status(self, task_id: str, status: str, progress: float = None) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            progress: 进度百分比
            
        Returns:
            bool: 是否更新成功
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if progress is not None:
                sql = "UPDATE algorithm_tasks SET status = %s, training_progress = %s WHERE task_id = %s"
                cursor.execute(sql, (status, progress, task_id))
            else:
                sql = "UPDATE algorithm_tasks SET status = %s WHERE task_id = %s"
                cursor.execute(sql, (status, task_id))
            cursor.close()
        
        return True
    
    # =====================================================
    # 会话状态兼容方法（保持原有功能）
    # =====================================================
    
    def save_session_state(self, user_id: str, session_id: str, 
                          conversation_id: str, task_id: str, state_data: Dict) -> bool:
        """
        保存会话状态（兼容原有接口）
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            conversation_id: 对话ID
            task_id: 任务ID
            state_data: 状态数据
            
        Returns:
            bool: 是否保存成功
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                sql = """
                    INSERT INTO session_states
                    (user_id, session_id, conversation_id, task_id, state_data)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    conversation_id = VALUES(conversation_id),
                    task_id = VALUES(task_id),
                    state_data = VALUES(state_data),
                    updated_at = CURRENT_TIMESTAMP
                """
                cursor.execute(sql, (user_id, session_id, conversation_id, task_id, json.dumps(state_data, cls=DateTimeEncoder)))
                conn.commit()  # 重要：提交事务
                logger.info(f"Session state saved successfully: user_id={user_id}, session_id={session_id}")
                return True
            except Exception as e:
                conn.rollback()
                logger.error(f"Failed to save session state: {e}")
                return False
            finally:
                cursor.close()
    
    def load_session_state(self, user_id: str, session_id: str) -> Tuple[Optional[str], Optional[Dict]]:
        """
        加载会话状态（兼容原有接口）
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            Tuple[Optional[str], Optional[Dict]]: (任务ID, 状态数据)
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            sql = """
                SELECT task_id, state_data FROM session_states 
                WHERE user_id = %s AND session_id = %s
            """
            cursor.execute(sql, (user_id, session_id))
            result = cursor.fetchone()
            cursor.close()
        
        if result:
            try:
                state_data = json.loads(result['state_data'])
                return result['task_id'], state_data
            except:
                return result['task_id'], None
        
        return None, None

    def get_user_conversations(self, user_id: str, limit: int = 20, offset: int = 0) -> List[Dict]:
        """
        获取用户历史对话列表

        Args:
            user_id: 用户ID
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            List[Dict]: 对话列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT c.conversation_id, c.title, c.status, c.current_stage,
                       c.created_at, c.updated_at, c.last_message_at,
                       c.algorithm_type, c.progress,
                       (SELECT COUNT(*) FROM conversation_messages
                        WHERE conversation_id = c.conversation_id) as message_count,
                       (SELECT content FROM conversation_messages
                        WHERE conversation_id = c.conversation_id
                        AND message_type = 'user'
                        ORDER BY message_sequence DESC LIMIT 1) as last_user_message
                FROM conversations c
                WHERE c.user_id = %s
                ORDER BY COALESCE(c.last_message_at, c.updated_at) DESC
                LIMIT %s OFFSET %s
            """, (user_id, limit, offset))

            result = cursor.fetchall()
            cursor.close()
            return result

    def get_conversation_messages_with_context(self, conversation_id: str, limit: int = 50) -> List[Dict]:
        """
        获取对话消息（包含上下文信息）

        Args:
            conversation_id: 对话ID
            limit: 消息数量限制

        Returns:
            List[Dict]: 消息列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT id, conversation_id, message_type, content, message_data,
                       interaction_type, message_sequence, key_info, token_count,
                       timestamp
                FROM conversation_messages
                WHERE conversation_id = %s
                ORDER BY message_sequence ASC
                LIMIT %s
            """, (conversation_id, limit))

            result = cursor.fetchall()
            cursor.close()

            # 解析JSON字段
            for message in result:
                if message['message_data']:
                    try:
                        message['message_data'] = json.loads(message['message_data'])
                    except:
                        message['message_data'] = None

                if message['key_info']:
                    try:
                        message['key_info'] = json.loads(message['key_info'])
                    except:
                        message['key_info'] = None

            return result

    # =====================================================
    # 上下文摘要管理相关方法
    # =====================================================

    def create_context_summary(self, conversation_id: str, summary_content: str,
                             key_points: Dict = None, message_count: int = 0) -> str:
        """
        创建对话摘要

        Args:
            conversation_id: 对话ID
            summary_content: 摘要内容
            key_points: 关键要点
            message_count: 摘要的消息数量

        Returns:
            str: 摘要ID
        """
        # 计算token数量
        token_count = len(summary_content.split()) * 1.3

        with self.get_connection() as conn:
            cursor = conn.cursor()
            sql = """
                INSERT INTO context_summaries
                (conversation_id, summary_content, key_points, message_count, token_count)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                conversation_id, summary_content,
                json.dumps(key_points, cls=DateTimeEncoder) if key_points else None,
                message_count, int(token_count)
            ))
            summary_id = cursor.lastrowid
            cursor.close()

        return str(summary_id)

    def get_latest_context_summary(self, conversation_id: str) -> Optional[Dict]:
        """
        获取最新的对话摘要

        Args:
            conversation_id: 对话ID

        Returns:
            Optional[Dict]: 摘要信息
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT id, conversation_id, summary_content, key_points,
                       message_count, token_count, summary_time
                FROM context_summaries
                WHERE conversation_id = %s
                ORDER BY summary_time DESC
                LIMIT 1
            """, (conversation_id,))

            result = cursor.fetchone()
            cursor.close()

            if result and result['key_points']:
                try:
                    result['key_points'] = json.loads(result['key_points'])
                except:
                    result['key_points'] = None

            return result

    def update_conversation_context_tokens(self, conversation_id: str, token_count: int):
        """
        更新对话的上下文token数量

        Args:
            conversation_id: 对话ID
            token_count: token数量
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE conversations
                SET context_token_count = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE conversation_id = %s
            """, (token_count, conversation_id))
            cursor.close()
