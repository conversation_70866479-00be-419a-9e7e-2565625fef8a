#!/usr/bin/env python3
"""
数据库验证脚本 - 查询实际数据来证明系统实现流程
"""

import pymysql
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET

def get_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset=MYSQL_CHARSET,
        autocommit=True
    )

def print_section_header(title: str):
    """打印章节标题"""
    print("\n" + "=" * 80)
    print(f"🔍 {title}")
    print("=" * 80)

def query_conversations_overview():
    """查询对话概览数据"""
    print_section_header("对话管理表 (conversations) 数据概览")
    
    with get_connection() as conn:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查询对话总数和状态分布
        cursor.execute("""
            SELECT 
                COUNT(*) as total_conversations,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_conversations,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_conversations,
                COUNT(CASE WHEN conversation_type = 'training' THEN 1 END) as training_conversations
            FROM conversations
        """)
        
        overview = cursor.fetchone()
        print(f"📊 对话统计:")
        print(f"   总对话数: {overview['total_conversations']}")
        print(f"   活跃对话: {overview['active_conversations']}")
        print(f"   已完成对话: {overview['completed_conversations']}")
        print(f"   训练类型对话: {overview['training_conversations']}")
        
        # 查询最近的对话记录
        cursor.execute("""
            SELECT conversation_id, user_id, title, status, current_stage, 
                   algorithm_type, progress, created_at, updated_at, last_message_at
            FROM conversations 
            ORDER BY updated_at DESC 
            LIMIT 5
        """)
        
        recent_conversations = cursor.fetchall()
        print(f"\n📋 最近5个对话:")
        for i, conv in enumerate(recent_conversations, 1):
            print(f"\n{i}. 对话ID: {conv['conversation_id']}")
            print(f"   用户ID: {conv['user_id']}")
            print(f"   标题: {conv['title']}")
            print(f"   状态: {conv['status']}")
            print(f"   当前阶段: {conv['current_stage']}")
            print(f"   算法类型: {conv['algorithm_type']}")
            print(f"   进度: {conv['progress']}%")
            print(f"   创建时间: {conv['created_at']}")
            print(f"   最后消息时间: {conv['last_message_at']}")
        
        cursor.close()
        return recent_conversations

def query_conversation_messages(conversation_id: str = None, limit: int = 10):
    """查询对话消息详情"""
    print_section_header("对话消息记录表 (conversation_messages) 数据详情")
    
    with get_connection() as conn:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        if conversation_id:
            # 查询特定对话的消息
            cursor.execute("""
                SELECT id, conversation_id, message_type, content, message_data,
                       interaction_type, message_sequence, token_count, timestamp
                FROM conversation_messages
                WHERE conversation_id = %s
                ORDER BY message_sequence ASC
                LIMIT %s
            """, (conversation_id, limit))
            
            messages = cursor.fetchall()
            print(f"📨 对话 {conversation_id} 的消息记录 (共{len(messages)}条):")
            
        else:
            # 查询最近的消息
            cursor.execute("""
                SELECT id, conversation_id, message_type, content, message_data,
                       interaction_type, message_sequence, token_count, timestamp
                FROM conversation_messages
                ORDER BY timestamp DESC
                LIMIT %s
            """, (limit,))
            
            messages = cursor.fetchall()
            print(f"📨 最近{limit}条消息记录:")
        
        for i, msg in enumerate(messages, 1):
            print(f"\n{i}. 消息ID: {msg['id']}")
            print(f"   对话ID: {msg['conversation_id']}")
            print(f"   消息类型: {msg['message_type']}")
            print(f"   交互类型: {msg['interaction_type']}")
            print(f"   消息序号: {msg['message_sequence']}")
            print(f"   Token数量: {msg['token_count']}")
            print(f"   时间戳: {msg['timestamp']}")
            print(f"   内容: {msg['content'][:100]}{'...' if len(msg['content']) > 100 else ''}")
            
            # 解析message_data
            if msg['message_data']:
                try:
                    message_data = json.loads(msg['message_data'])
                    print(f"   附加数据: {json.dumps(message_data, ensure_ascii=False, indent=6)}")
                except:
                    print(f"   附加数据: {msg['message_data']}")
        
        cursor.close()
        return messages

def demonstrate_multi_turn_conversation():
    """演示多轮对话的完整流程"""
    print_section_header("多轮对话流程演示")
    
    with get_connection() as conn:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查找一个有多条消息的对话
        cursor.execute("""
            SELECT conversation_id, COUNT(*) as message_count
            FROM conversation_messages
            GROUP BY conversation_id
            HAVING COUNT(*) >= 3
            ORDER BY COUNT(*) DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ 未找到包含多条消息的对话")
            return
        
        conversation_id = result['conversation_id']
        message_count = result['message_count']
        
        print(f"🎯 选择对话: {conversation_id} (共{message_count}条消息)")
        
        # 获取完整的对话流程
        cursor.execute("""
            SELECT message_type, content, interaction_type, message_sequence, 
                   timestamp, message_data
            FROM conversation_messages
            WHERE conversation_id = %s
            ORDER BY message_sequence ASC
        """, (conversation_id,))
        
        messages = cursor.fetchall()
        
        print(f"\n📝 完整对话流程:")
        for i, msg in enumerate(messages, 1):
            role = "👤 用户" if msg['message_type'] == 'user' else "🤖 智能体"
            print(f"\n{i}. {role} [{msg['interaction_type']}] (序号: {msg['message_sequence']})")
            print(f"   时间: {msg['timestamp']}")
            print(f"   内容: {msg['content']}")
            
            if msg['message_data'] and msg['message_type'] == 'assistant':
                try:
                    data = json.loads(msg['message_data'])
                    if 'type' in data:
                        print(f"   响应类型: {data['type']}")
                    if 'state' in data:
                        print(f"   状态信息: {json.dumps(data['state'], ensure_ascii=False)[:200]}...")
                except:
                    pass
        
        cursor.close()
        return conversation_id, messages

def demonstrate_context_building(conversation_id: str, n_messages: int = 5):
    """演示前N条消息的上下文构建"""
    print_section_header(f"前{n_messages}条消息上下文构建演示")
    
    with get_connection() as conn:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 获取最后N条消息
        cursor.execute("""
            SELECT * FROM (
                SELECT message_type, content, interaction_type, message_sequence, 
                       timestamp, message_data, key_info
                FROM conversation_messages
                WHERE conversation_id = %s
                ORDER BY message_sequence DESC
                LIMIT %s
            ) AS recent_messages
            ORDER BY message_sequence ASC
        """, (conversation_id, n_messages))
        
        recent_messages = cursor.fetchall()
        
        print(f"🔍 对话 {conversation_id} 的最后{len(recent_messages)}条消息:")
        
        # 分析消息模式
        user_messages = []
        assistant_messages = []
        interaction_types = []
        
        for i, msg in enumerate(recent_messages, 1):
            role = "👤 用户" if msg['message_type'] == 'user' else "🤖 智能体"
            print(f"\n{i}. {role} [{msg['interaction_type']}]")
            print(f"   内容: {msg['content']}")
            
            if msg['message_type'] == 'user':
                user_messages.append(msg['content'])
            else:
                assistant_messages.append(msg['content'])
            
            if msg['interaction_type']:
                interaction_types.append(msg['interaction_type'])
        
        # 上下文分析
        print(f"\n📊 上下文分析:")
        print(f"   用户消息数: {len(user_messages)}")
        print(f"   智能体回复数: {len(assistant_messages)}")
        print(f"   交互类型序列: {' -> '.join(interaction_types)}")
        
        # 判断对话阶段
        latest_interaction = interaction_types[-1] if interaction_types else None
        if latest_interaction:
            if 'algorithm' in latest_interaction.lower():
                context_type = "算法选择阶段"
            elif 'parameter' in latest_interaction.lower():
                context_type = "参数配置阶段"
            elif 'welcome' in latest_interaction.lower():
                context_type = "欢迎引导阶段"
            else:
                context_type = "一般对话阶段"
            
            print(f"   推断的对话阶段: {context_type}")
        
        cursor.close()
        return recent_messages

def query_session_states():
    """查询会话状态数据"""
    print_section_header("会话状态管理数据")
    
    with get_connection() as conn:
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 检查session_states表是否存在
        cursor.execute("SHOW TABLES LIKE 'session_states'")
        if not cursor.fetchone():
            print("ℹ️  session_states表不存在，检查agent_session表...")
            
            cursor.execute("SHOW TABLES LIKE 'agent_session'")
            if cursor.fetchone():
                cursor.execute("""
                    SELECT user_id, session_id, task_id, state, 
                           create_time, update_time
                    FROM agent_session 
                    WHERE del_flag = 0
                    ORDER BY update_time DESC 
                    LIMIT 5
                """)
                
                sessions = cursor.fetchall()
                print(f"📊 找到 {len(sessions)} 个会话状态 (agent_session表):")
                
                for i, session in enumerate(sessions, 1):
                    print(f"\n{i}. 用户ID: {session['user_id']}")
                    print(f"   会话ID: {session['session_id']}")
                    print(f"   任务ID: {session['task_id']}")
                    print(f"   创建时间: {session['create_time']}")
                    print(f"   更新时间: {session['update_time']}")
                    
                    # 解析状态数据
                    if session['state']:
                        try:
                            state_data = json.loads(session['state'])
                            print(f"   状态数据: {json.dumps(state_data, ensure_ascii=False, indent=6)}")
                        except:
                            print(f"   状态数据: {session['state'][:200]}...")
            else:
                print("❌ 未找到会话状态相关表")
        
        cursor.close()

def main():
    """主函数 - 执行完整的数据库验证"""
    print("🚀 开始数据库验证，证明系统实现流程...")
    
    try:
        # 1. 查询对话概览
        recent_conversations = query_conversations_overview()
        
        # 2. 查询消息详情
        if recent_conversations:
            # 选择第一个对话查看详细消息
            first_conv_id = recent_conversations[0]['conversation_id']
            query_conversation_messages(first_conv_id, 10)
        else:
            query_conversation_messages(limit=10)
        
        # 3. 演示多轮对话流程
        conv_id, messages = demonstrate_multi_turn_conversation()
        
        # 4. 演示上下文构建
        if conv_id:
            demonstrate_context_building(conv_id, 5)
        
        # 5. 查询会话状态
        query_session_states()
        
        print_section_header("验证完成")
        print("✅ 数据库验证完成！以上数据证明了系统的完整实现流程。")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
