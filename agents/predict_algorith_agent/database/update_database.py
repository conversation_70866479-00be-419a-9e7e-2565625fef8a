#!/usr/bin/env python3
"""
数据库表结构更新脚本
执行扩展的数据库表结构更新
"""

import pymysql
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET

def get_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host=MYSQL_HOST,
        port=MYSQL_PORT,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        charset=MYSQL_CHARSET,
        autocommit=True
    )

def execute_sql_file(sql_file_path):
    """执行SQL文件"""
    print(f"开始执行SQL文件: {sql_file_path}")
    
    try:
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with get_connection() as conn:
            cursor = conn.cursor()
            
            for i, statement in enumerate(sql_statements):
                if statement.upper().startswith('SELECT'):
                    # 跳过SELECT语句（通常是提示信息）
                    continue
                    
                try:
                    print(f"执行语句 {i+1}/{len(sql_statements)}: {statement[:50]}...")
                    cursor.execute(statement)
                    print(f"✅ 语句执行成功")
                except Exception as e:
                    if "already exists" in str(e) or "Duplicate" in str(e):
                        print(f"⚠️  表已存在，跳过: {e}")
                    else:
                        print(f"❌ 语句执行失败: {e}")
                        print(f"失败的语句: {statement}")
            
            cursor.close()
        
        print("✅ SQL文件执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 执行SQL文件失败: {e}")
        return False

def check_table_structure():
    """检查表结构是否正确更新"""
    print("\n检查表结构更新情况...")
    
    tables_to_check = [
        'conversations',
        'conversation_messages', 
        'session_states',
        'context_summaries'
    ]
    
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            
            for table in tables_to_check:
                print(f"\n检查表: {table}")
                cursor.execute(f"DESCRIBE {table}")
                columns = cursor.fetchall()
                
                print(f"表 {table} 的字段:")
                for col in columns:
                    print(f"  - {col[0]} ({col[1]})")
            
            cursor.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 数据库连接成功，MySQL版本: {version[0]}")
            cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("数据库表结构更新脚本")
    print("=" * 60)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        return False
    
    # 2. 执行SQL文件
    sql_file = Path(__file__).parent / "sql" / "database_schema.sql"
    if not sql_file.exists():
        print(f"❌ SQL文件不存在: {sql_file}")
        return False
    
    if not execute_sql_file(sql_file):
        return False
    
    # 3. 检查表结构
    if not check_table_structure():
        return False
    
    print("\n" + "=" * 60)
    print("✅ 数据库表结构更新完成！")
    print("=" * 60)
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
