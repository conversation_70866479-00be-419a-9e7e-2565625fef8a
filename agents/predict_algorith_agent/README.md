# 预测算法智能体 (Predictive Algorithm Agent)

[![Python](https://img.shields.io/badge/Python-3.10%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100%2B-green.svg)](https://fastapi.tiangolo.com)
[![Pydantic AI](https://img.shields.io/badge/Pydantic_AI-Latest-orange.svg)](https://ai.pydantic.dev)

## 📋 项目简介

预测算法智能体是一个基于大语言模型的工业算法智能化平台，专注于预测性算法的自动化开发、训练和部署。通过自然语言交互，用户可以轻松创建、配置和管理预测算法任务，实现从需求描述到算法部署的全流程自动化。

### 🎯 核心特性

- **🤖 多智能体架构**：参数推荐、训练监控、结果分析等专业智能体
- **🧠 智能参数推荐**：基于DeepSeek和通义千问的多LLM参数优化
- **⚡ 实时通信**：WebSocket支持的实时对话和状态更新
- **📊 多模型调用**：根据任务阶段智能选择最适合的模型
- **🔄 完整流程**：从历史算法检索到结果分析的全链路支持
- **💾 状态管理**：Redis+MySQL双重存储的会话状态管理

### 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API层         │    │   核心智能体    │    │   服务层        │
│                 │    │                 │    │                 │
│ WebSocket API   │◄──►│ 预测算法智能体  │◄──►│ 参数推荐服务    │
│ REST API        │    │ 历史算法智能体  │    │ 历史算法服务    │
│ 对话管理        │    │ 多模型调用      │    │ 数据库管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
agents/predict_algorith_agent/
├── 📄 README.md                    # 项目说明文档
├── 🚀 predict_main.py              # FastAPI 主入口
├── 📋 DEPLOYMENT.md                # 部署指南
├── ⚙️ deploy_config.py             # 部署配置
├── 🏗️ core/                       # 核心智能体模块
│   ├── predictive_agent.py         # 主预测算法智能体
│   └── history_algorithm_agent.py  # 历史算法检索智能体
├── 🌐 api/                         # API接口层
│   ├── websocket_routes.py         # WebSocket路由
│   └── conversation_routes.py      # 对话管理路由
├── 🔧 services/                    # 业务服务层
│   ├── parameter_recommendation_service.py  # 参数推荐服务
│   ├── history_algorithm_service.py         # 历史算法服务
│   └── fallback_responses.py               # 备用响应服务
├── 📊 models/                      # 数据模型
│   ├── predictive_models.py        # 预测算法模型
│   └── parameter_recommendation_models.py  # 参数推荐模型
├── 🗄️ database/                   # 数据库管理
│   ├── database_manager.py         # 数据库管理器
│   └── database_verification.py    # 数据库验证
├── 🛠️ utils/                      # 工具类
│   ├── llm_provider_manager.py     # LLM提供商管理
│   ├── config_manager.py           # 配置管理
│   ├── logging_config.py           # 日志配置
│   ├── predict_memory_manager.py   # 内存管理
│   ├── context_manager.py          # 上下文管理
│   ├── error_handler.py            # 错误处理
│   ├── data_validator.py           # 数据验证
│   └── deploy_config.py            # 部署配置
├── 🌐 network/                     # 网络通信
│   └── websocket_manager.py        # WebSocket管理
├── 📚 docs/                        # 文档
│   ├── legacy_issues.md            # 遗留问题列表
│   ├── database_design.md          # 数据库设计
│   ├── websocket_integration_guide.md  # WebSocket集成指南
│   ├── requirements_comparison_0721.md # 需求对比分析
│   └── DEPLOYMENT.md               # 部署指南
├── 🧪 tests/                       # 测试用例
│   ├── test_parameter_recommendation.py  # 参数推荐测试
│   ├── test_history_algorithm_flow.py    # 历史算法测试
│   └── test_api_extensibility.py         # API扩展性测试
├── 📜 scripts/                     # 脚本工具
│   └── fix_session_id_format.py    # 会话ID格式修复
└── 📁 static/                      # 静态资源
    ├── html/                       # HTML模板
    └── sql/                        # SQL脚本
```

## 🔧 核心功能模块

### 1. 🎯 参数推荐智能体
- **智能参数提取**：从自然语言描述中提取算法参数
- **多LLM对比**：DeepSeek和通义千问双重验证
- **参数优化建议**：基于数据规模和性能目标的智能推荐
- **模板匹配**：支持LSTM、CNN、Random Forest等多种算法模板

### 2. 🔍 历史算法检索智能体
- **算法列表获取**：调用三方系统API获取历史算法列表
- **列表展示**：通过WebSocket返回算法列表给前端
- **用户选择**：支持用户选择历史算法或创建新算法
- **简化流程**：无需复杂的推荐算法，直接展示所有可用算法

### 3. 🚀 智能训练管理
- **任务创建**：自动生成训练任务配置
- **状态监控**：实时跟踪训练进度和状态
- **异常处理**：智能检测和处理训练异常
- **结果确认**：用户确认机制和质量评估

### 4. 📊 结果分析智能体
- **多维度分析**：性能指标、准确率、效率评估
- **PDF报告生成**：自动生成专业的分析报告
- **可视化支持**：图表和曲线的智能生成
- **国标评估**：GBT+43555-2023标准评估支持

### 5. 💬 对话管理系统
- **多轮对话**：支持复杂的多轮交互流程
- **上下文理解**：智能理解对话上下文和用户意图
- **会话持久化**：Redis+MySQL双重存储保证数据安全
- **实时通信**：WebSocket支持的实时消息推送

## 🚀 快速开始

### 环境要求
- **Python**: 3.10 - 3.12
- **FastAPI**: 0.100+
- **MySQL**: >= 8.0 (用于数据持久化)
- **Redis**: >= 6.0 (用于会话缓存)
- **wkhtmltopdf**: 用于PDF报告生成

### 安装步骤

1. **安装Python依赖**
```bash
cd agents/predict_algorith_agent
pip install -r ../../requirement.txt
```

2. **配置数据库**
```bash
# 执行数据库初始化脚本
mysql -h ********* -P 23306 -u indusaio_user -p indusaio_agent < ../../db_scripts.sql
```

3. **配置环境变量**
```python
# 编辑 agents/config.py
MYSQL_HOST = "*********"
MYSQL_PORT = 23306
MYSQL_USER = "indusaio_user"
MYSQL_PASSWORD = "Sygy@2025"
MYSQL_DB = "indusaio_agent"

REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_PASSWORD = "redis"

# PDF生成工具路径
WKHTMLTOPDF_PATH = r"D:\software\wkhtmltopdf\bin\wkhtmltopdf.exe"
REPORT_PDF_DIR = r"D:/tmp"
```

### 启动服务

1. **独立启动预测算法智能体**
```bash
cd agents/predict_algorith_agent
python predict_main.py
```

2. **通过主服务启动**
```bash
# 在项目根目录
python main.py
```

3. **开发模式启动**
```bash
uvicorn agents.predict_algorith_agent.predict_main:app --reload --host 0.0.0.0 --port 8008 --log-level debug
```

### 服务验证

- **健康检查**: GET http://localhost:8008/health
- **API文档**: http://localhost:8008/docs
- **WebSocket测试**: ws://localhost:8008/ws/chat

## ⚙️ 配置说明

### 核心配置 (agents/config.py)
```python
# 大模型配置
SHOW_DEEPSEEK_THOUGHT = False  # 是否显示思考过程

# 第三方API配置
ALGORITHM_API_BASE = "http://thirdparty-algorithm/api"
TRAING_API_BASE = "http://thirdparty-training/api"

# 服务配置
PREDICT_MAIN_HOST = "0.0.0.0"
PREDICT_MAIN_PORT = 8008
```

### LLM提供商配置
- **DeepSeek**: 已配置API密钥，支持deepseek-chat模型
- **通义千问**: 已配置API密钥，支持qwen-turbo-latest模型
- **配置位置**: `utils/llm_provider_manager.py`

### 数据库配置
- **主数据库**: MySQL (持久化存储)
- **缓存数据库**: Redis (会话状态缓存)
- **核心表**: agent_session, conversations, algorithm_tasks

## 📚 API接口文档

### 核心接口概览

| 接口类型 | 路径 | 方法 | 描述 |
|---------|------|------|------|
| 智能体对话 | `/api/predict_algorith/chat` | POST | 主要对话接口 |
| WebSocket | `/ws/chat` | WS | 实时通信接口 |
| 对话管理 | `/api/predict_algorith/conversations` | GET/POST | 对话列表管理 |
| 任务查询 | `/api/predict_algorith/tasks/{task_id}` | GET | 任务状态查询 |
| 报告下载 | `/api/predict_algorith/report/pdf` | GET | PDF报告下载 |
| 健康检查 | `/health` | GET | 服务状态检查 |

### 1. 🤖 智能体对话接口

**接口地址**: `POST /api/predict_algorith/chat`

#### 请求参数
```json
{
  "message": "我想做一个设备预测性维护的算法，数据是传感器的温度和振动数据",
  "user_id": "user_12345",
  "session_id": "session_67890",
  "conversation_id": "conv_abcdef"
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "response": "我来帮您设计预测性维护算法...",
    "type": "parameter_recommendation",
    "stage": "parameter_recommendation",
    "model_used": "微调小型模型",
    "conversation_id": "conv_abcdef",
    "session_state": {
      "current_stage": "parameter_recommendation",
      "algorithm_type": "LSTM",
      "parameters": {...}
    }
  }
}
```

### 2. 🌐 WebSocket实时通信

**连接地址**: `ws://localhost:8008/ws/chat`

#### 连接参数
- `session_id`: 会话ID (可选，自动生成)
- `user_id`: 用户ID (可选)

#### 消息格式
```javascript
// 发送消息
ws.send(JSON.stringify({
    "message": "用户输入内容",
    "type": "chat",
    "session_id": "session_id"
}));

// 接收消息
{
    "type": "response",
    "data": {
        "response": "智能体回复",
        "stage": "parameter_recommendation",
        "model_used": "微调小型模型"
    },
    "timestamp": "2025-07-24T10:30:00Z"
}
```

### 3. 📋 对话管理接口

#### 创建对话
```http
POST /api/predict_algorith/conversations
Content-Type: application/json

{
  "title": "设备预测维护算法开发",
  "description": "基于传感器数据的预测性维护",
  "user_id": "user_12345",
  "conversation_type": "algorithm_development"
}
```

#### 获取对话列表
```http
GET /api/predict_algorith/conversations?user_id=user_12345&limit=10&offset=0
```

### 4. 📊 任务状态查询

```http
GET /api/predict_algorith/tasks/task_12345
```

**响应示例**:
```json
{
  "task_id": "task_12345",
  "status": "training",
  "progress": 65.5,
  "algorithm_type": "LSTM",
  "created_at": "2025-07-24T10:00:00Z",
  "estimated_completion": "2025-07-24T12:00:00Z"
}
```

### 5. 📄 PDF报告下载

```http
GET /api/predict_algorith/report/pdf?task_id=task_12345
```

**响应**: PDF文件流

## 📊 项目状态

### 完成度评估

| 功能模块 | 完成度 | 状态说明 |
|---------|--------|----------|
| **参数推荐智能体** | **65%** | ✅ 多模型架构、✅ 智能引导、✅ 参数提取 |
| **历史算法检索** | **80%** | ✅ 基础框架、✅ 需求简化、❌ API集成 |
| **智能训练管理** | **35%** | ✅ 状态管理、✅ 实时通信、❌ 监控详情 |
| **智能预测功能** | **15%** | ✅ 基础框架、❌ 数据源绑定 |
| **结果分析智能体** | **45%** | ✅ 多模型分析、✅ PDF生成、❌ 曲线报表 |
| **WebSocket通信** | **85%** | ✅ 实时通信、✅ 多用户支持、✅ 状态同步 |
| **对话管理系统** | **80%** | ✅ 会话管理、✅ 历史记录、✅ 状态持久化 |
| **数据库设计** | **90%** | ✅ 表结构、✅ 索引优化、✅ 数据迁移 |

**总体完成度：50-55%**（历史算法检索需求简化后提升）

### 🎉 已实现功能

- ✅ **多模型调用架构**：根据任务阶段智能选择模型
- ✅ **WebSocket实时通信**：支持多用户并发的实时交互
- ✅ **参数推荐服务**：多LLM对比的智能参数推荐
- ✅ **会话状态管理**：Redis+MySQL双重存储机制
- ✅ **对话管理系统**：完整的对话创建、查询、更新流程
- ✅ **数据库基础设施**：完善的数据模型和管理工具
- ✅ **错误处理机制**：全面的异常捕获和降级处理
- ✅ **日志系统**：统一的日志配置和管理

### 🚨 遗留问题 (详见 [legacy_issues.md](docs/legacy_issues.md))

**高优先级**：
- 🟡 **历史算法检索API集成**：需求已简化，仅需集成GET API（1-2人天）
- 🔴 **国标GBT+43555-2023评估**：缺少算法质量评估标准
- 🔴 **训练结果确认机制**：缺少用户确认和回退流程

**中优先级**：
- 🔴 **异常处理智能化**：需要更智能的异常分析和建议
- 🔴 **训练过程参数调整**：缺少动态参数调整功能
- 🔴 **智能预测核心逻辑**：需要完整的预测流程实现

**预计工作量**：35-53人天（历史算法检索简化后减少），建议1个月内完成核心功能

## 🛠️ 开发指南

### 代码结构说明

- **core/**: 核心智能体逻辑，包含主要的AI处理流程
- **api/**: API接口层，处理HTTP和WebSocket请求
- **services/**: 业务服务层，封装具体的业务逻辑
- **models/**: 数据模型定义，使用Pydantic进行数据验证
- **utils/**: 工具类和辅助功能
- **database/**: 数据库管理和操作
- **tests/**: 测试用例和测试工具

### 开发规范

- **代码风格**: 遵循PEP 8规范
- **类型注解**: 使用Python类型注解
- **文档字符串**: 使用Google风格的docstring
- **错误处理**: 统一使用ErrorHandler处理异常
- **日志记录**: 使用统一的logging配置

### 测试指南

```bash
# 运行所有测试
cd agents/predict_algorith_agent
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_parameter_recommendation.py -v

# 运行API测试
python tests/test_api_extensibility.py
```

### Mock数据说明

- **历史算法服务**: 当前使用mock数据，位于 `services/history_algorithm_service.py`
- **第三方API**: 算法生成器和训练环境API均为mock实现
- **真实API对接**: 修改相应服务类中的API调用逻辑即可

## 💾 数据存储架构

### Redis缓存策略
- **会话状态**: 实时缓存用户会话状态，TTL=24小时
- **对话历史**: 缓存最近的对话记录，提升响应速度
- **参数推荐**: 缓存常用的参数推荐结果

### MySQL持久化
- **agent_session**: 会话状态持久化存储
- **conversations**: 对话记录和元数据
- **algorithm_tasks**: 算法任务状态和结果

### 数据同步机制
- **写入**: Redis写入成功后异步同步到MySQL
- **读取**: 优先从Redis读取，失败时回源MySQL
- **一致性**: 定期同步确保数据一致性

## 🔧 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查端口8008是否被占用
   - 确认防火墙设置
   - 查看日志中的错误信息

2. **数据库连接问题**
   - 验证MySQL和Redis连接配置
   - 检查网络连通性
   - 确认数据库权限设置

3. **LLM调用失败**
   - 检查API密钥配置
   - 验证网络连接
   - 查看LLM提供商状态

### 日志查看

```bash
# 查看实时日志
tail -f logs/predict_agent.log

# 查看错误日志
grep "ERROR" logs/predict_agent.log

# 查看特定模块日志
grep "parameter_recommendation" logs/predict_agent.log
```

## 📞 技术支持

- **文档**: 查看 `docs/` 目录下的详细文档
- **测试**: 运行 `tests/` 目录下的测试用例
- **问题反馈**: 通过项目Issue系统提交问题
- **开发讨论**: 参与项目开发讨论

---

**📅 最后更新**: 2025年7月24日
**📝 文档版本**: v2.0
**👥 维护团队**: 算法智能体开发组