#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一数据验证模块
"""

import re
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from .error_handler import ErrorCode, ApplicationError

class ValidationError(ApplicationError):
    """数据验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        super().__init__(
            error_code=ErrorCode.VALIDATION_ERROR,
            message=message,
            details=f"字段: {field}, 值: {value}" if field else None
        )
        self.field = field
        self.value = value

class AlgorithmType(str, Enum):
    """支持的算法类型"""
    LSTM = "LSTM"
    CNN = "CNN"
    RNN = "RNN"
    LINEAR_REGRESSION = "线性回归"
    LOGISTIC_REGRESSION = "逻辑回归"
    RANDOM_FOREST = "随机森林"
    SVM = "SVM"
    OTHER = "其他"

class ConversationStatus(str, Enum):
    """对话状态"""
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ERROR = "error"

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_user_id(user_id: str) -> str:
        """验证用户ID"""
        if not user_id or not isinstance(user_id, str):
            raise ValidationError("用户ID不能为空", "user_id", user_id)
        
        if len(user_id.strip()) == 0:
            raise ValidationError("用户ID不能为空字符串", "user_id", user_id)
        
        if len(user_id) > 100:
            raise ValidationError("用户ID长度不能超过100个字符", "user_id", user_id)
        
        # 检查是否包含非法字符
        if not re.match(r'^[a-zA-Z0-9_\-@.]+$', user_id):
            raise ValidationError("用户ID只能包含字母、数字、下划线、连字符、@和点", "user_id", user_id)
        
        return user_id.strip()
    
    @staticmethod
    def validate_conversation_title(title: str) -> str:
        """验证对话标题"""
        if not title or not isinstance(title, str):
            raise ValidationError("对话标题不能为空", "title", title)
        
        title = title.strip()
        if len(title) == 0:
            raise ValidationError("对话标题不能为空字符串", "title", title)
        
        if len(title) > 200:
            raise ValidationError("对话标题长度不能超过200个字符", "title", title)
        
        return title
    
    @staticmethod
    def validate_conversation_id(conversation_id: str) -> str:
        """验证对话ID"""
        if not conversation_id or not isinstance(conversation_id, str):
            raise ValidationError("对话ID不能为空", "conversation_id", conversation_id)
        
        conversation_id = conversation_id.strip()
        if len(conversation_id) == 0:
            raise ValidationError("对话ID不能为空字符串", "conversation_id", conversation_id)
        
        # 检查格式（假设是UUID格式或特定格式）
        if not re.match(r'^[a-zA-Z0-9_\-]+$', conversation_id):
            raise ValidationError("对话ID格式不正确", "conversation_id", conversation_id)
        
        return conversation_id
    
    @staticmethod
    def validate_conversation_status(status: str) -> ConversationStatus:
        """验证对话状态"""
        if not status or not isinstance(status, str):
            raise ValidationError("对话状态不能为空", "status", status)
        
        try:
            return ConversationStatus(status.lower())
        except ValueError:
            valid_statuses = [s.value for s in ConversationStatus]
            raise ValidationError(
                f"无效的对话状态，支持的状态: {valid_statuses}",
                "status",
                status
            )
    
    @staticmethod
    def validate_progress(progress: Union[int, float]) -> float:
        """验证进度值"""
        if progress is None:
            raise ValidationError("进度值不能为空", "progress", progress)
        
        try:
            progress_float = float(progress)
        except (ValueError, TypeError):
            raise ValidationError("进度值必须是数字", "progress", progress)
        
        if progress_float < 0.0 or progress_float > 100.0:
            raise ValidationError("进度值必须在0-100之间", "progress", progress)
        
        return progress_float
    
    @staticmethod
    def validate_algorithm_type(algorithm_type: str) -> AlgorithmType:
        """验证算法类型"""
        if not algorithm_type or not isinstance(algorithm_type, str):
            raise ValidationError("算法类型不能为空", "algorithm_type", algorithm_type)
        
        # 尝试直接匹配
        for algo_type in AlgorithmType:
            if algo_type.value.upper() == algorithm_type.upper():
                return algo_type
        
        # 如果没有匹配，返回其他
        return AlgorithmType.OTHER
    
    @staticmethod
    def validate_learning_rate(learning_rate: Union[int, float]) -> float:
        """验证学习率"""
        if learning_rate is None:
            raise ValidationError("学习率不能为空", "learning_rate", learning_rate)
        
        try:
            lr_float = float(learning_rate)
        except (ValueError, TypeError):
            raise ValidationError("学习率必须是数字", "learning_rate", learning_rate)
        
        if lr_float <= 0.0 or lr_float > 1.0:
            raise ValidationError("学习率必须在0-1之间", "learning_rate", learning_rate)
        
        return lr_float
    
    @staticmethod
    def validate_batch_size(batch_size: Union[int, str]) -> int:
        """验证批次大小"""
        if batch_size is None:
            raise ValidationError("批次大小不能为空", "batch_size", batch_size)
        
        try:
            batch_int = int(batch_size)
        except (ValueError, TypeError):
            raise ValidationError("批次大小必须是整数", "batch_size", batch_size)
        
        if batch_int <= 0:
            raise ValidationError("批次大小必须大于0", "batch_size", batch_size)
        
        if batch_int > 10000:
            raise ValidationError("批次大小不能超过10000", "batch_size", batch_size)
        
        return batch_int
    
    @staticmethod
    def validate_epochs(epochs: Union[int, str]) -> int:
        """验证训练轮数"""
        if epochs is None:
            raise ValidationError("训练轮数不能为空", "epochs", epochs)
        
        try:
            epochs_int = int(epochs)
        except (ValueError, TypeError):
            raise ValidationError("训练轮数必须是整数", "epochs", epochs)
        
        if epochs_int <= 0:
            raise ValidationError("训练轮数必须大于0", "epochs", epochs)
        
        if epochs_int > 10000:
            raise ValidationError("训练轮数不能超过10000", "epochs", epochs)
        
        return epochs_int
    
    @staticmethod
    def validate_user_message(message: str) -> str:
        """验证用户消息"""
        if not message or not isinstance(message, str):
            raise ValidationError("用户消息不能为空", "message", message)
        
        message = message.strip()
        if len(message) == 0:
            raise ValidationError("用户消息不能为空字符串", "message", message)
        
        if len(message) > 5000:
            raise ValidationError("用户消息长度不能超过5000个字符", "message", message)
        
        return message
    
    @staticmethod
    def validate_algorithm_params(params: Dict[str, Any]) -> Dict[str, Any]:
        """验证算法参数"""
        if not isinstance(params, dict):
            raise ValidationError("算法参数必须是字典格式", "params", params)
        
        validated_params = {}
        
        # 验证算法类型
        if "algorithm_type" in params:
            validated_params["algorithm_type"] = DataValidator.validate_algorithm_type(
                params["algorithm_type"]
            ).value
        
        # 验证学习率
        if "learning_rate" in params:
            validated_params["learning_rate"] = DataValidator.validate_learning_rate(
                params["learning_rate"]
            )
        
        # 验证批次大小
        if "batch_size" in params:
            validated_params["batch_size"] = DataValidator.validate_batch_size(
                params["batch_size"]
            )
        
        # 验证训练轮数
        if "epochs" in params:
            validated_params["epochs"] = DataValidator.validate_epochs(
                params["epochs"]
            )
        
        # 验证其他数值参数
        numeric_params = ["hidden_size", "input_dim", "output_dim", "num_layers", "dropout"]
        for param_name in numeric_params:
            if param_name in params:
                try:
                    value = float(params[param_name])
                    if param_name == "dropout":
                        if value < 0.0 or value > 1.0:
                            raise ValidationError(f"{param_name}必须在0-1之间", param_name, value)
                    elif value <= 0:
                        raise ValidationError(f"{param_name}必须大于0", param_name, value)
                    
                    validated_params[param_name] = int(value) if param_name != "dropout" else value
                except (ValueError, TypeError):
                    raise ValidationError(f"{param_name}必须是数字", param_name, params[param_name])
        
        # 验证字符串参数
        string_params = ["optimizer", "loss_function", "activation"]
        for param_name in string_params:
            if param_name in params:
                if not isinstance(params[param_name], str):
                    raise ValidationError(f"{param_name}必须是字符串", param_name, params[param_name])
                validated_params[param_name] = params[param_name].strip()
        
        return validated_params

# 验证装饰器
def validate_input(**validators):
    """
    输入验证装饰器
    
    Args:
        **validators: 字段验证器映射
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 验证kwargs中的参数
            for field_name, validator_func in validators.items():
                if field_name in kwargs:
                    try:
                        kwargs[field_name] = validator_func(kwargs[field_name])
                    except ValidationError:
                        raise
                    except Exception as e:
                        raise ValidationError(
                            f"验证字段 {field_name} 时发生错误: {str(e)}",
                            field_name,
                            kwargs[field_name]
                        )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

# Pydantic模型验证
class ValidatedConversationCreate(BaseModel):
    """验证的对话创建模型"""
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="对话标题")
    
    @field_validator('user_id')
    @classmethod
    def validate_user_id(cls, v):
        return DataValidator.validate_user_id(v)

    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        return DataValidator.validate_conversation_title(v)

class ValidatedConversationUpdate(BaseModel):
    """验证的对话更新模型"""
    status: Optional[str] = Field(None, description="对话状态")
    progress: Optional[float] = Field(None, description="进度")
    
    @field_validator('status')
    @classmethod
    def validate_status(cls, v):
        if v is not None:
            return DataValidator.validate_conversation_status(v).value
        return v

    @field_validator('progress')
    @classmethod
    def validate_progress(cls, v):
        if v is not None:
            return DataValidator.validate_progress(v)
        return v

class ValidatedAlgorithmParams(BaseModel):
    """验证的算法参数模型"""
    algorithm_type: Optional[str] = Field(None, description="算法类型")
    learning_rate: Optional[float] = Field(None, description="学习率")
    batch_size: Optional[int] = Field(None, description="批次大小")
    epochs: Optional[int] = Field(None, description="训练轮数")
    hidden_size: Optional[int] = Field(None, description="隐藏层大小")
    
    @field_validator('algorithm_type')
    @classmethod
    def validate_algorithm_type(cls, v):
        if v is not None:
            return DataValidator.validate_algorithm_type(v).value
        return v

    @field_validator('learning_rate')
    @classmethod
    def validate_learning_rate(cls, v):
        if v is not None:
            return DataValidator.validate_learning_rate(v)
        return v

    @field_validator('batch_size')
    @classmethod
    def validate_batch_size(cls, v):
        if v is not None:
            return DataValidator.validate_batch_size(v)
        return v

    @field_validator('epochs')
    @classmethod
    def validate_epochs(cls, v):
        if v is not None:
            return DataValidator.validate_epochs(v)
        return v
