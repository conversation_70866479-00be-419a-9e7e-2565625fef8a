#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一错误处理模块
"""

import traceback
from typing import Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse

from .logging_config import get_main_logger

logger = get_main_logger()

class ErrorCode(Enum):
    """错误代码枚举"""
    
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = 1000
    VALIDATION_ERROR = 1001
    PERMISSION_DENIED = 1002
    RESOURCE_NOT_FOUND = 1003
    RATE_LIMIT_EXCEEDED = 1004
    
    # 数据库错误 (2000-2999)
    DATABASE_CONNECTION_ERROR = 2000
    DATABASE_QUERY_ERROR = 2001
    DATABASE_CONSTRAINT_ERROR = 2002
    DATABASE_TIMEOUT_ERROR = 2003
    
    # 算法智能体错误 (3000-3999)
    AGENT_INITIALIZATION_ERROR = 3000
    AGENT_PROCESSING_ERROR = 3001
    LLM_API_ERROR = 3002
    LLM_RESPONSE_ERROR = 3003
    TASK_CLASSIFICATION_ERROR = 3004
    PARAMETER_EXTRACTION_ERROR = 3005
    
    # 对话管理错误 (4000-4999)
    CONVERSATION_NOT_FOUND = 4000
    CONVERSATION_CREATE_ERROR = 4001
    CONVERSATION_UPDATE_ERROR = 4002
    CONVERSATION_DELETE_ERROR = 4003
    
    # WebSocket错误 (5000-5999)
    WEBSOCKET_CONNECTION_ERROR = 5000
    WEBSOCKET_MESSAGE_ERROR = 5001
    WEBSOCKET_BROADCAST_ERROR = 5002
    
    # 外部服务错误 (6000-6999)
    EXTERNAL_API_ERROR = 6000
    NETWORK_ERROR = 6001
    TIMEOUT_ERROR = 6002

class ErrorDetail(BaseModel):
    """错误详情模型"""
    code: int
    message: str
    details: Optional[str] = None
    timestamp: str
    trace_id: Optional[str] = None

class ApplicationError(Exception):
    """应用程序自定义异常基类"""
    
    def __init__(
        self,
        error_code: ErrorCode,
        message: str,
        details: Optional[str] = None,
        original_exception: Optional[Exception] = None
    ):
        self.error_code = error_code
        self.message = message
        self.details = details
        self.original_exception = original_exception
        self.timestamp = datetime.now().isoformat()
        
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "code": self.error_code.value,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp
        }

class DatabaseError(ApplicationError):
    """数据库相关错误"""
    pass

class AgentError(ApplicationError):
    """算法智能体相关错误"""
    pass

class ConversationError(ApplicationError):
    """对话管理相关错误"""
    pass

class WebSocketError(ApplicationError):
    """WebSocket相关错误"""
    pass

class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_exception(
        exception: Exception,
        context: Optional[str] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        处理异常并返回标准化的错误响应
        
        Args:
            exception: 异常实例
            context: 错误上下文
            user_message: 用户友好的错误消息
            
        Returns:
            Dict: 标准化的错误响应
        """
        
        # 记录错误日志
        error_msg = f"错误处理: {type(exception).__name__}: {str(exception)}"
        if context:
            error_msg = f"[{context}] {error_msg}"
        
        logger.error(error_msg)
        logger.debug(f"错误堆栈: {traceback.format_exc()}")
        
        # 处理自定义应用异常
        if isinstance(exception, ApplicationError):
            return {
                "success": False,
                "error": exception.to_dict(),
                "message": user_message or exception.message
            }
        
        # 处理HTTP异常
        if isinstance(exception, HTTPException):
            return {
                "success": False,
                "error": {
                    "code": exception.status_code,
                    "message": exception.detail,
                    "timestamp": datetime.now().isoformat()
                },
                "message": user_message or "请求处理失败"
            }
        
        # 处理其他异常
        error_code = ErrorHandler._classify_exception(exception)
        
        return {
            "success": False,
            "error": {
                "code": error_code.value,
                "message": str(exception),
                "details": traceback.format_exc() if logger.level <= 10 else None,  # DEBUG级别才显示堆栈
                "timestamp": datetime.now().isoformat()
            },
            "message": user_message or "系统内部错误，请稍后重试"
        }
    
    @staticmethod
    def _classify_exception(exception: Exception) -> ErrorCode:
        """根据异常类型分类错误代码"""
        
        exception_name = type(exception).__name__.lower()
        
        # 数据库相关异常
        if any(keyword in exception_name for keyword in ['database', 'sql', 'connection']):
            return ErrorCode.DATABASE_CONNECTION_ERROR
        
        # 网络相关异常
        if any(keyword in exception_name for keyword in ['timeout', 'connection', 'network']):
            return ErrorCode.NETWORK_ERROR
        
        # 验证相关异常
        if any(keyword in exception_name for keyword in ['validation', 'value', 'type']):
            return ErrorCode.VALIDATION_ERROR
        
        # 默认为未知错误
        return ErrorCode.UNKNOWN_ERROR
    
    @staticmethod
    def create_http_exception(
        status_code: int,
        error_code: ErrorCode,
        message: str,
        details: Optional[str] = None
    ) -> HTTPException:
        """创建HTTP异常"""
        
        error_detail = {
            "code": error_code.value,
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        return HTTPException(
            status_code=status_code,
            detail=error_detail
        )

    def handle_database_error(self, test_error):
        pass


# 全局异常处理器
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """全局异常处理器"""
    
    error_response = ErrorHandler.handle_exception(
        exception=exc,
        context=f"{request.method} {request.url.path}",
        user_message="请求处理失败，请稍后重试"
    )
    
    # 根据异常类型确定HTTP状态码
    if isinstance(exc, HTTPException):
        status_code = exc.status_code
    elif isinstance(exc, ApplicationError):
        status_code = 400  # Bad Request
    else:
        status_code = 500  # Internal Server Error
    
    return JSONResponse(
        status_code=status_code,
        content=error_response
    )

# 便捷的错误创建函数
def create_database_error(message: str, details: Optional[str] = None) -> DatabaseError:
    """创建数据库错误"""
    return DatabaseError(
        error_code=ErrorCode.DATABASE_QUERY_ERROR,
        message=message,
        details=details
    )

def create_agent_error(message: str, details: Optional[str] = None) -> AgentError:
    """创建算法智能体错误"""
    return AgentError(
        error_code=ErrorCode.AGENT_PROCESSING_ERROR,
        message=message,
        details=details
    )

def create_conversation_error(message: str, details: Optional[str] = None) -> ConversationError:
    """创建对话管理错误"""
    return ConversationError(
        error_code=ErrorCode.CONVERSATION_CREATE_ERROR,
        message=message,
        details=details
    )

def create_websocket_error(message: str, details: Optional[str] = None) -> WebSocketError:
    """创建WebSocket错误"""
    return WebSocketError(
        error_code=ErrorCode.WEBSOCKET_CONNECTION_ERROR,
        message=message,
        details=details
    )

# 错误处理装饰器
def handle_errors(
    context: Optional[str] = None,
    user_message: Optional[str] = None,
    reraise: bool = False
):
    """
    错误处理装饰器
    
    Args:
        context: 错误上下文
        user_message: 用户友好的错误消息
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_response = ErrorHandler.handle_exception(
                    exception=e,
                    context=context or func.__name__,
                    user_message=user_message
                )
                
                if reraise:
                    raise
                
                return error_response
        return wrapper
    return decorator

# 异步错误处理装饰器
def handle_async_errors(
    context: Optional[str] = None,
    user_message: Optional[str] = None,
    reraise: bool = False
):
    """
    异步错误处理装饰器
    
    Args:
        context: 错误上下文
        user_message: 用户友好的错误消息
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                error_response = ErrorHandler.handle_exception(
                    exception=e,
                    context=context or func.__name__,
                    user_message=user_message
                )
                
                if reraise:
                    raise
                
                return error_response
        return wrapper
    return decorator
