#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大模型配置管理工具
用于动态管理和切换大模型配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from agents.config import (
    LLM_PROVIDERS, CURRENT_LLM_PROVIDER, LLM_CONFIG,
    get_current_llm_config, set_llm_provider, update_llm_api_key, get_llm_providers_info
)
import json

class LLMConfigManager:
    """大模型配置管理器"""
    
    def __init__(self):
        self.providers = LLM_PROVIDERS
        self.current_provider = CURRENT_LLM_PROVIDER
    
    def show_current_config(self):
        """显示当前大模型配置"""
        print("🤖 当前大模型配置")
        print("=" * 50)
        
        try:
            config = get_current_llm_config()
            print(f"📋 提供商: {self.current_provider}")
            print(f"🏷️ 描述: {config['description']}")
            print(f"🎯 模型名称: {config['model_name']}")
            print(f"🌐 Base URL: {config['base_url']}")
            print(f"🔑 API Key: {config['api_key'][:10]}...{config['api_key'][-4:] if config['api_key'] else 'None'}")
            print(f"🌡️ Temperature: {config['temperature']}")
            print(f"📏 Max Tokens: {config['max_tokens']}")
            print(f"⏱️ Timeout: {config['timeout']}秒")
        except Exception as e:
            print(f"❌ 获取配置失败: {e}")
    
    def show_all_providers(self):
        """显示所有可用的大模型提供商"""
        print("🌟 所有可用的大模型提供商")
        print("=" * 50)
        
        for name, config in self.providers.items():
            status = "✅ 当前使用" if name == self.current_provider else "⭕ 可选择"
            api_key_status = "🔑 已配置" if config['api_key'] else "❌ 未配置"
            
            print(f"{status} {name}:")
            print(f"   🏷️ 描述: {config['description']}")
            print(f"   🎯 模型: {config['model_name']}")
            print(f"   🌐 URL: {config['base_url']}")
            print(f"   {api_key_status}")
            print()
    
    def switch_provider(self, provider_name: str) -> bool:
        """切换大模型提供商"""
        print(f"🔄 切换大模型提供商到: {provider_name}")
        
        if set_llm_provider(provider_name):
            self.current_provider = provider_name
            print("✅ 切换成功")
            print("⚠️ 注意: 需要重启服务才能生效")
            return True
        else:
            print("❌ 切换失败")
            return False
    
    def update_api_key(self, provider_name: str, api_key: str) -> bool:
        """更新API Key"""
        print(f"🔑 更新 {provider_name} 的API Key")
        
        if update_llm_api_key(provider_name, api_key):
            print("✅ API Key更新成功")
            print("⚠️ 注意: 需要重启服务才能生效")
            return True
        else:
            print("❌ API Key更新失败")
            return False
    
    def add_custom_provider(self, name: str, model_name: str, base_url: str, api_key: str, description: str = ""):
        """添加自定义大模型提供商"""
        print(f"➕ 添加自定义提供商: {name}")
        
        if name in self.providers:
            print(f"⚠️ 提供商 {name} 已存在，将覆盖现有配置")
        
        self.providers[name] = {
            "model_name": model_name,
            "base_url": base_url,
            "api_key": api_key,
            "description": description or f"自定义{name}模型"
        }
        
        print("✅ 自定义提供商添加成功")
        print("⚠️ 注意: 这是临时添加，重启后会丢失")
        print("💡 如需永久保存，请手动修改agents/config.py")
    
    def test_current_config(self):
        """测试当前大模型配置"""
        print("🧪 测试当前大模型配置")
        print("=" * 50)
        
        try:
            from pydantic_ai import Agent
            from pydantic_ai.models.openai import OpenAIModel
            from pydantic_ai.providers.openai import OpenAIProvider
            from pydantic import BaseModel, Field
            
            # 获取配置
            config = get_current_llm_config()
            
            # 创建测试模型
            test_model = OpenAIModel(
                config['model_name'],
                provider=OpenAIProvider(
                    base_url=config['base_url'],
                    api_key=config['api_key']
                )
            )
            
            # 创建测试Agent
            class TestResponse(BaseModel):
                message: str = Field(description="测试响应消息")
            
            test_agent = Agent(
                model=test_model,
                output_type=TestResponse,
                system_prompt="你是一个测试助手，请简单回复用户的问候。"
            )
            
            print("📤 发送测试消息...")
            result = test_agent.run_sync("你好，请简单介绍一下你自己。")
            
            print("📥 测试响应:")
            print(f"   {result.data.message}")
            print("✅ 大模型配置测试成功")
            
        except Exception as e:
            print(f"❌ 大模型配置测试失败: {e}")
            print("💡 请检查API Key是否正确，网络是否正常")
    
    def export_config(self, file_path: str = "llm_config_backup.json"):
        """导出当前配置到文件"""
        try:
            config_data = {
                "current_provider": self.current_provider,
                "providers": self.providers,
                "llm_config": LLM_CONFIG
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 配置已导出到: {file_path}")
        except Exception as e:
            print(f"❌ 配置导出失败: {e}")
    
    def import_config(self, file_path: str):
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 更新配置
            if "providers" in config_data:
                self.providers.update(config_data["providers"])
            
            if "current_provider" in config_data:
                self.current_provider = config_data["current_provider"]
            
            print(f"✅ 配置已从 {file_path} 导入")
            print("⚠️ 注意: 这是临时导入，重启后会丢失")
            
        except Exception as e:
            print(f"❌ 配置导入失败: {e}")

def interactive_config_manager():
    """交互式配置管理"""
    manager = LLMConfigManager()
    
    while True:
        print("\n🤖 大模型配置管理器")
        print("=" * 40)
        print("1. 📋 查看当前配置")
        print("2. 🌟 查看所有提供商")
        print("3. 🔄 切换提供商")
        print("4. 🔑 更新API Key")
        print("5. ➕ 添加自定义提供商")
        print("6. 🧪 测试当前配置")
        print("7. 💾 导出配置")
        print("8. 📥 导入配置")
        print("9. 🚪 退出")
        print("=" * 40)
        
        choice = input("请选择操作 (1-9): ").strip()
        
        if choice == "1":
            manager.show_current_config()
        elif choice == "2":
            manager.show_all_providers()
        elif choice == "3":
            provider = input("请输入提供商名称 (ds-int8/qwen/deepseek/openai/claude): ").strip()
            manager.switch_provider(provider)
        elif choice == "4":
            provider = input("请输入提供商名称: ").strip()
            api_key = input("请输入新的API Key: ").strip()
            manager.update_api_key(provider, api_key)
        elif choice == "5":
            name = input("提供商名称: ").strip()
            model_name = input("模型名称: ").strip()
            base_url = input("Base URL: ").strip()
            api_key = input("API Key: ").strip()
            description = input("描述 (可选): ").strip()
            manager.add_custom_provider(name, model_name, base_url, api_key, description)
        elif choice == "6":
            manager.test_current_config()
        elif choice == "7":
            file_path = input("导出文件路径 (默认: llm_config_backup.json): ").strip()
            if not file_path:
                file_path = "llm_config_backup.json"
            manager.export_config(file_path)
        elif choice == "8":
            file_path = input("导入文件路径: ").strip()
            if os.path.exists(file_path):
                manager.import_config(file_path)
            else:
                print(f"❌ 文件不存在: {file_path}")
        elif choice == "9":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    print("🚀 启动大模型配置管理器")
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "show":
            manager = LLMConfigManager()
            manager.show_current_config()
        elif command == "list":
            manager = LLMConfigManager()
            manager.show_all_providers()
        elif command == "test":
            manager = LLMConfigManager()
            manager.test_current_config()
        elif command == "interactive":
            interactive_config_manager()
        else:
            print("❌ 未知命令")
            print("💡 可用命令: show, list, test, interactive")
    else:
        interactive_config_manager()
