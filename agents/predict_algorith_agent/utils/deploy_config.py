"""
部署配置模块

用于处理不同部署环境下的路径和配置问题。
支持开发环境、测试环境、生产环境的自动适配。
"""

import os
import sys
from pathlib import Path


def setup_environment():
    """
    设置运行环境，自动检测并配置项目路径
    
    支持的部署方式：
    1. 开发环境：从项目根目录运行
    2. Docker容器：设置PROJECT_ROOT环境变量
    3. Linux服务器：相对路径部署
    4. Windows服务器：绝对路径部署
    
    Returns:
        str: 项目根目录路径
    """
    
    # 获取当前文件的目录
    current_file = Path(__file__).resolve()
    current_dir = current_file.parent
    
    # 尝试多种可能的项目根目录位置
    possible_roots = [
        # 开发环境：agents/predict_algorith_agent/utils/deploy_config.py -> 项目根目录
        current_dir.parent.parent.parent,
        # 部署环境1：可能直接在agents目录下
        current_dir.parent.parent,
        # 部署环境2：可能在predict_algorith_agent目录下
        current_dir.parent,
        # 当前工作目录
        Path.cwd(),
    ]
    
    # 检查每个可能的根目录
    for root in possible_roots:
        root = root.resolve()
        
        # 检查是否存在关键的项目文件
        if (root / 'core' / 'response.py').exists():
            project_root = str(root)
            
            # 添加到Python路径
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            
            # 设置环境变量
            os.environ['PROJECT_ROOT'] = project_root
            
            print(f"[OK] 项目根目录设置为: {project_root}")
            return project_root
    
    # 如果自动检测失败，尝试使用环境变量
    if 'PROJECT_ROOT' in os.environ:
        project_root = os.environ['PROJECT_ROOT']
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        print(f"[OK] 使用环境变量PROJECT_ROOT: {project_root}")
        return project_root

    # 最后的备用方案
    fallback_root = str(Path.cwd())
    if fallback_root not in sys.path:
        sys.path.insert(0, fallback_root)
    os.environ['PROJECT_ROOT'] = fallback_root

    print(f"[WARNING] 使用备用根目录: {fallback_root}")
    print("建议设置PROJECT_ROOT环境变量指向正确的项目根目录")
    
    return fallback_root


def get_config_for_environment():
    """
    根据当前环境返回配置信息
    
    Returns:
        dict: 环境配置信息
    """
    
    # 检测运行环境
    is_docker = os.path.exists('/.dockerenv')
    is_windows = os.name == 'nt'
    is_linux = os.name == 'posix' and not is_docker
    
    config = {
        'is_docker': is_docker,
        'is_windows': is_windows,
        'is_linux': is_linux,
        'project_root': os.environ.get('PROJECT_ROOT', ''),
        'python_path': sys.path,
    }
    
    return config


def print_environment_info():
    """打印当前环境信息，用于调试"""
    
    config = get_config_for_environment()
    
    print("=" * 50)
    print("🔧 部署环境信息")
    print("=" * 50)
    print(f"操作系统: {'Windows' if config['is_windows'] else 'Linux/Unix'}")
    print(f"Docker环境: {'是' if config['is_docker'] else '否'}")
    print(f"项目根目录: {config['project_root']}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径数量: {len(config['python_path'])}")
    print("=" * 50)


# 自动执行环境设置
if __name__ == "__main__":
    setup_environment()
    print_environment_info()
else:
    # 模块被导入时自动设置环境
    setup_environment()
