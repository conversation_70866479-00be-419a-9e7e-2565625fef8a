"""
上下文管理器 - 负责对话上下文的构建、压缩和恢复
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from agents.predict_algorith_agent.database.database_manager import DatabaseManager
from agents.predict_algorith_agent.models.predictive_models import PredictiveAgentState

logger = logging.getLogger(__name__)

class ContextManager:
    """
    上下文管理器 - 处理对话上下文的智能管理
    """
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.max_context_tokens = 4000  # 最大上下文token数
        self.summary_threshold = 20     # 触发摘要的消息数量阈值
    
    def build_context(self, conversation_id: str, max_tokens: int = None) -> Dict[str, Any]:
        """
        构建对话上下文
        
        Args:
            conversation_id: 对话ID
            max_tokens: 最大token数量
            
        Returns:
            Dict: 构建的上下文信息
        """
        if max_tokens is None:
            max_tokens = self.max_context_tokens
            
        try:
            # 1. 获取对话基本信息
            try:
                conversation = self.db_manager.get_conversation_detail(conversation_id)
            except Exception as e:
                logger.warning(f"获取对话信息失败: {e}")
                conversation = None
            
            # 2. 获取历史消息
            messages = self.db_manager.get_conversation_messages_with_context(
                conversation_id, limit=100
            )
            
            # 3. 分层构建上下文
            context_layers = {
                'system_prompt': self._get_system_prompt(),
                'conversation_summary': self._get_conversation_summary(conversation_id),
                'key_parameters': self._extract_key_parameters(messages),
                'recent_messages': self._format_recent_messages(messages),
                'context_memory': self._get_context_memory(conversation_id)
            }
            
            # 4. 智能压缩
            compressed_context = self._compress_context(context_layers, max_tokens)
            
            # 5. 计算token数量并更新数据库
            total_tokens = self._count_total_tokens(compressed_context)
            self.db_manager.update_conversation_context_tokens(conversation_id, total_tokens)
            
            return {
                'conversation_id': conversation_id,
                'context_layers': compressed_context,
                'total_tokens': total_tokens,
                'message_count': len(messages),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"构建上下文失败: conversation_id={conversation_id}, error={e}")
            return self._create_empty_context()
    
    def rebuild_agent_state(self, conversation_id: str) -> PredictiveAgentState:
        """
        从历史消息重建智能体状态
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            PredictiveAgentState: 重建的智能体状态
        """
        try:
            # 1. 获取对话信息
            conversation = self.db_manager.get_conversation_detail(conversation_id)
            messages = self.db_manager.get_conversation_messages_with_context(conversation_id)
            
            # 2. 初始化状态
            state = PredictiveAgentState()
            state.is_new_conversation = False
            
            # 3. 从对话信息恢复基本状态
            if conversation:
                state.conversation_stage = conversation.get('current_stage', 'welcome')
                state.algorithm_type = conversation.get('algorithm_type')
                
            # 4. 从消息历史重建状态
            for message in sorted(messages, key=lambda x: x['message_sequence']):
                if message['message_type'] == 'assistant' and message['message_data']:
                    self._update_state_from_message(state, message['message_data'])
                    
                # 添加到历史记录
                state.history.append({
                    'type': message['message_type'],
                    'content': message['content'],
                    'timestamp': message['timestamp'].isoformat() if message['timestamp'] else None,
                    'interaction_type': message.get('interaction_type')
                })
            
            # 5. 设置最后交互类型
            if messages:
                last_message = messages[-1]
                state.last_interaction = last_message.get('interaction_type', 'unknown')
            
            logger.info(f"智能体状态重建成功: conversation_id={conversation_id}")
            return state
            
        except Exception as e:
            logger.error(f"重建智能体状态失败: conversation_id={conversation_id}, error={e}")
            return PredictiveAgentState()
    
    def create_context_summary(self, conversation_id: str, force: bool = False) -> Optional[str]:
        """
        创建对话摘要
        
        Args:
            conversation_id: 对话ID
            force: 是否强制创建摘要
            
        Returns:
            Optional[str]: 摘要ID
        """
        try:
            # 1. 检查是否需要创建摘要
            messages = self.db_manager.get_conversation_messages_with_context(conversation_id)
            
            if not force and len(messages) < self.summary_threshold:
                return None
            
            # 2. 生成摘要内容
            summary_content = self._generate_summary(messages)
            key_points = self._extract_key_points(messages)
            
            # 3. 保存摘要
            summary_id = self.db_manager.create_context_summary(
                conversation_id=conversation_id,
                summary_content=summary_content,
                key_points=key_points,
                message_count=len(messages)
            )
            
            logger.info(f"对话摘要创建成功: conversation_id={conversation_id}, summary_id={summary_id}")
            return summary_id
            
        except Exception as e:
            logger.error(f"创建对话摘要失败: conversation_id={conversation_id}, error={e}")
            return None
    
    def _create_empty_context(self) -> Dict[str, Any]:
        """创建空的上下文"""
        return {
            'conversation_id': None,
            'context_layers': {
                'system_prompt': self._get_system_prompt(),
                'conversation_summary': '',
                'key_parameters': {},
                'recent_messages': [],
                'context_memory': ''
            },
            'total_tokens': 0,
            'message_count': 0,
            'last_updated': datetime.now().isoformat()
        }
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的算法智能体，专门帮助用户进行机器学习算法的训练和优化。
你的主要功能包括：
1. 算法推荐和选择
2. 参数配置和优化
3. 训练过程监控
4. 结果分析和报告生成

请始终保持专业、友好的态度，并提供准确的技术指导。"""
    
    def _get_conversation_summary(self, conversation_id: str) -> str:
        """获取对话摘要"""
        summary = self.db_manager.get_latest_context_summary(conversation_id)
        if summary:
            return summary['summary_content']
        return ""
    
    def _extract_key_parameters(self, messages: List[Dict]) -> Dict[str, Any]:
        """从消息中提取关键参数"""
        key_params = {}
        
        for message in messages:
            if message['message_type'] == 'assistant' and message['message_data']:
                data = message['message_data']
                if isinstance(data, dict):
                    # 提取参数信息
                    if 'params' in data:
                        key_params.update(data['params'])
                    if 'task_id' in data:
                        key_params['task_id'] = data['task_id']
                    if 'state' in data and isinstance(data['state'], dict):
                        if 'current_params' in data['state']:
                            key_params.update(data['state']['current_params'])
        
        return key_params
    
    def _format_recent_messages(self, messages: List[Dict], max_messages: int = 10) -> List[Dict]:
        """格式化最近的消息"""
        # 取最近的消息
        recent = messages[-max_messages:] if len(messages) > max_messages else messages
        
        formatted = []
        for msg in recent:
            formatted.append({
                'type': msg['message_type'],
                'content': msg['content'],
                'timestamp': msg['timestamp'].isoformat() if msg['timestamp'] else None,
                'sequence': msg['message_sequence']
            })
        
        return formatted
    
    def _get_context_memory(self, conversation_id: str) -> str:
        """获取上下文记忆"""
        # 这里可以实现更复杂的记忆机制
        # 目前返回空字符串，后续可以扩展
        return ""
    
    def _compress_context(self, context_layers: Dict, max_tokens: int) -> Dict[str, Any]:
        """智能压缩上下文"""
        # 优先级排序
        priority_order = [
            'system_prompt',      # 最高优先级
            'key_parameters',     # 关键参数
            'recent_messages',    # 最近消息
            'conversation_summary', # 对话摘要
            'context_memory'      # 历史记忆
        ]
        
        final_context = {}
        used_tokens = 0
        
        for layer_name in priority_order:
            layer_content = context_layers[layer_name]
            layer_tokens = self._count_tokens(layer_content)
            
            if used_tokens + layer_tokens <= max_tokens:
                final_context[layer_name] = layer_content
                used_tokens += layer_tokens
            else:
                # 部分添加或智能摘要
                remaining_tokens = max_tokens - used_tokens
                if remaining_tokens > 100:  # 最小有效长度
                    compressed_content = self._smart_compress(layer_content, remaining_tokens)
                    final_context[layer_name] = compressed_content
                    used_tokens += remaining_tokens
                break
        
        return final_context
    
    def _count_tokens(self, content: Any) -> int:
        """计算内容的token数量（简单估算）"""
        if isinstance(content, str):
            return int(len(content.split()) * 1.3)
        elif isinstance(content, (list, dict)):
            return int(len(str(content)) * 0.3)
        else:
            return int(len(str(content)) * 0.5)
    
    def _count_total_tokens(self, context: Dict) -> int:
        """计算总token数量"""
        total = 0
        for content in context.values():
            total += self._count_tokens(content)
        return total
    
    def _smart_compress(self, content: Any, max_tokens: int) -> Any:
        """智能压缩内容"""
        if isinstance(content, str):
            # 简单截断，保留前面的内容
            words = content.split()
            target_words = int(max_tokens / 1.3)
            if len(words) > target_words:
                return ' '.join(words[:target_words]) + '...'
            return content
        elif isinstance(content, list):
            # 保留最重要的项目
            if len(content) > max_tokens // 10:
                return content[-(max_tokens // 10):]
            return content
        else:
            return content
    
    def _update_state_from_message(self, state: PredictiveAgentState, message_data: Dict):
        """从消息数据更新状态"""
        if not isinstance(message_data, dict):
            return
            
        # 更新任务相关信息
        if 'task_id' in message_data:
            state.task_id = message_data['task_id']
        
        # 更新状态信息
        if 'state' in message_data and isinstance(message_data['state'], dict):
            state_data = message_data['state']
            
            # 更新参数
            if 'current_params' in state_data:
                state.current_params.update(state_data['current_params'])
            
            if 'missing_params' in state_data:
                state.missing_params = state_data['missing_params']
            
            if 'awaiting_confirmation' in state_data:
                state.awaiting_confirmation = state_data['awaiting_confirmation']
            
            if 'algorithm_type' in state_data:
                state.algorithm_type = state_data['algorithm_type']
            
            if 'task_status' in state_data:
                state.task_status = state_data['task_status']

            # 恢复历史算法相关状态
            if 'awaiting_algorithm_choice' in state_data:
                state.awaiting_algorithm_choice = state_data['awaiting_algorithm_choice']

            if 'recommended_algorithms' in state_data:
                state.recommended_algorithms = state_data['recommended_algorithms']

            if 'history_search_completed' in state_data:
                state.history_search_completed = state_data['history_search_completed']

            if 'selected_algorithm_id' in state_data:
                state.selected_algorithm_id = state_data['selected_algorithm_id']

            if 'conversation_stage' in state_data:
                state.conversation_stage = state_data['conversation_stage']
    
    def _generate_summary(self, messages: List[Dict]) -> str:
        """生成对话摘要"""
        # 简单的摘要生成逻辑
        user_messages = [msg for msg in messages if msg['message_type'] == 'user']
        assistant_messages = [msg for msg in messages if msg['message_type'] == 'assistant']
        
        summary_parts = []
        
        if user_messages:
            summary_parts.append(f"用户共发送了{len(user_messages)}条消息")
            
        if assistant_messages:
            summary_parts.append(f"智能体回复了{len(assistant_messages)}次")
        
        # 提取关键词
        all_content = ' '.join([msg['content'] for msg in messages])
        if 'LSTM' in all_content:
            summary_parts.append("讨论了LSTM算法")
        if '学习率' in all_content:
            summary_parts.append("涉及学习率参数")
        if '批次大小' in all_content:
            summary_parts.append("涉及批次大小参数")
        
        return '；'.join(summary_parts) + '。'
    
    def _extract_key_points(self, messages: List[Dict]) -> Dict[str, Any]:
        """提取关键要点"""
        key_points = {
            'algorithms_mentioned': [],
            'parameters_discussed': [],
            'tasks_created': [],
            'main_topics': []
        }
        
        for message in messages:
            content = message['content'].lower()
            
            # 提取算法类型
            if 'lstm' in content:
                key_points['algorithms_mentioned'].append('LSTM')
            if 'cnn' in content:
                key_points['algorithms_mentioned'].append('CNN')
            
            # 提取参数
            if '学习率' in content:
                key_points['parameters_discussed'].append('learning_rate')
            if '批次大小' in content:
                key_points['parameters_discussed'].append('batch_size')
            
            # 提取任务ID
            if message['message_data'] and isinstance(message['message_data'], dict):
                if 'task_id' in message['message_data']:
                    key_points['tasks_created'].append(message['message_data']['task_id'])
        
        # 去重
        for key in key_points:
            if isinstance(key_points[key], list):
                key_points[key] = list(set(key_points[key]))
        
        return key_points
