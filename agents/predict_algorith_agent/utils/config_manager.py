#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一配置管理模块
"""

import os
from typing import Dict, Any, Optional, Union
from pathlib import Path
from pydantic import BaseModel, Field
from enum import Enum

class Environment(str, Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

class DatabaseConfig(BaseModel):
    """数据库配置"""
    db_path: str = Field(default="data/conversations.db", description="数据库文件路径")
    connection_timeout: int = Field(default=30, description="连接超时时间（秒）")
    max_connections: int = Field(default=10, description="最大连接数")
    enable_wal_mode: bool = Field(default=True, description="启用WAL模式")

class LLMConfig(BaseModel):
    """LLM配置"""
    api_key: str = Field(..., description="API密钥")
    base_url: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1", description="API基础URL")
    model_name: str = Field(default="qwen-turbo-latest", description="模型名称")
    max_tokens: int = Field(default=2000, description="最大token数")
    temperature: float = Field(default=0.7, description="温度参数")
    timeout: int = Field(default=60, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    retry_delay: float = Field(default=1.0, description="重试延迟（秒）")

class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8008, description="服务器端口")
    debug: bool = Field(default=False, description="调试模式")
    reload: bool = Field(default=False, description="自动重载")
    workers: int = Field(default=1, description="工作进程数")
    log_level: str = Field(default="info", description="日志级别")

class WebSocketConfig(BaseModel):
    """WebSocket配置"""
    max_connections: int = Field(default=100, description="最大连接数")
    heartbeat_interval: int = Field(default=30, description="心跳间隔（秒）")
    message_queue_size: int = Field(default=1000, description="消息队列大小")
    connection_timeout: int = Field(default=300, description="连接超时时间（秒）")

class SecurityConfig(BaseModel):
    """安全配置"""
    enable_cors: bool = Field(default=True, description="启用CORS")
    allowed_origins: list = Field(default=["*"], description="允许的源")
    allowed_methods: list = Field(default=["GET", "POST"], description="允许的HTTP方法")
    allowed_headers: list = Field(default=["*"], description="允许的请求头")
    rate_limit_enabled: bool = Field(default=False, description="启用速率限制")
    rate_limit_requests: int = Field(default=100, description="速率限制请求数")
    rate_limit_window: int = Field(default=60, description="速率限制时间窗口（秒）")

class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    enable_file_logging: bool = Field(default=False, description="启用文件日志")
    log_file_path: str = Field(default="logs/app.log", description="日志文件路径")
    max_file_size: int = Field(default=10485760, description="最大文件大小（字节）")
    backup_count: int = Field(default=5, description="备份文件数量")
    enable_colors: bool = Field(default=True, description="启用彩色日志")

class ReportConfig(BaseModel):
    """报告配置"""
    template_dir: str = Field(default="templates", description="模板目录")
    output_dir: str = Field(default="reports", description="输出目录")
    pdf_engine: str = Field(default="wkhtmltopdf", description="PDF引擎")
    enable_caching: bool = Field(default=True, description="启用缓存")
    cache_ttl: int = Field(default=3600, description="缓存TTL（秒）")

class AppConfig(BaseModel):
    """应用程序配置"""
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="运行环境")
    app_name: str = Field(default="算法生成智能体", description="应用名称")
    version: str = Field(default="1.0.0", description="版本号")
    debug: bool = Field(default=False, description="调试模式")
    
    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    llm: LLMConfig = Field(..., description="LLM配置")
    server: ServerConfig = Field(default_factory=ServerConfig)
    websocket: WebSocketConfig = Field(default_factory=WebSocketConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    report: ReportConfig = Field(default_factory=ReportConfig)

class ConfigManager:
    """配置管理器"""
    
    _instance: Optional['ConfigManager'] = None
    _config: Optional[AppConfig] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._config = self._load_config()
    
    def _load_config(self) -> AppConfig:
        """加载配置"""
        
        # 从环境变量获取基础配置
        env = os.getenv("ENVIRONMENT", "development")
        debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # LLM配置（必需）
        llm_api_key = os.getenv("LLM_API_KEY", "sk-b43e1b0d51e24f359b1d3c6342149577")
        if not llm_api_key:
            raise ValueError("LLM_API_KEY环境变量未设置")
        
        # 构建配置
        config_data = {
            "environment": env,
            "debug": debug,
            "llm": {
                "api_key": llm_api_key,
                "base_url": os.getenv("LLM_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                "model_name": os.getenv("LLM_MODEL_NAME", "qwen-turbo-latest"),
                "max_tokens": int(os.getenv("LLM_MAX_TOKENS", "2000")),
                "temperature": float(os.getenv("LLM_TEMPERATURE", "0.7")),
                "timeout": int(os.getenv("LLM_TIMEOUT", "60")),
            },
            "server": {
                "host": os.getenv("SERVER_HOST", "0.0.0.0"),
                "port": int(os.getenv("SERVER_PORT", "8008")),
                "debug": debug,
                "reload": os.getenv("SERVER_RELOAD", "false").lower() == "true",
                "log_level": os.getenv("LOG_LEVEL", "info"),
            },
            "database": {
                "db_path": os.getenv("DB_PATH", "data/conversations.db"),
                "connection_timeout": int(os.getenv("DB_TIMEOUT", "30")),
            },
            "logging": {
                "level": os.getenv("LOG_LEVEL", "INFO").upper(),
                "enable_file_logging": os.getenv("ENABLE_FILE_LOGGING", "false").lower() == "true",
                "log_file_path": os.getenv("LOG_FILE_PATH", "logs/app.log"),
            },
            "security": {
                "enable_cors": os.getenv("ENABLE_CORS", "true").lower() == "true",
                "allowed_origins": os.getenv("ALLOWED_ORIGINS", "*").split(","),
                "rate_limit_enabled": os.getenv("RATE_LIMIT_ENABLED", "false").lower() == "true",
            }
        }
        
        return AppConfig(**config_data)
    
    @property
    def config(self) -> AppConfig:
        """获取配置"""
        return self._config
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        return self._config.database
    
    def get_llm_config(self) -> LLMConfig:
        """获取LLM配置"""
        return self._config.llm
    
    def get_server_config(self) -> ServerConfig:
        """获取服务器配置"""
        return self._config.server
    
    def get_websocket_config(self) -> WebSocketConfig:
        """获取WebSocket配置"""
        return self._config.websocket
    
    def get_security_config(self) -> SecurityConfig:
        """获取安全配置"""
        return self._config.security
    
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        return self._config.logging
    
    def get_report_config(self) -> ReportConfig:
        """获取报告配置"""
        return self._config.report
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self._config.environment == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self._config.environment == Environment.PRODUCTION
    
    def is_debug(self) -> bool:
        """是否为调试模式"""
        return self._config.debug
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        config_dict = self._config.dict()
        
        # 递归更新配置
        def update_nested_dict(d: dict, updates: dict):
            for key, value in updates.items():
                if key in d and isinstance(d[key], dict) and isinstance(value, dict):
                    update_nested_dict(d[key], value)
                else:
                    d[key] = value
        
        update_nested_dict(config_dict, kwargs)
        self._config = AppConfig(**config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self._config.dict()
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return self._config.json(indent=2)

# 全局配置管理器实例
config_manager = ConfigManager()

# 便捷的配置获取函数
def get_config() -> AppConfig:
    """获取应用配置"""
    return config_manager.config

def get_database_config() -> DatabaseConfig:
    """获取数据库配置"""
    return config_manager.get_database_config()

def get_llm_config() -> LLMConfig:
    """获取LLM配置"""
    return config_manager.get_llm_config()

def get_server_config() -> ServerConfig:
    """获取服务器配置"""
    return config_manager.get_server_config()

def get_websocket_config() -> WebSocketConfig:
    """获取WebSocket配置"""
    return config_manager.get_websocket_config()

def get_security_config() -> SecurityConfig:
    """获取安全配置"""
    return config_manager.get_security_config()

def get_logging_config() -> LoggingConfig:
    """获取日志配置"""
    return config_manager.get_logging_config()

def get_report_config() -> ReportConfig:
    """获取报告配置"""
    return config_manager.get_report_config()

# 环境检查函数
def is_development() -> bool:
    """是否为开发环境"""
    return config_manager.is_development()

def is_production() -> bool:
    """是否为生产环境"""
    return config_manager.is_production()

def is_debug() -> bool:
    """是否为调试模式"""
    return config_manager.is_debug()

# 使用示例
if __name__ == "__main__":
    # 获取配置
    config = get_config()
    print(f"应用名称: {config.app_name}")
    print(f"运行环境: {config.environment}")
    print(f"服务器端口: {config.server.port}")
    print(f"数据库路径: {config.database.db_path}")
    
    # 输出完整配置
    print("\n完整配置:")
    print(config_manager.to_json())
