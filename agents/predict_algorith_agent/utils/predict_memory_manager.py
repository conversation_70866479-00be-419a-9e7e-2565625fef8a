from agents.utils.redis_helper import get_session_state, set_session_state
from agents.utils.mysql_helper import save_session_state, load_session_state

class AgentMemoryManager:
    def get_state(self, user_id, session_id):
        state = get_session_state(user_id, session_id)
        if state is None:
            # 回源MySQL
            task_id, state = load_session_state(user_id, session_id)
            if state:
                set_session_state(user_id, session_id, state)
        return state

    def save_state(self, user_id, session_id, task_id, state):
        set_session_state(user_id, session_id, state)
        save_session_state(user_id, session_id, task_id, state) 