#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一日志配置模块 - 优化版本
支持环境变量配置、文件日志、日志轮转等功能
"""

import logging
import logging.handlers
import sys
import os
from datetime import datetime
from typing import Optional
from pathlib import Path

# 环境变量配置加载
def load_env_config():
    """加载环境变量配置"""
    config = {}

    # 尝试加载.env文件
    env_file = Path(__file__).parent.parent / '.env'
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()

    # 读取配置
    config['log_level'] = os.getenv('LOG_LEVEL', 'INFO').upper()
    config['enable_file_logging'] = os.getenv('ENABLE_FILE_LOGGING', 'false').lower() == 'true'
    config['log_file_path'] = os.getenv('LOG_FILE_PATH', 'logs/agent.log')
    config['log_file_max_size'] = int(os.getenv('LOG_FILE_MAX_SIZE', '100'))
    config['log_file_backup_count'] = int(os.getenv('LOG_FILE_BACKUP_COUNT', '5'))
    config['enable_color_logging'] = os.getenv('ENABLE_COLOR_LOGGING', 'true').lower() == 'true'
    config['enable_verbose_logging'] = os.getenv('ENABLE_VERBOSE_LOGGING', 'false').lower() == 'true'
    config['enable_performance_logging'] = os.getenv('ENABLE_PERFORMANCE_LOGGING', 'false').lower() == 'true'
    config['production_mode'] = os.getenv('PRODUCTION_MODE', 'false').lower() == 'true'
    config['debug_mode'] = os.getenv('DEBUG_MODE', 'false').lower() == 'true'

    # 生产环境自动优化
    if config['production_mode']:
        config['log_level'] = 'WARNING'
        config['enable_file_logging'] = True
        config['enable_performance_logging'] = False
        config['enable_color_logging'] = False

    return config

# 全局配置
ENV_CONFIG = load_env_config()

class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""

    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }

    def format(self, record):
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']

        # 格式化消息
        formatted = super().format(record)

        # 添加颜色（仅在终端输出时且启用颜色时）
        if (ENV_CONFIG['enable_color_logging'] and
            hasattr(sys.stderr, 'isatty') and sys.stderr.isatty()):
            return f"{color}{formatted}{reset}"
        else:
            return formatted

def setup_file_logging(logger: logging.Logger, log_file_path: str):
    """设置文件日志"""
    if not ENV_CONFIG['enable_file_logging']:
        return

    # 确保日志目录存在
    log_dir = Path(log_file_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # 创建轮转文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_file_path,
        maxBytes=ENV_CONFIG['log_file_max_size'] * 1024 * 1024,  # MB转字节
        backupCount=ENV_CONFIG['log_file_backup_count'],
        encoding='utf-8'
    )

    # 文件日志使用简洁格式
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(getattr(logging, ENV_CONFIG['log_level']))

    logger.addHandler(file_handler)

def setup_logging(
    name: str,
    level: Optional[str] = None,
    format_string: Optional[str] = None,
    use_colors: Optional[bool] = None
) -> logging.Logger:
    """
    设置统一的日志配置

    Args:
        name: 日志器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)，None时使用环境配置
        format_string: 自定义格式字符串
        use_colors: 是否使用颜色输出，None时使用环境配置

    Returns:
        logging.Logger: 配置好的日志器
    """

    # 使用环境配置或传入参数
    if level is None:
        level = ENV_CONFIG['log_level']
    if use_colors is None:
        use_colors = ENV_CONFIG['enable_color_logging']

    # 默认格式字符串
    if format_string is None:
        if ENV_CONFIG['enable_verbose_logging']:
            format_string = (
                "%(asctime)s - %(name)s - %(levelname)s - "
                "%(filename)s:%(lineno)d - %(funcName)s - %(message)s"
            )
        else:
            format_string = (
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )

    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))

    # 选择格式化器
    if use_colors:
        formatter = ColoredFormatter(format_string)
    else:
        formatter = logging.Formatter(format_string)

    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 设置文件日志
    setup_file_logging(logger, ENV_CONFIG['log_file_path'])

    return logger



# 预定义的日志器配置
def get_main_logger() -> logging.Logger:
    """获取主应用日志器"""
    level = os.getenv('MAIN_LOG_LEVEL', ENV_CONFIG['log_level'])
    return setup_logging(
        name="predict_main",
        level=level,
        format_string="%(asctime)s - [MAIN] - %(levelname)s - %(message)s"
    )

def get_agent_logger() -> logging.Logger:
    """获取算法智能体日志器"""
    level = os.getenv('AGENT_LOG_LEVEL', ENV_CONFIG['log_level'])
    return setup_logging(
        name="predictive_agent",
        level=level,
        format_string="%(asctime)s - [AGENT] - %(levelname)s - %(message)s"
    )

def get_database_logger() -> logging.Logger:
    """获取数据库日志器"""
    level = os.getenv('DATABASE_LOG_LEVEL', 'WARNING')
    return setup_logging(
        name="database_manager",
        level=level,
        format_string="%(asctime)s - [DB] - %(levelname)s - %(message)s"
    )

def get_conversation_logger() -> logging.Logger:
    """获取对话管理日志器"""
    level = os.getenv('CONVERSATION_LOG_LEVEL', ENV_CONFIG['log_level'])
    return setup_logging(
        name="conversation_routes",
        level=level,
        format_string="%(asctime)s - [CONV] - %(levelname)s - %(message)s"
    )

def get_websocket_logger() -> logging.Logger:
    """获取WebSocket日志器"""
    level = os.getenv('WEBSOCKET_LOG_LEVEL', ENV_CONFIG['log_level'])
    return setup_logging(
        name="websocket_manager",
        level=level,
        format_string="%(asctime)s - [WS] - %(levelname)s - %(message)s"
    )

def get_api_logger() -> logging.Logger:
    """获取API服务日志器"""
    level = os.getenv('API_LOG_LEVEL', ENV_CONFIG['log_level'])
    return setup_logging(
        name="api_service",
        level=level,
        format_string="%(asctime)s - [API] - %(levelname)s - %(message)s"
    )

# 全局日志配置
def configure_global_logging(level: Optional[str] = None) -> None:
    """
    配置全局日志设置

    Args:
        level: 全局日志级别，None时使用环境配置
    """

    if level is None:
        level = ENV_CONFIG['log_level']

    # 设置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加统一的控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))

    # 选择格式化器
    if ENV_CONFIG['enable_verbose_logging']:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    else:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    formatter = ColoredFormatter(format_string)
    console_handler.setFormatter(formatter)

    root_logger.addHandler(console_handler)

    # 设置文件日志
    if ENV_CONFIG['enable_file_logging']:
        # 确保日志目录存在
        log_dir = Path(ENV_CONFIG['log_file_path']).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            ENV_CONFIG['log_file_path'],
            maxBytes=ENV_CONFIG['log_file_max_size'] * 1024 * 1024,  # MB转字节
            backupCount=ENV_CONFIG['log_file_backup_count'],
            encoding='utf-8'
        )

        # 文件日志使用简洁格式
        file_formatter = logging.Formatter(format_string)
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(getattr(logging, level.upper()))

        root_logger.addHandler(file_handler)

    # 配置第三方库的日志级别
    third_party_configs = {
        "uvicorn": os.getenv('UVICORN_LOG_LEVEL', 'WARNING'),
        "uvicorn.access": os.getenv('UVICORN_ACCESS_LOG_LEVEL', 'ERROR'),
        "fastapi": os.getenv('FASTAPI_LOG_LEVEL', 'WARNING'),
        "pydantic_ai": os.getenv('PYDANTIC_AI_LOG_LEVEL', 'ERROR'),
        "httpx": os.getenv('HTTPX_LOG_LEVEL', 'ERROR'),
        "asyncio": os.getenv('ASYNCIO_LOG_LEVEL', 'ERROR'),
    }

    for logger_name, log_level in third_party_configs.items():
        logging.getLogger(logger_name).setLevel(getattr(logging, log_level.upper()))

# 日志装饰器
def log_function_call(logger: logging.Logger, enable_debug: bool = None):
    """
    函数调用日志装饰器

    Args:
        logger: 日志器实例
        enable_debug: 是否启用调试日志，None时使用环境配置
    """
    if enable_debug is None:
        enable_debug = ENV_CONFIG['debug_mode']

    def decorator(func):
        def wrapper(*args, **kwargs):
            if enable_debug:
                logger.debug(f"调用函数: {func.__name__}")
            try:
                result = func(*args, **kwargs)
                if enable_debug:
                    logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator

# 性能监控日志
def log_performance(logger: logging.Logger, operation: str):
    """
    性能监控日志上下文管理器

    Args:
        logger: 日志器实例
        operation: 操作名称
    """
    class PerformanceLogger:
        def __init__(self, logger, operation):
            self.logger = logger
            self.operation = operation
            self.start_time = None
            self.enable_performance_logging = ENV_CONFIG['enable_performance_logging']

        def __enter__(self):
            if self.enable_performance_logging:
                self.start_time = datetime.now()
                self.logger.debug(f"开始执行: {self.operation}")
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.enable_performance_logging and self.start_time:
                end_time = datetime.now()
                duration = (end_time - self.start_time).total_seconds()

                if exc_type is None:
                    self.logger.debug(f"完成执行: {self.operation} (耗时: {duration:.3f}秒)")
                else:
                    self.logger.error(f"执行失败: {self.operation} (耗时: {duration:.3f}秒) - {exc_val}")
            return False  # 不抑制异常

    return PerformanceLogger(logger, operation)

# 便捷函数
def should_log_debug() -> bool:
    """检查是否应该记录DEBUG级别日志"""
    return ENV_CONFIG['debug_mode'] or ENV_CONFIG['log_level'] == 'DEBUG'

def should_log_performance() -> bool:
    """检查是否应该记录性能日志"""
    return ENV_CONFIG['enable_performance_logging']

def get_log_config_summary() -> dict:
    """获取当前日志配置摘要"""
    return {
        'log_level': ENV_CONFIG['log_level'],
        'production_mode': ENV_CONFIG['production_mode'],
        'debug_mode': ENV_CONFIG['debug_mode'],
        'enable_file_logging': ENV_CONFIG['enable_file_logging'],
        'enable_color_logging': ENV_CONFIG['enable_color_logging'],
        'enable_verbose_logging': ENV_CONFIG['enable_verbose_logging'],
        'enable_performance_logging': ENV_CONFIG['enable_performance_logging'],
        'log_file_path': ENV_CONFIG['log_file_path'] if ENV_CONFIG['enable_file_logging'] else None
    }

# 使用示例
if __name__ == "__main__":
    # 配置全局日志
    configure_global_logging("DEBUG")
    
    # 获取不同模块的日志器
    main_logger = get_main_logger()
    agent_logger = get_agent_logger()
    db_logger = get_database_logger()
    
    # 测试日志输出
    main_logger.info("主应用启动")
    agent_logger.debug("算法智能体初始化")
    db_logger.warning("数据库连接警告")
    
    # 测试性能监控
    with log_performance(main_logger, "测试操作"):
        import time
        time.sleep(0.1)
    
    print("日志配置测试完成")
