"""
多LLM提供商管理器
支持多个LLM提供商的统一调用和管理
"""

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from typing import Dict, List, Any, Optional
import logging
import asyncio
import time
from agents.predict_algorith_agent.models.parameter_recommendation_models import (
    LLMProvider, LLMProviderConfig, ParameterRecommendationRequest,
    ParameterRecommendationResponse, ParameterRecommendation
)

logger = logging.getLogger(__name__)


class LLMProviderManager:
    """多LLM提供商管理器"""
    
    def __init__(self):
        """初始化LLM提供商管理器"""
        self.providers: Dict[LLMProvider, LLMProviderConfig] = {}
        self.agents: Dict[LLMProvider, Agent] = {}
        self._initialize_default_providers()
    
    def _initialize_default_providers(self):
        """初始化默认的LLM提供商配置"""
        # DeepSeek配置
        deepseek_config = LLMProviderConfig(
            provider=LLMProvider.DEEPSEEK,
            model_name="deepseek-chat",
            api_key="***********************************",
            base_url="https://api.deepseek.com",
            max_tokens=2000,
            temperature=0.7
        )
        
        # 通义千问配置
        qwen_config = LLMProviderConfig(
            provider=LLMProvider.QWEN,
            model_name="qwen-turbo-latest",
            api_key="sk-b43e1b0d51e24f359b1d3c6342149577",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            max_tokens=2000,
            temperature=0.7
        )
        
        # 注册默认提供商
        self.register_provider(deepseek_config)
        self.register_provider(qwen_config)
    
    def register_provider(self, config: LLMProviderConfig):
        """注册LLM提供商"""
        try:
            self.providers[config.provider] = config
            
            # 创建对应的Agent
            model = OpenAIModel(
                config.model_name,
                provider=OpenAIProvider(
                    base_url=config.base_url,
                    api_key=config.api_key
                )
            )
            
            agent = Agent(
                model=model,
                output_type=ParameterRecommendationResponse,
                system_prompt=self._get_parameter_recommendation_prompt()
            )
            
            self.agents[config.provider] = agent
            logger.info(f"成功注册LLM提供商: {config.provider.value}")
            
        except Exception as e:
            logger.error(f"注册LLM提供商失败 {config.provider.value}: {e}")
    
    def _get_parameter_recommendation_prompt(self) -> str:
        """获取参数推荐的系统提示词"""
        return """
你是一个专业的机器学习参数推荐专家。你的任务是根据用户的算法需求和数据特征，推荐最优的算法参数配置。

请根据以下信息进行参数推荐：
1. 算法类型和特点
2. 数据规模和特征
3. 性能目标和约束条件
4. 当前已有的参数设置

对于每个推荐的参数，请提供：
- 推荐值和合理范围
- 详细的推荐理由
- 参数对模型性能的影响
- 调优建议和注意事项

请确保推荐的参数配置：
- 符合算法的理论基础
- 适合给定的数据规模
- 平衡性能和效率
- 具有实际可操作性

输出格式必须严格按照ParameterRecommendationResponse模型的结构。
"""
    
    def get_available_providers(self) -> List[LLMProvider]:
        """获取可用的LLM提供商列表"""
        return list(self.providers.keys())
    
    async def get_parameter_recommendation(
        self, 
        request: ParameterRecommendationRequest,
        provider: LLMProvider = LLMProvider.QWEN
    ) -> ParameterRecommendationResponse:
        """从指定提供商获取参数推荐"""
        if provider not in self.agents:
            raise ValueError(f"未注册的LLM提供商: {provider.value}")
        
        try:
            start_time = time.time()
            
            # 构建推荐请求的详细描述
            request_description = self._build_request_description(request)
            
            # 调用LLM获取推荐
            agent = self.agents[provider]
            result = await agent.run(request_description)
            
            generation_time = time.time() - start_time
            
            # 更新响应中的元信息
            response = result.output
            response.llm_provider = provider
            response.model_name = self.providers[provider].model_name
            response.generation_time = generation_time
            response.algorithm_type = request.algorithm_type
            
            logger.info(f"成功获取参数推荐 - 提供商: {provider.value}, 耗时: {generation_time:.2f}s")
            return response
            
        except Exception as e:
            logger.error(f"获取参数推荐失败 - 提供商: {provider.value}, 错误: {e}")
            # 返回默认推荐
            return self._get_fallback_recommendation(request, provider)
    
    async def get_multi_llm_recommendations(
        self, 
        request: ParameterRecommendationRequest,
        providers: Optional[List[LLMProvider]] = None
    ) -> List[ParameterRecommendationResponse]:
        """从多个LLM提供商获取参数推荐"""
        if providers is None:
            providers = self.get_available_providers()
        
        # 并发调用多个LLM
        tasks = []
        for provider in providers:
            if provider in self.agents:
                task = self.get_parameter_recommendation(request, provider)
                tasks.append(task)
        
        if not tasks:
            raise ValueError("没有可用的LLM提供商")
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤出成功的结果
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, ParameterRecommendationResponse):
                successful_results.append(result)
            else:
                logger.error(f"LLM提供商 {providers[i].value} 调用失败: {result}")
        
        return successful_results
    
    def _build_request_description(self, request: ParameterRecommendationRequest) -> str:
        """构建推荐请求的详细描述"""
        description = f"""
请为以下机器学习任务推荐最优的参数配置：

算法类型: {request.algorithm_type}
数据规模: {request.data_scale.value}
性能目标: {request.performance_objective.value}

数据特征:
{self._format_dict(request.data_features)}

约束条件:
{self._format_dict(request.constraints)}

当前已有参数:
{self._format_dict(request.current_params)}

用户偏好:
{self._format_dict(request.user_preferences)}

额外上下文:
{request.context}

请根据以上信息，推荐最优的参数配置，并提供详细的推理过程和调优建议。
"""
        return description.strip()
    
    def _format_dict(self, data: Dict[str, Any]) -> str:
        """格式化字典数据为可读字符串"""
        if not data:
            return "无"
        
        formatted = []
        for key, value in data.items():
            formatted.append(f"- {key}: {value}")
        return "\n".join(formatted)
    
    def _get_fallback_recommendation(
        self, 
        request: ParameterRecommendationRequest,
        provider: LLMProvider
    ) -> ParameterRecommendationResponse:
        """获取回退的默认参数推荐"""
        from .parameter_recommendation_models import ALGORITHM_PARAMETER_TEMPLATES
        
        # 获取算法模板
        template = ALGORITHM_PARAMETER_TEMPLATES.get(request.algorithm_type)
        if not template:
            # 如果没有模板，返回基础推荐
            return ParameterRecommendationResponse(
                algorithm_type=request.algorithm_type,
                recommended_parameters=[
                    ParameterRecommendation(
                        parameter_name="learning_rate",
                        recommended_value=0.001,
                        confidence=0.5,
                        reasoning="默认学习率，适用于大多数情况",
                        impact_description="控制模型参数更新的步长"
                    )
                ],
                overall_confidence=0.5,
                llm_provider=provider,
                model_name="fallback",
                generation_time=0.0,
                optimization_suggestions=["建议根据实际数据调整参数"]
            )
        
        # 基于模板生成推荐
        recommendations = []
        for param_name in template.required_parameters + template.optional_parameters:
            if param_name in template.parameter_defaults:
                default_value = template.parameter_defaults[param_name]
                description = template.parameter_descriptions.get(param_name, f"{param_name}参数")
                
                recommendations.append(ParameterRecommendation(
                    parameter_name=param_name,
                    recommended_value=default_value,
                    confidence=0.7,
                    reasoning=f"基于{request.algorithm_type}算法的默认配置",
                    impact_description=description
                ))
        
        return ParameterRecommendationResponse(
            algorithm_type=request.algorithm_type,
            recommended_parameters=recommendations,
            overall_confidence=0.7,
            llm_provider=provider,
            model_name="fallback",
            generation_time=0.0,
            optimization_suggestions=[
                "建议根据数据规模调整batch_size",
                "建议根据模型复杂度调整learning_rate",
                "建议通过验证集调优关键参数"
            ]
        )


# 全局LLM提供商管理器实例
llm_provider_manager = LLMProviderManager()
