#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
对话管理路由
实现对话的创建、查询、更新、删除等功能
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import Optional
import math
import logging
from datetime import datetime
from pydantic import BaseModel, Field

import sys
import os

# 动态添加项目根目录到Python路径（兼容不同部署环境）
def setup_project_path():
    """设置项目路径，兼容不同的部署环境"""
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 尝试多种可能的项目根目录位置
    possible_roots = [
        os.path.abspath(os.path.join(current_dir, '../../..')),  # 开发环境
        os.path.abspath(os.path.join(current_dir, '../..')),     # 可能的部署环境1
        os.path.abspath(os.path.join(current_dir, '..')),        # 可能的部署环境2
        os.getcwd(),  # 当前工作目录
    ]

    for root in possible_roots:
        if os.path.exists(os.path.join(root, 'core', 'response.py')):
            if root not in sys.path:
                sys.path.insert(0, root)
            return root

    # 如果都找不到，使用环境变量
    if 'PROJECT_ROOT' in os.environ:
        project_root = os.environ['PROJECT_ROOT']
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        return project_root

    return None

# 设置项目路径
project_root = setup_project_path()

try:
    from core.response import Response
except ImportError as e:
    print(f"Warning: Could not import core.response: {e}")
    # 创建一个兼容的Response类作为备用
    from typing import TypeVar, Generic, Optional, Any
    from pydantic import BaseModel

    T = TypeVar('T')

    class Response(BaseModel, Generic[T]):
        data: Optional[Any] = None
        message: str = ""
        success: bool = True

        class Config:
            arbitrary_types_allowed = True

        def __class_getitem__(cls, item):
            return cls

# 导入优化的日志配置
from agents.predict_algorith_agent.utils.logging_config import get_conversation_logger, should_log_debug

# 配置日志
logger = get_conversation_logger()
from agents.predict_algorith_agent.models.predictive_models import (
    CreateConversationRequest, ConversationSummary, ConversationDetail,
    ConversationListResponse, ConversationDetailResponse, ConversationMessage,
    ConversationType, ConversationStatus, ConversationStage
)
from agents.predict_algorith_agent.database.database_manager import DatabaseManager

# 更新对话状态请求模型
class UpdateConversationStatusRequest(BaseModel):
    status: ConversationStatus = Field(description="新状态")
    progress: Optional[float] = Field(None, description="进度百分比")
    algorithm_type: Optional[str] = Field(None, description="算法类型")
    task_summary: Optional[str] = Field(None, description="任务摘要")

router = APIRouter()

# 数据库管理器实例
db_manager = DatabaseManager()

# 记录路由注册
if should_log_debug():
    logger.debug("=== 对话管理路由模块加载 ===")
    logger.debug("路由器创建完成，准备注册路由...")

@router.post("/conversations", response_model=Response[ConversationSummary])
def create_conversation(request: CreateConversationRequest):
    """
    创建新对话
    
    Args:
        request: 创建对话请求
        
    Returns:
        Response[ConversationSummary]: 创建的对话摘要信息
    """
    try:
        # 创建对话
        conversation_id = db_manager.create_conversation(
            user_id=request.user_id,
            title=request.title,
            conversation_type=request.conversation_type.value
        )
        
        # 获取创建的对话详情
        conversation_data = db_manager.get_conversation_detail(conversation_id)
        if not conversation_data:
            raise HTTPException(status_code=500, detail="创建对话后无法获取详情")
        
        # 转换为响应模型
        conversation_summary = ConversationSummary(
            conversation_id=conversation_data['conversation_id'],
            title=conversation_data['title'],
            conversation_type=ConversationType(conversation_data['conversation_type']),
            status=ConversationStatus(conversation_data['status']),
            current_stage=ConversationStage(conversation_data['current_stage']),
            progress=float(conversation_data['progress']),
            algorithm_type=conversation_data['algorithm_type'],
            created_at=conversation_data['created_at'],
            updated_at=conversation_data['updated_at']
        )
        
        return Response(data=conversation_summary, msg="对话创建成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")

@router.get("/conversations", response_model=Response[ConversationListResponse])
def list_conversations(
    user_id: str = Query(..., description="用户ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小")
):
    """
    获取用户的对话列表
    
    Args:
        user_id: 用户ID
        page: 页码
        size: 每页大小
        
    Returns:
        Response[ConversationListResponse]: 对话列表响应
    """
    try:
        # 获取对话列表
        conversations_data, total = db_manager.get_conversations_by_user(
            user_id=user_id,
            page=page,
            size=size
        )
        
        # 转换为响应模型
        conversations = []
        for conv_data in conversations_data:
            conversation = ConversationSummary(
                conversation_id=conv_data['conversation_id'],
                title=conv_data['title'],
                conversation_type=ConversationType(conv_data['conversation_type']),
                status=ConversationStatus(conv_data['status']),
                current_stage=ConversationStage(conv_data['current_stage']),
                progress=float(conv_data['progress']),
                algorithm_type=conv_data['algorithm_type'],
                created_at=conv_data['created_at'],
                updated_at=conv_data['updated_at']
            )
            conversations.append(conversation)
        
        # 计算总页数
        total_pages = math.ceil(total / size) if total > 0 else 0
        
        response_data = ConversationListResponse(
            conversations=conversations,
            total=total,
            page=page,
            size=size,
            total_pages=total_pages
        )
        
        return Response(data=response_data, msg=f"获取到 {len(conversations)} 条对话记录")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话列表失败: {str(e)}")

@router.get("/conversations/{conversation_id}", response_model=Response[ConversationDetailResponse])
def get_conversation_detail(conversation_id: str):
    """
    获取对话详情
    
    Args:
        conversation_id: 对话ID
        
    Returns:
        Response[ConversationDetailResponse]: 对话详情响应
    """
    try:
        # 获取对话详情
        conversation_data = db_manager.get_conversation_detail(conversation_id)
        if not conversation_data:
            raise HTTPException(status_code=404, detail="对话不存在")
        
        # 获取对话消息
        messages_data = db_manager.get_conversation_messages(conversation_id)
        
        # 转换对话详情
        conversation_detail = ConversationDetail(
            conversation_id=conversation_data['conversation_id'],
            user_id=conversation_data['user_id'],
            title=conversation_data['title'],
            conversation_type=ConversationType(conversation_data['conversation_type']),
            status=ConversationStatus(conversation_data['status']),
            current_stage=ConversationStage(conversation_data['current_stage']),
            progress=float(conversation_data['progress']),
            task_summary=conversation_data['task_summary'],
            algorithm_type=conversation_data['algorithm_type'],
            created_at=conversation_data['created_at'],
            updated_at=conversation_data['updated_at'],
            completed_at=conversation_data['completed_at']
        )
        
        # 转换消息列表
        messages = []
        for msg_data in messages_data:
            message = ConversationMessage(
                id=msg_data['id'],
                message_type=msg_data['message_type'],
                content=msg_data['content'],
                message_data=msg_data['message_data'],
                interaction_type=msg_data['interaction_type'],
                timestamp=msg_data['timestamp']
            )
            messages.append(message)
        
        response_data = ConversationDetailResponse(
            conversation=conversation_detail,
            messages=messages,
            message_count=len(messages)
        )
        
        return Response(data=response_data, msg="获取对话详情成功")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话详情失败: {str(e)}")

@router.post("/conversations/{conversation_id}/delete", response_model=Response[bool])
def delete_conversation(conversation_id: str):
    """
    删除对话

    Args:
        conversation_id: 对话ID

    Returns:
        Response[bool]: 删除结果
    """
    logger.debug(f"🗑️ 删除对话接口被调用: conversation_id={conversation_id}")

    try:
        logger.debug(f"检查对话是否存在: {conversation_id}")

        # 检查对话是否存在
        conversation_data = db_manager.get_conversation_detail(conversation_id)
        if not conversation_data:
            logger.debug(f"对话不存在: {conversation_id}")
            raise HTTPException(status_code=404, detail="对话不存在")

        logger.debug(f"对话存在，准备删除: {conversation_data.get('title', 'Unknown')}")

        # 删除对话（级联删除相关数据）
        success = db_manager.delete_conversation(conversation_id)

        if success:
            logger.debug(f"对话删除成功: {conversation_id}")
            return Response(data=True, msg="对话删除成功")
        else:
            logger.debug(f"对话删除失败: {conversation_id}")
            raise HTTPException(status_code=500, detail="删除对话失败")

    except HTTPException as he:
        logger.debug(f"HTTP异常: {he.status_code} - {he.detail}")
        raise
    except Exception as e:
        logger.error(f"删除对话时发生异常: {conversation_id} - {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")

# 删除旧的有问题的接口，创建新的工作接口
@router.post("/conversations/{conversation_id}/update-status", response_model=Response[bool])
def update_conversation_status_fixed(
    conversation_id: str,
    status_update: dict
):
    """
    更新对话状态（修复版本）

    Args:
        conversation_id: 对话ID
        status_update: 更新请求数据

    Returns:
        Response[bool]: 更新结果
    """
    logger.debug(f"🔄 更新对话状态接口被调用: conversation_id={conversation_id}")
    logger.debug(f"更新数据: {status_update}")

    try:
        logger.debug(f"检查对话是否存在: {conversation_id}")

        # 检查对话是否存在
        conversation_data = db_manager.get_conversation_detail(conversation_id)
        if not conversation_data:
            logger.debug(f"对话不存在: {conversation_id}")
            raise HTTPException(status_code=404, detail="对话不存在")

        logger.debug(f"对话存在，当前状态: {conversation_data.get('status', 'Unknown')}")

        # 验证和构建更新参数
        if "status" not in status_update:
            logger.debug("缺少status字段")
            raise HTTPException(status_code=400, detail="status字段是必需的")

        # 验证status值
        try:
            status_value = ConversationStatus(status_update["status"])
            logger.debug(f"状态值验证成功: {status_value.value}")
        except ValueError:
            logger.debug(f"无效的状态值: {status_update['status']}")
            raise HTTPException(status_code=400, detail=f"无效的状态值: {status_update['status']}")

        update_params = {"status": status_value.value}

        # 可选参数
        if "progress" in status_update and status_update["progress"] is not None:
            update_params["progress"] = float(status_update["progress"])
            logger.debug(f"设置进度: {update_params['progress']}")
        if "algorithm_type" in status_update and status_update["algorithm_type"] is not None:
            update_params["algorithm_type"] = str(status_update["algorithm_type"])
            logger.debug(f"设置算法类型: {update_params['algorithm_type']}")
        if "task_summary" in status_update and status_update["task_summary"] is not None:
            update_params["task_summary"] = str(status_update["task_summary"])
            logger.debug(f"设置任务摘要: {update_params['task_summary']}")

        logger.debug(f"准备更新对话，参数: {update_params}")

        # 更新对话
        success = db_manager.update_conversation(conversation_id, **update_params)

        if success:
            logger.debug(f"对话状态更新成功: {conversation_id}")
            return Response(data=True, msg="对话状态更新成功")
        else:
            logger.debug(f"对话状态更新失败: {conversation_id}")
            raise HTTPException(status_code=500, detail="更新对话状态失败")

    except HTTPException as he:
        logger.debug(f"HTTP异常: {he.status_code} - {he.detail}")
        raise
    except Exception as e:
        logger.error(f"更新对话状态时发生异常: {conversation_id} - {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新对话状态失败: {str(e)}")

# 模块加载完成日志
if should_log_debug():
    logger.debug("=== 对话管理路由模块加载完成 ===")
    logger.debug(f"已注册路由数量: {len(router.routes)}")
    for route in router.routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            logger.debug(f"  - {route.path} [{list(route.methods)}]")
