#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DS-INT8 大模型配置验证脚本
验证新配置是否正确设置
"""

import sys
import os

# 添加agents目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def verify_configuration():
    """验证DS-INT8配置"""
    print("🔍 验证DS-INT8大模型配置...")
    print("=" * 50)
    
    try:
        from agents.config import (
            get_current_llm_config, 
            CURRENT_LLM_PROVIDER, 
            LLM_PROVIDERS
        )
        
        # 检查当前提供商
        print(f"📋 当前提供商: {CURRENT_LLM_PROVIDER}")
        
        if CURRENT_LLM_PROVIDER == "ds-int8":
            print("✅ 默认提供商已设置为 ds-int8")
        else:
            print(f"⚠️ 默认提供商为 {CURRENT_LLM_PROVIDER}，不是 ds-int8")
        
        # 检查ds-int8配置是否存在
        if "ds-int8" in LLM_PROVIDERS:
            print("✅ ds-int8 提供商配置已添加")
            
            config = LLM_PROVIDERS["ds-int8"]
            print(f"   🎯 模型名称: {config['model_name']}")
            print(f"   🌐 API URL: {config['base_url']}")
            print(f"   🔑 API Key: {config['api_key'][:10]}...{config['api_key'][-4:]}")
            print(f"   📝 描述: {config['description']}")
            
            # 验证配置完整性
            required_fields = ['model_name', 'base_url', 'api_key', 'description']
            missing_fields = [field for field in required_fields if not config.get(field)]
            
            if missing_fields:
                print(f"❌ 缺少必要字段: {', '.join(missing_fields)}")
            else:
                print("✅ 配置字段完整")
                
        else:
            print("❌ ds-int8 提供商配置未找到")
        
        # 获取当前配置
        current_config = get_current_llm_config()
        print(f"\n🔧 当前生效配置:")
        print(f"   📋 提供商: {CURRENT_LLM_PROVIDER}")
        print(f"   🎯 模型: {current_config['model_name']}")
        print(f"   🌐 URL: {current_config['base_url']}")
        print(f"   🔑 Key: {current_config['api_key'][:10]}...{current_config['api_key'][-4:]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_predictive_agent_integration():
    """验证预测智能体集成"""
    print("\n🤖 验证预测智能体集成...")
    print("=" * 50)
    
    try:
        # 检查predictive_agent.py是否能正确加载配置
        from agents.predict_algorith_agent.core.predictive_agent import create_llm_instance
        
        print("📦 正在创建大模型实例...")
        llm_instance = create_llm_instance()
        
        if llm_instance:
            print("✅ 大模型实例创建成功")
            print(f"   🎯 模型类型: {type(llm_instance).__name__}")
        else:
            print("❌ 大模型实例创建失败")
            
        return True
        
    except Exception as e:
        print(f"❌ 预测智能体集成验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 使用说明")
    print("=" * 50)
    print("1. 配置已成功设置，ds-int8 现在是默认的大模型提供商")
    print("2. 如需切换到其他提供商，使用以下命令:")
    print("   python switch_llm_config.py switch qwen")
    print("   python switch_llm_config.py switch deepseek")
    print("3. 如需切换回ds-int8:")
    print("   python switch_llm_config.py switch ds-int8")
    print("4. 查看所有提供商:")
    print("   python switch_llm_config.py list")
    print("5. 更新API Key:")
    print("   python switch_llm_config.py update ds-int8 <new-api-key>")
    print("\n⚠️ 注意: 修改配置后需要重启服务才能生效")

def main():
    """主函数"""
    print("🚀 DS-INT8 大模型配置验证")
    print("=" * 60)
    
    # 验证配置
    config_ok = verify_configuration()
    
    # 验证集成
    integration_ok = verify_predictive_agent_integration()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    if config_ok and integration_ok:
        print("🎉 验证完成 - 所有配置正常!")
    else:
        print("⚠️ 验证完成 - 发现问题，请检查上述错误信息")

if __name__ == "__main__":
    main()
