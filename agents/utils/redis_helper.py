import redis
import json
from agents.config import REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD, REDIS_DECODE_RESPONSES

redis_client = redis.Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    password=REDIS_PASSWORD,
    decode_responses=REDIS_DECODE_RESPONSES
)

def get_session_state(user_id, session_id, prefix="predictive_agent"):
    key = f"{prefix}:{user_id}:{session_id}"
    value = redis_client.get(key)
    return json.loads(value) if value else None

def set_session_state(user_id, session_id, state_dict, prefix="predictive_agent"):
    key = f"{prefix}:{user_id}:{session_id}"
    redis_client.set(key, json.dumps(state_dict), ex=3600)  # 1小时过期