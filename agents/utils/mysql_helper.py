from sqlalchemy import create_engine, text
import json
from urllib.parse import quote
from agents.config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET

# 构建数据库连接字符串，对用户名和密码进行URL编码以处理特殊字符
# 特别是密码中的 @ 符号需要编码为 %40
encoded_user = quote(MYSQL_USER, safe='')
encoded_password = quote(MYSQL_PASSWORD, safe='')
DB_URL = f"mysql+pymysql://{encoded_user}:{encoded_password}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset={MYSQL_CHARSET}"

engine = create_engine(DB_URL)

def save_session_state(user_id, session_id, task_id, state_dict):
    """保存会话状态到MySQL数据库"""
    state_json = json.dumps(state_dict, ensure_ascii=False)

    with engine.connect() as conn:
        # 开始事务
        trans = conn.begin()
        try:
            sql = text("""
                INSERT INTO agent_session (user_id, session_id, task_id, state, del_flag)
                VALUES (:user_id, :session_id, :task_id, :state, 0)
                ON DUPLICATE KEY UPDATE
                    task_id = :task_id,
                    state = :state,
                    update_time = CURRENT_TIMESTAMP,
                    del_flag = 0
            """)

            result = conn.execute(sql, {
                "user_id": user_id,
                "session_id": session_id,
                "task_id": task_id,
                "state": state_json
            })

            # 提交事务
            trans.commit()
            # 可选：在开发环境中启用调试日志
            # print(f"✅ 成功保存会话状态: user_id={user_id}, session_id={session_id}, affected_rows={result.rowcount}")

        except Exception as e:
            # 回滚事务
            trans.rollback()
            # 生产环境中应该使用日志记录而不是print
            # print(f"❌ 保存会话状态失败: {str(e)}")
            raise e

def load_session_state(user_id, session_id):
    """从MySQL数据库加载会话状态"""
    try:
        with engine.connect() as conn:
            sql = text("SELECT task_id, state FROM agent_session WHERE user_id=:user_id AND session_id=:session_id AND del_flag=0")
            result = conn.execute(sql, {"user_id": user_id, "session_id": session_id}).fetchone()

            if result:
                # 使用索引访问结果，因为某些SQLAlchemy版本可能不支持字典式访问
                task_id = result[0]  # task_id
                state_json = result[1]  # state
                state = json.loads(state_json)
                # 可选：在开发环境中启用调试日志
                # print(f"✅ 成功加载会话状态: user_id={user_id}, session_id={session_id}")
                return task_id, state
            else:
                # 可选：在开发环境中启用调试日志
                # print(f"ℹ️  未找到会话状态: user_id={user_id}, session_id={session_id}")
                return None, None

    except Exception as e:
        # 生产环境中应该使用日志记录而不是print
        # print(f"❌ 加载会话状态失败: {str(e)}")
        return None, None