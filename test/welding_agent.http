### 焊接仿真智能体HTTP测试文件
### 基础URL配置
@baseUrl = http://localhost:8008
@contentType = application/json

### ===========================================
### 1. 基础连通性测试
### ===========================================

### 1.1 测试根路径
GET {{baseUrl}}/

### ===========================================
### 2. 任务分类测试
### ===========================================

### 2.1 测试发起任务指令 - 新建项目
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "我想新建一个焊接仿真项目，项目名称叫做测试项目"
}

### 2.2 测试发起任务指令 - 创建网格
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建一个T型网格，长度500mm，宽度300mm，厚度10mm"
}

### 2.3 测试发起任务指令 - 设置材料
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料为SUS304不锈钢"
}

### 2.4 测试查询任务情况
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "当前仿真任务进展如何？"
}

### 2.5 测试生成任务报告
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "生成仿真结果报告"
}

### 2.6 测试不支持的操作
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "今天天气怎么样？"
}

### ===========================================
### 3. 项目管理类命令测试
### ===========================================

### 3.1 新建项目命令
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "新建项目，名称为焊接测试项目，路径为/home/<USER>/welding_test"
}

### 3.2 打开项目命令
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "打开项目，路径为/home/<USER>/existing_project"
}

### 3.3 保存项目命令
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "保存当前项目"
}

### 3.4 显示向导命令
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "打开向导界面"
}

### 3.5 加载报告命令
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "加载报告文件，路径为/home/<USER>/simulation_report.xml"
}

### ===========================================
### 4. 网格创建类命令测试
### ===========================================

### 4.1 创建T型网格
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度600mm，宽度400mm，厚度12mm，网格尺寸2mm"
}

### 4.2 创建板状网格
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建板状网格，长度800mm，宽度500mm，厚度8mm，网格尺寸1.5mm"
}

### 4.3 创建管状网格
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建圆筒网格，内径100mm，外径120mm，长度300mm，网格尺寸3mm"
}

### ===========================================
### 5. 参数设置类命令测试
### ===========================================

### 5.1 设置材料 - SUS304
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料为SUS304"
}

### 5.2 设置材料 - Q235
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料为Q235钢"
}

### 5.3 设置工艺参数
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置焊接工艺参数，电流200A，电压25V，移动速度10mm/s，热源效率0.9"
}

### 5.4 设置双椭球热源
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置双椭球热源，功率2000W，前半长轴6mm，后半长轴10mm，半短轴5mm"
}

### 5.5 设置高斯热源
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置高斯热源，功率1500W，作用半径4mm，集中系数0.8"
}

### 5.6 设置回转体热源
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置回转体热源，功率2200W，顶部半径6mm，高度10mm，热源效率0.85"
}

### 5.7 设置焊接路径
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置焊接路径，起点(0,0,0)，中点(50,0,0)，终点(100,0,0)"
}

### 5.8 设置热学边界
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置热学边界条件，环境温度25℃，初始温度20℃，散热系数0.9"
}

### 5.9 设置力学边界
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置力学边界条件，约束XYZ方向"
}

### 5.10 设置测点
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置测点，位置(25,0,0)和(75,0,0)"
}

### ===========================================
### 6. 仿真执行类命令测试
### ===========================================

### 6.1 执行热学仿真
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "执行热学仿真计算"
}

### 6.2 执行变形仿真
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "执行变形仿真分析"
}

### ===========================================
### 7. 状态持久化测试（多轮对话）
### ===========================================

### 7.1 第一轮：发起命令（带用户ID和会话ID）
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度500mm",
    "user_id": "test_user_001",
    "session_id": "session_001"
}

### 7.2 第二轮：调整参数
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "修改厚度为8mm",
    "user_id": "test_user_001",
    "session_id": "session_001"
}

### 7.3 第三轮：确认执行
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "确认",
    "user_id": "test_user_001",
    "session_id": "session_001"
}

### ===========================================
### 8. 参数确认和调整测试
### ===========================================

### 8.1 发起复杂命令（等待确认）
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置双椭球热源，功率1800W"
}

### 8.2 调整参数
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "修改功率为2000W，前半长轴改为7mm",
    "context": {
        "awaiting_confirmation": true,
        "command_type": "SetDoubleEllipsoidHeatSource",
        "extracted_params": {
            "Power": 1800,
            "EnergyFf": 0.8,
            "EnergyFr": 1.2,
            "RotAngle": 45,
            "AFront": 5,
            "ARear": 8,
            "Breadth": 4,
            "Depth": 4
        }
    }
}

### 8.3 确认执行
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "好的，确认执行",
    "context": {
        "awaiting_confirmation": true,
        "command_type": "SetDoubleEllipsoidHeatSource",
        "extracted_params": {
            "Power": 2000,
            "EnergyFf": 0.8,
            "EnergyFr": 1.2,
            "RotAngle": 45,
            "AFront": 7,
            "ARear": 8,
            "Breadth": 4,
            "Depth": 4
        }
    }
}

### 8.4 取消执行
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "取消这个操作",
    "context": {
        "awaiting_confirmation": true,
        "command_type": "SetDoubleEllipsoidHeatSource",
        "extracted_params": {
            "Power": 1800
        }
    }
}

### ===========================================
### 9. 错误处理和边界情况测试
### ===========================================

### 9.1 空输入测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": ""
}

### 9.2 无意义输入测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "asdfghjkl"
}

### 9.3 缺少必需参数测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "新建项目"
}

### 9.4 无效材料类型测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料为铝合金"
}

### 9.5 超出范围的数值测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度-100mm，厚度0mm"
}

### 9.6 格式错误的坐标测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置焊接路径，起点(abc,def,ghi)"
}

### 9.7 缺少question字段测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "user_id": "test_user",
    "session_id": "test_session"
}

### ===========================================
### 10. 复杂场景测试
### ===========================================

### 10.1 完整工作流测试 - 第1步：新建项目
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "新建一个焊接仿真项目，名称为完整测试项目，路径为/tmp/full_test",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.2 完整工作流测试 - 第2步：创建网格
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度400mm，宽度200mm，厚度6mm，网格尺寸2mm",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.3 完整工作流测试 - 第3步：设置材料
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料为Q235",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.4 完整工作流测试 - 第4步：设置工艺参数
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置焊接工艺，电流180A，电压22V，移动速度8mm/s",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.5 完整工作流测试 - 第5步：设置热源
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置双椭球热源，功率1600W，前半长轴5mm，后半长轴8mm",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.6 完整工作流测试 - 第6步：设置边界条件
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置热学边界，环境温度20℃，散热系数0.8",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.7 完整工作流测试 - 第7步：执行仿真
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "执行热学仿真",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.8 完整工作流测试 - 第8步：查询状态
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "查询仿真进度",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### 10.9 完整工作流测试 - 第9步：生成报告
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "生成仿真报告",
    "user_id": "workflow_user",
    "session_id": "workflow_session"
}

### ===========================================
### 11. 中文自然语言测试
### ===========================================

### 11.1 口语化表达测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "帮我弄个新项目，就叫测试用的"
}

### 11.2 专业术语测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "进行T型接头的焊接热循环分析，采用双椭球热源模型"
}

### 11.3 混合表达测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建一个plate网格，尺寸500x300x10，mesh size设为2"
}

### 11.4 模糊表达测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "我想做个焊接仿真，大概这样那样的"
}

### ===========================================
### 12. 特殊字符和编码测试
### ===========================================

### 12.1 特殊字符测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "新建项目：测试@#$%^&*()项目！"
}

### 12.2 长文本测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "我需要创建一个非常详细的焊接仿真项目，这个项目需要包含完整的T型接头焊接过程分析，包括热传导、相变、残余应力等多个物理场的耦合计算，材料选用SUS304不锈钢，焊接工艺参数需要根据实际生产条件进行优化设置"
}

### 12.3 多行文本测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格\n长度：500mm\n宽度：300mm\n厚度：10mm\n网格尺寸：2mm"
}

### ===========================================
### 13. 并发测试（同一用户多会话）
### ===========================================

### 13.1 用户A会话1
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度400mm",
    "user_id": "user_A",
    "session_id": "session_1"
}

### 13.2 用户A会话2
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料为SUS304",
    "user_id": "user_A",
    "session_id": "session_2"
}

### 13.3 用户B会话1
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "新建项目，名称为用户B的项目",
    "user_id": "user_B",
    "session_id": "session_1"
}

### ===========================================
### 14. 性能测试
### ===========================================

### 14.1 快速连续请求测试1
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建板状网格"
}

### 14.2 快速连续请求测试2
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置材料Q235"
}

### 14.3 快速连续请求测试3
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "执行热学仿真"
}

### ===========================================
### 15. 边界值测试
### ===========================================

### 15.1 最小值测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度1mm，宽度1mm，厚度0.1mm"
}

### 15.2 最大值测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建板状网格，长度10000mm，宽度5000mm，厚度100mm"
}

### 15.3 零值测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置工艺参数，电流0A，电压0V"
}

### 15.4 负值测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置热学边界，环境温度-50℃"
}

### ===========================================
### 16. 数据类型测试
### ===========================================

### 16.1 整数vs浮点数测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度500，宽度300.5，厚度10.25"
}

### 16.2 科学计数法测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置双椭球热源，功率1.8e3W"
}

### 16.3 单位转换测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建板状网格，长度0.5m，宽度30cm，厚度8mm"
}

### ===========================================
### 17. 状态恢复测试
### ===========================================

### 17.1 中断后恢复 - 第1步
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "创建T型网格，长度600mm",
    "user_id": "recovery_user",
    "session_id": "recovery_session"
}

### 17.2 中断后恢复 - 第2步（模拟中断后重新连接）
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "修改宽度为400mm",
    "user_id": "recovery_user",
    "session_id": "recovery_session"
}

### 17.3 中断后恢复 - 第3步（确认执行）
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "确认执行",
    "user_id": "recovery_user",
    "session_id": "recovery_session"
}

### ===========================================
### 18. 命令组合测试
### ===========================================

### 18.1 多命令组合请求
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "新建项目叫组合测试，然后创建T型网格长度500mm，再设置材料为SUS304"
}

### 18.2 条件命令测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "如果材料是SUS304，那么设置工艺参数电流200A"
}

### ===========================================
### 19. 国际化测试
### ===========================================

### 19.1 英文输入测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "Create a new welding simulation project"
}

### 19.2 中英混合测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "Create T-mesh，长度500mm，width 300mm"
}

### ===========================================
### 20. 压力测试数据
### ===========================================

### 20.1 大量参数测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置双椭球热源，功率2000W，能量系数Ff为0.9，能量系数Fr为1.3，旋转角度50度，前半长轴6mm，后半长轴9mm，半短轴b为5mm，半短轴c为5mm"
}

### 20.2 复杂路径测试
POST {{baseUrl}}/agents/welding
Content-Type: {{contentType}}

{
    "question": "设置焊接路径，包含10个点：(0,0,0),(10,0,0),(20,5,0),(30,10,0),(40,15,0),(50,20,0),(60,25,0),(70,30,0),(80,35,0),(90,40,0)"
}

### ===========================================
### 测试说明和使用指南
### ===========================================

###
### 使用说明：
### 1. 确保服务器运行在 http://localhost:8000
### 2. 按顺序执行测试用例，观察返回结果
### 3. 重点关注以下几个方面：
###    - 任务分类是否正确
###    - 命令分类是否准确
###    - 参数提取是否完整
###    - 交互确认流程是否正常
###    - 状态持久化是否有效
###    - 错误处理是否合理
###
### 预期结果：
### - 正常请求应返回 code: 0, msg: "success"
### - 错误请求应返回相应的错误信息
### - 状态持久化测试应能正确维护多轮对话状态
### - 参数确认测试应能正确处理用户的确认和调整
###
### 测试重点：
### 1. 功能完整性：所有19种命令都能正确识别和处理
### 2. 参数准确性：参数提取和默认值设置正确
### 3. 交互友好性：确认流程和错误提示清晰
### 4. 状态一致性：多轮对话状态管理正确
### 5. 错误处理：异常情况处理得当
### 6. 性能表现：响应时间合理
###
### 注意事项：
### - 某些测试可能需要按顺序执行（特别是状态持久化测试）
### - 错误测试用例的返回结果应该是错误信息，这是正常的
### - 如果服务器未启动，所有请求都会失败
### - 建议在测试前清理任何现有的状态数据
###