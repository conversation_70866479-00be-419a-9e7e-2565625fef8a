# 算法智能体部署说明 - ***********服务器

## 📋 部署步骤

### 1️⃣ 准备部署包
在本地项目根目录下：
```bash
# 打包agents目录
zip -r agents.zip agents/

# 或者使用tar打包
tar -czf agents.tar.gz agents/
```

### 2️⃣ 上传到服务器
```bash
# 使用scp上传
scp agents.zip root@***********:/data/agent/

# 或者使用rsync
rsync -avz agents.zip root@***********:/data/agent/
```

### 3️⃣ 在服务器上解压
```bash
# SSH连接到服务器
ssh root@***********

# 进入项目目录
cd /data/agent

# 停止现有服务（如果有）
./manage_server.sh stop 2>/dev/null || true

# 备份现有agents目录（如果存在）
if [ -d "agents" ]; then
    mv agents agents.backup.$(date +%Y%m%d_%H%M%S)
fi

# 解压新的agents目录
unzip -o agents.zip
# 或者: tar -xzf agents.tar.gz

# 设置执行权限
chmod +x agents/predict_algorith_agent/scripts/*.sh

# 复制管理脚本到根目录（如果不存在）
if [ ! -f "manage_server.sh" ]; then
    cp agents/predict_algorith_agent/scripts/manage_server.sh ./
    chmod +x manage_server.sh
fi
```

### 4️⃣ 启动服务
```bash
# 启动服务
./manage_server.sh start

# 或者重启服务
./manage_server.sh restart
```

### 5️⃣ 验证部署
```bash
# 查看服务状态
./manage_server.sh status

# 测试WebSocket连接
./manage_server.sh test

# 查看日志
./manage_server.sh logs
```

## 🔧 服务管理命令

### 基础命令
```bash
./manage_server.sh start      # 启动服务
./manage_server.sh stop       # 停止服务
./manage_server.sh restart    # 重启服务
./manage_server.sh status     # 查看状态
./manage_server.sh logs       # 查看日志
./manage_server.sh test       # 测试连接
./manage_server.sh help       # 显示帮助
```

### 高级诊断
```bash
# 运行WebSocket诊断（如果脚本存在）
./diagnose_websocket.sh

# 手动测试WebSocket
python agents/predict_algorith_agent/scripts/test_websocket.py
```

## 🌐 服务访问地址

- **HTTP服务**: http://***********:8008
- **WebSocket**: ws://***********:8008/api/ws/chat
- **API文档**: http://***********:8008/docs
- **健康检查**: http://***********:8008/health
- **WebSocket健康检查**: http://***********:8008/api/ws/health

## 📁 目录结构

部署后的目录结构：
```
/data/agent/
├── manage_server.sh              # 服务管理脚本（根目录）
├── diagnose_websocket.sh         # 诊断脚本（可选）
├── activate_env.sh               # 环境激活脚本
├── server.pid                    # 服务器进程ID文件
├── log_monitor.pid               # 日志监控进程ID文件
├── logs/                         # 日志目录
│   ├── server.log               # 服务器日志
│   └── log_monitor.log          # 日志监控日志
└── agents/                       # 主要代码目录
    ├── config.py                # 全局配置文件
    └── predict_algorith_agent/   # 算法智能体模块
        ├── predict_main.py      # 主启动文件
        ├── api/                 # API路由
        ├── core/                # 核心逻辑
        ├── network/             # 网络管理
        ├── database/            # 数据库管理
        ├── services/            # 服务层
        ├── utils/               # 工具类
        └── scripts/             # 脚本目录
            ├── start_server_background.sh  # 后台启动脚本
            ├── manage_server.sh            # 管理脚本（原版）
            ├── test_websocket_241.py       # WebSocket测试
            └── diagnose_websocket.sh       # 诊断脚本
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
./manage_server.sh logs

# 检查端口占用
netstat -tlnp | grep :8008

# 检查Python环境
python3 --version
python3 -c "import fastapi, uvicorn, websockets"
```

#### 2. WebSocket连接失败
```bash
# 运行诊断
./diagnose_websocket.sh

# 检查WebSocket健康
curl http://localhost:8008/api/ws/health

# 手动测试连接
./manage_server.sh test
```

#### 3. 依赖包缺失
```bash
# 激活环境
source activate_env.sh

# 安装缺失的包
pip install fastapi uvicorn websockets pydantic-ai mysql-connector-python redis
```

#### 4. 权限问题
```bash
# 设置脚本权限
chmod +x manage_server.sh
chmod +x agents/predict_algorith_agent/scripts/*.sh

# 检查目录权限
ls -la /data/agent/
```

### 日志查看
```bash
# 实时查看服务器日志
./manage_server.sh logs

# 查看最近的错误
tail -50 logs/server.log | grep -i error

# 查看启动日志
head -100 logs/server.log
```

## 🔄 更新部署

### 快速更新
```bash
# 1. 停止服务
./manage_server.sh stop

# 2. 备份当前版本
mv agents agents.backup.$(date +%Y%m%d_%H%M%S)

# 3. 解压新版本
unzip -o agents.zip

# 4. 设置权限
chmod +x agents/predict_algorith_agent/scripts/*.sh

# 5. 启动服务
./manage_server.sh start
```

### 回滚操作
```bash
# 如果新版本有问题，回滚到备份版本
./manage_server.sh stop
mv agents agents.failed
mv agents.backup.YYYYMMDD_HHMMSS agents
./manage_server.sh start
```

## 📞 技术支持

如果遇到问题：
1. 运行 `./manage_server.sh status` 检查服务状态
2. 运行 `./diagnose_websocket.sh` 进行全面诊断
3. 查看 `logs/server.log` 获取详细错误信息
4. 确认所有依赖包已正确安装
5. 检查网络连接和防火墙设置

## 🎯 部署检查清单

- [ ] agents.zip文件已上传到/data/agent/
- [ ] 现有服务已停止
- [ ] agents目录已解压
- [ ] 脚本权限已设置
- [ ] manage_server.sh已复制到根目录
- [ ] 服务启动成功
- [ ] 端口8008正在监听
- [ ] HTTP健康检查通过
- [ ] WebSocket健康检查通过
- [ ] WebSocket连接测试通过
- [ ] 日志文件正常生成
