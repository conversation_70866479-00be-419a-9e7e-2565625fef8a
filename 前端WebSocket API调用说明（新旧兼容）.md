# 算法生成智能体WebSocket API调用说明（新旧兼容）

## 📋 文档概述

本文档为前端开发团队提供算法生成智能体WebSocket API的完整调用标准，包含：
1. **原有聊天对话功能**（完全兼容，无需修改）
2. **新增8个第三方API接口调用功能**（可选使用）
3. **混合使用场景**（聊天+API调用无缝切换）

**重要说明**：新增功能完全向后兼容，现有前端代码无需任何修改即可正常使用。

## 🔗 连接信息

### WebSocket连接地址
```
ws://***********:8008/api/ws/chat
```

### 连接参数
```javascript
const wsUrl = `ws://***********:8008/api/ws/chat?user_id=${userId}&session_id=${sessionId}&username=${username}`;
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| user_id | string | 是 | 用户唯一标识 |
| session_id | string | 否 | 会话ID，不提供将自动生成 |
| username | string | 否 | 用户显示名称 |

## 💬 原有聊天对话功能（完全兼容）

### 连接建立
```javascript
// 原有代码完全不需要修改
const ws = new WebSocket('ws://***********:8008/api/ws/chat?user_id=123&session_id=abc&username=张三');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const response = JSON.parse(event.data);
    handleMessage(response);
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};

ws.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};
```

### 发送聊天消息
```javascript
// 原有聊天消息格式完全保持不变
const chatMessage = {
    type: "chat",
    data: {
        message: "我想用LSTM做预测，学习率0.001，批次大小32"
    },
    session_id: "abc123",
    user_id: "123"
};

ws.send(JSON.stringify(chatMessage));
```

### 接收聊天响应
```javascript
// 原有响应处理逻辑完全保持不变
function handleMessage(response) {
    if (response.type === "chat_response") {
        // 处理AI回复
        const aiMessage = response.data.message;
        const taskId = response.data.task_id;
        const conversationId = response.data.conversation_id;
        
        // 显示AI回复
        displayAIMessage(aiMessage);
        
        // 处理任务状态
        if (response.data.state) {
            updateTaskState(response.data.state);
        }
    }
}
```

### 原有消息类型（完全支持）
```javascript
// 1. 聊天消息
{
    type: "chat",
    data: { message: "用户输入内容" }
}

// 2. 查询训练进度
{
    type: "get_progress",
    data: { task_id: "task_123" }
}

// 3. 生成分析报告
{
    type: "generate_report",
    data: { task_id: "task_123" }
}

// 4. 获取历史记录
{
    type: "get_history",
    data: { user_id: "123", limit: 10 }
}

// 5. 心跳检测
{
    type: "ping",
    data: {}
}
```

## ⚡ 新增8个API接口调用功能

### API调用消息格式
```javascript
const apiMessage = {
    type: "api_call",  // 新增消息类型
    data: {
        action: "接口名称",
        project_id: "项目ID",  // 部分接口需要
        // 其他参数根据具体接口而定
    },
    session_id: "abc123",
    user_id: "123"
};
```

### 1. 查询全部算法名称 ⭐ **无需project_id**
```javascript
const message = {
    type: "api_call",
    data: {
        action: "get_all_algorithms"
        // 注意：此接口不需要project_id参数
    },
    session_id: sessionId,
    user_id: userId
};

ws.send(JSON.stringify(message));
```

**响应示例**：
```javascript
{
    type: "api_response",
    data: {
        action: "get_all_algorithms",
        status: "success",
        result: {
            msg: "查询成功",
            algorithm_name: [
                ["LSTM预测算法", "285"],
                ["CNN分类算法", "286"],
                ["RNN序列算法", "287"]
            ]
        }
    },
    timestamp: "2025-01-22T18:00:00",
    session_id: "abc123"
}
```

### 2. 查询全部数据集名称 ⭐ **无需project_id**
```javascript
const message = {
    type: "api_call",
    data: {
        action: "get_all_datasets"
        // 注意：此接口不需要project_id参数
    },
    session_id: sessionId,
    user_id: userId
};
```

**响应示例**：
```javascript
{
    type: "api_response",
    data: {
        action: "get_all_datasets",
        status: "success",
        result: {
            msg: "查询成功",
            dataset_name: [
                ["时序数据集A", "dataset_001"],
                ["图像数据集B", "dataset_002"]
            ]
        }
    }
}
```

### 3. 查询全部IOT名称
```javascript
const message = {
    type: "api_call",
    data: {
        action: "get_all_iot",
        project_id: "285"  // 必需参数
    },
    session_id: sessionId,
    user_id: userId
};
```

### 4. 查询全部服务名称
```javascript
const message = {
    type: "api_call",
    data: {
        action: "get_all_services",
        project_id: "285"  // 必需参数
    },
    session_id: sessionId,
    user_id: userId
};
```

### 5. 查询算法训练信息
```javascript
const message = {
    type: "api_call",
    data: {
        action: "get_algorithm_training_info",
        algorithm_name: "LSTM预测算法"  // 必需参数
    },
    session_id: sessionId,
    user_id: userId
};
```

### 6. 查询服务预测结果
```javascript
const message = {
    type: "api_call",
    data: {
        action: "get_service_prediction_result",
        service_name: "预测服务A"  // 必需参数
    },
    session_id: sessionId,
    user_id: userId
};
```

### 7. 训练新算法
```javascript
const message = {
    type: "api_call",
    data: {
        action: "train_algorithm",
        // 训练参数
        algorithm_name: "我的LSTM算法",
        algorithm_type: "预测维护",
        dataset_name: "时序数据集A",
        input_dim: 10,
        lstm_hidden_size: 64,
        lstm_num_layers: 3,
        dropout: 0.2,
        bidirectional: false,
        output_dim: 1,
        learning_rate: 0.01,
        train_ratio: 0.8,
        optimizer: "Adam",
        loss_function: "MSE",
        label_column: 0,
        data_columns: "1,2,3,4,5",
        field: "temperature"
    },
    session_id: sessionId,
    user_id: userId
};
```

### 8. 启动监控分析服务
```javascript
const message = {
    type: "api_call",
    data: {
        action: "start_monitoring_service",
        database_address: "*************",
        database_table: "sensor_data",
        key_data_field: "temperature,pressure",
        service_name: "设备监控服务"
    },
    session_id: sessionId,
    user_id: userId
};
```

## 🔄 混合使用场景

### 场景1：聊天引导 + API执行
```javascript
// 1. 先通过聊天了解需求
const chatMessage = {
    type: "chat",
    data: {
        message: "我想训练一个LSTM算法用于设备预测维护"
    }
};
ws.send(JSON.stringify(chatMessage));

// 2. 根据AI建议调用API
const apiMessage = {
    type: "api_call",
    data: {
        action: "get_all_algorithms"
    }
};
ws.send(JSON.stringify(apiMessage));

// 3. 继续聊天讨论结果
const followUpChat = {
    type: "chat",
    data: {
        message: "这些算法中哪个最适合我的需求？"
    }
};
ws.send(JSON.stringify(followUpChat));
```

### 场景2：API查询 + 聊天分析
```javascript
// 1. 直接查询可用资源
ws.send(JSON.stringify({
    type: "api_call",
    data: { action: "get_all_datasets" }
}));

// 2. 询问AI建议
ws.send(JSON.stringify({
    type: "chat",
    data: {
        message: "我看到有这些数据集，哪个适合做预测维护？"
    }
}));
```

## 📊 响应处理

### 统一响应处理函数
```javascript
function handleWebSocketMessage(event) {
    const response = JSON.parse(event.data);
    
    switch (response.type) {
        case "chat_response":
            // 处理聊天响应（原有逻辑）
            handleChatResponse(response);
            break;
            
        case "api_response":
            // 处理API调用响应（新增逻辑）
            handleApiResponse(response);
            break;
            
        case "error":
            // 处理错误响应
            handleError(response);
            break;
            
        default:
            console.log("未知响应类型:", response.type);
    }
}

function handleChatResponse(response) {
    // 原有聊天响应处理逻辑
    const message = response.data.message;
    displayAIMessage(message);
    
    if (response.data.task_id) {
        updateTaskStatus(response.data.task_id);
    }
}

function handleApiResponse(response) {
    // 新增API响应处理逻辑
    const action = response.data.action;
    const result = response.data.result;
    
    switch (action) {
        case "get_all_algorithms":
            displayAlgorithmList(result.algorithm_name);
            break;
            
        case "get_all_datasets":
            displayDatasetList(result.dataset_name);
            break;
            
        case "train_algorithm":
            handleTrainingResult(result);
            break;
            
        // 其他API响应处理...
    }
}
```

## 🛡️ 错误处理

### 错误响应格式
```javascript
{
    type: "error",
    data: {
        error_type: "api_error",
        error_message: "算法平台服务器连接失败",
        error_code: "PLATFORM_CONNECTION_ERROR",
        action: "get_all_algorithms"  // 失败的操作
    },
    timestamp: "2025-01-22T18:00:00"
}
```

### 错误处理示例
```javascript
function handleError(response) {
    const errorType = response.data.error_type;
    const errorMessage = response.data.error_message;
    const action = response.data.action;
    
    switch (errorType) {
        case "api_error":
            showApiError(action, errorMessage);
            break;
            
        case "validation_error":
            showValidationError(errorMessage);
            break;
            
        case "system_error":
            showSystemError(errorMessage);
            break;
            
        default:
            showGenericError(errorMessage);
    }
}
```

## 🔧 最佳实践

### 1. 连接管理
```javascript
class WebSocketManager {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }
    
    connect() {
        this.ws = new WebSocket(this.url);
        
        this.ws.onopen = () => {
            console.log('WebSocket连接成功');
            this.reconnectAttempts = 0;
        };
        
        this.ws.onmessage = (event) => {
            handleWebSocketMessage(event);
        };
        
        this.ws.onclose = () => {
            this.handleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }
    
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, 3000);
        }
    }
    
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接');
        }
    }
}
```

### 2. 消息队列管理
```javascript
class MessageQueue {
    constructor(wsManager) {
        this.wsManager = wsManager;
        this.queue = [];
        this.processing = false;
    }
    
    async sendMessage(message) {
        this.queue.push(message);
        if (!this.processing) {
            await this.processQueue();
        }
    }
    
    async processQueue() {
        this.processing = true;
        
        while (this.queue.length > 0) {
            const message = this.queue.shift();
            this.wsManager.send(message);
            
            // 避免消息发送过快
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        this.processing = false;
    }
}
```

## 📋 完整示例

### HTML页面示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>算法智能体WebSocket测试</title>
</head>
<body>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="输入聊天消息">
    <button onclick="sendChat()">发送聊天</button>
    <button onclick="getAlgorithms()">查询算法</button>
    <button onclick="getDatasets()">查询数据集</button>
    
    <script src="websocket-client.js"></script>
</body>
</html>
```

### JavaScript客户端示例
```javascript
// websocket-client.js
const wsManager = new WebSocketManager(
    'ws://***********:8008/api/ws/chat?user_id=test123&username=测试用户'
);
const messageQueue = new MessageQueue(wsManager);

// 连接WebSocket
wsManager.connect();

// 发送聊天消息
function sendChat() {
    const input = document.getElementById('messageInput');
    const message = {
        type: "chat",
        data: { message: input.value },
        user_id: "test123"
    };
    
    messageQueue.sendMessage(message);
    input.value = '';
}

// 查询算法列表
function getAlgorithms() {
    const message = {
        type: "api_call",
        data: { action: "get_all_algorithms" },
        user_id: "test123"
    };
    
    messageQueue.sendMessage(message);
}

// 查询数据集列表
function getDatasets() {
    const message = {
        type: "api_call",
        data: { action: "get_all_datasets" },
        user_id: "test123"
    };
    
    messageQueue.sendMessage(message);
}
```

## 📞 技术支持

如有问题，请联系后端开发团队：
- **WebSocket地址**：ws://***********:8008/api/ws/chat
- **API文档**：http://***********:8008/docs
- **健康检查**：http://***********:8008/health

## 📋 API接口参数详细说明

### 训练新算法参数说明
```javascript
{
    type: "api_call",
    data: {
        action: "train_algorithm",

        // 基础参数（必填）
        algorithm_name: "算法名称",           // 自定义算法名称
        algorithm_type: "算法类型",          // 风险预警/故障诊断/预测维护
        dataset_name: "数据集名称",          // 选择的训练数据集

        // LSTM网络参数
        input_dim: 10,                      // 输入特征维度
        lstm_hidden_size: 64,               // LSTM隐藏层大小
        lstm_num_layers: 3,                 // LSTM层数
        dropout: 0.2,                       // Dropout比例
        bidirectional: false,               // 是否双向LSTM

        // 训练参数
        output_dim: 1,                      // 输出维度
        learning_rate: 0.01,                // 学习率
        train_ratio: 0.8,                   // 训练集比例
        optimizer: "Adam",                  // 优化器：Adam/SGD/RMSprop
        loss_function: "MSE",               // 损失函数：MSE/MAE/CrossEntropy

        // 数据参数
        label_column: 0,                    // 标签列索引
        data_columns: "1,2,3,4,5",         // 数据列索引（逗号分隔）
        field: "temperature"                // 数据字段名称
    }
}
```

### 启动监控服务参数说明
```javascript
{
    type: "api_call",
    data: {
        action: "start_monitoring_service",

        // 数据库配置（必填）
        database_address: "*************", // 数据库IP地址
        database_table: "sensor_data",      // 监控数据表名
        key_data_field: "temp,pressure",   // 关键数据字段（逗号分隔）
        service_name: "设备监控服务"        // 自定义服务名称
    }
}
```

## 🎯 前端集成建议

### 1. 渐进式集成策略
```javascript
// 阶段1：保持现有聊天功能不变
// 现有代码完全不需要修改，继续使用原有的聊天功能

// 阶段2：添加API调用功能
// 在现有基础上添加新的API调用按钮和处理逻辑

// 阶段3：混合模式优化
// 实现聊天和API调用的智能切换
```

### 2. UI组件设计建议
```javascript
// 建议的UI结构
const UIComponents = {
    // 原有聊天区域（保持不变）
    chatArea: {
        messageList: "消息列表",
        inputBox: "输入框",
        sendButton: "发送按钮"
    },

    // 新增API功能区域
    apiArea: {
        projectSelector: "项目选择器",
        apiButtons: [
            "查询算法", "查询数据集", "查询IOT", "查询服务",
            "查询训练信息", "查询预测结果", "训练算法", "启动监控"
        ],
        parameterForms: "参数表单"
    }
};
```

### 3. 状态管理建议
```javascript
class AppState {
    constructor() {
        this.currentMode = 'chat'; // 'chat' | 'api' | 'mixed'
        this.selectedProject = null;
        this.chatHistory = [];
        this.apiResults = [];
        this.isConnected = false;
    }

    switchMode(mode) {
        this.currentMode = mode;
        this.updateUI();
    }

    updateUI() {
        // 根据当前模式更新UI显示
        switch (this.currentMode) {
            case 'chat':
                this.showChatInterface();
                break;
            case 'api':
                this.showApiInterface();
                break;
            case 'mixed':
                this.showMixedInterface();
                break;
        }
    }
}
```

## 🔍 调试和测试

### 1. WebSocket连接测试
```javascript
// 连接测试函数
function testWebSocketConnection() {
    const testWs = new WebSocket('ws://***********:8008/api/ws/chat?user_id=test&username=测试');

    testWs.onopen = () => {
        console.log('✅ WebSocket连接测试成功');

        // 测试心跳
        testWs.send(JSON.stringify({
            type: "ping",
            data: {}
        }));
    };

    testWs.onmessage = (event) => {
        const response = JSON.parse(event.data);
        console.log('📨 收到响应:', response);
    };

    testWs.onerror = (error) => {
        console.error('❌ WebSocket连接测试失败:', error);
    };
}
```

### 2. API功能测试
```javascript
// API功能测试套件
const apiTests = {
    // 测试查询算法
    testGetAlgorithms() {
        const message = {
            type: "api_call",
            data: { action: "get_all_algorithms" },
            user_id: "test"
        };

        wsManager.send(message);
        console.log('🧪 测试查询算法接口');
    },

    // 测试查询数据集
    testGetDatasets() {
        const message = {
            type: "api_call",
            data: { action: "get_all_datasets" },
            user_id: "test"
        };

        wsManager.send(message);
        console.log('🧪 测试查询数据集接口');
    },

    // 运行所有测试
    runAllTests() {
        setTimeout(() => this.testGetAlgorithms(), 1000);
        setTimeout(() => this.testGetDatasets(), 2000);
        // 添加更多测试...
    }
};
```

### 3. 错误场景测试
```javascript
// 错误场景测试
const errorTests = {
    // 测试无效消息格式
    testInvalidMessage() {
        wsManager.send({ invalid: "message" });
    },

    // 测试缺少必需参数
    testMissingParameters() {
        wsManager.send({
            type: "api_call",
            data: { action: "get_all_iot" } // 缺少project_id
        });
    },

    // 测试网络断开重连
    testReconnection() {
        wsManager.ws.close();
        // 应该自动重连
    }
};
```

## 📊 性能优化建议

### 1. 消息缓存
```javascript
class MessageCache {
    constructor(maxSize = 100) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }

    set(key, value) {
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    get(key) {
        return this.cache.get(key);
    }
}
```

### 2. 请求防抖
```javascript
class RequestDebouncer {
    constructor(delay = 300) {
        this.delay = delay;
        this.timers = new Map();
    }

    debounce(key, callback) {
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key));
        }

        const timer = setTimeout(() => {
            callback();
            this.timers.delete(key);
        }, this.delay);

        this.timers.set(key, timer);
    }
}
```

## 🔒 安全注意事项

### 1. 输入验证
```javascript
function validateMessage(message) {
    // 验证消息格式
    if (!message.type || !message.data) {
        throw new Error('消息格式无效');
    }

    // 验证用户ID
    if (!message.user_id || message.user_id.length > 50) {
        throw new Error('用户ID无效');
    }

    // 验证消息内容长度
    if (message.type === 'chat' && message.data.message.length > 1000) {
        throw new Error('消息内容过长');
    }

    return true;
}
```

### 2. XSS防护
```javascript
function sanitizeMessage(message) {
    // 转义HTML特殊字符
    return message
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;');
}
```

## 📱 移动端适配

### 1. 响应式设计
```css
/* 移动端样式建议 */
@media (max-width: 768px) {
    .chat-container {
        flex-direction: column;
    }

    .api-buttons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .message-input {
        font-size: 16px; /* 防止iOS缩放 */
    }
}
```

### 2. 触摸优化
```javascript
// 移动端触摸事件处理
function setupMobileEvents() {
    let touchStartY = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartY = e.touches[0].clientY;
    });

    document.addEventListener('touchmove', (e) => {
        const touchY = e.touches[0].clientY;
        const deltaY = touchStartY - touchY;

        // 处理滚动逻辑
        if (deltaY > 50) {
            // 向上滑动
            hideKeyboard();
        }
    });
}
```

---

**重要提醒**：本文档确保了完全的向后兼容性，现有前端代码无需任何修改即可正常使用。新增的API调用功能为可选功能，可以根据需要逐步集成。建议采用渐进式集成策略，先保持现有功能稳定，再逐步添加新功能。
