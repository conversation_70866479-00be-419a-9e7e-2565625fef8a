from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class UserCreate(BaseModel):
    username: str
    password: str
    realname: str

class UserUpdate(BaseModel):
    password: Optional[str] = None
    realname: Optional[str] = None

class UserOut(BaseModel):
    id: int
    username: str
    password: str
    realname: str
    created_time: datetime

class UserLogin(BaseModel): 
    username: str
    password: str
    