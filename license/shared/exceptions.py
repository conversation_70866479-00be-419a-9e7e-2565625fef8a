class LicenseException(Exception):
    """许可证系统基础异常"""
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message
        super().__init__(self.message)

class LicenseNotFoundError(LicenseException):
    """许可证未找到异常"""
    def __init__(self, license_id: int):
        super().__init__(404, f"License with ID {license_id} not found")

class InvalidLicenseStatusError(LicenseException):
    """无效许可证状态异常"""
    def __init__(self, status: int):
        super().__init__(400, f"Invalid license status: {status}")

class LicenseActivationError(LicenseException):
    """许可证激活错误"""
    def __init__(self, reason: str):
        super().__init__(403, f"License activation failed: {reason}")

class LicenseUserNotFoundError(LicenseException):
    """许可证用户未找到异常"""
    def __init__(self, email: str):
        super().__init__(404, f"License user with email {email} not found")

class DuplicateLicenseUserError(LicenseException):
    """重复许可证用户异常"""
    def __init__(self, email: str):
        super().__init__(409, f"License user with email {email} already exists")

class CryptoOperationError(LicenseException):
    """加密操作异常"""
    def __init__(self, operation: str):
        super().__init__(500, f"Crypto operation failed: {operation}")