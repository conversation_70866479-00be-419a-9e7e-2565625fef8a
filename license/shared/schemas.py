"""
共享的Pydantic模型
"""
from datetime import datetime
from pydantic import BaseModel, EmailStr

class LicenseUserCreate(BaseModel):
    user_id: int
    email: EmailStr

class LicenseUserResponse(BaseModel):
    id: int
    user_id: int
    email: EmailStr

class LicenseCreateRequest(BaseModel):
    license_user_id: int
    machine_key: str
    file_signature: str

class LicenseActivateRequest(BaseModel):
    license_key: str
    machine_key: str
    file_signature: str

class LicenseResponse(BaseModel):
    id: int
    license_user_id: int
    status: int
    expires_at: datetime
    activated_at: datetime = None
    created_at: datetime
    license_key: str = None  # 通常在创建时返回，激活后不再返回

    class Config:
        orm_mode = True