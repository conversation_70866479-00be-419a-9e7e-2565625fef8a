import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 数据库配置
    DB_HOST: str = os.getenv("DB_HOST", "*********")
    DB_PORT: int = int(os.getenv("DB_PORT", 23306))
    DB_USER: str = os.getenv("DB_USER", "indusaio_user")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "Sygy@2025")
    DB_NAME: str = os.getenv("DB_NAME", "indusaio_agent")
    
    # JWT配置
    JWT_SECRET: str = os.getenv("JWT_SECRET", "your-secret-key")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))
    
    # 服务器配置
    SERVER_HOST: str = os.getenv("SERVER_HOST", "127.0.0.1")
    SERVER_PORT: int = int(os.getenv("SERVER_PORT", 8000))
    
    # 许可证配置
    LICENSE_DEFAULT_DURATION: int = int(os.getenv("LICENSE_DEFAULT_DURATION", 365))  # 默认有效期天数

    # 软件包配置
    SOFTWARE_DIRECTORY: str = os.getenv("SOFTWARE_DIRECTORY", "api")  # 软件包所在目录（相对于server目录）
    SOFTWARE_SUPPORTED_FORMATS: str = os.getenv("SOFTWARE_SUPPORTED_FORMATS", "zip,rar,7z,tar.gz,tar")  # 支持的压缩格式

    # 用户默认密码
    DEFAULT_USER_PASSWORD: str = os.getenv("DEFAULT_USER_PASSWORD", "Cmsr@2025!")

    def _get_default_software_filename(self) -> str:
        """动态获取默认软件包文件名（支持多种压缩格式）"""
        import os
        import glob

        # 获取软件包目录路径
        server_dir = os.path.dirname(os.path.abspath(__file__))
        software_dir = os.path.join(server_dir, self.SOFTWARE_DIRECTORY)

        if os.path.exists(software_dir):
            # 从配置获取支持的格式
            formats_str = self.SOFTWARE_SUPPORTED_FORMATS
            supported_extensions = [ext.strip() for ext in formats_str.split(',')]

            # 构建格式优先级列表
            format_priority = []
            for ext in supported_extensions:
                pattern = f"*.{ext}"
                default_name = f"software.{ext}"
                format_priority.append((pattern, default_name))

            # 按优先级查找压缩文件
            for pattern, default_name in format_priority:
                files = glob.glob(os.path.join(software_dir, pattern))
                if files:
                    # 返回第一个找到的文件名（按字母顺序排序）
                    files.sort()
                    found_file = os.path.basename(files[0])
                    return found_file

        # 如果没有找到任何压缩文件，返回通用默认名（ZIP格式）
        return "software.zip"

    @property
    def SOFTWARE_FILENAME(self) -> str:
        """软件包文件名（支持环境变量或动态检测）"""
        return os.getenv("SOFTWARE_FILENAME", self._get_default_software_filename())

    @property
    def software_path(self) -> str:
        """获取软件包的完整路径"""
        import os
        # 获取当前文件所在目录（license/server）
        server_dir = os.path.dirname(os.path.abspath(__file__))
        # 拼接软件包路径
        return os.path.join(server_dir, self.SOFTWARE_DIRECTORY, self.SOFTWARE_FILENAME)

    @property
    def software_file_extension(self) -> str:
        """获取软件包文件的扩展名"""
        import os
        filename = self.SOFTWARE_FILENAME
        if '.' in filename:
            # 处理复合扩展名（如 .tar.gz）
            if filename.endswith('.tar.gz'):
                return 'tar.gz'
            else:
                return filename.split('.')[-1]
        return 'unknown'

    def get_software_info(self) -> dict:
        """获取软件包的详细信息"""
        import os

        info = {
            'filename': self.SOFTWARE_FILENAME,
            'directory': self.SOFTWARE_DIRECTORY,
            'full_path': self.software_path,
            'extension': self.software_file_extension,
            'exists': os.path.exists(self.software_path),
            'supported_formats': self.SOFTWARE_SUPPORTED_FORMATS.split(',')
        }

        if info['exists']:
            try:
                info['size'] = os.path.getsize(self.software_path)
                info['size_mb'] = round(info['size'] / (1024 * 1024), 2)
            except:
                info['size'] = 0
                info['size_mb'] = 0

        return info

settings = Settings()