import rsa
import hashlib
import base64
from passlib.context import Crypt<PERSON>ontext
from typing import <PERSON>ple
from rsa import PrivateKey, sign, decrypt
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def generate_rsa_key_pair() -> Tuple[str, str]:
    """
    生成RSA密钥对，返回(PEM格式公钥, PEM格式私钥)
    
    Returns:
        tuple: (public_key, private_key) 字符串元组
    """
    (pub_key, priv_key) = rsa.newkeys(2048)
    return (
        pub_key.save_pkcs1().decode('utf-8'),
        priv_key.save_pkcs1().decode('utf-8')
    )

def rsa_encrypt(data: str, public_key: str) -> bytes:
    """
    使用RSA公钥加密数据
    
    Args:
        data (str): 要加密的字符串数据
        public_key (str): PEM格式的公钥字符串
        
    Returns:
        bytes: 加密后的二进制数据
    """
    pub_key = rsa.PublicKey.load_pkcs1(public_key.encode('utf-8'))
    return rsa.encrypt(data.encode('utf-8'), pub_key)
    
def rsa_decrypt(encrypted_data: bytes, private_key: str) -> str:
    """
    使用RSA私钥解密数据
    
    Args:
        encrypted_data (bytes): 加密的二进制数据
        private_key (str): PEM格式的私钥字符串
        
    Returns:
        str: 解密后的原始字符串
    """
    priv_key = rsa.PrivateKey.load_pkcs1(private_key.encode('utf-8'))
    return rsa.decrypt(encrypted_data, priv_key).decode('utf-8')

def calculate_file_hash(file_path: str) -> str:
    """
    计算文件的SHA256哈希值
    
    Args:
        file_path (str): 要计算哈希的文件路径
        
    Returns:
        str: 文件的SHA256哈希值（十六进制字符串）
    """
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        while chunk := f.read(4096):
            sha256.update(chunk)
    return sha256.hexdigest()

def encode_base64(data: bytes) -> str:
    """
    将二进制数据编码为Base64字符串
    
    Args:
        data (bytes): 要编码的二进制数据
        
    Returns:
        str: Base64编码的字符串
    """
    return base64.b64encode(data).decode('utf-8')

def decode_base64(base64_str: str) -> bytes:
    """
    将Base64字符串解码为二进制数据
    
    Args:
        base64_str (str): Base64编码的字符串
        
    Returns:
        bytes: 解码后的二进制数据
    """
    return base64.b64decode(base64_str)

def rsa_decrypt_key(encrypted_key: str, private_key: str) -> str:
    encrypted_bytes = base64.b64decode(encrypted_key)
    priv_key = rsa.PrivateKey.load_pkcs1(private_key.encode('utf-8'))
    return decrypt(encrypted_bytes, priv_key).decode('utf-8')

def rsa_encrypt_license(data: str, private_key: str) -> str:
    priv_key = rsa.PrivateKey.load_pkcs1(private_key.encode('utf-8'))
    # 这里可以用签名，也可以用加密，视业务需求
    signature = sign(data.encode('utf-8'), priv_key, 'SHA-256')
    return base64.b64encode(signature).decode('utf-8')

def sign_cpu_with_private_key(cpu_str: str, private_key: str) -> str:
    """
    用私钥对CPU字符串做PKCS1+SHA1签名，返回base64字符串
    """
    priv_key = rsa.PrivateKey.load_pkcs1(private_key.encode('utf-8'))
    signature = sign(cpu_str.encode('utf-8'), priv_key, 'SHA-1')

    return base64.b64encode(signature).decode('utf-8')

# ==================== CPU专用加解密函数 ====================

def rsa_encrypt_cpu_only(cpu_str: str, public_key: str) -> str:
    """
    仅对CPU信息进行RSA加密

    Args:
        cpu_str (str): CPU信息字符串
        public_key (str): PEM格式的RSA公钥

    Returns:
        str: base64编码的加密CPU信息
    """
    try:
        pub_key = rsa.PublicKey.load_pkcs1(public_key.encode('utf-8'))
        encrypted_bytes = rsa.encrypt(cpu_str.encode('utf-8'), pub_key)
        return base64.b64encode(encrypted_bytes).decode('utf-8')
    except Exception as e:
        raise Exception(f"CPU信息加密失败: {str(e)}")

def rsa_decrypt_cpu_only(encrypted_cpu: str, private_key: str) -> str:
    """
    仅对CPU信息进行RSA解密

    Args:
        encrypted_cpu (str): base64编码的加密CPU信息
        private_key (str): PEM格式的RSA私钥

    Returns:
        str: 解密后的CPU信息字符串
    """
    try:
        encrypted_bytes = base64.b64decode(encrypted_cpu)
        priv_key = rsa.PrivateKey.load_pkcs1(private_key.encode('utf-8'))
        decrypted_bytes = rsa.decrypt(encrypted_bytes, priv_key)
        return decrypted_bytes.decode('utf-8')
    except Exception as e:
        raise Exception(f"CPU信息解密失败: {str(e)}")

def generate_cpu_machine_key(cpu_str: str) -> str:
    """
    基于CPU信息生成唯一机器码

    Args:
        cpu_str (str): CPU信息字符串

    Returns:
        str: 基于CPU的MD5机器码
    """
    # 为CPU-only license添加特殊前缀以区分
    cpu_data = f"CPU_ONLY:{cpu_str}"
    return hashlib.md5(cpu_data.encode()).hexdigest()

def create_cpu_license(cpu_str: str, user_id: int, email: str, expires_days: int = 365) -> str:
    """
    基于CPU信息创建license

    Args:
        cpu_str (str): CPU信息字符串
        user_id (int): 用户ID
        email (str): 用户邮箱
        expires_days (int): 过期天数，默认365天

    Returns:
        str: base64编码的license字符串
    """
    from datetime import datetime, timedelta

    # 生成过期时间
    expires_at = datetime.now() + timedelta(days=expires_days)

    # 生成CPU机器码
    cpu_machine_key = generate_cpu_machine_key(cpu_str)

    # 创建license内容：格式为 user_id|cpu_machine_key|cpu_str|expires_date
    license_plain = f"{user_id}|{cpu_machine_key}|{cpu_str}|{expires_at.strftime('%Y-%m-%d')}"

    # base64编码
    license_key = base64.b64encode(license_plain.encode()).decode()

    return license_key

def sign_cpu_license_with_private_key(license_data: str, private_key: str) -> str:
    """
    用私钥对CPU license数据进行签名

    Args:
        license_data (str): license数据字符串
        private_key (str): PEM格式的RSA私钥

    Returns:
        str: base64编码的签名
    """
    try:
        priv_key = rsa.PrivateKey.load_pkcs1(private_key.encode('utf-8'))
        signature = sign(license_data.encode('utf-8'), priv_key, 'SHA-256')
        return base64.b64encode(signature).decode('utf-8')
    except Exception as e:
        raise Exception(f"CPU license签名失败: {str(e)}")