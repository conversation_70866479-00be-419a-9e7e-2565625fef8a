"""
服务器主程序
"""
import uvicorn
from fastapi import FastAPI
from .database import engine, Base
from .api.license import router as license_router
from .api.users import router as users_router
from .api.user_management import router as user_management_router
from .api.auth import router as auth_router
from .config import settings
import asyncio
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from .services.license_service import LicenseService
from .database import get_db
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建数据库表（如果不存在）
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Software License System API",
    description="API for managing software licenses",
    version="1.0.0"
)

# 包含license路由
app.include_router(license_router, prefix="/api/v1/license")
# 包含users路由
app.include_router(users_router, prefix="/api/v1/users")
# 包含user_management路由
app.include_router(user_management_router, prefix="/api/v1")
# 包含auth路由
app.include_router(auth_router, prefix="/api/v1")

# 创建调度器
scheduler = AsyncIOScheduler()

async def expire_licenses_job():
    """定期任务：过期许可证检查"""
    try:
        db = next(get_db())
        service = LicenseService(db)
        expired_count = service.check_and_expire_licenses()
        print(f"Expired licenses checked: {expired_count} licenses marked as expired")
    except Exception as e:
        print(f"Error in expire_licenses_job: {str(e)}")
    finally:
        if db:
            db.close()

@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    # 添加定时任务（每天凌晨2点执行）
    scheduler.add_job(expire_licenses_job, "cron", hour=2)
    scheduler.start()

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    scheduler.shutdown()

if __name__ == "__main__":
    uvicorn.run(
        "license.server.main:app",
        host=settings.SERVER_HOST,
        port=settings.SERVER_PORT,
        reload=True
    )