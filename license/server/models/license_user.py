from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Text, SmallInteger
from sqlalchemy.sql import func
from ..database import Base

class UserInfo(Base):
    __tablename__ = 'user_info'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='许可证用户ID')
    user_id = Column(BigInteger, nullable=False, unique=True, comment='关联的用户ID')
    user_name = Column(String(255), nullable=True, comment='用户姓名')
    real_name = Column(String(255), nullable=True, comment='用户真实姓名')
    email = Column(String(255), nullable=False, unique=True, comment='用户邮箱(唯一标识)')
    company = Column(String(500), nullable=True, comment='用户所在公司')
    password = Column(String(255), nullable=False, comment='用户密码')
    public_key = Column(Text, nullable=False, comment='RSA公钥')
    private_key = Column(Text, nullable=False, comment='RSA私钥(加密存储)')
    created_at = Column(DateTime, nullable=False, server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment='最后更新时间')
    del_flag = Column(SmallInteger, nullable=False, default=0, comment='逻辑删除标志：0-未删除，1-已删除')
    role_id = Column(BigInteger, nullable=True, comment='角色ID，特殊ID：999999，表示角色是管理员')

    def __repr__(self):
        return f"<UserInfo(id={self.id}, user_id={self.user_id}, email={self.email})>"