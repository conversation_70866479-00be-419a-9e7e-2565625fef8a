from sqlalchemy import Column, <PERSON>I<PERSON>ger, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from ..database import Base

class LicenseInfo(Base):
    __tablename__ = 'license_info'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    license_user_id = Column(BigInteger, nullable=False)
    machine_key = Column(String(32), nullable=False)
    file_signature = Column(String(64), nullable=False)
    license_key = Column(Text, nullable=False)
    status = Column(Integer, nullable=False, default=0)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    activated_at = Column(DateTime, nullable=True)
    
    @property
    def status_name(self):
        status_map = {
            0: "未激活",
            1: "已激活",
            2: "已过期"
        }
        return status_map.get(self.status, "未知状态")
    
    def __repr__(self):
        return f"<LicenseInfo(id={self.id}, user_id={self.license_user_id}, status={self.status_name})>"