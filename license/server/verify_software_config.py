#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证软件包配置修改
"""

import os
import re

def verify_software_config_changes():
    """验证软件包配置修改"""
    print("=" * 60)
    print("验证软件包配置修改")
    print("=" * 60)
    
    # 1. 检查config.py中的配置
    print("1. 检查config.py中的软件包配置...")
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        config_checks = [
            ('SOFTWARE_FILENAME', '软件文件名配置'),
            ('SOFTWARE_DIRECTORY', '软件目录配置'),
            ('software_path', '软件路径属性'),
            ('def software_path', '软件路径方法')
        ]
        
        for check_str, desc in config_checks:
            if check_str in config_content:
                print(f"   ✅ {desc}: 已添加")
            else:
                print(f"   ❌ {desc}: 未找到")
                
    except Exception as e:
        print(f"   ❌ 检查config.py失败: {e}")
    
    print()
    
    # 2. 检查license.py中的修改
    print("2. 检查license.py中的硬编码移除...")
    try:
        with open('api/license.py', 'r', encoding='utf-8') as f:
            license_content = f.read()
        
        # 检查硬编码是否已移除
        hardcode_count = license_content.count('software_v1.zip')
        print(f"   硬编码 'software_v1.zip' 出现次数: {hardcode_count}")
        
        if hardcode_count == 0:
            print("   ✅ 所有硬编码已移除")
        else:
            print("   ❌ 仍有硬编码存在")
            lines = license_content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'software_v1.zip' in line:
                    print(f"      第{i}行: {line.strip()}")
        
        # 检查配置使用
        config_usage_checks = [
            ('settings.software_path', '使用软件路径配置'),
            ('settings.SOFTWARE_FILENAME', '使用软件文件名配置')
        ]
        
        for check_str, desc in config_usage_checks:
            count = license_content.count(check_str)
            if count > 0:
                print(f"   ✅ {desc}: 使用了 {count} 次")
            else:
                print(f"   ❌ {desc}: 未使用")
                
    except Exception as e:
        print(f"   ❌ 检查license.py失败: {e}")
    
    print()
    
    # 3. 检查软件包文件是否存在
    print("3. 检查软件包文件...")
    try:
        software_file_path = os.path.join('api', 'software_v1.zip')
        
        if os.path.exists(software_file_path):
            file_size = os.path.getsize(software_file_path)
            print(f"   ✅ 软件包文件存在: {software_file_path}")
            print(f"   文件大小: {file_size} 字节")
        else:
            print(f"   ⚠️ 软件包文件不存在: {software_file_path}")
            print("   （这可能是正常的，如果文件确实不存在）")
            
    except Exception as e:
        print(f"   ❌ 检查软件包文件失败: {e}")
    
    print()
    
    # 4. 模拟配置路径生成
    print("4. 模拟配置路径生成...")
    try:
        # 模拟配置类的路径生成逻辑
        server_dir = os.path.dirname(os.path.abspath(__file__))
        software_directory = "api"  # 默认值
        software_filename = "software_v1.zip"  # 默认值
        
        generated_path = os.path.join(server_dir, software_directory, software_filename)
        expected_path = os.path.join(server_dir, 'api', 'software_v1.zip')
        
        print(f"   服务器目录: {server_dir}")
        print(f"   生成的路径: {generated_path}")
        print(f"   期望的路径: {expected_path}")
        print(f"   路径匹配: {generated_path == expected_path}")
        
        if generated_path == expected_path:
            print("   ✅ 路径生成逻辑正确")
        else:
            print("   ❌ 路径生成逻辑错误")
            
    except Exception as e:
        print(f"   ❌ 路径生成模拟失败: {e}")
    
    print()
    
    # 5. 检查环境变量支持
    print("5. 检查环境变量支持...")
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        env_checks = [
            ('os.getenv("SOFTWARE_FILENAME"', '软件文件名环境变量'),
            ('os.getenv("SOFTWARE_DIRECTORY"', '软件目录环境变量')
        ]
        
        for check_str, desc in env_checks:
            if check_str in config_content:
                print(f"   ✅ {desc}: 支持")
            else:
                print(f"   ❌ {desc}: 不支持")
                
    except Exception as e:
        print(f"   ❌ 检查环境变量支持失败: {e}")
    
    print()
    
    # 6. 生成使用示例
    print("6. 配置使用示例...")
    print("   环境变量方式:")
    print("   export SOFTWARE_FILENAME=my_software.zip")
    print("   export SOFTWARE_DIRECTORY=packages")
    print()
    print("   代码中使用:")
    print("   from config import settings")
    print("   software_path = settings.software_path")
    print("   filename = settings.SOFTWARE_FILENAME")
    
    print()
    print("=" * 60)
    print("软件包配置验证完成")
    print("=" * 60)

if __name__ == "__main__":
    verify_software_config_changes()
