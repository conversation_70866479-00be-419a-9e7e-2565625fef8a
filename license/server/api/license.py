import logging
from fastapi import APIRouter, Depends, HTTPException, Response
from ..services.license_user_service import LicenseUserService
from ..services.license_service import LicenseService, LicenseStatus
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List
import io
import csv
from datetime import datetime, timedelta
import json
import os
from ..schemas import (
    LicenseCreate,
    LicenseResponse,
    LicenseUpdate,
    LicenseListResponse,
    LicenseStatusUpdate,
    LicenseUserResponse,
    LicenseUserCreate,
    LicenseValidation
)
from ..database import get_db
from ..utils.auth import get_current_user
from ..exceptions import (
    LicenseNotFoundError,
    LicenseActivationError,
    LicenseUserNotFoundError,
    DuplicateLicenseUserError
)
from ..utils.crypto import generate_rsa_key_pair
from ..schemas import DownloadRequest
from ..schemas import LicenseActivateRequest, LicenseStringResponse
from ..schemas import (
    CpuInfoRequest, CpuInfoResponse,
    CpuLicenseCreateRequest, CpuLicenseCreateResponse,
    CpuLicenseActivateRequest, CpuLicenseActivateResponse
)
from ..utils.crypto import rsa_decrypt_key, rsa_encrypt_license
from ..utils.crypto import (
    rsa_decrypt_cpu_only,
    generate_cpu_machine_key,
    create_cpu_license,
    sign_cpu_license_with_private_key
)
from fastapi import BackgroundTasks
from ..config import settings
from sqlalchemy.orm import Session
from ..models.license import LicenseInfo
from ..models.license_user import UserInfo
import tempfile
import hashlib
import random
import zipfile
import string
import base64
from pydantic import BaseModel, EmailStr
from ..utils.crypto import sign_cpu_with_private_key

router = APIRouter()

logger = logging.getLogger("license_api")
logger.setLevel(logging.DEBUG)

class CreateLicenseRequest(BaseModel):
    email: EmailStr
    key_content: str

class CpuSignRequest(BaseModel):
    email: str
    cpu: str

class CpuSignResponse(BaseModel):
    signed_cpu: str

@router.post("/users", response_model=LicenseUserResponse, status_code=201)
def create_license_user(
    user_data: LicenseUserCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    创建新的许可证用户
    
    - **user_id**: 系统用户ID
    - **email**: 用户邮箱
    """
    logger.debug(f"创建用户: user_id={user_data.user_id}, email={user_data.email}")
    try:
        user_service = LicenseUserService(db)
        license_user = user_service.create_license_user(user_data.user_id, user_data.email)
        logger.debug(f"用户创建成功: id={license_user.id}")
        return LicenseUserResponse(
            id=license_user.id,
            user_id=license_user.user_id,
            email=license_user.email
        )
    except DuplicateLicenseUserError as e:
        logger.debug(f"用户重复: {str(e)}")
        raise HTTPException(status_code=409, detail=str(e))
    except Exception as e:
        logger.debug(f"用户创建异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/licenses", response_model=LicenseResponse, status_code=201)
def create_license(
    license_data: LicenseCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    创建新许可证
    
    - **license_user_id**: 许可证用户ID
    - **machine_key**: 机器唯一标识码
    """
    try:
        service = LicenseService(db)
        license = service.generate_license(license_data.license_user_id, license_data.machine_key)
        return LicenseResponse(
            id=license.id,
            license_user_id=license.license_user_id,
            status=license.status,
            expires_at=license.expires_at,
            activated_at=license.activated_at
        )
    except LicenseUserNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/activate", response_model=LicenseStringResponse)
def activate_license(req: LicenseActivateRequest, db: Session = Depends(get_db)):
    """
    激活许可证
    
    - **email**: 购买时使用的邮箱
    - **key**: 加密的Key文件内容
    
    Returns:
        dict: 包含加密后的license字符串
    """
    email = req.email
    encrypted_key = req.key
    logger.debug(f"激活请求: email={email}")
    
    # 1. 查找用户并获取私钥
    user_service = LicenseUserService(db)
    user = user_service.get_license_user_by_email(email)
    if not user:
        logger.debug(f"激活失败，用户不存在: {email}")
        raise HTTPException(status_code=404, detail="用户不存在")
    
    private_key = user.private_key
    if not private_key:
        logger.debug("激活失败，用户私钥不存在")
        raise HTTPException(status_code=400, detail="用户私钥不存在")
    
    try:
        # 2. 使用RSA私钥解密Key文件内容
        decrypted_key = rsa_decrypt_key(encrypted_key, private_key)
        logger.debug(f"Key文件解密成功")
        
        # 3. 解析Key文件内容（格式：mac|cpu|disk|sha256）
        try:
            mac, cpu, disk, file_sha256 = decrypted_key.split('|')
        except ValueError:
            logger.debug("Key文件格式错误")
            raise HTTPException(status_code=400, detail="Key文件格式错误，应为mac|cpu|disk|sha256")
        
        # 4. 验证文件SHA256签名
        software_path = settings.software_path
        if not os.path.exists(software_path):
            logger.debug(f"软件包不存在: {software_path}")
            raise HTTPException(status_code=404, detail="软件包不存在")
            
        with open(software_path, 'rb') as f:
            software_content = f.read()
            actual_sha256 = hashlib.sha256(software_content).hexdigest()
            
        if file_sha256 != actual_sha256:
            logger.debug(f"SHA256验证失败: 期望={actual_sha256}, 实际={file_sha256}")
            raise HTTPException(status_code=400, detail="文件签名验证失败")
        
        # 5. 生成过期时间
        expire_time = (datetime.utcnow() + timedelta(days=settings.LICENSE_DEFAULT_DURATION)).isoformat()
        
        # 6. 生成license字符串（包含所有必要信息）
        license_data = f"{decrypted_key}|{email}|{expire_time}"
        logger.debug(f"生成license数据: {license_data[:30]}... (已截断)")
        
        # 7. 使用RSA私钥加密license字符串
        license_str = rsa_encrypt_license(license_data, private_key)
        logger.debug(f"生成license字符串: {license_str[:30]}... (已截断)")
        
        # 8. 更新license_info表中的记录
        machine_key = hashlib.md5(decrypted_key.encode()).hexdigest()
        current_time = datetime.utcnow()
        
        # 查找对应的license记录
        license_info = db.query(LicenseInfo).filter_by(
            license_user_id=user.id,
            machine_key=machine_key
        ).first()
        
        if not license_info:
            logger.debug(f"未找到对应的license记录: user_id={user.id}, machine_key={machine_key}")
            raise HTTPException(status_code=404, detail="未找到对应的license记录")
            
        # 检查license状态
        if license_info.status == 1:  # 已激活
            logger.debug("license已经激活")
            raise HTTPException(status_code=400, detail="license已经激活")
        elif license_info.status == 2:  # 已过期
            logger.debug("license已过期")
            raise HTTPException(status_code=400, detail="license已过期")
            
        # 更新license状态和激活时间
        license_info.status = 1  # 设置为已激活
        license_info.activated_at = current_time
        license_info.license_key = license_str  # 更新加密后的license字符串
        
        try:
            db.commit()
            logger.debug(f"license激活成功: id={license_info.id}")
        except Exception as e:
            db.rollback()
            logger.error(f"更新license状态失败: {str(e)}")
            raise HTTPException(status_code=500, detail="更新license状态失败，请重新申请和激活License！")
        
        return {"license": license_str}
        
    except Exception as e:
        logger.error(f"激活过程发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"激活失败: {str(e)}")

@router.post("/licenses/validate")
def validate_license(
    validation_data: LicenseValidation,
    db: Session = Depends(get_db)
):
    """
    验证许可证有效性
    
    - **license_key**: Base64编码的许可证密钥
    - **machine_key**: 机器唯一标识码
    
    Returns:
        dict: 包含验证结果的字典
    """
    try:
        service = LicenseService(db)
        is_valid = service.validate_license(validation_data.license_key, validation_data.machine_key)
        return {"valid": is_valid, "message": "License is valid"}
    except LicenseActivationError as e:
        return {"valid": False, "message": str(e)}
    except Exception as e:
        return {"valid": False, "message": f"Validation error: {str(e)}"}

@router.post("/expire-check")
def expire_license_check(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    执行许可证过期检查
    
    返回:
        dict: 包含过期许可证数量的字典
    """
    try:
        service = LicenseService(db)
        expired_count = service.check_and_expire_licenses()
        return {"expired_count": expired_count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    

@router.post("/download", response_class=StreamingResponse)
async def download_package(request: DownloadRequest, db: Session = Depends(get_db)):
    """
    下载软件包和许可证文件
    """
    try:
        # 1. 验证用户
        user_service = LicenseUserService(db)
        user = user_service.authenticate_user(request.email, request.password)
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # 2. 获取用户公钥
        public_key = user.public_key
        if not public_key:
            raise HTTPException(status_code=400, detail="Public key not found")

        # 3. 创建内存中的zip文件
        mem_zip = io.BytesIO()
        with zipfile.ZipFile(mem_zip, 'w', zipfile.ZIP_DEFLATED) as zf:
            # 添加软件包
            software_path = settings.software_path
            if os.path.exists(software_path):
                # 计算软件包的SHA256
                with open(software_path, 'rb') as f:
                    software_content = f.read()
                    software_sha256 = hashlib.sha256(software_content).hexdigest()
                zf.write(software_path, settings.SOFTWARE_FILENAME)
            else:
                raise HTTPException(status_code=404, detail="Software package not found")

            # 添加签名信息文件
            sign_info = (
                f"User Account: {user.email}\n"
                f"Register Time: {datetime.utcnow().isoformat()}\n"
                f"Software SHA256 Signature: {software_sha256}"
            )
            zf.writestr("sign_info.txt", sign_info)

            # 添加RSA公钥文件
            zf.writestr("pub_key_rsa.txt", public_key)

        # 4. 将指针移到开始位置
        mem_zip.seek(0)
        zip_bytes = mem_zip.getvalue()
        file_size = len(zip_bytes)
        # 5. 返回下载流，设置Content-Length
        logger.debug(f"打包zip完成，返回下载包")
        return StreamingResponse(
            io.BytesIO(zip_bytes),
            media_type="application/x-zip-compressed",
            headers={
                "Content-Disposition": "attachment; filename=package.zip",
                "Content-Length": str(file_size)
            }
        )

    except Exception as e:
        logger.error(f"下载包生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/download_info")
async def download_info(request: DownloadRequest, db: Session = Depends(get_db)):
    """
    获取指定用户的SHA256签名和RSA公钥信息
    """
    user_service = LicenseUserService(db)
    user = user_service.authenticate_user(request.email, request.password)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    # 读取软件包SHA256
    software_path = settings.software_path
    if os.path.exists(software_path):
        with open(software_path, 'rb') as f:
            software_content = f.read()
            software_sha256 = hashlib.sha256(software_content).hexdigest()
    else:
        raise HTTPException(status_code=404, detail="Software package not found")
    return {
        "sha256": software_sha256,
        "rsaPubKey": user.public_key
    }

@router.post("/create_license", response_model=LicenseStringResponse)
def create_license_api(
    req: CreateLicenseRequest,
    db: Session = Depends(get_db)
):
    """
    创建并激活license
    
    - **email**: 用户邮箱
    - **key_content**: 客户端用RSA公钥加密后的key内容
    
    Returns:
        dict: 包含加密后的license字符串
    """
    # 1. 查找用户
    user = db.query(UserInfo).filter_by(email=req.email).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
        
    # 2. 用私钥解密key_content
    try:
        raw = rsa_decrypt_key(req.key_content, user.private_key)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"机器Key解密失败，请检查Key文件内容是否正确: {str(e)}")
        
    # 3. 拆分原始字符串（mac|cpu|disk|sha256）
    try:
        mac, cpu, disk, sha256 = raw.split('|')
    except Exception:
        raise HTTPException(status_code=400, detail="机器Key内容格式错误，应为mac|cpu|disk|sha256")
        
    # 4. 生成唯一machine_key（用原始字符串做MD5）
    machine_key = hashlib.md5(raw.encode()).hexdigest()
    
    # 5. 检查该用户该机器是否已生成过license
    exist = db.query(LicenseInfo).filter_by(license_user_id=user.id, machine_key=machine_key).first()
    if exist:
        raise HTTPException(status_code=409, detail="该机器已生成过license")
        
    # 6. 生成license内容
    expires_at = datetime.now() + timedelta(days=365)
    license_plain = f"{user.id}|{machine_key}|{sha256}|{expires_at.strftime('%Y-%m-%d')}"
    license_key = base64.b64encode(license_plain.encode()).decode()
    
    # 7. 创建license记录
    current_time = datetime.now()
    license_info = LicenseInfo(
        license_user_id=user.id,
        machine_key=machine_key,
        file_signature=sha256,
        license_key=license_key,
        status=1,  # 直接设置为已激活状态
        expires_at=expires_at,
        created_at=current_time,
        activated_at=current_time  # 设置激活时间
    )
    
    try:
        db.add(license_info)
        db.commit()
        logger.debug(f"创建并激活license成功: id={license_info.id}")
    except Exception as e:
        db.rollback()
        logger.error(f"创建license失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建license失败，请重试！")
    
    return {"license": license_key}

@router.post("/sign_cpu", response_model=CpuSignResponse)
def sign_cpu(
    req: CpuSignRequest,
    db: Session = Depends(get_db)
):
    """
    用用户私钥对CPU字符串做PKCS1+SHA1签名
    """
    user = db.query(UserInfo).filter_by(email=req.email).first()
    if not user or not user.private_key:
        raise HTTPException(status_code=404, detail="用户不存在或未设置私钥")

    signed_cpu = sign_cpu_with_private_key(req.cpu, user.private_key)
    return CpuSignResponse(signed_cpu=signed_cpu)

# ==================== CPU专用API端点 ====================

@router.post("/cpu_info", response_model=CpuInfoResponse)
async def get_cpu_info(request: CpuInfoRequest, db: Session = Depends(get_db)):
    """
    获取CPU信息处理所需的RSA公钥

    Args:
        request: 包含用户邮箱和密码的请求

    Returns:
        CpuInfoResponse: 包含RSA公钥的响应
    """
    logger.debug(f"CPU信息请求: email={request.email}")

    # 验证用户身份
    user_service = LicenseUserService(db)
    user = user_service.authenticate_user(request.email, request.password)
    if not user:
        logger.debug(f"CPU信息获取失败，用户认证失败: {request.email}")
        raise HTTPException(status_code=401, detail="邮箱或密码错误")

    if not user.public_key:
        logger.debug("CPU信息获取失败，用户公钥不存在")
        raise HTTPException(status_code=400, detail="用户公钥不存在")

    logger.debug(f"CPU信息获取成功: {request.email}")
    return CpuInfoResponse(rsaPubKey=user.public_key)

@router.post("/cpu_license_create", response_model=CpuLicenseCreateResponse)
def create_cpu_license_api(
    req: CpuLicenseCreateRequest,
    db: Session = Depends(get_db)
):
    """
    基于CPU信息创建并激活license

    Args:
        req: 包含邮箱和加密CPU信息的请求

    Returns:
        CpuLicenseCreateResponse: 包含license信息的响应
    """
    logger.debug(f"CPU license创建请求: email={req.email}")

    # 1. 查找用户
    user = db.query(UserInfo).filter_by(email=req.email).first()
    if not user:
        logger.debug(f"CPU license创建失败，用户不存在: {req.email}")
        raise HTTPException(status_code=404, detail="用户不存在")

    # 2. 用私钥解密CPU信息
    try:
        cpu_str = rsa_decrypt_cpu_only(req.encrypted_cpu, user.private_key)
        logger.debug(f"CPU信息解密成功: {cpu_str}")
    except Exception as e:
        logger.debug(f"CPU信息解密失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"CPU信息解密失败: {str(e)}")

    # 3. 生成CPU机器码
    try:
        cpu_machine_key = generate_cpu_machine_key(cpu_str)
        logger.debug(f"CPU机器码生成成功: {cpu_machine_key}")
    except Exception as e:
        logger.debug(f"CPU机器码生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CPU机器码生成失败: {str(e)}")

    # 4. 检查该用户该CPU是否已生成过license
    exist = db.query(LicenseInfo).filter_by(
        license_user_id=user.id,
        machine_key=cpu_machine_key
    ).first()
    if exist:
        logger.debug(f"CPU license已存在: {cpu_machine_key}")
        raise HTTPException(status_code=409, detail="该CPU已生成过license")

    # 5. 创建CPU license
    try:
        license_key = create_cpu_license(cpu_str, user.id, user.email, 365)
        logger.debug(f"CPU license创建成功")
    except Exception as e:
        logger.debug(f"CPU license创建失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CPU license创建失败: {str(e)}")

    # 6. 保存到数据库
    try:
        expires_at = datetime.now() + timedelta(days=365)
        current_time = datetime.now()

        license_info = LicenseInfo(
            license_user_id=user.id,
            machine_key=cpu_machine_key,
            file_signature="CPU_ONLY",  # 标记为CPU-only license
            license_key=license_key,
            status=1,  # 直接设置为已激活状态
            expires_at=expires_at,
            created_at=current_time,
            activated_at=current_time
        )

        db.add(license_info)
        db.commit()
        logger.debug(f"CPU license数据库保存成功: id={license_info.id}")

    except Exception as e:
        db.rollback()
        logger.error(f"CPU license数据库保存失败: {str(e)}")
        raise HTTPException(status_code=500, detail="CPU license保存失败，请重试")

    return CpuLicenseCreateResponse(
        license=license_key,
        cpu_machine_key=cpu_machine_key,
        expires_at=expires_at.strftime('%Y-%m-%d')
    )

@router.post("/cpu_license_activate", response_model=CpuLicenseActivateResponse)
def activate_cpu_license_api(
    req: CpuLicenseActivateRequest,
    db: Session = Depends(get_db)
):
    """
    基于CPU信息激活已存在的license

    Args:
        req: 包含邮箱和加密CPU信息的请求

    Returns:
        CpuLicenseActivateResponse: 包含激活后的license信息
    """
    logger.debug(f"CPU license激活请求: email={req.email}")

    # 1. 查找用户
    user = db.query(UserInfo).filter_by(email=req.email).first()
    if not user:
        logger.debug(f"CPU license激活失败，用户不存在: {req.email}")
        raise HTTPException(status_code=404, detail="用户不存在")

    # 2. 用私钥解密CPU信息
    try:
        cpu_str = rsa_decrypt_cpu_only(req.encrypted_cpu, user.private_key)
        logger.debug(f"CPU信息解密成功: {cpu_str}")
    except Exception as e:
        logger.debug(f"CPU信息解密失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"CPU信息解密失败: {str(e)}")

    # 3. 生成CPU机器码
    try:
        cpu_machine_key = generate_cpu_machine_key(cpu_str)
        logger.debug(f"CPU机器码生成成功: {cpu_machine_key}")
    except Exception as e:
        logger.debug(f"CPU机器码生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CPU机器码生成失败: {str(e)}")

    # 4. 查找对应的license记录
    license_info = db.query(LicenseInfo).filter_by(
        license_user_id=user.id,
        machine_key=cpu_machine_key
    ).first()

    if not license_info:
        logger.debug(f"CPU license不存在: {cpu_machine_key}")
        raise HTTPException(status_code=404, detail="该CPU的license不存在，请先创建")

    # 5. 检查license状态和有效期
    current_time = datetime.now()
    if license_info.expires_at < current_time:
        logger.debug(f"CPU license已过期: {license_info.expires_at}")
        raise HTTPException(status_code=400, detail="License已过期")

    # 6. 激活license（如果未激活）
    if license_info.status != 1:
        try:
            license_info.status = 1
            license_info.activated_at = current_time
            db.commit()
            logger.debug(f"CPU license激活成功: id={license_info.id}")
        except Exception as e:
            db.rollback()
            logger.error(f"CPU license激活失败: {str(e)}")
            raise HTTPException(status_code=500, detail="License激活失败，请重试")

    # 7. 对license进行签名
    try:
        signed_license = sign_cpu_license_with_private_key(license_info.license_key, user.private_key)
        logger.debug(f"CPU license签名成功")
    except Exception as e:
        logger.debug(f"CPU license签名失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"License签名失败: {str(e)}")

    return CpuLicenseActivateResponse(
        license=license_info.license_key,
        signed_license=signed_license,
        expires_at=license_info.expires_at.strftime('%Y-%m-%d')
    )