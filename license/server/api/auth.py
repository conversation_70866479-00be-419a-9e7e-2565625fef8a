from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from ..database import get_db
from ..services.license_user_service import LicenseUserService
from ..schemas import Token
from ..utils.auth import create_access_token
import logging
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/login")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    用户登录
    
    Args:
        form_data (OAuth2PasswordRequestForm): 包含用户名和密码的表单数据
        db (Session): 数据库会话
        
    Returns:
        Token: 包含访问令牌的响应
        
    Raises:
        HTTPException: 当认证失败时
    """
    logger.info(f"收到登录请求: username={form_data.username}")
    user_service = LicenseUserService(db)
    user = user_service.authenticate_user(form_data.username, form_data.password)
    if not user:
        logger.warning(f"登录失败: 用户名或密码错误, username={form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user.email})
    logger.info(f"登录成功: {user.email}")
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "user_name": user.user_name,
            "email": user.email,
            "role_id": user.role_id,
            "id": user.id,
            "user_id": user.user_id,
            "real_name": user.real_name,
            "company": user.company
        }
    } 