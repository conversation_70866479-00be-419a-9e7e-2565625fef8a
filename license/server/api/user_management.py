from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from ..database import get_db
from ..services.license_user_service import LicenseUserService
from ..schemas import LicenseUserCreate, LicenseUserResponse, UserListResponse
from ..exceptions import DuplicateLicenseUserError, LicenseUserNotFoundError
from ..config import settings

router = APIRouter()

@router.post("/user/add", response_model=LicenseUserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: dict,
    db: Session = Depends(get_db)
):
    """
    创建新用户
    
    Args:
        user_data (dict): 用户创建数据
        db (Session): 数据库会话
        
    Returns:
        LicenseUserResponse: 创建的用户信息
        
    Raises:
        HTTPException: 当邮箱已存在时
    """
    try:
        user_service = LicenseUserService(db)
        user = user_service.create_license_user(
            user_id=None,
            email=user_data.get('email'),
            password=getattr(settings, 'DEFAULT_USER_PASSWORD', 'Cmsr@2025!'),
            user_name=user_data.get('user_name'),
            real_name=user_data.get('real_name'),
            company=user_data.get('company'),
            role_id=user_data.get('role_id')
        )
        return user
    except DuplicateLicenseUserError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/user/list", response_model=UserListResponse)
async def list_users(
    skip: int = 0,
    limit: int = 10,
    user_name: str = '',
    real_name: str = '',
    email: str = '',
    company: str = '',
    db: Session = Depends(get_db)
):
    """
    获取用户列表
    
    Args:
        skip (int): 跳过的记录数
        limit (int): 返回的最大记录数
        user_name (str): 用户名
        real_name (str): 真实姓名
        email (str): 邮箱
        company (str): 公司
        db (Session): 数据库会话
        
    Returns:
        UserListResponse: 用户列表和总数
    """
    user_service = LicenseUserService(db)
    return user_service.get_user_list_with_total(skip=skip, limit=limit, user_name=user_name, real_name=real_name, email=email, company=company)

@router.get("/user/{user_id}", response_model=LicenseUserResponse)
async def get_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    获取指定用户信息

    Args:
        user_id (int): 用户ID
        db (Session): 数据库会话

    Returns:
        LicenseUserResponse: 用户信息

    Raises:
        HTTPException: 当用户不存在时
    """
    user_service = LicenseUserService(db)
    user = user_service.get_license_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with ID {user_id} not found"
        )
    return user

@router.get("/user/email/{email}", response_model=LicenseUserResponse)
async def get_user_by_email(
    email: str,
    db: Session = Depends(get_db)
):
    """
    根据邮箱获取用户信息

    Args:
        email (str): 用户邮箱
        db (Session): 数据库会话

    Returns:
        LicenseUserResponse: 用户信息

    Raises:
        HTTPException: 当用户不存在时
    """
    user_service = LicenseUserService(db)
    user = user_service.get_license_user_by_email(email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with email {email} not found"
        )
    return user

@router.put("/user/{user_id}", response_model=LicenseUserResponse)
async def update_user(
    user_id: int,
    user_data: LicenseUserCreate,
    db: Session = Depends(get_db)
):
    """
    更新用户信息
    
    Args:
        user_id (int): 用户ID
        user_data (LicenseUserCreate): 更新的用户数据
        db (Session): 数据库会话
        
    Returns:
        LicenseUserResponse: 更新后的用户信息
        
    Raises:
        HTTPException: 当用户不存在或邮箱已存在时
    """
    try:
        user_service = LicenseUserService(db)
        password = user_data.password if user_data.password else "Cmsr@2025!"
        user = user_service.update_user(
            user_id=user_id,
            email=user_data.email,
            user_name=user_data.user_name,
            real_name=user_data.real_name,
            password=password,
            company=user_data.company,
            role_id=user_data.role_id
        )
        return user
    except LicenseUserNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except DuplicateLicenseUserError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/user/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    删除用户
    
    Args:
        user_id (int): 用户ID
        db (Session): 数据库会话
        
    Raises:
        HTTPException: 当用户不存在时
    """
    try:
        user_service = LicenseUserService(db)
        user_service.delete_user(user_id)
    except LicenseUserNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        ) 