"""
License用户API
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from ..services.license_user_service import LicenseUserService
from ..schemas import LicenseUserCreate, LicenseUserResponse
from ..database import get_db

router = APIRouter()

@router.post("/license-users", response_model=LicenseUserResponse)
def create_license_user(
    user_data: LicenseUserCreate,
    db: Session = Depends(get_db)
):
    service = LicenseUserService(db)
    try:
        user = service.create_license_user(user_data.user_id, user_data.email, user_data.password)
        return user
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))