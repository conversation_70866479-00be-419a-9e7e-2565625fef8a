from sqlalchemy.orm import Session
from ..models.license_user import UserInfo
from ..utils.crypto import generate_rsa_key_pair, hash_password, verify_password
from ..exceptions import LicenseUserNotFoundError, DuplicateLicenseUserError
import logging

logger = logging.getLogger(__name__)

class LicenseUserService:
    def __init__(self, db: Session):
        self.db = db
        
    def create_license_user(self, user_id: int, email: str, password: str, user_name: str = None, real_name: str = None, company: str = None, role_id: int = None) -> UserInfo:
        """
        创建新的许可证用户
        
        Args:
            user_id (int): 用户ID
            email (str): 用户邮箱
            password (str): 用户密码
            user_name (str, optional): 用户名
            real_name (str, optional): 真实姓名
            company (str, optional): 公司
            role_id (int, optional): 角色ID
            
        Returns:
            UserInfo: 新创建的用户对象
            
        Raises:
            DuplicateLicenseUserError: 当邮箱已存在时
        """
        # 检查邮箱是否已存在
        existing_user = self.db.query(UserInfo).filter(UserInfo.email == email).first()
        if existing_user:
            raise DuplicateLicenseUserError(f"Email {email}")
        
        # 生成RSA密钥对
        public_key, private_key = generate_rsa_key_pair()
        
        # user_id自增（1001起）
        max_user = self.db.query(UserInfo).order_by(UserInfo.user_id.desc()).first()
        next_user_id = 1001 if not max_user or not max_user.user_id else max(max_user.user_id + 1, 1001)
        
        # 创建新用户
        user = UserInfo(
            user_id=next_user_id,
            user_name=user_name,
            real_name=real_name,
            email=email,
            password=hash_password(password),
            public_key=public_key,
            private_key=private_key,
            company=company,
            role_id=role_id,
            del_flag=0
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user
        
    def get_all_users(self, skip: int = 0, limit: int = 10) -> list[UserInfo]:
        """
        获取用户列表
        
        Args:
            skip (int): 跳过的记录数
            limit (int): 返回的最大记录数
            
        Returns:
            list[UserInfo]: 用户列表
        """
        return self.db.query(UserInfo).filter(UserInfo.del_flag == 0).order_by(UserInfo.updated_at.desc()).offset(skip).limit(limit).all()
        
    def get_license_user_by_id(self, license_user_id: int) -> UserInfo:
        """
        根据ID获取许可证用户
        
        Args:
            license_user_id (int): 许可证用户ID
            
        Returns:
            UserInfo: 找到的许可证用户对象，如果未找到返回None
        """
        return self.db.query(UserInfo).filter(
            UserInfo.id == license_user_id
        ).first()
        
    def get_license_user_by_user_id(self, user_id: int) -> UserInfo:
        """
        根据系统用户ID获取许可证用户
        
        Args:
            user_id (int): 系统用户ID
            
        Returns:
            UserInfo: 找到的许可证用户对象，如果未找到返回None
        """
        return self.db.query(UserInfo).filter(
            UserInfo.user_id == user_id
        ).first()
        
    def get_license_user_by_email(self, email: str) -> UserInfo:
        """
        根据邮箱获取许可证用户
        
        Args:
            email (str): 用户邮箱
            
        Returns:
            UserInfo: 找到的许可证用户对象，如果未找到返回None
        """
        return self.db.query(UserInfo).filter(
            UserInfo.email == email
        ).first()
        
    def update_user(self, user_id: int, email: str = None, user_name: str = None, 
                   real_name: str = None, password: str = None, company: str = None, role_id: int = None) -> UserInfo:
        """
        更新用户信息
        
        Args:
            user_id (int): 用户ID
            email (str, optional): 新邮箱
            user_name (str, optional): 新用户名
            real_name (str, optional): 新真实姓名
            password (str, optional): 新密码
            company (str, optional): 新公司
            role_id (int, optional): 新角色ID
            
        Returns:
            UserInfo: 更新后的用户对象
            
        Raises:
            LicenseUserNotFoundError: 当用户不存在时
            DuplicateLicenseUserError: 当新邮箱已被使用时
        """
        user = self.get_license_user_by_id(user_id)
        if not user:
            raise LicenseUserNotFoundError(f"ID {user_id}")
            
        if email and email != user.email:
            # 检查新邮箱是否已被使用
            existing_user = self.get_license_user_by_email(email)
            if existing_user:
                raise DuplicateLicenseUserError(f"Email {email}")
            user.email = email
            
        if user_name is not None:
            user.user_name = user_name
        if real_name is not None:
            user.real_name = real_name
        if password:
            user.password = hash_password(password)
        if company is not None:
            user.company = company
        if role_id is not None:
            user.role_id = role_id
        self.db.commit()
        self.db.refresh(user)
        return user

    def delete_user(self, user_id: int):
        """
        删除用户
        
        Args:
            user_id (int): 用户ID
            
        Raises:
            LicenseUserNotFoundError: 当用户不存在时
        """
        user = self.get_license_user_by_id(user_id)
        if not user:
            raise LicenseUserNotFoundError(f"ID {user_id}")
            
        user.del_flag = 1
        self.db.commit()

    def authenticate_user(self, email: str, password: str) -> UserInfo:
        """
        验证用户凭据
        
        Args:
            email (str): 用户邮箱
            password (str): 用户密码
            
        Returns:
            UserInfo: 如果验证成功返回用户对象，否则返回None
        """
        logger.info(f"开始验证用户: email={email}")
        user = self.get_license_user_by_email(email)
        if not user:
            logger.warning(f"用户不存在: email={email}")
            return None
            
        logger.info(f"找到用户: id={user.id}, email={user.email}, password_hash={user.password[:20]}...")
        if not verify_password(password, user.password):
            logger.warning(f"密码验证失败: email={email}")
            return None
            
        logger.info(f"用户验证成功: email={email}")
        return user

    def get_user_list_with_total(self, skip: int = 0, limit: int = 10, user_name: str = '', real_name: str = '', email: str = '', company: str = ''):
        query = self.db.query(UserInfo).filter(UserInfo.del_flag == 0)
        if user_name:
            query = query.filter(UserInfo.user_name.like(f"%{user_name}%"))
        if real_name:
            query = query.filter(UserInfo.real_name.like(f"%{real_name}%"))
        if email:
            query = query.filter(UserInfo.email.like(f"%{email}%"))
        if company:
            query = query.filter(UserInfo.company.like(f"%{company}%"))
        total = query.count()
        items = query.order_by(UserInfo.updated_at.desc()).offset(skip).limit(limit).all()
        return {"total": total, "items": items}