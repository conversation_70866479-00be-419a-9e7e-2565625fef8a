from datetime import datetime, timedelta
import json
from sqlalchemy.orm import Session
from ..models.license import LicenseInfo
from ..models.license_user import UserInfo
from ..utils.crypto import rsa_encrypt, encode_base64
from ..config import settings
from ..exceptions import (
    LicenseUserNotFoundError, 
    LicenseActivationError,
    CryptoOperationError
)

class LicenseStatus:
    """许可证状态常量"""
    INACTIVE = 0
    ACTIVE = 1
    EXPIRED = 2

class LicenseService:
    def __init__(self, db: Session):
        self.db = db
        
    def generate_license(self, license_user_id: int, machine_key: str) -> LicenseInfo:
        """
        生成新许可证
        
        Args:
            license_user_id (int): 许可证用户ID
            machine_key (str): 机器唯一标识码
            
        Returns:
            LicenseInfo: 新创建的许可证对象
            
        Raises:
            LicenseUserNotFoundError: 当用户不存在时
            CryptoOperationError: 当加密操作失败时
        """
        # 验证许可证用户存在
        license_user = self.db.query(UserInfo).filter(
            UserInfo.id == license_user_id
        ).first()
        
        if not license_user:
            raise LicenseUserNotFoundError(f"ID {license_user_id}")
        
        # 计算过期时间
        expires_at = datetime.utcnow() + timedelta(days=settings.LICENSE_DEFAULT_DURATION)
        
        # 创建许可证信息
        license_info = {
            "license_user_id": license_user_id,
            "email": license_user.email,
            "machine_key": machine_key,
            "expires_at": expires_at.isoformat()
        }
        
        try:
            # 序列化并加密许可证信息
            license_json = json.dumps(license_info)
            encrypted_license = rsa_encrypt(license_json, license_user.public_key)
            license_base64 = encode_base64(encrypted_license)
            
            # 创建许可证记录
            license = LicenseInfo(
                license_user_id=license_user_id,
                machine_key=machine_key,
                license_key=license_base64,
                status=LicenseStatus.INACTIVE,
                expires_at=expires_at
            )
            
            self.db.add(license)
            self.db.commit()
            self.db.refresh(license)
            return license
        except Exception as e:
            raise CryptoOperationError(f"License encryption: {str(e)}")
        
    def activate_license(self, license_id: int, machine_key: str) -> LicenseInfo:
        """
        激活许可证
        
        Args:
            license_id (int): 许可证ID
            machine_key (str): 机器唯一标识码
            
        Returns:
            LicenseInfo: 更新后的许可证对象
            
        Raises:
            LicenseActivationError: 当激活失败时
        """
        license = self.db.query(LicenseInfo).filter(
            LicenseInfo.id == license_id
        ).first()
        
        if not license:
            raise LicenseActivationError("License not found")
            
        # 验证机器码
        if license.machine_key != machine_key:
            raise LicenseActivationError("Machine key mismatch")
            
        # 验证状态
        if license.status != LicenseStatus.INACTIVE:
            raise LicenseActivationError("License is not in inactive state")
            
        # 验证是否过期
        if datetime.utcnow() > license.expires_at:
            license.status = LicenseStatus.EXPIRED
            self.db.commit()
            raise LicenseActivationError("License has expired")
            
        # 更新状态和激活时间
        license.status = LicenseStatus.ACTIVE
        license.activated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(license)
        return license
        
    def validate_license(self, license_key: str, machine_key: str) -> bool:
        """
        验证许可证有效性
        
        Args:
            license_key (str): Base64编码的许可证密钥
            machine_key (str): 机器唯一标识码
            
        Returns:
            bool: 许可证是否有效
            
        Raises:
            LicenseActivationError: 当验证失败时
        """
        license = self.db.query(LicenseInfo).filter(
            LicenseInfo.license_key == license_key
        ).first()
        
        if not license:
            raise LicenseActivationError("License not found")
            
        # 验证机器码
        if license.machine_key != machine_key:
            raise LicenseActivationError("Machine key mismatch")
            
        # 检查激活状态
        if license.status != LicenseStatus.ACTIVE:
            if license.status == LicenseStatus.INACTIVE:
                raise LicenseActivationError("License is not activated")
            else:
                raise LicenseActivationError("License is expired or revoked")
                
        # 检查过期时间
        current_time = datetime.utcnow()
        if license.expires_at < current_time:
            license.status = LicenseStatus.EXPIRED
            self.db.commit()
            raise LicenseActivationError("License has expired")
        
        return True
    
    def check_and_expire_licenses(self) -> int:
        """
        检查并标记过期的许可证
        
        Returns:
            int: 被标记为过期的许可证数量
        """
        now = datetime.utcnow()
        expired_licenses = self.db.query(LicenseInfo).filter(
            LicenseInfo.status == LicenseStatus.ACTIVE,
            LicenseInfo.expires_at <= now
        ).all()
        
        for license in expired_licenses:
            license.status = LicenseStatus.EXPIRED
            
        self.db.commit()
        return len(expired_licenses)