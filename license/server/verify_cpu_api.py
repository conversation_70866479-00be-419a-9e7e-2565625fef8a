#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证CPU专用API端点是否正确添加
"""

import re

def verify_cpu_api_implementation():
    """验证CPU API实现"""
    print("=" * 60)
    print("验证CPU专用API实现")
    print("=" * 60)
    
    # 1. 检查schemas.py中的CPU模型
    print("1. 检查schemas.py中的CPU数据模型...")
    try:
        with open('schemas.py', 'r', encoding='utf-8') as f:
            schemas_content = f.read()
        
        cpu_models = [
            'CpuInfoRequest',
            'CpuInfoResponse', 
            'CpuLicenseCreateRequest',
            'CpuLicenseCreateResponse',
            'CpuLicenseActivateRequest',
            'CpuLicenseActivateResponse'
        ]
        
        for model in cpu_models:
            if f'class {model}' in schemas_content:
                print(f"   ✅ {model} - 已定义")
            else:
                print(f"   ❌ {model} - 未找到")
                
    except Exception as e:
        print(f"   ❌ 检查schemas.py失败: {e}")
    
    print()
    
    # 2. 检查utils/crypto.py中的CPU函数
    print("2. 检查utils/crypto.py中的CPU专用函数...")
    try:
        with open('utils/crypto.py', 'r', encoding='utf-8') as f:
            crypto_content = f.read()
        
        cpu_functions = [
            'rsa_encrypt_cpu_only',
            'rsa_decrypt_cpu_only',
            'generate_cpu_machine_key',
            'create_cpu_license',
            'sign_cpu_license_with_private_key'
        ]
        
        for func in cpu_functions:
            if f'def {func}' in crypto_content:
                print(f"   ✅ {func} - 已定义")
            else:
                print(f"   ❌ {func} - 未找到")
                
    except Exception as e:
        print(f"   ❌ 检查utils/crypto.py失败: {e}")
    
    print()
    
    # 3. 检查api/license.py中的CPU API端点
    print("3. 检查api/license.py中的CPU API端点...")
    try:
        with open('api/license.py', 'r', encoding='utf-8') as f:
            api_content = f.read()
        
        cpu_endpoints = [
            ('/cpu_info', 'get_cpu_info'),
            ('/cpu_license_create', 'create_cpu_license_api'),
            ('/cpu_license_activate', 'activate_cpu_license_api')
        ]
        
        for endpoint, func_name in cpu_endpoints:
            route_pattern = f'@router.post("{endpoint}")'
            func_pattern = f'def {func_name}'

            route_found = route_pattern in api_content
            func_found = func_pattern in api_content

            if route_found and func_found:
                print(f"   ✅ {endpoint} -> {func_name} - 已定义")
            else:
                print(f"   ❌ {endpoint} -> {func_name} - 路由:{route_found}, 函数:{func_found}")
        
        # 检查导入
        cpu_imports = [
            'CpuInfoRequest, CpuInfoResponse',
            'CpuLicenseCreateRequest, CpuLicenseCreateResponse',
            'CpuLicenseActivateRequest, CpuLicenseActivateResponse',
            'rsa_decrypt_cpu_only',
            'generate_cpu_machine_key',
            'create_cpu_license',
            'sign_cpu_license_with_private_key'
        ]
        
        print("\n   导入检查:")
        for imp in cpu_imports:
            if imp in api_content:
                print(f"   ✅ {imp} - 已导入")
            else:
                print(f"   ❌ {imp} - 未导入")
                
    except Exception as e:
        print(f"   ❌ 检查api/license.py失败: {e}")
    
    print()
    
    # 4. 检查API端点的完整性
    print("4. 检查API端点实现完整性...")
    try:
        with open('api/license.py', 'r', encoding='utf-8') as f:
            api_content = f.read()
        
        # 检查每个API端点的关键组件
        endpoints_check = {
            'get_cpu_info': [
                'user_service.authenticate_user',
                'user.public_key',
                'CpuInfoResponse'
            ],
            'create_cpu_license_api': [
                'rsa_decrypt_cpu_only',
                'generate_cpu_machine_key',
                'create_cpu_license',
                'LicenseInfo',
                'CpuLicenseCreateResponse'
            ],
            'activate_cpu_license_api': [
                'rsa_decrypt_cpu_only',
                'generate_cpu_machine_key',
                'sign_cpu_license_with_private_key',
                'CpuLicenseActivateResponse'
            ]
        }
        
        for endpoint, components in endpoints_check.items():
            print(f"\n   {endpoint}:")
            for component in components:
                if component in api_content:
                    print(f"     ✅ {component}")
                else:
                    print(f"     ❌ {component}")
    
    except Exception as e:
        print(f"   ❌ 检查API完整性失败: {e}")
    
    print()
    print("=" * 60)
    print("CPU API验证完成")
    print("=" * 60)

if __name__ == "__main__":
    verify_cpu_api_implementation()
