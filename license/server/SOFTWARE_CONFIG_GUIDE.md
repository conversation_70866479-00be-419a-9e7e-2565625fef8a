# 软件包配置化实现指南

## 🎯 实现目标

将硬编码的 `software_v1.zip` 修改为从配置文件动态获取，支持通过环境变量自定义软件包名称和位置。

## ✅ 已完成的修改

### 1. 配置文件修改 (`license/server/config.py`)

**新增配置项**：
```python
# 软件包配置
SOFTWARE_FILENAME: str = os.getenv("SOFTWARE_FILENAME", "software_v1.zip")  # 软件包文件名
SOFTWARE_DIRECTORY: str = os.getenv("SOFTWARE_DIRECTORY", "api")  # 软件包所在目录

@property
def software_path(self) -> str:
    """获取软件包的完整路径"""
    import os
    server_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(server_dir, self.SOFTWARE_DIRECTORY, self.SOFTWARE_FILENAME)
```

### 2. API文件修改 (`license/server/api/license.py`)

**修改位置**：
- 第172行：软件包存在性检查
- 第300行：下载时的软件包路径
- 第306行：ZIP文件中的软件包名称
- 第350行：SHA256计算时的软件包路径

**修改内容**：
```python
# 原来的硬编码方式
software_path = os.path.join(os.path.dirname(__file__), "software_v1.zip")

# 修改后的配置方式
software_path = settings.software_path
```

## 🔧 配置使用方法

### 1. 默认配置（无需修改）
```python
from config import settings

# 使用默认配置
software_path = settings.software_path  # 指向 license/server/api/software_v1.zip
filename = settings.SOFTWARE_FILENAME   # "software_v1.zip"
directory = settings.SOFTWARE_DIRECTORY # "api"
```

### 2. 环境变量配置

**Linux/Mac**：
```bash
export SOFTWARE_FILENAME=my_custom_software.zip
export SOFTWARE_DIRECTORY=packages
python main.py
```

**Windows**：
```cmd
set SOFTWARE_FILENAME=my_custom_software.zip
set SOFTWARE_DIRECTORY=packages
python main.py
```

**Docker**：
```dockerfile
ENV SOFTWARE_FILENAME=production_software.zip
ENV SOFTWARE_DIRECTORY=releases
```

### 3. 代码中动态配置
```python
import os
from config import Settings

# 临时修改配置
os.environ['SOFTWARE_FILENAME'] = 'test_software.zip'
os.environ['SOFTWARE_DIRECTORY'] = 'test_packages'

# 创建新的配置实例
custom_settings = Settings()
test_path = custom_settings.software_path
```

## 📁 目录结构

```
license/server/
├── config.py              # 配置文件（已修改）
├── api/
│   ├── license.py         # API文件（已修改）
│   └── software_v1.zip    # 默认软件包
└── packages/              # 可选的自定义目录
    └── custom_software.zip
```

## 🔍 验证结果

### ✅ 配置验证通过
- 软件文件名配置: 已添加
- 软件目录配置: 已添加  
- 软件路径属性: 已添加
- 软件路径方法: 已添加

### ✅ 硬编码移除完成
- 硬编码 'software_v1.zip' 出现次数: 0
- 所有硬编码已移除
- 使用软件路径配置: 3次
- 使用软件文件名配置: 1次

### ✅ 环境变量支持
- 软件文件名环境变量: 支持
- 软件目录环境变量: 支持

### ✅ 文件访问正常
- 软件包文件存在: api\software_v1.zip
- 文件大小: 10,915,223 字节
- 路径生成逻辑正确

## 🚀 使用场景

### 1. 开发环境
```bash
export SOFTWARE_FILENAME=dev_software.zip
export SOFTWARE_DIRECTORY=dev_packages
```

### 2. 测试环境
```bash
export SOFTWARE_FILENAME=test_software.zip
export SOFTWARE_DIRECTORY=test_packages
```

### 3. 生产环境
```bash
export SOFTWARE_FILENAME=production_software_v2.1.zip
export SOFTWARE_DIRECTORY=releases
```

### 4. 多版本支持
```bash
# 版本1
export SOFTWARE_FILENAME=software_v1.0.zip

# 版本2
export SOFTWARE_FILENAME=software_v2.0.zip

# 测试版本
export SOFTWARE_FILENAME=software_beta.zip
```

## 🔒 安全考虑

1. **路径验证**：配置的路径会自动基于服务器目录，防止路径遍历攻击
2. **文件存在性检查**：API会验证文件是否存在
3. **环境变量隔离**：不同环境可以使用不同的配置

## 📝 注意事项

1. **文件位置**：软件包文件必须放在配置的目录中
2. **权限**：确保应用有读取软件包文件的权限
3. **备份**：更换软件包时建议备份原文件
4. **重启**：修改环境变量后需要重启应用

## 🎉 总结

✅ **完全移除硬编码**：所有 `software_v1.zip` 硬编码已替换为配置
✅ **灵活配置**：支持环境变量和代码配置
✅ **向后兼容**：默认配置保持原有行为
✅ **路径安全**：自动生成安全的文件路径
✅ **验证通过**：所有功能测试正常

现在您可以通过环境变量轻松配置不同的软件包名称和位置，无需修改代码！
