from datetime import datetime
from pydantic import BaseModel, EmailStr
from typing import Optional, List
from .config import settings

class LicenseUserCreate(BaseModel):
    """创建许可证用户请求模型"""
    user_id: int
    email: EmailStr
    password: Optional[str] = None
    user_name: Optional[str] = None
    real_name: Optional[str] = None
    company: Optional[str] = None
    role_id: Optional[int] = None

class LicenseUserResponse(BaseModel):
    """许可证用户响应模型"""
    id: int
    user_id: int
    user_name: Optional[str]
    real_name: Optional[str]
    email: str
    company: Optional[str]
    role_id: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class LicenseCreate(BaseModel):
    """创建许可证请求模型"""
    license_user_id: int
    machine_key: str

class LicenseUpdate(BaseModel):
    """更新许可证请求模型"""
    machine_key: Optional[str] = None
    status: Optional[int] = None
    expires_at: Optional[datetime] = None

class LicenseStatusUpdate(BaseModel):
    """更新许可证状态请求模型"""
    status: int

class LicenseResponse(BaseModel):
    """许可证响应模型"""
    id: int
    license_user_id: int
    machine_key: str
    license_key: str
    status: int
    status_name: str
    expires_at: datetime
    created_at: datetime
    activated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class LicenseListResponse(BaseModel):
    """许可证列表响应模型"""
    total: int
    items: List[LicenseResponse]

class LicenseActivate(BaseModel):
    """激活许可证请求模型"""
    license_id: int
    machine_key: str

class Token(BaseModel):
    """认证令牌模型"""
    access_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    """令牌数据模型"""
    user_id: int
    email: str

class LicenseValidation(BaseModel):
    """许可证验证请求模型"""
    license_key: str
    machine_key: str

class DownloadRequest(BaseModel):
    email: str
    password: str

class LicenseActivateRequest(BaseModel):
    email: str
    key: str

class LicenseStringResponse(BaseModel):
    license: str

class UserListResponse(BaseModel):
    total: int
    items: List[LicenseUserResponse]

# ==================== CPU专用数据模型 ====================

class CpuInfoRequest(BaseModel):
    """获取CPU信息处理所需公钥的请求模型"""
    email: EmailStr
    password: str

class CpuInfoResponse(BaseModel):
    """CPU信息处理响应模型"""
    rsaPubKey: str

class CpuLicenseCreateRequest(BaseModel):
    """基于CPU信息创建license的请求模型"""
    email: EmailStr
    encrypted_cpu: str  # RSA公钥加密后的CPU信息

class CpuLicenseCreateResponse(BaseModel):
    """CPU license创建响应模型"""
    license: str
    cpu_machine_key: str
    expires_at: str

class CpuLicenseActivateRequest(BaseModel):
    """基于CPU信息激活license的请求模型"""
    email: EmailStr
    encrypted_cpu: str  # RSA公钥加密后的CPU信息

class CpuLicenseActivateResponse(BaseModel):
    """CPU license激活响应模型"""
    license: str
    signed_license: str  # 用私钥签名的license
    expires_at: str