"""
定时任务
"""
from license.services.license_service import LicenseService
from license.database import get_db_session
from license.utils.logger import logger

async def expire_licenses_task():
    logger.info("Starting license expiration check")
    async for session in get_db_session():
        try:
            service = LicenseService(session)
            expired_count = await service.expire_licenses()
            logger.info(f"Expired {expired_count} licenses")
        except Exception as e:
            logger.error(f"Error during expiration task: {e}")