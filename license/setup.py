from setuptools import setup, find_packages

setup(
    name="license",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "fastapi",
        "uvicorn",
        "sqlalchemy",
        "apscheduler",
        "python-jose[cryptography]",
        "passlib[bcrypt]",
        "python-multipart",
        "PyJWT",
        "pymysql",
        "pydantic",
        "pydantic-settings",
        "aiosqlite",
        "asyncmy",
        "pydantic_ai",
        "pydantic[email]",
    ],
) 