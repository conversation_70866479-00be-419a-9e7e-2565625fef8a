# License Server 快速部署

## 📦 部署包

**推荐使用**: `license-server-final.zip` (10.46 MB)

包含内容：
- ✅ 完整的Python后端代码
- ✅ requirements.txt 依赖列表
- ✅ 详细的部署指南
- ✅ 自动化部署脚本

## 🚀 快速部署（推荐）

### 方法1：一键自动部署
```bash
# 1. 上传 license-server-final.zip 到服务器
scp license-server-final.zip user@server:/home/<USER>/license/

# 2. 在服务器上执行
cd /home/<USER>/license
unzip license-server-final.zip
chmod +x deploy-server.sh
sudo ./deploy-server.sh
```

### 方法2：手动部署
```bash
# 1. 创建目录并解压
sudo mkdir -p /home/<USER>/license/server
cd /home/<USER>/license/server
sudo unzip license-server-final.zip

# 2. 设置权限
sudo chown -R sy_cmsr:sy_cmsr /home/<USER>/license/server

# 3. 安装Python环境
sudo apt update
sudo apt install python3 python3-pip python3-venv -y

# 4. 创建虚拟环境并安装依赖
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 5. 创建环境配置
nano .env
# 复制以下内容到 .env 文件：
```

### .env 配置内容：
```env
DB_HOST=*********
DB_PORT=23306
DB_USER=indusaio_user
DB_PASSWORD=Sygy@2025
DB_NAME=indusaio_agent

JWT_SECRET=your-secret-key-change-this
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

SERVER_HOST=0.0.0.0
SERVER_PORT=8000

LICENSE_DEFAULT_DURATION=365
DEFAULT_USER_PASSWORD=Cmsr@2025!
```

### 启动服务：
```bash
# 测试运行
source venv/bin/activate
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 或创建系统服务（参考 SERVER_DEPLOYMENT_GUIDE.md）
```

## 🔍 验证部署

```bash
# 检查服务
curl http://localhost:8000/docs

# 测试API
curl -X POST "http://localhost:8000/api/v1/user/add" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "user_name": "测试用户"}'
```

## 📋 文件说明

- `license-server-final.zip` - **推荐使用**，包含完整部署包
- `license-server-complete.zip` - 完整代码包
- `license-server.zip` - 基础代码包

## 🆘 需要帮助？

查看详细文档：
- `SERVER_DEPLOYMENT_GUIDE.md` - 完整部署指南
- `deploy-server.sh` - 自动化部署脚本

## 🎯 目标路径

部署到：`/home/<USER>/license/server`
