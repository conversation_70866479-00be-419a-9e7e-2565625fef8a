#!/bin/bash

# License Server 快速部署脚本
# 使用方法: ./deploy-server.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署 License Server..."

# 配置变量
TARGET_DIR="/home/<USER>/license/server"
SERVICE_NAME="license-server"
PYTHON_VERSION="python3"

# 检查是否为root用户或有sudo权限
if [[ $EUID -eq 0 ]]; then
    SUDO=""
else
    SUDO="sudo"
fi

echo "📁 创建目标目录..."
$SUDO mkdir -p $TARGET_DIR
cd $TARGET_DIR

echo "📦 解压服务器代码..."
if [ -f "license-server-complete.zip" ]; then
    $SUDO unzip -o license-server-complete.zip
    echo "✅ 解压完成"
else
    echo "❌ 错误: 找不到 license-server-complete.zip 文件"
    echo "请确保将 license-server-complete.zip 上传到 $TARGET_DIR 目录"
    exit 1
fi

echo "🔧 设置文件权限..."
$SUDO chown -R sy_cmsr:sy_cmsr $TARGET_DIR

echo "🐍 检查Python环境..."
if ! command -v $PYTHON_VERSION &> /dev/null; then
    echo "❌ Python3 未安装，正在安装..."
    $SUDO apt update
    $SUDO apt install python3 python3-pip python3-venv -y
fi

echo "📦 创建虚拟环境..."
if [ ! -d "venv" ]; then
    $PYTHON_VERSION -m venv venv
    echo "✅ 虚拟环境创建完成"
fi

echo "📚 安装Python依赖..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
echo "✅ 依赖安装完成"

echo "⚙️  创建环境配置文件..."
if [ ! -f ".env" ]; then
    cat > .env << EOF
# 数据库配置
DB_HOST=*********
DB_PORT=23306
DB_USER=indusaio_user
DB_PASSWORD=Sygy@2025
DB_NAME=indusaio_agent

# JWT配置
JWT_SECRET=your-secret-key-change-this-$(date +%s)
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 许可证配置
LICENSE_DEFAULT_DURATION=365

# 用户默认密码
DEFAULT_USER_PASSWORD=Cmsr@2025!
EOF
    echo "✅ 环境配置文件创建完成"
else
    echo "⚠️  环境配置文件已存在，跳过创建"
fi

echo "🔧 创建系统服务..."
$SUDO tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null << EOF
[Unit]
Description=License Server
After=network.target

[Service]
Type=simple
User=sy_cmsr
WorkingDirectory=$TARGET_DIR
Environment=PATH=$TARGET_DIR/venv/bin
ExecStart=$TARGET_DIR/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

echo "🔄 重新加载系统服务..."
$SUDO systemctl daemon-reload
$SUDO systemctl enable $SERVICE_NAME

echo "🚀 启动服务..."
$SUDO systemctl start $SERVICE_NAME

echo "⏳ 等待服务启动..."
sleep 3

echo "📊 检查服务状态..."
if $SUDO systemctl is-active --quiet $SERVICE_NAME; then
    echo "✅ 服务启动成功!"
    echo "📋 服务状态:"
    $SUDO systemctl status $SERVICE_NAME --no-pager -l
else
    echo "❌ 服务启动失败!"
    echo "📋 错误日志:"
    $SUDO journalctl -u $SERVICE_NAME --no-pager -l
    exit 1
fi

echo ""
echo "🎉 部署完成!"
echo ""
echo "📋 服务信息:"
echo "   - 服务名称: $SERVICE_NAME"
echo "   - 服务地址: http://localhost:8000"
echo "   - API文档: http://localhost:8000/docs"
echo "   - 配置文件: $TARGET_DIR/.env"
echo ""
echo "🔧 常用命令:"
echo "   - 查看状态: sudo systemctl status $SERVICE_NAME"
echo "   - 重启服务: sudo systemctl restart $SERVICE_NAME"
echo "   - 查看日志: sudo journalctl -u $SERVICE_NAME -f"
echo "   - 停止服务: sudo systemctl stop $SERVICE_NAME"
echo ""
echo "🧪 测试API:"
echo "   curl http://localhost:8000/docs"
echo ""
