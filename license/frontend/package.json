{"name": "license-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "pack": "npm run build && powershell -Command \"Compress-Archive -Path 'dist\\*' -DestinationPath 'dist.zip' -Force\"", "deploy": "npm run pack && echo 部署包已生成: dist.zip"}, "dependencies": {"axios": "^1.3.4", "element-plus": "^2.3.1", "jsencrypt": "^3.3.2", "vue": "^3.2.47", "vue-router": "^4.1.6"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "terser": "^5.42.0", "vite": "^4.1.4"}}