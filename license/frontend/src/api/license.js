import axios from 'axios';
import request from '@/utils/request';

export async function downloadPackage(email, password, onProgress) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '/api/v1/license/download', true);
    xhr.responseType = 'blob';
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.setRequestHeader('accept', 'application/octet-stream');
    xhr.onprogress = (event) => {
      if (event.lengthComputable && onProgress) {
        let percent = Math.round((event.loaded / event.total) * 100);
        if (percent < 1) percent = 1;
        if (percent > 100) percent = 100;
        onProgress(percent);
      }
    };
    xhr.onload = function () {
      const contentType = xhr.getResponseHeader('content-type') || '';
      const isZip = contentType.includes('application/zip') ||
        contentType.includes('application/octet-stream') ||
        contentType.includes('application/x-zip-compressed');
      if (xhr.status === 200 && isZip) {
        // 下载并保存
        const url = window.URL.createObjectURL(xhr.response);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'package.zip';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        resolve();
      } else if (xhr.status === 401) {
        alert('邮箱或密码错误，下载失败');
        reject(new Error('邮箱或密码错误，下载失败'));
      } else {
        // 不是zip包，说明后端返回了错误信息
        const reader = new FileReader();
        reader.onload = function() {
          alert(reader.result);
          reject(new Error(reader.result));
        };
        reader.readAsText(xhr.response);
      }
    };
    xhr.onerror = function () {
      alert('下载失败，请重试');
      reject(new Error('下载失败，请重试'));
    };
    xhr.send(JSON.stringify({ email, password }));
  });
}

export async function getDownloadInfo(email, password) {
  const res = await fetch('/api/v1/license/download_info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, password })
  });
  if (!res.ok) {
    throw new Error('获取签名信息失败');
  }
  return await res.json();
}

export function createLicense(data) {
  return request({
    url: '/v1/license/create_license',
    method: 'post',
    data
  })
}

// ==================== CPU专用API调用函数 ====================

export async function getCpuInfo(email, password) {
  /**
   * 获取CPU信息处理所需的RSA公钥
   * @param {string} email - 用户邮箱
   * @param {string} password - 用户密码
   * @returns {Promise} 包含RSA公钥的响应
   */
  const res = await fetch('/api/v1/license/cpu_info', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, password })
  });

  if (!res.ok) {
    const errorData = await res.json().catch(() => ({ detail: '获取CPU信息失败' }));
    throw new Error(errorData.detail || '获取CPU信息失败');
  }

  return await res.json();
}

export function createCpuLicense(data) {
  /**
   * 基于CPU信息创建license
   * @param {Object} data - 包含email和encrypted_cpu的数据
   * @returns {Promise} license创建响应
   */
  return request({
    url: '/v1/license/cpu_license_create',
    method: 'post',
    data
  });
}

export function activateCpuLicense(data) {
  /**
   * 基于CPU信息激活license
   * @param {Object} data - 包含email和encrypted_cpu的数据
   * @returns {Promise} license激活响应
   */
  return request({
    url: '/v1/license/cpu_license_activate',
    method: 'post',
    data
  });
}