import request from '@/utils/request';

export function getUserList(params) {
  return request({
    url: '/v1/user/list',
    method: 'get',
    params
  });
}

export function addUser(data) {
  return request({
    url: '/v1/user/add',
    method: 'post',
    data
  });
}

export function updateUser(user_id, data) {
  return request({
    url: `/v1/user/${user_id}`,
    method: 'put',
    data
  });
}

export function delUser(user_id) {
  return request({
    url: `/v1/user/${user_id}`,
    method: 'delete'
  });
}

export function getUserDetail(id) {
  return request({
    url: `/v1/user/${id}`,
    method: 'get'
  });
} 