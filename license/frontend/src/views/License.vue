<template>
  <div style="max-width: 500px; margin: 0 auto;">
    <h2>凭证获取</h2>
    <div style="margin-bottom: 32px;">
      <h3>获取License</h3>
      <form @submit.prevent="handleGetLicense">
        <div style="margin-bottom: 12px;">
          <label>邮箱：</label>
          <input v-model="email" type="email" required style="width: 100%;" />
        </div>
        <div style="margin-bottom: 12px;">
          <label>客户端Key：</label>
          <input v-model="machineKey" type="text" required style="width: 100%;" />
        </div>
        <button type="submit" style="margin-right: 12px;">获取License</button>
        <span v-if="licenseStr" style="color: #409eff; margin-left: 8px;">{{ licenseStr }}</span>
      </form>
    </div>
    <div>
      <h3>激活License</h3>
      <form @submit.prevent="handleActivate">
        <div style="margin-bottom: 12px;">
          <label>邮箱：</label>
          <input v-model="email2" type="email" required style="width: 100%;" />
        </div>
        <div style="margin-bottom: 12px;">
          <label>待激活License：</label>
          <input v-model="activateKey" type="text" required style="width: 100%;" />
        </div>
        <button type="submit">激活</button>
        <span v-if="activateResult" style="color: #67c23a; margin-left: 8px;">{{ activateResult }}</span>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const email = ref('');
const machineKey = ref('');
const licenseStr = ref('');
const email2 = ref('');
const activateKey = ref('');
const activateResult = ref('');

function handleGetLicense() {
  // TODO: 调用后端接口获取License
  licenseStr.value = '示例License字符串';
}

function handleActivate() {
  // TODO: 调用后端接口激活License
  activateResult.value = '激活成功';
}
</script> 