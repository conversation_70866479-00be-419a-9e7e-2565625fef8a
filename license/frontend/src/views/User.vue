<template>
  <div class="user-page">
    <div class="user-toolbar">
      <div class="user-search">
        <el-input v-model="search.user_name" placeholder="用户名" class="user-input" clearable />
        <el-input v-model="search.real_name" placeholder="真实姓名" class="user-input" clearable />
        <el-input v-model="search.email" placeholder="邮箱" class="user-input" clearable />
        <el-input v-model="search.company" placeholder="公司" class="user-input" clearable />
        <el-select v-model="search.role_id" placeholder="角色" class="user-input" clearable style="width: 120px">
          <el-option :value="100001" label="普通用户" />
          <el-option :value="999999" label="管理员" />
        </el-select>
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
    <div class="add-btn-bar">
      <el-button type="success" class="add-btn" @click="openAddDialog">新建用户</el-button>
    </div>
    <el-table :data="userList" class="user-table" border stripe style="width:auto;min-width:1000px;">
      <el-table-column prop="user_id" label="用户ID" align="center" />
      <el-table-column prop="user_name" label="用户名" align="center" />
      <el-table-column prop="real_name" label="真实姓名" align="center" />
      <el-table-column prop="email" label="邮箱" align="center" />
      <el-table-column prop="company" label="公司" align="center" />
      <el-table-column prop="role_id" label="角色" align="center">
        <template #default="scope">
          <span v-if="scope.row.role_id === 999999">管理员</span>
          <span v-else>普通用户</span>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" align="center" />
      <el-table-column prop="updated_at" label="更新时间" align="center" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button size="small" class="edit-btn" @click="openEditDialog(scope.row)">编辑</el-button>
          <el-button size="small" class="del-btn" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-wrapper">
      <el-pagination
        background
        layout="prev, pager, next"
        :page-size="pageSize"
        :current-page="currentPage"
        :total="total"
        @current-change="handlePageChange"
      />
    </div>
    <el-dialog v-model="dialogVisible" :title="dialogType==='add' ? '新增用户' : '编辑用户'" class="user-dialog">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="90px">
        <el-form-item label="用户名" prop="user_name">
          <el-input v-model="form.user_name" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="form.real_name" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
        <el-form-item label="公司" prop="company">
          <el-input v-model="form.company" />
        </el-form-item>
        <el-form-item label="角色" prop="role_id">
          <el-select v-model="form.role_id" placeholder="请选择角色" style="width: 100%">
            <el-option :value="100001" label="普通用户" />
            <el-option :value="999999" label="管理员" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible=false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getUserList, addUser, updateUser, delUser, getUserDetail } from '@/api/user';

const userList = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = 10;
const search = reactive({ user_name: '', real_name: '', email: '', company: '', role_id: '' });
const dialogVisible = ref(false);
const dialogType = ref('add');
const form = reactive({ user_name: '', real_name: '', email: '', company: '', role_id: '' });
const formRef = ref();
const rules = {
  user_name: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }]
};
let editId = null;

function fetchList() {
  getUserList({ ...search, skip: (currentPage.value - 1) * pageSize, limit: pageSize }).then(res => {
    userList.value = res.items || [];
    total.value = res.total || 0;
  });
}

function handlePageChange(page) {
  currentPage.value = page;
  fetchList();
}

function handleSearch() {
  currentPage.value = 1;
  fetchList();
}

function handleReset() {
  search.user_name = '';
  search.real_name = '';
  search.email = '';
  search.company = '';
  search.role_id = '';
  currentPage.value = 1;
  fetchList();
}

function openAddDialog() {
  dialogType.value = 'add';
  Object.assign(form, { user_name: '', real_name: '', email: '', company: '', role_id: '' });
  dialogVisible.value = true;
  editId = null;
}

async function openEditDialog(row) {
  dialogType.value = 'edit';
  const detail = await getUserDetail(row.id);
  Object.assign(form, detail);
  dialogVisible.value = true;
  editId = row.id;
}

function submitForm() {
  formRef.value.validate(async valid => {
    if (!valid) return;
    try {
      if (dialogType.value === 'add') {
        await addUser({ ...form });
        ElMessage.success('新增成功');
      } else {
        await updateUser(editId, { ...form });
        ElMessage.success('更新成功');
      }
      dialogVisible.value = false;
      fetchList();
    } catch (e) {
      ElMessage.error(e?.response?.data?.detail || '操作失败');
    }
  });
}

function handleDelete(row) {
  if (!confirm('确定要删除该用户吗？')) return;
  delUser(row.id).then(() => {
    ElMessage.success('删除成功');
    fetchList();
  }).catch(e => {
    ElMessage.error(e?.response?.data?.detail || '删除失败');
  });
}

onMounted(fetchList);
</script>

<style scoped>
.user-page {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 32px 20px 0 20px;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}
.user-toolbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 18px;
  gap: 18px;
}
.user-search {
  display: flex;
  align-items: center;
  gap: 10px;
}
.user-input {
  width: 140px;
  font-size: 15px;
}
.search-btn {
  margin-left: 8px;
  border-radius: 20px;
  background: linear-gradient(90deg, #409eff 60%, #66b1ff 100%);
  color: #fff;
  font-weight: 500;
  font-size: 15px;
  border: none;
  box-shadow: 0 2px 8px #e6f0fa;
  transition: background 0.2s, box-shadow 0.2s;
}
.search-btn:hover {
  background: linear-gradient(90deg, #337ecc 60%, #409eff 100%);
}
.reset-btn {
  margin-left: 8px;
  border-radius: 20px;
  background: #f4f4f4;
  color: #409eff;
  font-weight: 500;
  font-size: 15px;
  border: 1px solid #d9ecff;
  transition: background 0.2s, color 0.2s;
}
.reset-btn:hover {
  background: #e6f7ff;
  color: #337ecc;
}
.add-btn {
  border-radius: 20px;
  background: linear-gradient(90deg, #67c23a 60%, #95d475 100%);
  color: #fff;
  font-weight: 500;
  font-size: 15px;
  border: none;
  box-shadow: 0 2px 8px #e6f0fa;
  transition: background 0.2s, box-shadow 0.2s;
}
.add-btn:hover {
  background: linear-gradient(90deg, #529b2e 60%, #67c23a 100%);
}
.user-table {
  background: #fff;
  border-radius: 10px;
  font-size: 15px;
  box-shadow: 0 2px 8px #f0f1f2;
  margin-bottom: 18px;
}
.user-table th, .user-table td {
  text-align: center;
  padding: 10px 0;
}
.edit-btn {
  border-radius: 16px;
  background: linear-gradient(90deg, #409eff 60%, #66b1ff 100%);
  color: #fff;
  font-weight: 500;
  border: none;
  margin-right: 8px;
  transition: background 0.2s;
}
.edit-btn:hover {
  background: linear-gradient(90deg, #337ecc 60%, #409eff 100%);
}
.del-btn {
  border-radius: 16px;
  background: linear-gradient(90deg, #f56c6c 60%, #f89898 100%);
  color: #fff;
  font-weight: 500;
  border: none;
  transition: background 0.2s;
}
.del-btn:hover {
  background: linear-gradient(90deg, #c45656 60%, #f56c6c 100%);
}
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 18px;
}
.user-dialog >>> .el-dialog__header {
  font-size: 18px;
  font-weight: bold;
}
.add-btn-bar {
  margin-bottom: 12px;
  text-align: left;
}
</style> 