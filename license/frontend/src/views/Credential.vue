<template>
  <div style="max-width: 600px; margin: 40px auto; padding: 32px 24px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #f0f1f2;">

    <!-- CPU专用License生成区 -->
    <div style="margin-bottom: 40px; border-bottom: 1px solid #eee; padding-bottom: 32px;">
      <div style="font-weight: bold; font-size: 16px; margin-bottom: 16px; color: #409eff;">🔥 CPU专用License生成（推荐）</div>
      <div style="margin-bottom: 16px; padding: 12px; background: #f0f9ff; border-radius: 4px; color: #1f2937; font-size: 14px;">
        <strong>新功能：</strong>只需要CPU信息即可生成License，更简单、更安全！
      </div>

      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span class="input-label">用户邮箱：</span>
        <el-input v-model="cpuEmailInput" placeholder="请输入邮箱" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span class="input-label">用户密码：</span>
        <el-input v-model="cpuPasswordInput" type="password" placeholder="请输入密码" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 18px;">
        <span class="input-label">CPU信息：</span>
        <el-input v-model="cpuOnlyInput" placeholder="请输入CPU信息，如：Intel-Core-i7-12700K-12345678" style="flex:1; min-width:0;" />
      </div>

      <div style="text-align: center; margin-bottom: 16px;">
        <el-button type="primary" @click="generateCpuLicense" :loading="cpuLoading">
          {{ cpuLoading ? '生成中...' : '生成CPU License' }}
        </el-button>
        <el-button @click="activateCpuLicenseHandler" :disabled="!cpuLicenseResult" :loading="cpuActivateLoading">
          {{ cpuActivateLoading ? '激活中...' : '激活License' }}
        </el-button>
      </div>

      <div v-if="cpuLicenseResult" style="margin-top: 16px;">
        <div style="font-weight: bold; margin-bottom: 8px;">CPU License生成成功：</div>
        <el-input v-model="cpuLicenseResult" type="textarea" rows="3" readonly style="margin-bottom: 8px;" />
        <div style="text-align: center;">
          <el-button size="small" @click="copyCpuLicense">复制License</el-button>
        </div>
        <div v-if="cpuMachineKey" style="margin-top: 8px; font-size: 12px; color: #666;">
          CPU机器码: {{ cpuMachineKey }}
        </div>
        <div v-if="cpuExpiresAt" style="margin-top: 4px; font-size: 12px; color: #666;">
          过期时间: {{ cpuExpiresAt }}
        </div>
      </div>

      <div v-if="cpuSignedLicense" style="margin-top: 16px;">
        <div style="font-weight: bold; margin-bottom: 8px;">签名License（用于验证）：</div>
        <el-input v-model="cpuSignedLicense" type="textarea" rows="2" readonly style="margin-bottom: 8px;" />
        <div style="text-align: center;">
          <el-button size="small" @click="copySignedLicense">复制签名License</el-button>
        </div>
      </div>
    </div>

    <!-- 本机Key生成区 -->
    <div style="margin-bottom: 40px; border-bottom: 1px solid #eee; padding-bottom: 32px;">
      <div style="font-weight: bold; font-size: 16px; margin-bottom: 16px;">传统方式：本机Key生成</div>
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span class="input-label">本机MAC地址：</span>
        <el-input v-model="macInput" placeholder="请输入MAC地址" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span class="input-label">本机CPU序列号：</span>
        <el-input v-model="cpuInput" placeholder="请输入CPU信息" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span class="input-label">本机磁盘信序列号：</span>
        <el-input v-model="diskInput" placeholder="请输入磁盘信息" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <span class="input-label">SHA256签名：</span>
        <el-input v-model="sha256Input" placeholder="请输入SHA256签名" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: flex-start; margin-bottom: 18px;">
        <span class="input-label" style="margin-top: 6px;">RSA公钥：</span>
        <el-input v-model="rsaPubInput" placeholder="请输入RSA公钥" type="textarea" rows="3" style="flex:1; min-width:0;" />
      </div>
      <div style="text-align: center;">
        <el-button type="primary" @click="generateLocalKey" style="width: 180px; font-size: 16px;">生成本地机器key</el-button>
      </div>
      <div v-if="localKey" style="margin-top: 18px;">
        <el-input v-model="localKey" type="textarea" rows="3" readonly style="width: 100%;" />
        <el-button type="primary" @click="copyLocalKey" style="margin-top: 5px;">复制本机Key</el-button>
      </div>
    </div>
    <!-- 原有内容 -->
    <div style="display: flex; align-items: center; margin-bottom: 24px;">
      <span class="input-label">购买邮箱：</span>
      <el-input v-model="email" placeholder="请输入购买邮箱" style="flex:1; min-width:0;" />
    </div>
    <div style="display: flex; align-items: flex-start; margin-bottom: 32px;">
      <span class="input-label" style="margin-top: 6px;">本机Key内容：</span>
      <el-input 
        v-model="keyContent"
        placeholder="请粘贴Key文件内容"
        type="textarea"
        rows="5"
        style="flex:1; min-width:0;"
      />
    </div>
    <div style="text-align: center;">
      <el-button type="success" @click="submitLicense" style="width: 180px; font-size: 16px;">获取并激活License</el-button>
    </div>
    <div v-if="license" style="margin-top: 32px;">
      <el-input
        v-model="license"
        type="textarea"
        rows="5"
        readonly
        style="width: 100%;"
      />
      <el-button
        type="primary"
        @click="copyLicense"
        style="margin-top: 5px;"
      >复制</el-button>
    </div>
  </div>
</template>

<style scoped>
.input-label {
  font-size: 13px;
  color: #666;
  min-width: 90px;
  margin-right: 8px;
  white-space: nowrap;
}
</style>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getDownloadInfo, createLicense, getCpuInfo, createCpuLicense, activateCpuLicense } from '@/api/license'
import JSEncrypt from 'jsencrypt'

// CPU专用License生成区相关
const cpuEmailInput = ref('')
const cpuPasswordInput = ref('')
const cpuOnlyInput = ref('')
const cpuLicenseResult = ref('')
const cpuMachineKey = ref('')
const cpuExpiresAt = ref('')
const cpuSignedLicense = ref('')
const cpuLoading = ref(false)
const cpuActivateLoading = ref(false)

// 本机Key生成区相关
const macInput = ref('')
const cpuInput = ref('')
const diskInput = ref('')
const sha256Input = ref('')
const rsaPubInput = ref('')
const localKey = ref('')

const generateLocalKey = () => {
  if (!macInput.value || !cpuInput.value || !diskInput.value || !sha256Input.value || !rsaPubInput.value) {
    ElMessage.warning('请填写所有信息')
    return
  }
  const raw = `${macInput.value}|${cpuInput.value}|${diskInput.value}|${sha256Input.value}`
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(rsaPubInput.value)
  const encrypted = encrypt.encrypt(raw)
  if (!encrypted) {
    ElMessage.error('加密失败，请检查公钥')
    return
  }
  localKey.value = encrypted
  ElMessage.success('本地Key已生成')
}

const copyLocalKey = () => {
  navigator.clipboard.writeText(localKey.value)
  ElMessage.success('已复制到剪贴板')
}

// ==================== CPU专用License函数 ====================

const generateCpuLicense = async () => {
  if (!cpuEmailInput.value || !cpuPasswordInput.value || !cpuOnlyInput.value) {
    ElMessage.warning('请填写邮箱、密码和CPU信息')
    return
  }

  cpuLoading.value = true

  try {
    // 1. 获取RSA公钥
    console.log('获取CPU信息处理公钥...')
    const cpuInfo = await getCpuInfo(cpuEmailInput.value, cpuPasswordInput.value)
    const rsaPublicKey = cpuInfo.rsaPubKey

    if (!rsaPublicKey) {
      throw new Error('获取RSA公钥失败')
    }

    console.log('RSA公钥获取成功')

    // 2. 加密CPU信息
    console.log('加密CPU信息...')
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(rsaPublicKey)
    const encryptedCpu = encrypt.encrypt(cpuOnlyInput.value)

    if (!encryptedCpu) {
      throw new Error('CPU信息加密失败，请检查CPU信息格式')
    }

    console.log('CPU信息加密成功')

    // 3. 创建CPU License
    console.log('创建CPU License...')
    const response = await createCpuLicense({
      email: cpuEmailInput.value,
      encrypted_cpu: encryptedCpu
    })

    cpuLicenseResult.value = response.license
    cpuMachineKey.value = response.cpu_machine_key
    cpuExpiresAt.value = response.expires_at

    ElMessage.success('CPU License生成成功！')
    console.log('CPU License生成成功:', response)

  } catch (error) {
    console.error('CPU License生成失败:', error)
    ElMessage.error(error.message || 'CPU License生成失败')
  } finally {
    cpuLoading.value = false
  }
}

const activateCpuLicenseHandler = async () => {
  if (!cpuEmailInput.value || !cpuPasswordInput.value || !cpuOnlyInput.value) {
    ElMessage.warning('请先生成CPU License')
    return
  }

  cpuActivateLoading.value = true

  try {
    // 1. 获取RSA公钥
    console.log('获取CPU信息处理公钥...')
    const cpuInfo = await getCpuInfo(cpuEmailInput.value, cpuPasswordInput.value)
    const rsaPublicKey = cpuInfo.rsaPubKey

    // 2. 加密CPU信息
    console.log('加密CPU信息...')
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(rsaPublicKey)
    const encryptedCpu = encrypt.encrypt(cpuOnlyInput.value)

    if (!encryptedCpu) {
      throw new Error('CPU信息加密失败')
    }

    // 3. 激活CPU License
    console.log('激活CPU License...')
    const response = await activateCpuLicense({
      email: cpuEmailInput.value,
      encrypted_cpu: encryptedCpu
    })

    cpuLicenseResult.value = response.license
    cpuSignedLicense.value = response.signed_license
    cpuExpiresAt.value = response.expires_at

    ElMessage.success('CPU License激活成功！')
    console.log('CPU License激活成功:', response)

  } catch (error) {
    console.error('CPU License激活失败:', error)
    ElMessage.error(error.message || 'CPU License激活失败')
  } finally {
    cpuActivateLoading.value = false
  }
}

const copyCpuLicense = () => {
  navigator.clipboard.writeText(cpuLicenseResult.value)
  ElMessage.success('CPU License已复制到剪贴板')
}

const copySignedLicense = () => {
  navigator.clipboard.writeText(cpuSignedLicense.value)
  ElMessage.success('签名License已复制到剪贴板')
}

// 原有内容
const email = ref('')
const keyContent = ref('')
const license = ref('')

const mac = ref('00-11-22-33-44-55') // 实际可让用户输入或用桌面端自动获取
const cpu = ref('CPU-123456')
const disk = ref('DISK-987654')
const sha256 = ref('')
const rsaPublicKey = ref('')

const generateKey = () => {
  // 拼接格式：mac|cpu|disk|sha256
  const raw = `${mac.value}|${cpu.value}|${disk.value}|${sha256.value}`
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(rsaPublicKey.value)
  const encrypted = encrypt.encrypt(raw)
  if (!encrypted) {
    ElMessage.error('加密失败，请检查公钥')
    return
  }
  keyContent.value = encrypted
  ElMessage.success('本机Key已生成，请复制或直接用于获取并激活License')
}

const submitLicense = async () => {
  if (!email.value || !keyContent.value) {
    ElMessage.warning('请填写邮箱并生成/粘贴Key内容')
    return
  }
  try {
    const res = await createLicense({
      email: email.value,
      key_content: keyContent.value
    })
    license.value = res.license
    ElMessage.success('License获取并激活成功')
  } catch (e) {
    ElMessage.error(e?.response?.data?.detail || '获取License或激活License失败')
  }
}

const copyLicense = () => {
  navigator.clipboard.writeText(license.value)
  ElMessage.success('已复制到剪贴板')
}
</script> 