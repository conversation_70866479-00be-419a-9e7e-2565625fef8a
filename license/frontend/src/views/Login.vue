<template>
  <div class="login-bg">
    <div class="login-left">
      <div class="slogan-main">
        <div class="slogan-title">
          <span class="blue">科技</span>赋能
        </div>
        <div class="slogan-title">
          <span class="blue">创新</span>引领AI时代
        </div>
        <div class="ai-logo">AI</div>
        <div class="ai-factory"></div>
      </div>
    </div>
    <div class="login-form-card">
      <div class="login-title">账号密码登录</div>
      <LoginForm :onLogin="handleLogin" />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { login, getUserByEmail } from '../api/auth';
import { setToken, setUserInfo } from '../utils/storage';
import LoginForm from '../components/LoginForm.vue';

const router = useRouter();

const handleLogin = async (userName, password) => {
  try {
    console.log('开始登录:', { userName });
    const res = await login(userName, password);
    console.log('登录响应:', res);
    console.log('登录响应数据:', res.data);

    // 检查响应数据结构
    console.log('响应数据的所有键:', Object.keys(res.data));
    console.log('用户信息 (res.data.user):', res.data.user);

    // 保存token
    setToken(res.data.access_token);
    console.log('Token已保存:', res.data.access_token);

    // 检查用户信息字段 - 针对远程服务器的特殊处理
    let userInfo = null;

    console.log('🔍 开始分析响应数据结构...');
    console.log('响应数据类型:', typeof res.data);
    console.log('响应数据内容:', JSON.stringify(res.data, null, 2));

    // 方法1：直接从 res.data.user 获取
    if (res.data && res.data.user && typeof res.data.user === 'object' && res.data.user.email) {
      userInfo = res.data.user;
      console.log('✅ 从 res.data.user 获取到用户信息:', userInfo);
    }
    // 方法2：如果 res.data.user 不存在，尝试其他字段
    else if (res.data && res.data.email) {
      userInfo = res.data;
      console.log('✅ 从 res.data 直接获取到用户信息:', userInfo);
    }
    // 方法3：尝试其他可能的字段名
    else if (res.data) {
      const possibleFields = ['userInfo', 'userData', 'profile', 'account', 'data'];
      for (const field of possibleFields) {
        if (res.data[field] && res.data[field].email) {
          userInfo = res.data[field];
          console.log(`✅ 从 res.data.${field} 获取到用户信息:`, userInfo);
          break;
        }
      }
    }

    // 方法4：如果以上都失败，但登录成功了，尝试从后端API获取用户信息
    if (!userInfo && res.data && res.data.access_token) {
      console.log('🔄 登录成功但无用户信息，尝试从后端API获取...');
      const loginEmail = userName; // userName 来自登录表单
      if (loginEmail && loginEmail.includes('@')) {
        try {
          console.log('📡 调用后端API获取用户信息:', loginEmail);
          const userResponse = await getUserByEmail(loginEmail);
          console.log('📡 后端API响应:', userResponse);

          if (userResponse.data && userResponse.data.email) {
            userInfo = userResponse.data;
            console.log('✅ 从后端API获取到用户信息:', userInfo);
          } else {
            throw new Error('API返回的用户信息无效');
          }
        } catch (apiError) {
          console.warn('⚠️ 从后端API获取用户信息失败:', apiError.message);
          console.log('🔄 降级到构造基本用户信息...');

          // 降级方案：构造基本用户信息
          userInfo = {
            email: loginEmail,
            user_name: loginEmail.split('@')[0], // 从邮箱提取用户名
            role_id: null,
            id: null,
            user_id: null,
            real_name: null,
            company: null
          };
          console.log('🔄 构造的用户信息:', userInfo);
        }
      }
    }

    if (userInfo && userInfo.email) {
      setUserInfo(userInfo);
      console.log('✅ 用户信息已保存:', userInfo);

      // 验证保存是否成功
      setTimeout(() => {
        const savedUserStr = localStorage.getItem('user');
        console.log('localStorage中的原始用户信息:', savedUserStr);
        if (savedUserStr && savedUserStr !== 'undefined' && savedUserStr !== 'null') {
          try {
            const savedUserInfo = JSON.parse(savedUserStr);
            console.log('✅ 验证保存的用户信息:', savedUserInfo);
          } catch (e) {
            console.error('❌ 解析保存的用户信息失败:', e);
          }
        } else {
          console.error('❌ localStorage中没有找到用户信息!');
        }
      }, 100);
    } else {
      console.error('❌ 无法找到或构造有效的用户信息!');
      console.error('响应数据结构:', res.data);
      console.error('可用字段:', res.data ? Object.keys(res.data) : 'res.data 为空');
      console.error('登录用户名:', userName);
    }

    router.push('/download');
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};
</script>

<style scoped>
.login-bg {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(120deg, #0f1c2f 60%, #1a2a4a 100%);
}
.login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.slogan-main {
  color: #fff;
  margin-left: 80px;
}
.slogan-title {
  font-size: 40px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 2px 2px 8px #0f1c2f;
}
.slogan-title .blue {
  color: #1ecfff;
  font-size: 44px;
  letter-spacing: 2px;
}
.ai-logo {
  font-size: 180px;
  font-weight: bold;
  color: #1ecfff;
  opacity: 0.15;
  margin-top: 40px;
  letter-spacing: 10px;
}
.ai-factory {
  width: 320px;
  height: 60px;
  background: url('/ai-factory.png') no-repeat center/contain;
  margin-top: -60px;
}
.login-form-card {
  width: 400px;
  background: rgba(255,255,255,0.08);
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  padding: 40px 36px 32px 36px;
  margin: 80px 120px 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  backdrop-filter: blur(8px);
}
.login-title {
  font-size: 22px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 32px;
  letter-spacing: 2px;
}
</style> 