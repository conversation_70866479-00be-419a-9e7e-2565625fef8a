<template>
  <div style="max-width: 500px; margin: 60px auto; padding: 32px 24px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #f0f1f2;">
    <div style="font-weight: bold; font-size: 18px; margin-bottom: 24px;">软件下载</div>
    <form @submit.prevent="handleDownload">
      <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <span class="input-label">购买邮箱：</span>
        <el-input v-model="email" placeholder="请输入购买邮箱" style="flex:1; min-width:0;" />
      </div>
      <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <span class="input-label">下载密码：</span>
        <el-input v-model="password" type="password" placeholder="请输入下载密码" style="flex:1; min-width:0;" />
      </div>
      <div v-if="error" style="color: #f56c6c; margin-bottom: 16px; font-size: 13px;">{{ error }}</div>
      <div v-if="showProgress" style="margin-bottom: 16px;">
        <el-progress :percentage="progress" :stroke-width="16" status="success" v-if="progress===100" />
        <el-progress :percentage="progress" :stroke-width="16" v-else />
      </div>
      <div style="text-align: center; margin-bottom: 8px;">
        <el-button type="primary" native-type="submit" :loading="loading" style="width: 180px; font-size: 16px;">{{ loading ? '下载中...' : '下载' }}</el-button>
      </div>
    </form>
    <!-- 下载完成后显示sha256和rsa公钥 -->
    <div v-if="downloaded" style="margin-top: 32px;">
      <div style="display: flex; align-items: flex-start; margin-bottom: 18px;">
        <span class="input-label" style="margin-top: 6px;">SHA256签名：</span>
        <el-input type="textarea" readonly rows="3" v-model="sha256Info" style="flex:1; min-width:0;" />
        <el-button class="copy-btn" @click="copyText(sha256Info)">复制</el-button>
      </div>
      <div style="display: flex; align-items: flex-start;">
        <span class="input-label" style="margin-top: 6px;">RSA公钥：</span>
        <el-input type="textarea" readonly rows="3" v-model="rsaPubKey" style="flex:1; min-width:0;" />
        <el-button class="copy-btn" @click="copyText(rsaPubKey)">复制</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.input-label {
  font-size: 13px;
  color: #666;
  min-width: 90px;
  margin-right: 8px;
  white-space: nowrap;
}
.copy-btn {
  margin-left: 12px;
  height: 38px;
  min-width: 64px;
  font-size: 15px;
  border-radius: 20px;
  background: linear-gradient(90deg, #409eff 60%, #66b1ff 100%);
  color: #fff;
  border: none;
  box-shadow: 0 2px 8px #e6f0fa;
  transition: background 0.2s, box-shadow 0.2s;
}
.copy-btn:hover {
  background: linear-gradient(90deg, #337ecc 60%, #409eff 100%);
  box-shadow: 0 4px 16px #d0e6fa;
}
</style>

<script setup>
import { ref } from 'vue';
import { downloadPackage, getDownloadInfo } from '../api/license';
import { ElMessage } from 'element-plus';

const email = ref('');
const password = ref('');
const loading = ref(false);
const error = ref('');
const progress = ref(0);
const showProgress = ref(false);
const downloaded = ref(false);
const sha256Info = ref('');
const rsaPubKey = ref('');

const handleDownload = async () => {
  loading.value = true;
  error.value = '';
  progress.value = 1;
  showProgress.value = true;
  try {
    await downloadPackage(email.value, password.value, (percent) => {
      progress.value = percent;
    });
    loading.value = false;
    progress.value = 100;
    downloaded.value = true;
    // 下载完成后获取sha256和rsa公钥
    const info = await getDownloadInfo(email.value, password.value);
    sha256Info.value = info.sha256 || '';
    rsaPubKey.value = info.rsaPubKey || '';
    ElMessage.success('下载完成！');
  } catch (e) {
    error.value = e.message || '下载失败，请检查邮箱和密码';
    loading.value = false;
    // 失败时进度条保持当前状态
  }
};

function copyText(text) {
  if (!text) return;
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板');
  });
}
</script> 