export function setToken(token) {
  localStorage.setItem('token', token);
}

export function getToken() {
  const token = localStorage.getItem('token');
  if (!token || token === 'undefined' || token === 'null') {
    return null;
  }
  return token;
}

export function setUserInfo(user) {
  console.log('setUserInfo 被调用，参数:', user);
  try {
    if (!user) {
      console.warn('用户信息为空，无法保存');
      return;
    }

    const userStr = JSON.stringify(user);
    console.log('序列化后的用户信息:', userStr);
    localStorage.setItem('user', userStr);
    console.log('用户信息已保存到localStorage');

    // 验证保存
    const saved = localStorage.getItem('user');
    console.log('验证localStorage中的用户信息:', saved);
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
}

export function getUserInfo() {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr || userStr === 'undefined' || userStr === 'null') {
      console.log('No valid user info found in localStorage');
      return {};
    }
    return JSON.parse(userStr);
  } catch (error) {
    console.error('Error parsing user info from localStorage:', error);
    // 清除损坏的数据
    localStorage.removeItem('user');
    return {};
  }
}

export function clearAuth() {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
} 