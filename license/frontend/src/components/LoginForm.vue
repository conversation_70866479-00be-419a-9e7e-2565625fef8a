<template>
  <form class="login-form" @submit.prevent="handleSubmit">
    <div class="input-group">
      <span class="input-icon"><svg width="18" height="18" viewBox="0 0 24 24"><path fill="#999" d="M12 12c2.7 0 8 1.34 8 4v2H4v-2c0-2.66 5.3-4 8-4zm0-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8z"/></svg></span>
      <input
        v-model="userName"
        type="text"
        placeholder="请输入用户名"
        required
        class="login-input"
      />
    </div>
    <div class="input-group">
      <span class="input-icon"><svg width="18" height="18" viewBox="0 0 24 24"><path fill="#999" d="M12 17a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2zm6-6V9a6 6 0 0 0-12 0v2a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2zm-8-2a4 4 0 0 1 8 0v2H6V9z"/></svg></span>
      <input
        v-model="password"
        type="password"
        placeholder="请输入密码"
        required
        class="login-input"
      />
    </div>
    <div v-if="error" class="login-error">{{ error }}</div>
    <button type="submit" :disabled="loading" class="login-btn">
      {{ loading ? '登录中...' : '立即登录' }}
    </button>
  </form>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  onLogin: Function
});

const userName = ref('');
const password = ref('');
const loading = ref(false);
const error = ref('');

const handleSubmit = async () => {
  loading.value = true;
  error.value = '';
  try {
    await props.onLogin(userName.value, password.value);
  } catch (e) {
    error.value = '用户名或密码错误，请重新输入';
  }
  loading.value = false;
};
</script>

<style scoped>
.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}
.input-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.login-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: none;
  border-radius: 8px;
  background: rgba(255,255,255,0.18);
  color: #fff;
  font-size: 16px;
  box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.10);
  outline: none;
  transition: box-shadow 0.2s, background 0.2s;
}
.login-input:focus {
  background: rgba(255,255,255,0.28);
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.18);
}
.login-error {
  color: #f56c6c;
  font-size: 14px;
  text-align: left;
  margin-top: -12px;
  margin-bottom: -8px;
}
.login-btn {
  width: 100%;
  padding: 12px 0;
  background: linear-gradient(90deg, #1ecfff 0%, #409eff 100%);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.10);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}
.login-btn:active {
  background: linear-gradient(90deg, #409eff 0%, #1ecfff 100%);
}
.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style> 