import {createRouter, createWebHistory} from 'vue-router';
import Login from '../views/Login.vue';
import Home from '../views/Home.vue';
import Download from '../views/Download.vue';
import License from '../views/License.vue';
import Credential from '../views/Credential.vue';
import MainLayout from '../layout/MainLayout.vue';
import {getToken} from '../utils/storage';

const routes = [
  { path: '/login', component: Login },
  {
    path: '/',
    component: MainLayout,
    children: [
      { path: '', redirect: '/download' },
      { path: 'download', component: Download },
      { path: 'license', component: License },
      { path: 'credential', component: Credential },
      { path: 'user', component: () => import('../views/User.vue') }
    ]
  },
  { path: '/home', component: Home }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
});

// 添加路由错误处理
router.onError((error) => {
  console.error('Router error:', error);
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  console.log('Router beforeEach:', { to: to.path, from: from.path });
  const token = getToken();
  console.log('Token exists:', !!token);

  if (!token && to.path !== '/login') {
    // 未登录，强制跳转到登录页
    console.log('Redirecting to login - no token');
    next({ path: '/login' });
  } else if (token && to.path === '/login') {
    // 已登录，访问登录页，自动跳转到下载页
    console.log('Redirecting to download - already logged in');
    next({ path: '/download' });
  } else {
    console.log('Proceeding to route:', to.path);
    next();
  }
});

export default router; 