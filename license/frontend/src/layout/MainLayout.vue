<template>
  <div style="display: flex; height: 100vh;">
    <!-- 左侧菜单栏 -->
    <aside style="width: 200px; background: #2d3a4b; color: #fff; display: flex; flex-direction: column;">
      <div style="height: 60px; display: flex; align-items: center; justify-content: center; font-size: 20px; font-weight: bold; border-bottom: 1px solid #223044;">
        <img src="" alt="logo" style="height: 32px; margin-right: 8px;" />
        </div>
      <nav style="flex: 1; padding-top: 24px;">
        <div :class="['menu-item', $route.path === '/download' ? 'active' : '']" @click="goMenu('/download')">
          软件下载
        </div>
        <!-- <div :class="['menu-item', $route.path === '/license' ? 'active' : '']" @click="goMenu('/license')">
          凭证获取OLD
        </div> -->
        <div :class="['menu-item', $route.path === '/credential' ? 'active' : '']" @click="goMenu('/credential')">
          凭证获取
        </div>
        <div style="flex:1"></div>
        <div v-if="isAdmin" :class="['menu-item', $route.path === '/user' ? 'active' : '']" @click="goMenu('/user')">
          用户管理
        </div>
      </nav>
    </aside>
    <!-- 右侧主内容区 -->
    <div style="flex: 1; display: flex; flex-direction: column;">
      <!-- 顶部栏 -->
      <header style="height: 60px; background: #fff; border-bottom: 1px solid #eee; display: flex; align-items: center; justify-content: space-between; padding: 0 24px;">
        <div style="font-size: 18px; font-weight: bold; color: #2d3a4b;">软件授权管理系统</div>
        <div style="display: flex; align-items: center;">
          <span style="margin-right: 16px; color: #666;">{{ userName }}</span>
          <button @click="logout" style="background: #f56c6c; color: #fff; border: none; border-radius: 4px; padding: 6px 16px; cursor: pointer;">退出登录</button>
        </div>
      </header>
      <!-- 内容区 -->
      <main style="flex: 1; background: #f7f8fa; padding: 32px; overflow: auto;">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { getUserInfo, clearAuth } from '../utils/storage';
import { ref, computed, onMounted } from 'vue';

const router = useRouter();
const route = useRoute();

const userInfo = ref({});

// 响应式计算属性
const isAdmin = computed(() => userInfo.value && userInfo.value.role_id === 999999);
const userName = computed(() => {
  if (userInfo.value && userInfo.value.email) {
    return userInfo.value.email;
  }
  return '用户';
});

// 加载用户信息的函数
const loadUserInfo = () => {
  try {
    const info = getUserInfo();
    userInfo.value = info;
    console.log('User info loaded in MainLayout:', { userInfo: info, isAdmin: isAdmin.value, userName: userName.value });
    return info;
  } catch (error) {
    console.error('Error loading user info in MainLayout:', error);
    userInfo.value = {};
    return {};
  }
};

// 组件挂载时加载用户信息
onMounted(() => {
  loadUserInfo();

  // 监听localStorage变化，当用户信息更新时自动刷新
  window.addEventListener('storage', (e) => {
    if (e.key === 'user') {
      console.log('检测到用户信息变化，重新加载');
      loadUserInfo();
    }
  });

  // 定期检查用户信息（作为备用方案）
  const checkInterval = setInterval(() => {
    const currentInfo = getUserInfo();
    if (JSON.stringify(currentInfo) !== JSON.stringify(userInfo.value)) {
      console.log('定期检查发现用户信息变化，重新加载');
      userInfo.value = currentInfo;
    }
  }, 1000);

  // 清理定时器
  setTimeout(() => {
    clearInterval(checkInterval);
  }, 10000); // 10秒后停止定期检查
});

function goMenu(path) {
  if (route.path !== path) {
    router.push(path);
  }
}

function logout() {
  clearAuth();
  router.push({ path: '/login' });
}
</script>

<style scoped>
.menu-item {
  padding: 14px 32px;
  cursor: pointer;
  font-size: 16px;
  border-left: 4px solid transparent;
  transition: background 0.2s, border-color 0.2s;
}
.menu-item.active {
  background: #223044;
  border-left: 4px solid #409eff;
}
.menu-item:hover {
  background: #223044;
}
</style> 