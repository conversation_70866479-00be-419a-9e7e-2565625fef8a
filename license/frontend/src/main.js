import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

console.log('Starting Vue app...');
console.log('Current location:', window.location.href);
console.log('Base URL:', import.meta.env.BASE_URL);

const app = createApp(App);

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err);
  console.error('Component:', vm);
  console.error('Info:', info);
};

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('Vue Warning:', msg);
  console.warn('Trace:', trace);
};

app.use(router).use(ElementPlus).mount('#app');

console.log('Vue app mounted successfully');