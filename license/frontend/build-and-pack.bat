@echo off
echo ========================================
echo 前端项目构建和打包脚本
echo ========================================

cd /d "D:\project\jiaoda\twinbuilder-agents\license\frontend"

echo.
echo [1/4] 检查依赖...
if not exist "node_modules" (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 依赖安装失败！
        pause
        exit /b 1
    )
) else (
    echo 依赖已存在，跳过安装
)

echo.
echo [2/4] 构建项目...
call npm run build
if errorlevel 1 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo [3/4] 清理旧的压缩包...
if exist "dist.zip" del "dist.zip"

echo.
echo [4/4] 打包dist目录...
powershell -Command "Compress-Archive -Path 'dist\*' -DestinationPath 'dist.zip' -Force"
if errorlevel 1 (
    echo 打包失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo 文件位置: %CD%\dist.zip
echo 文件大小: 
powershell -Command "(Get-Item 'dist.zip').Length / 1MB | ForEach-Object { '{0:N2} MB' -f $_ }"
echo ========================================

pause
