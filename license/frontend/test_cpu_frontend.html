<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU专用License前端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #409eff;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, textarea {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #337ecc;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .info {
            color: #909399;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 CPU专用License前端测试页面</h1>
        
        <div class="section">
            <h3>📋 测试清单</h3>
            <div id="testChecklist">
                <p>✅ 前端文件修改完成</p>
                <p>✅ API函数添加完成</p>
                <p>✅ Vue组件更新完成</p>
                <p>✅ 语法检查通过</p>
                <p>⏳ 等待功能测试...</p>
            </div>
        </div>

        <div class="section">
            <h3>🚀 CPU License生成测试</h3>
            <div class="form-group">
                <label>用户邮箱：</label>
                <input type="email" id="cpuEmail" placeholder="<EMAIL>" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>用户密码：</label>
                <input type="password" id="cpuPassword" placeholder="Cmsr@2025!" value="Cmsr@2025!">
            </div>
            <div class="form-group">
                <label>CPU信息：</label>
                <input type="text" id="cpuInfo" placeholder="Intel-Core-i7-12700K-TEST-FRONTEND" value="Intel-Core-i7-12700K-TEST-FRONTEND">
            </div>
            <div class="form-group">
                <button onclick="testCpuLicense()" id="testBtn">测试CPU License生成</button>
                <button onclick="clearResults()" id="clearBtn">清除结果</button>
            </div>
            <div id="testResults" class="result" style="display: none;">
                <h4>测试结果：</h4>
                <div id="resultContent"></div>
            </div>
        </div>

        <div class="section">
            <h3>📝 实现总结</h3>
            <h4>✅ 已完成的前端修改：</h4>
            <ul>
                <li><strong>src/api/license.js</strong> - 新增3个CPU专用API函数</li>
                <li><strong>src/views/Credential.vue</strong> - 新增CPU专用界面和逻辑</li>
            </ul>
            
            <h4>🔄 用户使用流程：</h4>
            <ol>
                <li>输入邮箱、密码、CPU信息</li>
                <li>点击"生成CPU License"按钮</li>
                <li>系统自动获取公钥、加密CPU信息、创建License</li>
                <li>显示生成的License、机器码、过期时间</li>
                <li>可选：点击"激活License"获取签名</li>
            </ol>
            
            <h4>🎯 关键特性：</h4>
            <ul>
                <li>✅ 简化流程：只需CPU信息，无需MAC、磁盘、SHA256</li>
                <li>✅ 自动加密：前端自动处理RSA加密</li>
                <li>✅ 错误处理：完整的错误捕获和用户提示</li>
                <li>✅ 状态反馈：Loading状态和成功/失败消息</li>
                <li>✅ 兼容性：保留原有功能，新老并存</li>
            </ul>
        </div>

        <div class="section">
            <h3>🔧 技术实现细节</h3>
            <h4>API端点：</h4>
            <ul>
                <li><code>POST /api/v1/license/cpu_info</code> - 获取RSA公钥</li>
                <li><code>POST /api/v1/license/cpu_license_create</code> - 创建CPU License</li>
                <li><code>POST /api/v1/license/cpu_license_activate</code> - 激活CPU License</li>
            </ul>
            
            <h4>前端函数：</h4>
            <ul>
                <li><code>getCpuInfo(email, password)</code> - 获取公钥</li>
                <li><code>createCpuLicense(data)</code> - 创建License</li>
                <li><code>activateCpuLicense(data)</code> - 激活License</li>
                <li><code>generateCpuLicense()</code> - 完整生成流程</li>
                <li><code>activateCpuLicenseHandler()</code> - 完整激活流程</li>
            </ul>
        </div>
    </div>

    <script>
        function testCpuLicense() {
            const resultDiv = document.getElementById('testResults');
            const contentDiv = document.getElementById('resultContent');
            const testBtn = document.getElementById('testBtn');
            
            resultDiv.style.display = 'block';
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';
            
            contentDiv.innerHTML = `
                <p class="info">⏳ 模拟测试CPU License生成流程...</p>
                <p class="success">✅ 1. 获取RSA公钥成功</p>
                <p class="success">✅ 2. CPU信息加密成功</p>
                <p class="success">✅ 3. CPU License创建成功</p>
                <p class="success">✅ 4. License激活成功</p>
                <br>
                <p><strong>模拟结果：</strong></p>
                <p>License: <code>MXxjMjAxMjBlMTMzZDkxOTQxM2M0YzFjOWU3Y2ZiZTk2OHxJbn...</code></p>
                <p>CPU机器码: <code>c20120e133d919413c4c1c9e7cfbe968</code></p>
                <p>过期时间: <code>2026-08-01</code></p>
                <p class="info">注意：这是模拟测试，实际功能需要后端服务运行</p>
            `;
            
            setTimeout(() => {
                testBtn.disabled = false;
                testBtn.textContent = '测试CPU License生成';
            }, 2000);
        }
        
        function clearResults() {
            document.getElementById('testResults').style.display = 'none';
        }
    </script>
</body>
</html>
