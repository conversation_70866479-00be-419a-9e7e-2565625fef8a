# 用户信息调试版本

## 问题现状
- Token存在但用户信息为空对象 `{}`
- 右上角显示默认的"用户"而不是邮箱
- localStorage中没有有效的用户信息

## 调试版本改进

### 1. 登录流程调试
在 `Login.vue` 中添加了详细的登录调试信息：
- 登录请求参数
- 登录响应完整数据
- 用户信息保存过程
- localStorage验证

### 2. 存储函数调试
在 `storage.js` 中的 `setUserInfo` 函数添加了：
- 参数验证
- 序列化过程
- 保存验证
- 错误捕获

### 3. 用户信息加载增强
在 `MainLayout.vue` 中添加了：
- localStorage变化监听
- 定期检查机制
- 自动刷新用户信息

## 部署调试版本

### 1. 上传新包
```bash
cd /usr/share/nginx/
sudo rm -rf license_front/*
sudo unzip dist-debug.zip -d license_front/
sudo chown -R nginx:nginx license_front/
sudo chmod -R 755 license_front/
```

### 2. 清除缓存
```javascript
// 在浏览器控制台执行
localStorage.clear();
sessionStorage.clear();
location.reload();
```

## 调试步骤

### 第一步：检查登录过程
1. 打开 `http://*********:38083/license/`
2. 打开F12控制台
3. 输入用户名密码登录
4. 观察控制台输出，应该看到：

```
开始登录: {userName: "<EMAIL>"}
登录响应: {data: {...}, status: 200, ...}
登录响应数据: {access_token: "...", user: {...}}
用户信息: {email: "<EMAIL>", role_id: ..., ...}
Token已保存: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
setUserInfo 被调用，参数: {email: "...", ...}
序列化后的用户信息: {"email":"...","role_id":...}
用户信息已保存到localStorage
验证localStorage中的用户信息: {"email":"...","role_id":...}
用户信息已保存: {email: "...", ...}
验证保存的用户信息: {email: "...", ...}
```

### 第二步：检查页面跳转后的用户信息加载
登录成功跳转到下载页面后，应该看到：

```
User info loaded in MainLayout: {
  userInfo: {email: "<EMAIL>", role_id: 1, ...},
  isAdmin: false,
  userName: "<EMAIL>"
}
```

### 第三步：手动验证localStorage
在控制台执行：
```javascript
// 检查token
console.log('Token:', localStorage.getItem('token'));

// 检查用户信息
console.log('User info raw:', localStorage.getItem('user'));
console.log('User info parsed:', JSON.parse(localStorage.getItem('user') || '{}'));
```

## 可能的问题和解决方案

### 问题1：登录API响应结构不匹配
**症状**：控制台显示 `res.data.user` 为 `undefined`
**解决**：检查后端API响应结构，可能需要修改为 `res.data` 而不是 `res.data.user`

### 问题2：setUserInfo函数调用失败
**症状**：看不到 "setUserInfo 被调用" 的日志
**解决**：检查登录流程是否正确执行

### 问题3：localStorage权限问题
**症状**：保存时出现权限错误
**解决**：检查浏览器设置，确保允许localStorage

### 问题4：跨域或安全策略问题
**症状**：登录成功但数据丢失
**解决**：检查浏览器安全策略和nginx配置

## 临时解决方案

如果问题仍然存在，可以在控制台手动设置用户信息：

```javascript
// 手动设置用户信息（替换为实际的邮箱）
const userInfo = {
  email: "<EMAIL>",
  role_id: 1,
  user_name: "Your Name"
};
localStorage.setItem('user', JSON.stringify(userInfo));
location.reload();
```

## 预期结果

调试版本部署后，你应该能够：
1. 在控制台看到详细的登录和用户信息保存过程
2. 确定问题出现在哪个环节
3. 根据日志信息进行针对性修复

## 下一步

请按照上述步骤部署调试版本，然后：
1. 执行完整的登录流程
2. 截图控制台的所有输出
3. 告诉我具体在哪个步骤出现了问题

这样我就能准确定位问题并提供精确的解决方案。
