server {
    listen 8099;
    server_name ***********;

    # 前端静态文件
    location /license/ {
        alias /wwwroot/license/;
        index index.html;
        try_files $uri $uri/ /license/index.html;
    }

    # API代理：后端服务地址
    location /api/ {
        proxy_pass http://***********:8009;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}