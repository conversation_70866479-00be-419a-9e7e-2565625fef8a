# 许可证管理系统前端

## 项目介绍
许可证管理系统的前端项目，基于 Vue 3 + Vite + Element Plus 开发。

## 技术栈
- Vue 3
- Vite
- Element Plus
- Vue Router
- Pinia
- Axios
- ESLint
- Prettier

## 开发环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0

## 安装依赖
```bash
npm install
```

## 开发
```bash
npm run dev
```

## 构建
```bash
npm run build
```

## 预览构建结果
```bash
npm run preview
```

## 代码检查
```bash
npm run lint
```

## 项目结构
```
src/
├── api/          # API 接口
├── assets/       # 静态资源
├── components/   # 公共组件
├── composables/  # 组合式函数
├── config/       # 配置文件
├── layouts/      # 布局组件
├── router/       # 路由配置
├── stores/       # 状态管理
├── styles/       # 全局样式
├── utils/        # 工具函数
└── views/        # 页面组件
```

## 环境变量
- `.env` - 基础环境变量
- `.env.development` - 开发环境变量
- `.env.production` - 生产环境变量

## 开发规范
1. 使用 ESLint 和 Prettier 进行代码格式化
2. 遵循 Vue 3 组合式 API 风格
3. 使用 TypeScript 进行类型检查
4. 遵循组件命名规范
5. 使用 Git Flow 工作流

## 部署
1. 构建项目：`npm run build`
2. 将 `dist` 目录下的文件部署到 Web 服务器
3. 配置 Nginx 反向代理

## 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建 Pull Request

## 许可证
MIT 