# License Server 部署指南

## 📦 部署包信息

- **文件名**: `license-server.zip`
- **目标路径**: `/home/<USER>/license/server`
- **包含内容**: 完整的Python后端代码

## 🚀 部署步骤

### 1. 上传并解压
```bash
# 在远程Linux服务器上执行
cd /home/<USER>/license
sudo mkdir -p server
cd server

# 上传 license-server.zip 到当前目录，然后解压
sudo unzip license-server.zip
sudo chown -R sy_cmsr:sy_cmsr /home/<USER>/license/server
```

### 2. 安装Python环境
```bash
# 确保Python 3.8+已安装
python3 --version

# 安装pip（如果没有）
sudo apt update
sudo apt install python3-pip python3-venv -y
```

### 3. 创建虚拟环境
```bash
cd /home/<USER>/license/server
python3 -m venv venv
source venv/bin/activate
```

### 4. 安装依赖
```bash
# 激活虚拟环境后
pip install -r requirements.txt
```

### 5. 配置环境变量
```bash
# 创建环境配置文件
nano .env
```

在 `.env` 文件中添加：
```env
# 数据库配置
DB_HOST=*********
DB_PORT=23306
DB_USER=indusaio_user
DB_PASSWORD=Sygy@2025
DB_NAME=indusaio_agent

# JWT配置
JWT_SECRET=your-secret-key-change-this
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 许可证配置
LICENSE_DEFAULT_DURATION=365

# 用户默认密码
DEFAULT_USER_PASSWORD=Cmsr@2025!
```

### 6. 测试运行
```bash
# 激活虚拟环境
source venv/bin/activate

# 测试运行
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 7. 创建系统服务（推荐）
```bash
# 创建服务文件
sudo nano /etc/systemd/system/license-server.service
```

服务文件内容：
```ini
[Unit]
Description=License Server
After=network.target

[Service]
Type=simple
User=sy_cmsr
WorkingDirectory=/home/<USER>/license/server
Environment=PATH=/home/<USER>/license/server/venv/bin
ExecStart=/home/<USER>/license/server/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable license-server
sudo systemctl start license-server
sudo systemctl status license-server
```

### 8. 配置Nginx反向代理（可选）
```bash
sudo nano /etc/nginx/sites-available/license-server
```

Nginx配置：
```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名或IP

    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/license-server /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔧 验证部署

### 1. 检查服务状态
```bash
sudo systemctl status license-server
```

### 2. 检查端口监听
```bash
sudo netstat -tlnp | grep :8000
```

### 3. 测试API
```bash
curl http://localhost:8000/docs
```

### 4. 测试创建用户
```bash
curl -X POST "http://localhost:8000/api/v1/user/add" \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "user_name": "测试用户",
       "real_name": "张三"
     }'
```

### 5. 测试登录
```bash
curl -X POST "http://localhost:8000/api/v1/login" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=<EMAIL>&password=Cmsr@2025!"
```

## 📋 目录结构

部署后的目录结构：
```
/home/<USER>/license/server/
├── api/                    # API路由
├── database/              # 数据库配置
├── models/                # 数据模型
├── services/              # 业务逻辑
├── utils/                 # 工具函数
├── main.py               # 主程序入口
├── config.py             # 配置文件
├── requirements.txt      # 依赖列表
├── .env                  # 环境变量（需要创建）
└── venv/                 # 虚拟环境（安装后创建）
```

## 🔍 故障排除

### 查看日志
```bash
# 查看服务日志
sudo journalctl -u license-server -f

# 查看最近的错误
sudo journalctl -u license-server --since "1 hour ago"
```

### 常见问题
1. **端口被占用**: 修改config.py中的SERVER_PORT
2. **数据库连接失败**: 检查.env中的数据库配置
3. **权限问题**: 确保sy_cmsr用户有正确的文件权限

## 📞 支持

如果遇到问题，请检查：
1. 服务日志
2. 数据库连接
3. 防火墙设置
4. 环境变量配置
