#!/bin/bash
# WebSocket修复部署脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SERVER_IP="***********"
SERVER_USER="root"
PROJECT_DIR="/data/agent"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查本地文件
check_local_files() {
    log_info "检查本地修复文件..."
    
    required_files=(
        "agents/predict_algorith_agent/network/websocket_manager.py"
        "agents/predict_algorith_agent/api/websocket_routes.py"
        "agents/predict_algorith_agent/scripts/simple_websocket_test.py"
        "agents/predict_algorith_agent/scripts/fix_websocket_errors.sh"
        "manage_server.sh"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少文件: $file"
            return 1
        fi
    done
    
    log_success "本地文件检查通过"
    return 0
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    # 创建临时目录
    temp_dir="websocket_fix_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$temp_dir"
    
    # 复制文件
    cp -r agents "$temp_dir/"
    cp manage_server.sh "$temp_dir/"
    
    # 创建部署脚本
    cat > "$temp_dir/deploy.sh" << 'EOF'
#!/bin/bash
# 服务器端部署脚本

PROJECT_DIR="/data/agent"
BACKUP_DIR="/data/agent/backup_$(date +%Y%m%d_%H%M%S)"

echo "🔧 开始WebSocket修复部署..."

# 停止服务
echo "⏹️ 停止服务..."
cd "$PROJECT_DIR"
./manage_server.sh stop 2>/dev/null || true
pkill -f "predict_main.py" 2>/dev/null || true
sleep 3

# 备份现有文件
echo "💾 备份现有文件..."
mkdir -p "$BACKUP_DIR"
cp -r agents "$BACKUP_DIR/" 2>/dev/null || true
cp manage_server.sh "$BACKUP_DIR/" 2>/dev/null || true

# 应用修复
echo "🔄 应用修复文件..."
cp -r agents/* agents/
cp manage_server.sh ./

# 设置权限
chmod +x manage_server.sh
chmod +x agents/predict_algorith_agent/scripts/*.sh

# 清理PID文件
rm -f server.pid log_monitor.pid

# 重启服务
echo "🚀 重启服务..."
./manage_server.sh start

# 等待服务启动
sleep 5

# 验证服务
echo "✅ 验证服务状态..."
if ./manage_server.sh status | grep -q "运行中"; then
    echo "✅ 服务启动成功"
    
    # 运行简单测试
    echo "🧪 运行连接测试..."
    python3 agents/predict_algorith_agent/scripts/simple_websocket_test.py
    
    if [ $? -eq 0 ]; then
        echo "🎉 WebSocket修复部署成功！"
    else
        echo "⚠️ 服务启动成功，但连接测试失败"
    fi
else
    echo "❌ 服务启动失败"
    exit 1
fi
EOF
    
    chmod +x "$temp_dir/deploy.sh"
    
    # 打包
    tar -czf "${temp_dir}.tar.gz" -C "$temp_dir" .
    rm -rf "$temp_dir"
    
    log_success "部署包创建完成: ${temp_dir}.tar.gz"
    echo "$temp_dir"
}

# 上传并部署
upload_and_deploy() {
    local package_name="$1"
    
    log_info "上传部署包到服务器..."
    
    # 上传文件
    scp "${package_name}.tar.gz" "${SERVER_USER}@${SERVER_IP}:/tmp/" || {
        log_error "文件上传失败"
        return 1
    }
    
    log_success "文件上传成功"
    
    # 远程部署
    log_info "在服务器上执行部署..."
    
    ssh "${SERVER_USER}@${SERVER_IP}" << EOF
        cd /tmp
        tar -xzf "${package_name}.tar.gz"
        cd "$PROJECT_DIR"
        bash /tmp/deploy.sh
EOF
    
    if [ $? -eq 0 ]; then
        log_success "远程部署成功"
        return 0
    else
        log_error "远程部署失败"
        return 1
    fi
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    rm -f websocket_fix_*.tar.gz
    log_success "清理完成"
}

# 主函数
main() {
    echo "🔧 WebSocket修复部署工具"
    echo "🎯 目标服务器: ${SERVER_USER}@${SERVER_IP}"
    echo "📂 项目目录: ${PROJECT_DIR}"
    echo "=" * 50
    
    # 检查本地文件
    if ! check_local_files; then
        log_error "本地文件检查失败"
        exit 1
    fi
    
    # 创建部署包
    package_name=$(create_deployment_package)
    if [ -z "$package_name" ]; then
        log_error "部署包创建失败"
        exit 1
    fi
    
    # 上传并部署
    if ! upload_and_deploy "$package_name"; then
        log_error "部署失败"
        cleanup
        exit 1
    fi
    
    # 清理
    cleanup
    
    echo "=" * 50
    log_success "WebSocket修复部署完成！"
    echo ""
    echo "🔍 后续验证步骤:"
    echo "  1. ssh ${SERVER_USER}@${SERVER_IP}"
    echo "  2. cd ${PROJECT_DIR}"
    echo "  3. ./manage_server.sh logs  # 查看日志"
    echo "  4. ./manage_server.sh test  # 运行测试"
    echo ""
    echo "🎯 修复内容:"
    echo "  ✅ 优化了WebSocket状态检查逻辑"
    echo "  ✅ 改进了连接建立流程"
    echo "  ✅ 增加了连接延迟处理"
    echo "  ✅ 提供了简单测试工具"
    echo "=" * 50
}

# 执行部署
main "$@"
