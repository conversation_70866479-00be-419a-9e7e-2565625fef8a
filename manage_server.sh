#!/bin/bash
# 算法智能体服务器管理脚本 - 根目录版本
# 适用于远程Linux服务器 ***********
# 放置位置: /data/agent/manage_server.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_DIR="/data/agent"
SERVER_HOST="0.0.0.0"
SERVER_PORT="8008"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查项目目录和文件
check_project_directory() {
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        return 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/predict_algorith_agent/predict_main.py" ]; then
        log_error "主启动文件不存在: $PROJECT_DIR/agents/predict_algorith_agent/predict_main.py"
        return 1
    fi
    
    if [ ! -f "$PROJECT_DIR/agents/config.py" ]; then
        log_error "配置文件不存在: $PROJECT_DIR/agents/config.py"
        return 1
    fi
    
    return 0
}

# 获取服务器PID
get_server_pid() {
    if [ -f "$PROJECT_DIR/server.pid" ]; then
        cat "$PROJECT_DIR/server.pid"
    else
        echo ""
    fi
}

# 获取日志监控PID
get_monitor_pid() {
    if [ -f "$PROJECT_DIR/log_monitor.pid" ]; then
        cat "$PROJECT_DIR/log_monitor.pid"
    else
        echo ""
    fi
}

# 检查进程是否运行
is_process_running() {
    local pid=$1
    if [ -z "$pid" ]; then
        return 1
    fi
    kill -0 "$pid" 2>/dev/null
}

# 检查端口是否被占用
is_port_listening() {
    netstat -tlnp | grep ":$SERVER_PORT " > /dev/null
}

# 启动服务器
start_server() {
    log_info "启动算法智能体服务器..."
    
    # 检查项目目录
    if ! check_project_directory; then
        return 1
    fi
    
    cd "$PROJECT_DIR"
    
    # 检查是否已经运行
    server_pid=$(get_server_pid)
    if is_process_running "$server_pid"; then
        log_warning "服务器已在运行 (PID: $server_pid)"
        return 0
    fi
    
    # 检查端口占用
    if is_port_listening; then
        log_warning "端口 $SERVER_PORT 被其他进程占用"
        pid=$(netstat -tlnp | grep ":$SERVER_PORT " | awk '{print $7}' | cut -d'/' -f1 | head -1)
        if [ ! -z "$pid" ] && [ "$pid" != "-" ]; then
            log_info "占用端口的进程 PID: $pid"
        fi
        return 1
    fi
    
    # 使用后台启动脚本
    if [ -f "agents/predict_algorith_agent/scripts/start_server_background.sh" ]; then
        log_info "使用后台启动脚本..."
        ./agents/predict_algorith_agent/scripts/start_server_background.sh
    else
        log_error "后台启动脚本不存在: agents/predict_algorith_agent/scripts/start_server_background.sh"
        return 1
    fi
    
    return $?
}

# 停止服务器
stop_server() {
    log_info "停止算法智能体服务器..."
    
    cd "$PROJECT_DIR" 2>/dev/null || {
        log_error "无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 停止服务器
    server_pid=$(get_server_pid)
    if is_process_running "$server_pid"; then
        log_info "停止服务器 (PID: $server_pid)"
        kill "$server_pid"
        sleep 3
        
        if is_process_running "$server_pid"; then
            log_warning "强制停止服务器"
            kill -9 "$server_pid"
            sleep 1
        fi
        
        rm -f server.pid
        log_success "服务器已停止"
    else
        log_info "服务器未运行"
        rm -f server.pid
    fi
    
    # 停止日志监控
    monitor_pid=$(get_monitor_pid)
    if is_process_running "$monitor_pid"; then
        log_info "停止日志监控 (PID: $monitor_pid)"
        kill "$monitor_pid"
        sleep 2
        
        if is_process_running "$monitor_pid"; then
            log_warning "强制停止日志监控"
            kill -9 "$monitor_pid"
        fi
        
        rm -f log_monitor.pid
        log_success "日志监控已停止"
    else
        log_info "日志监控未运行"
        rm -f log_monitor.pid
    fi
}

# 重启服务器
restart_server() {
    log_info "重启算法智能体服务器..."
    stop_server
    sleep 2
    start_server
}

# 查看服务器状态
show_status() {
    log_info "算法智能体服务器状态"
    echo "=" * 50
    
    cd "$PROJECT_DIR" 2>/dev/null || {
        log_error "无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 检查服务器状态
    server_pid=$(get_server_pid)
    if is_process_running "$server_pid"; then
        log_success "服务器运行中 (PID: $server_pid)"
        
        if is_port_listening; then
            log_success "端口 $SERVER_PORT 正在监听"
            log_info "服务地址: http://***********:$SERVER_PORT"
            log_info "WebSocket: ws://***********:$SERVER_PORT/api/ws/chat"
            log_info "API文档: http://***********:$SERVER_PORT/docs"
        else
            log_error "端口 $SERVER_PORT 未监听"
        fi
        
        # 显示进程信息
        echo ""
        log_info "进程信息:"
        ps aux | grep "$server_pid" | grep -v grep || true
        
    else
        log_error "服务器未运行"
        rm -f server.pid
    fi
    
    # 检查日志监控状态
    monitor_pid=$(get_monitor_pid)
    if is_process_running "$monitor_pid"; then
        log_success "日志监控运行中 (PID: $monitor_pid)"
    else
        log_error "日志监控未运行"
        rm -f log_monitor.pid
    fi
    
    # 检查日志文件
    if [ -f "logs/server.log" ]; then
        file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
        file_size_mb=$((file_size / 1024 / 1024))
        log_info "日志文件大小: ${file_size_mb}MB"
        
        if [ "$file_size_mb" -gt 1024 ]; then
            log_warning "日志文件较大 (${file_size_mb}MB)，建议清理"
        fi
    else
        log_warning "日志文件不存在"
    fi
    
    echo "=" * 50
}

# 查看日志
show_logs() {
    cd "$PROJECT_DIR" 2>/dev/null || {
        log_error "无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    if [ -f "logs/server.log" ]; then
        file_size=$(stat -c%s "logs/server.log" 2>/dev/null || echo "0")
        file_size_mb=$((file_size / 1024 / 1024))
        
        log_info "日志文件大小: ${file_size_mb}MB"
        log_info "日志文件路径: $PROJECT_DIR/logs/server.log"
        log_info "开始显示日志内容 (Ctrl+C 退出)..."
        echo "----------------------------------------"
        tail -f logs/server.log
    else
        log_error "日志文件不存在: $PROJECT_DIR/logs/server.log"
        return 1
    fi
}

# 测试WebSocket连接
test_websocket() {
    log_info "测试WebSocket连接..."
    
    cd "$PROJECT_DIR" 2>/dev/null || {
        log_error "无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 检查服务是否运行
    if ! is_port_listening; then
        log_error "服务器未运行或端口未监听"
        return 1
    fi
    
    # 基础连接测试
    log_info "测试HTTP健康检查..."
    if curl -s "http://localhost:$SERVER_PORT/health" > /dev/null; then
        log_success "HTTP健康检查通过"
    else
        log_error "HTTP健康检查失败"
        return 1
    fi
    
    # WebSocket健康检查
    log_info "测试WebSocket健康检查..."
    if curl -s "http://localhost:$SERVER_PORT/api/ws/health" > /dev/null; then
        log_success "WebSocket健康检查通过"
    else
        log_error "WebSocket健康检查失败"
        return 1
    fi
    
    # WebSocket连接测试
    if [ -f "agents/predict_algorith_agent/scripts/test_websocket.py" ]; then
        log_info "运行WebSocket连接测试..."
        source activate_env.sh 2>/dev/null || true
        python agents/predict_algorith_agent/scripts/test_websocket.py
    else
        log_info "运行简单WebSocket测试..."
        
        # 创建临时测试脚本
        cat > temp_ws_test.py << 'EOF'
import asyncio
import websockets
import json
import time

async def test():
    session_id = f"test_{int(time.time())}"
    # 尝试多个连接地址
    urls = [
        f"ws://127.0.0.1:8008/api/ws/chat?user_id=test_user&username=测试用户&session_id={session_id}",
        f"ws://localhost:8008/api/ws/chat?user_id=test_user&username=测试用户&session_id={session_id}",
        f"ws://0.0.0.0:8008/api/ws/chat?user_id=test_user&username=测试用户&session_id={session_id}"
    ]

    websocket = None
    successful_url = None

    for url in urls:
        print(f"🔗 尝试连接: {url}")
        try:
            # 设置连接超时
            websocket = await asyncio.wait_for(
                websockets.connect(url),
                timeout=5.0
            )
            successful_url = url
            print(f"✅ 连接成功: {successful_url}")
            break
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            continue

    if not websocket:
        print("❌ 所有连接尝试都失败")
        return False

    try:

        # 尝试接收欢迎消息（可选）
        try:
            welcome = await asyncio.wait_for(websocket.recv(), timeout=3.0)
            print("✅ 欢迎消息接收成功")
        except asyncio.TimeoutError:
            print("⚠️ 未收到欢迎消息，继续测试")

        # 发送ping测试
        ping_msg = {"type": "ping", "data": {"test": True}}
        await websocket.send(json.dumps(ping_msg))
        print("📤 Ping消息已发送")

        # 接收pong响应
        pong = await asyncio.wait_for(websocket.recv(), timeout=5.0)
        pong_data = json.loads(pong)

        if pong_data.get("type") == "pong":
            print("✅ 心跳测试成功")
        else:
            print(f"⚠️ 收到响应: {pong_data.get('type')}")

        # 正常关闭连接
        await websocket.close()
        print("🎉 WebSocket基础功能正常")
        return True

    except Exception as e:
        print(f"❌ 消息处理失败: {e}")
        if websocket:
            try:
                await websocket.close()
            except:
                pass
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(test())
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("🛑 测试被中断")
        exit(1)
EOF
        
        python temp_ws_test.py
        test_result=$?
        rm -f temp_ws_test.py
        return $test_result
    fi
}

# 显示使用帮助
show_help() {
    echo "🤖 算法智能体服务器管理脚本"
    echo "📍 适用于远程Linux服务器 ***********"
    echo "📂 项目目录: $PROJECT_DIR"
    echo ""
    echo "用法: $0 {start|stop|restart|status|logs|test|help}"
    echo ""
    echo "命令说明:"
    echo "  start      - 启动服务器 (后台运行)"
    echo "  stop       - 停止服务器和日志监控"
    echo "  restart    - 重启服务器"
    echo "  status     - 查看服务器运行状态"
    echo "  logs       - 实时查看服务器日志"
    echo "  test       - 测试WebSocket连接"
    echo "  help       - 显示此帮助信息"
    echo ""
    echo "📋 服务信息:"
    echo "  - 监听端口: $SERVER_PORT"
    echo "  - 服务地址: http://***********:$SERVER_PORT"
    echo "  - WebSocket: ws://***********:$SERVER_PORT/api/ws/chat"
    echo "  - API文档: http://***********:$SERVER_PORT/docs"
    echo ""
    echo "📄 相关文件:"
    echo "  - PID文件: $PROJECT_DIR/server.pid"
    echo "  - 日志文件: $PROJECT_DIR/logs/server.log"
    echo "  - 启动脚本: $PROJECT_DIR/agents/predict_algorith_agent/scripts/start_server_background.sh"
}

# 主函数
main() {
    case "$1" in
        start)
            start_server
            ;;
        stop)
            stop_server
            ;;
        restart)
            restart_server
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        test)
            test_websocket
            ;;
        help|--help|-h|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
