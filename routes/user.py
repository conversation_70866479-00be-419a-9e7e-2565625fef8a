from fastapi import APIRouter,HTTPException,Depends
from models.user import User
from schemas.user import UserOut,UserCreate,UserUpdate,UserLogin
from core.security import get_password_hash
from core.response import Response
from auth.auth import authenticate_user,create_access_token,create_token_response,get_current_user


router = APIRouter()


#用户注册
@router.post("/register",response_model=Response[UserOut])
async def register(user:UserCreate):
    exist_user = await User.get_or_none(username=user.username)
    if exist_user:
        raise HTTPException(status_code=400,detail="用户名已存在")
    else:
        new_user = await User.create(username = user.username,
                                     password = get_password_hash(user.password),
                                     realname = user.realname
                                     )
        return Response(data=new_user)

#用户登录
@router.post("/login")
async def login(user:UserLogin):
    user = await authenticate_user(user.username,user.password)
    if not user:
        raise HTTPException(status_code=401, detail="Incorrect username or password")
    return create_token_response(user.username)
    
#测试获取用户身份
@router.get("/users/me")
async def read_users_me(current_user: dict = Depends(get_current_user)):
        return current_user['username']

@router.post("/users",response_model=UserOut)
async def create_user(user:UserCreate):
    exist_user = await User.get_or_none(username=user.username)
    if exist_user:
        raise HTTPException(status_code=400,detail="用户名已存在")
    else:
        new_user = await User.create(**user.model_dump())
        return new_user



@router.get("/users/{user_id}",response_model=UserOut)
async def read_user(user_id:int):
    user = await User.get_or_none(id=user_id)
    if user:
        return user
    else:
        raise HTTPException(status_code=404,detail="用户不存在")

@router.get("/users", response_model=list[UserOut])
async def read_users(current_user: dict = Depends(get_current_user)):
    users = await User.all()
    return users

@router.put("/users/{user_id}")
async def update_user(user_id:int,user:UserUpdate):
    exist_user = await User.get_or_none(id=user_id)
    if exist_user:
        update_user = await User.filter(id=user_id).update(**user.model_dump(exclude_unset=True))
        #print(update_user)
        if(update_user == 0) :
            raise HTTPException(status_code=400,detail="更新失败")
        else :
            return {"detail":"更新成功"}
    else:
       raise HTTPException(status_code=404,detail="用户不存在")

@router.delete("/users/{user_id}")
async def delete_user(user_id:int):
    exist_user = await User.get_or_none(id=user_id)
    if exist_user:
        delete_user = await User.filter(id=user_id).delete()
        if delete_user == 0 :
            raise HTTPException(status_code=400,detail="删除失败")
        else :
            return {"detail":"删除成功"}
    else:
        raise HTTPException(status_code=404,detail="用户不存在")