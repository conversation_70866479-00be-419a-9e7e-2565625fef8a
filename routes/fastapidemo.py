from fastapi import BackgroundT<PERSON><PERSON>,APIRouter,Query,Path
from pydantic import BaseModel
import asyncio
from typing import Union

router = APIRouter()

class Item(BaseModel):
    name: str
    price: float
    tags: list[str] = []

async def task_1():
    await asyncio.sleep(1)
    return "mission 1 complete"

async def task_2():
    await asyncio.sleep(1)
    return "mission 2 complete"

async def task_3():
    await asyncio.sleep(1)
    return "mission 3 complete"

def write_log(msg:str):
    with open("../log.txt", "a") as f:
        f.write(msg + "\n")

@router.get("/aysnc_demo")
async def async_demo():
    await asyncio.sleep(1)
    return {"message": "Hello, Async World"}

@router.get("/multitask_demo")
async def multitask_demo():
    tasks = []
    task1 = asyncio.create_task(task_1())
    tasks.append(task1)
    task2 = asyncio.create_task(task_2())
    tasks.append(task2)
    task3 = asyncio.create_task(task_3())
    tasks.append(task3) 
    result = await asyncio.gather(*tasks)
    return {"result": result}

@router.get("/background_tasks_demo")
def backgroud_tasks_demo(background_tasks: BackgroundTasks):
    background_tasks.add_task(write_log, "后台执行:background task 1")
    background_tasks.add_task(write_log, "后台执行:background task 2")
    background_tasks.add_task(write_log, "后台执行:background task 3")
    return {"message": "任务已提交"}

@router.get("/items/{item_id}")
def read_item(item_id: int = Path(..., title="物品ID", ge=1), q: str = Query(None, max_length=20, description="关键词"), limit: int = 10):
    return {"item_id": item_id, "q": q, "limit": limit}

@router.post("/items/")
def create_item(item: Item):
    return item

@router.put("/items/{item_id}")
def update_item(item_id: int, 
                q: Union[str, None] = None,
                item: Item = None
                ):
    return {"item_id": item_id,
            "q": q,
            "item": item
            }

