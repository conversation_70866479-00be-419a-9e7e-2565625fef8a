#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
独立WebSocket测试脚本
用于本地测试WebSocket连接问题，不依赖项目结构
"""

import asyncio
import json
import sys
from datetime import datetime

# 检查依赖
try:
    import websockets
    print("✅ websockets库已安装")
except ImportError:
    print("❌ 缺少websockets库")
    print("请安装: pip install websockets")
    sys.exit(1)

async def test_websocket_connection():
    """测试WebSocket连接"""
    
    # 测试不同的URL
    test_urls = [
        "ws://127.0.0.1:8008/api/ws/chat?user_id=test&username=test&session_id=test_session",
        "ws://localhost:8008/api/ws/chat?user_id=test&username=test&session_id=test_session",
        "ws://127.0.0.1:8008/ws/chat?user_id=test&username=test&session_id=test_session",
        "ws://localhost:8008/ws/chat?user_id=test&username=test&session_id=test_session"
    ]
    
    print("🧪 WebSocket连接测试")
    print("=" * 60)
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🔗 测试 {i}/{len(test_urls)}: {url}")
        print("-" * 60)
        
        try:
            print("📡 正在尝试连接...")
            
            # 尝试建立连接
            websocket = await asyncio.wait_for(
                websockets.connect(url),
                timeout=10.0
            )
            
            print("✅ WebSocket连接成功建立！")
            
            # 等待服务器消息
            try:
                print("⏳ 等待服务器消息...")
                message = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=5.0
                )
                
                print(f"📥 收到服务器消息:")
                print(f"   {message}")
                
                # 尝试解析JSON
                try:
                    data = json.loads(message)
                    msg_type = data.get("type", "unknown")
                    print(f"📋 消息类型: {msg_type}")
                    
                    if msg_type == "welcome":
                        print("🎉 收到欢迎消息！")
                        welcome_msg = data.get("data", {}).get("message", "")
                        print(f"💬 欢迎内容: {welcome_msg}")
                    
                except json.JSONDecodeError:
                    print("⚠️ 消息不是JSON格式")
                
            except asyncio.TimeoutError:
                print("⚠️ 未收到服务器消息（可能正常）")
            
            # 发送测试消息
            print("\n📤 发送测试消息...")
            test_message = {
                "type": "ping",
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "test": "connection_test"
                }
            }
            
            await websocket.send(json.dumps(test_message, ensure_ascii=False))
            print("✅ 测试消息已发送")
            
            # 等待响应
            try:
                print("⏳ 等待响应...")
                response = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=5.0
                )
                
                print(f"📥 收到响应:")
                print(f"   {response}")
                
                # 解析响应
                try:
                    response_data = json.loads(response)
                    response_type = response_data.get("type", "unknown")
                    print(f"📋 响应类型: {response_type}")
                    
                    if response_type == "pong":
                        print("💓 心跳响应正常！")
                    elif response_type == "error":
                        error_msg = response_data.get("data", {}).get("error", "未知错误")
                        print(f"❌ 服务器错误: {error_msg}")
                    else:
                        print(f"📝 其他响应: {response_type}")
                        
                except json.JSONDecodeError:
                    print("⚠️ 响应不是JSON格式")
                
            except asyncio.TimeoutError:
                print("⚠️ 响应超时")
            
            # 关闭连接
            await websocket.close()
            print("🔌 连接已关闭")
            print("🎉 测试成功完成！")
            
            return True
            
        except asyncio.TimeoutError:
            print("❌ 连接超时")
            print("   可能原因: 服务器未启动或端口不正确")
            
        except ConnectionRefusedError:
            print("❌ 连接被拒绝")
            print("   可能原因: 服务器未启动或端口被占用")
            
        except OSError as e:
            print(f"❌ 网络错误: {e}")
            print("   可能原因: 网络配置问题或防火墙阻止")
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            print(f"   异常类型: {type(e).__name__}")
            
        print("⏭️ 尝试下一个URL...")
    
    print("\n❌ 所有连接尝试都失败了")
    print("\n🔍 可能的问题:")
    print("   1. 服务器未启动")
    print("   2. 端口8008被占用或不正确")
    print("   3. WebSocket路径不正确")
    print("   4. 防火墙阻止连接")
    print("   5. 服务器配置问题")
    
    return False

async def test_server_availability():
    """测试服务器可用性"""
    print("\n🔍 检查服务器可用性...")
    
    # 检查端口是否开放
    import socket
    
    hosts = ["127.0.0.1", "localhost"]
    ports = [8008, 8000, 8080]
    
    for host in hosts:
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    print(f"✅ {host}:{port} 端口开放")
                else:
                    print(f"❌ {host}:{port} 端口关闭")
                    
            except Exception as e:
                print(f"❌ {host}:{port} 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 独立WebSocket连接测试")
    print("=" * 60)
    print("📝 此脚本将测试WebSocket连接到本地服务器")
    print("🎯 目标: 验证WebSocket基础连接功能")
    print("=" * 60)
    
    try:
        # 检查服务器可用性
        asyncio.run(test_server_availability())
        
        # 测试WebSocket连接
        success = asyncio.run(test_websocket_connection())
        
        if success:
            print("\n🎉 WebSocket连接测试成功！")
            print("✅ 基础WebSocket功能正常")
            return 0
        else:
            print("\n❌ WebSocket连接测试失败")
            print("🔧 请检查服务器状态和配置")
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        import traceback
        print("📋 详细错误信息:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n📊 测试结果: {'成功' if exit_code == 0 else '失败'}")
    sys.exit(exit_code)
