# WebSocket集成指南

## 📋 概述

本文档介绍如何将算法生成智能体与前端UI对话框通过WebSocket进行实时交互。

## 🏗️ 架构设计

### 系统架构
```
前端UI对话框 <--WebSocket--> WebSocket管理器 <--> 算法智能体 <--> 数据库
```

### 核心组件
- **WebSocket管理器** (`websocket_manager.py`) - 处理连接管理和消息路由
- **WebSocket路由** (`websocket_routes.py`) - 定义WebSocket端点
- **连接管理器** (`ConnectionManager`) - 管理多用户连接和会话状态

## 🔌 WebSocket端点

### 主要端点
- `ws://<server_ip>:<port>/api/ws/chat?user_id=<用户ID>&session_id=<会话ID>&username=<用户名>` - 主要聊天WebSocket端点
- `GET /api/ws/stats` - 获取连接统计信息
- `POST /api/ws/broadcast` - 广播消息
- `DELETE /api/ws/session/{session_id}` - 断开指定会话
- `GET /api/ws/health` - 健康检查

### 连接参数说明
- `user_id`：**必需参数**，当前登录用户的唯一标识
- `session_id`：可选参数，32位随机字符串，如不提供将自动生成
- `username`：可选参数，用户显示名称

## 📡 消息协议

### 客户端发送消息格式
```json
{
    "type": "chat",
    "data": {
        "message": "我想用LSTM做预测，学习率0.001，批次大小32"
    },
    "session_id": "a1b2c3d4e5f6789012345678901234ab",
    "user_id": "12345"
}
```

### 服务器响应消息格式
```json
{
    "type": "chat_response",
    "data": {
        "message": "我理解您想使用LSTM进行预测...",
        "task_id": "task_001",
        "conversation_id": "conv_67890",
        "state": {...},
        "has_error": false
    },
    "timestamp": "2025-01-22T18:00:00",
    "session_id": "a1b2c3d4e5f6789012345678901234ab",
    "user_id": "12345",
    "status": "success"
}
```

### 支持的消息类型

#### 客户端 → 服务器
- `chat` - 对话消息
- `get_progress` - 查询训练进度
- `generate_report` - 生成分析报告
- `get_history` - 获取历史记录
- `ping` - 心跳检测

#### 服务器 → 客户端
- `welcome` - 欢迎消息
- `chat_response` - 对话响应
- `thinking` - 正在思考状态
- `progress_update` - 进度更新
- `report_generated` - 报告生成完成
- `history_data` - 历史数据
- `error` - 错误消息
- `pong` - 心跳响应

## 🌐 前端集成示例

### JavaScript WebSocket客户端
```javascript
class AlgorithmChatClient {
    constructor(userId, sessionId = null, username = null) {
        this.userId = userId;  // 必需参数
        this.sessionId = sessionId || this.generateSessionId();  // 32位随机字符串
        this.username = username || `user_${userId}`;
        this.websocket = null;
        this.messageHandlers = {};
    }

    generateSessionId() {
        // 生成32位随机字符串
        return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
            const r = Math.random() * 16 | 0;
            return r.toString(16);
        });
    }

    connect() {
        const wsUrl = `ws://localhost:8008/api/ws/chat?user_id=${this.userId}&session_id=${this.sessionId}&username=${encodeURIComponent(this.username)}`;
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = (event) => {
            console.log('WebSocket连接已建立');
            this.onConnectionOpen(event);
        };
        
        this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };
        
        this.websocket.onclose = (event) => {
            console.log('WebSocket连接已关闭');
            this.onConnectionClose(event);
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.onConnectionError(error);
        };
    }
    
    sendMessage(type, data) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            const message = {
                type: type,
                data: data,
                session_id: this.sessionId,
                user_id: this.userId
            };
            this.websocket.send(JSON.stringify(message));
        } else {
            console.error('WebSocket未连接');
        }
    }
    
    sendChatMessage(message) {
        this.sendMessage('chat', { message: message });
    }
    
    requestProgress(taskId) {
        this.sendMessage('get_progress', { task_id: taskId });
    }
    
    generateReport(taskId) {
        this.sendMessage('generate_report', { task_id: taskId });
    }
    
    handleMessage(message) {
        const handler = this.messageHandlers[message.type];
        if (handler) {
            handler(message);
        } else {
            console.log('收到消息:', message);
        }
    }
    
    onMessage(type, handler) {
        this.messageHandlers[type] = handler;
    }
    
    onConnectionOpen(event) {
        // 连接建立后的处理
    }
    
    onConnectionClose(event) {
        // 连接关闭后的处理
    }
    
    onConnectionError(error) {
        // 连接错误处理
    }
    
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 使用示例
const chatClient = new AlgorithmChatClient('12345', null, '张三');

// 设置消息处理器
chatClient.onMessage('welcome', (message) => {
    console.log('欢迎消息:', message.data.message);
    displayWelcomeMessage(message.data);
});

chatClient.onMessage('chat_response', (message) => {
    console.log('AI回复:', message.data.message);
    displayChatMessage(message.data);
});

chatClient.onMessage('thinking', (message) => {
    showThinkingIndicator(message.data.message);
});

chatClient.onMessage('progress_update', (message) => {
    updateProgressBar(message.data);
});

chatClient.onMessage('report_generated', (message) => {
    displayReport(message.data);
});

chatClient.onMessage('error', (message) => {
    showErrorMessage(message.data.error);
});

// 连接WebSocket
chatClient.connect();

// 发送消息
function sendUserMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    if (message) {
        chatClient.sendChatMessage(message);
        input.value = '';
        displayUserMessage(message);
    }
}
```

### Vue 示例
```javascript

```

## 🗄️ 数据存储

### 数据存储流程

1. **WebSocket连接建立**
   - 验证`user_id`参数是否传入（user_id必需）
   - 生成或验证`session_id`（32位UUID）
   - 创建`conversations`记录
   - 创建`session_states`记录

2. **消息交互处理**
   - 每条用户消息存储到`conversation_messages`表
   - 每条AI回复存储到`conversation_messages`表
   - 更新`session_states`中的状态数据

3. **会话状态管理**
   - 实时更新`last_activity`时间戳
   - 保存算法训练状态、参数等到`state_data`
   - 支持会话恢复和状态持久化

4. **连接断开处理**
   - 更新`last_activity`时间戳
   - 保持会话数据完整性
   - 支持重新连接时状态恢复

### Session ID生成规则

```javascript
// 32位随机字符串生成
function generateSessionId() {
    return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
        const r = Math.random() * 16 | 0;
        return r.toString(16);
    });
}

// 或使用UUID库
import { v4 as uuidv4 } from 'uuid';
const sessionId = uuidv4().replace(/-/g, ''); // 移除连字符得到32位字符串
```

## 🔧 部署和配置

### 1. 启动智能体服务（基于Pydantic ai）
```bash
cd /path/twinbuilder-agents
python agents/predict_algorith_agent/predict_main.py
```

### 2. 测试WebSocket连接

注： <server_ip> 是服务器固定IP或域名，端口<port>根据服务对外映射的端口确定

```bash
# 检查WebSocket健康状态
curl http://<server_ip>:<port>/api/ws/health

# 查看连接统计
curl http://<server_ip>:<port>/api/ws/stats
```

### 3. 前端配置
- WebSocket URL: `ws://<server_ip>:<port>/api/ws/chat?user_id=<用户ID>&session_id=<会话ID>&username=<用户名>`
- `user_id`参数是必需的，缺少将导致连接失败
- `session_id`支持自动生成32位随机字符串或手动指定
- 建议实现断线重连机制
- 建议实现心跳检测

## 🚀 高级特性

### 1. 多用户支持
- 每个用户独立的会话状态
- 支持并发连接
- 会话状态持久化

### 2. 实时进度监控
- 训练进度实时推送
- 状态变化通知
- 错误实时反馈

### 3. 智能报告生成
- 异步报告生成
- 生成状态实时通知
- 支持HTML和PDF格式

### 4. 错误处理和恢复

#### 常见错误码
- `4000`: 一般错误
- `4001`: 认证失败（缺少user_id参数）
- `4002`: 会话不存在
- `4003`: 消息格式错误
- `4004`: 服务器内部错误
- `4005`: 数据库连接错误
- `4006`: Session ID格式错误

#### 连接参数验证
```javascript
function validateConnectionParams(userId, sessionId = null) {
    if (!userId || userId.trim() === '') {
        throw new Error('user_id是必需参数，不能为空');
    }

    if (sessionId && sessionId.length !== 32) {
        throw new Error('session_id必须是32位字符串');
    }

    return true;
}
```

#### 错误处理示例
```javascript
websocket.onclose = (event) => {
    switch (event.code) {
        case 4001:
            console.error('认证失败：请检查user_id参数');
            break;
        case 4005:
            console.error('数据库连接错误：服务器暂时不可用');
            setTimeout(() => reconnect(), 10000);
            break;
        default:
            if (event.code !== 1000) {
                setTimeout(() => reconnect(), 5000);
            }
    }
};
```

#### 自动重连机制
- 连接断开自动重连
- 消息重发机制
- 优雅的错误提示

## 📊 监控和调试

### 日志监控
- WebSocket连接日志
- 消息处理日志
- 错误异常日志

### 性能监控
- 连接数统计
- 消息处理延迟
- 内存使用情况

### 调试工具
- WebSocket连接测试工具
- 消息格式验证
- 实时连接状态查看

## 🔒 安全考虑

### 1. 认证和授权
- 建议添加JWT token验证
- 会话ID验证
- 用户权限控制

### 2. 消息验证
- 输入消息格式验证
- 消息长度限制
- 恶意消息过滤

### 3. 连接管理
- 连接数限制
- 频率限制
- 异常连接检测

## 🎯 最佳实践

1. **前端实现**
   - 实现断线重连机制
   - 添加消息队列缓存
   - 优化用户体验反馈

2. **后端优化**
   - 合理的连接数限制
   - 消息处理异步化
   - 内存使用优化

3. **错误处理**
   - 完善的错误分类
   - 用户友好的错误提示
   - 自动恢复机制

4. **性能优化**
   - 消息压缩
   - 批量消息处理
   - 连接池管理

5. **数据库存储最佳实践**
   - 确保user_id的有效性和唯一性
   - 使用32位UUID作为session_id
   - 定期清理过期的会话数据
   - 实现会话状态的增量更新
   - 使用数据库索引优化查询性能

## 🔐 安全考虑

### 用户身份验证
- **user_id验证**: 确保user_id来自可信的认证系统
- **会话管理**: 实现会话超时和清理机制
- **权限控制**: 基于user_id实现数据访问权限控制

### 数据保护
- **敏感数据加密**: 对存储在数据库中的敏感信息进行加密
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **数据备份**: 定期备份会话和消息数据

### 连接安全
- **WSS协议**: 生产环境使用WSS（WebSocket Secure）
- **CORS配置**: 正确配置跨域资源共享
- **速率限制**: 实现连接和消息的速率限制

---

**注意**: 本文档基于最新的WebSocket实现，包含了user_id必需参数、32位session_id生成和完整的数据库存储架构。请确保客户端实现遵循这些规范。
