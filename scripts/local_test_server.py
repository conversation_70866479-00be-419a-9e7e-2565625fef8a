#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
本地测试服务器
用于本地WebSocket连接测试，解决导入问题
"""

import sys
import os
import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent  # 到达 twinbuilder-agents 目录
sys.path.insert(0, str(project_root))

print(f"当前目录: {current_dir}")
print(f"项目根目录: {project_root}")
print(f"Python路径已添加: {project_root}")

try:
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Query
    from fastapi.responses import JSONResponse
    import uvicorn
    print("✅ FastAPI导入成功")
except ImportError as e:
    print(f"❌ FastAPI导入失败: {e}")
    print("请安装: pip install fastapi uvicorn")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LocalConnectionManager:
    """本地连接管理器"""
    
    def __init__(self):
        self.active_connections = {}
        
    async def connect(self, websocket: WebSocket, session_id: str, user_info: dict = None):
        """建立连接"""
        logger.info(f"🔗 建立连接: session_id={session_id}")
        
        try:
            # 接受连接
            await websocket.accept()
            self.active_connections[session_id] = websocket
            logger.info(f"✅ 连接建立成功: session_id={session_id}")
            
            # 发送欢迎消息
            welcome_msg = {
                "type": "welcome",
                "data": {
                    "message": "欢迎连接到本地测试服务器！",
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "user_info": user_info or {},
                    "server": "local_test_server"
                }
            }
            
            await websocket.send_text(json.dumps(welcome_msg, ensure_ascii=False))
            logger.info(f"📤 欢迎消息已发送: session_id={session_id}")
            
        except Exception as e:
            logger.error(f"❌ 连接建立失败: session_id={session_id}, error={e}")
            raise
    
    def disconnect(self, session_id: str):
        """断开连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
            logger.info(f"🔌 连接已断开: session_id={session_id}")
    
    async def handle_message(self, websocket: WebSocket, session_id: str, message_data: dict):
        """处理消息"""
        msg_type = message_data.get("type", "unknown")
        logger.info(f"📥 收到消息: session_id={session_id}, type={msg_type}")
        
        if msg_type == "ping":
            # 心跳响应
            pong_msg = {
                "type": "pong",
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "session_id": session_id,
                    "original": message_data.get("data", {})
                }
            }
            await websocket.send_text(json.dumps(pong_msg, ensure_ascii=False))
            logger.info(f"💓 心跳响应: session_id={session_id}")
            
        elif msg_type == "chat":
            # 聊天响应
            user_msg = message_data.get("data", {}).get("message", "")
            chat_response = {
                "type": "chat_response",
                "data": {
                    "message": f"本地服务器收到: {user_msg}",
                    "timestamp": datetime.now().isoformat(),
                    "session_id": session_id,
                    "echo": True
                }
            }
            await websocket.send_text(json.dumps(chat_response, ensure_ascii=False))
            logger.info(f"💬 聊天响应: session_id={session_id}")
            
        else:
            # 未知消息类型
            error_msg = {
                "type": "error",
                "data": {
                    "error": f"未知消息类型: {msg_type}",
                    "timestamp": datetime.now().isoformat(),
                    "session_id": session_id,
                    "received": message_data
                }
            }
            await websocket.send_text(json.dumps(error_msg, ensure_ascii=False))
            logger.warning(f"⚠️ 未知消息类型: session_id={session_id}, type={msg_type}")

# 创建FastAPI应用
app = FastAPI(title="本地WebSocket测试服务器", version="1.0.0")

# 创建连接管理器
connection_manager = LocalConnectionManager()

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "本地WebSocket测试服务器",
        "timestamp": datetime.now().isoformat(),
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "active_connections": len(connection_manager.active_connections),
        "server": "local_test_server"
    }

@app.websocket("/api/ws/chat")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    session_id: str = Query(None, description="会话ID"),
    user_id: str = Query(None, description="用户ID"),
    username: str = Query(None, description="用户名")
):
    """WebSocket聊天端点"""
    
    # 生成session_id（如果没有提供）
    if not session_id:
        session_id = f"local_test_{int(datetime.now().timestamp())}"
    
    logger.info(f"🚀 WebSocket连接请求: session_id={session_id}, user_id={user_id}, username={username}")
    
    try:
        # 建立连接
        user_info = {
            "user_id": user_id or "test_user",
            "username": username or "测试用户"
        }
        
        await connection_manager.connect(websocket, session_id, user_info)
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                logger.info(f"📨 收到原始数据: session_id={session_id}, data={data}")
                
                # 解析JSON
                message_data = json.loads(data)
                
                # 处理消息
                await connection_manager.handle_message(websocket, session_id, message_data)
                
            except WebSocketDisconnect:
                logger.info(f"🔌 WebSocket正常断开: session_id={session_id}")
                break
            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON解析错误: session_id={session_id}, error={e}")
                error_msg = {
                    "type": "error",
                    "data": {"error": "JSON格式错误", "details": str(e)}
                }
                await websocket.send_text(json.dumps(error_msg, ensure_ascii=False))
            except Exception as e:
                logger.error(f"❌ 消息处理异常: session_id={session_id}, error={e}")
                try:
                    error_msg = {
                        "type": "error",
                        "data": {"error": "消息处理失败", "details": str(e)}
                    }
                    await websocket.send_text(json.dumps(error_msg, ensure_ascii=False))
                except Exception as send_error:
                    logger.error(f"❌ 发送错误响应失败: {send_error}")
                    break
                
    except Exception as e:
        logger.error(f"❌ WebSocket连接异常: session_id={session_id}, error={e}")
    finally:
        # 清理连接
        connection_manager.disconnect(session_id)

@app.get("/api/ws/stats")
async def get_websocket_stats():
    """获取WebSocket统计信息"""
    return {
        "active_connections": len(connection_manager.active_connections),
        "connection_ids": list(connection_manager.active_connections.keys()),
        "timestamp": datetime.now().isoformat(),
        "server": "local_test_server"
    }

def main():
    """启动服务器"""
    print("🚀 启动本地WebSocket测试服务器...")
    print("=" * 60)
    print("📍 服务地址: http://127.0.0.1:8008")
    print("🔗 WebSocket地址: ws://127.0.0.1:8008/api/ws/chat")
    print("💊 健康检查: http://127.0.0.1:8008/health")
    print("📊 连接统计: http://127.0.0.1:8008/api/ws/stats")
    print("=" * 60)
    
    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8008,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 服务器被用户停止")
    except Exception as e:
        print(f"\n💥 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
