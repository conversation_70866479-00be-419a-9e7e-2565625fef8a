@echo off
echo 启动智能体服务...

echo 启动前端服务器...
start "Frontend Server" cmd /k "cd agents\predict_algorith_agent\static\html && python -m http.server 8080"

echo 等待3秒...
timeout /t 3 /nobreak

echo 启动后端服务器...
start "Backend Server" cmd /k "cd agents\predict_algorith_agent && python predict_main.py"

echo 等待5秒让服务启动...
timeout /t 5 /nobreak

echo 打开浏览器进行测试...
start http://localhost:8080/chat_interface_v3.html

echo 服务启动完成！
echo 前端地址: http://localhost:8080/chat_interface_v3.html
echo 后端地址: http://localhost:8008/health
pause
