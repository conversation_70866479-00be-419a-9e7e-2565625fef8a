from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from fastapi import Depends,HTTPException
from models.user import User
from core.security import verify_password,create_access_token,verify_token
from datetime import timedelta

oauth_schema = OAuth2PasswordBearer(tokenUrl="/api/login")

async def authenticate_user(username: str, password: str):
    user = await User.get_or_none(username=username)
    if not user:
        return False
    if not verify_password(password, user.password):
        return False
    return user
async def get_current_user(token: str = Depends(oauth_schema)):
    token_data = verify_token(token)
    if token_data['r'] == 200:
        return {'tokenValid':True,'username':token_data['username']}
    else :
        raise HTTPException(status_code=401, detail="Incorrect token")
    

def create_token_response(username:str):
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": username},
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}