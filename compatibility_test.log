2025-08-04 09:58:29,104 - __main__ - INFO - ��ʼ�������������Բ���...
2025-08-04 09:58:34,591 - __main__ - INFO - ����������������
2025-08-04 09:58:36,136 - __main__ - INFO - ����ҳ��������: file://D:\project\jiaoda\twinbuilder-agents\agents\predict_algorith_agent\static\html\chat_interface_v3.html
2025-08-04 09:58:36,136 - __main__ - INFO - ��ʼ�����û���¼...
2025-08-04 09:58:36,239 - __main__ - INFO - �û���¼����ͨ��
2025-08-04 09:58:36,239 - __main__ - INFO - ��ʼ����WebSocket����...
2025-08-04 09:58:36,537 - __main__ - INFO - WebSocket����: ws://localhost:8008/api/ws/chat?session_id=4bcfac70237045ba859f65abeacb920b&user_id=test_user_001&username=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7
2025-08-04 09:58:36,800 - __main__ - INFO - ���������̨: WebSocket�����ѽ���
2025-08-04 09:58:36,801 - __main__ - INFO - WebSocket����: {"type": "welcome", "data": {"message": "���ã���ӭʹ���㷨���������壡\n\n������ʲô�ܰ������Ҳ������ʣ�\n(1) ����ѵ��һ���㷨����Ҫ��ô��?\n(2) �����ṩ��Щ������\n\n��ѡ��������Ȥ�����⣬����ֱ��������������\n\n����: �����㷨�Ƽ�, �����Զ���ȡ, ʵʱ�Ի�����, ѵ�����ȼ��, ������������", "session_id": "4bcfac70237045ba859f65abeacb920b", "type": "welcome_guide", "options": [{"key": "1", "text": "����ѵ��һ���㷨����Ҫ��ô��?"}, {"key": "2", "text": "�����ṩ��Щ������"}], "features": ["�����㷨�Ƽ�", "�����Զ���ȡ", "ʵʱ�Ի�����", "ѵ�����ȼ��", "������������"]}, "timestamp": "2025-08-04T09:58:36.795474", "session_id": "4bcfac70237045ba859f65abeacb920b", "status": "success"}
2025-08-04 09:58:36,801 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: welcome, data: Object, timestamp: 2025-08-04T09:58:36.795474, session_id: 4bcfac70237045ba859f65abeacb920b, status: success}
2025-08-04 09:58:36,810 - __main__ - INFO - WebSocket����: ws://localhost:8008/api/ws/chat?session_id=078106d56b5746e19ff09caed4de26ed&user_id=test_user_001&username=%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7
2025-08-04 09:58:36,940 - __main__ - INFO - ���������̨: WebSocket�����ѽ���
2025-08-04 09:58:36,940 - __main__ - INFO - WebSocket����: {"type": "welcome", "data": {"message": "���ã���ӭʹ���㷨���������壡\n\n������ʲô�ܰ������Ҳ������ʣ�\n(1) ����ѵ��һ���㷨����Ҫ��ô��?\n(2) �����ṩ��Щ������\n\n��ѡ��������Ȥ�����⣬����ֱ��������������\n\n����: �����㷨�Ƽ�, �����Զ���ȡ, ʵʱ�Ի�����, ѵ�����ȼ��, ������������", "session_id": "078106d56b5746e19ff09caed4de26ed", "type": "welcome_guide", "options": [{"key": "1", "text": "����ѵ��һ���㷨����Ҫ��ô��?"}, {"key": "2", "text": "�����ṩ��Щ������"}], "features": ["�����㷨�Ƽ�", "�����Զ���ȡ", "ʵʱ�Ի�����", "ѵ�����ȼ��", "������������"]}, "timestamp": "2025-08-04T09:58:36.936464", "session_id": "078106d56b5746e19ff09caed4de26ed", "status": "success"}
2025-08-04 09:58:36,941 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: welcome, data: Object, timestamp: 2025-08-04T09:58:36.936464, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:37,086 - __main__ - INFO - WebSocket���Ӳ���ͨ��
2025-08-04 09:58:37,086 - __main__ - INFO - ��ʼ������Ŀѡ��...
2025-08-04 09:58:37,806 - __main__ - INFO - WebSocket����: {"type":"get_conversations","data":{"user_id":"078106d56b5746e19ff09caed4de26ed","limit":20,"offset":0},"session_id":"078106d56b5746e19ff09caed4de26ed"}
2025-08-04 09:58:37,940 - __main__ - INFO - WebSocket����: {"type":"get_conversations","data":{"user_id":"078106d56b5746e19ff09caed4de26ed","limit":20,"offset":0},"session_id":"078106d56b5746e19ff09caed4de26ed"}
2025-08-04 09:58:37,947 - __main__ - INFO - WebSocket����: {"type": "conversations_list", "data": {"conversations": [{"conversation_id": "conv_1edb4503dcf84244", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T16:08:43", "last_message_at": "2025-08-01T16:08:44", "message_count": 2, "last_user_message": "��ã�����ѵ��һ��LSTM�㷨����Ԥ�����"}, {"conversation_id": "conv_e9e23f1a266c4558", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T16:00:51", "last_message_at": "2025-08-01T16:00:51", "message_count": 2, "last_user_message": "���"}, {"conversation_id": "conv_94fa1a6c33c142a4", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T15:52:07", "last_message_at": "2025-08-01T15:52:08", "message_count": 2, "last_user_message": "����v2��ʽ����Ϣ����"}, {"conversation_id": "conv_76a7e22b2b1c4359", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T15:51:41", "last_message_at": "2025-08-01T15:51:42", "message_count": 2, "last_user_message": "��ã������˽�LSTM�㷨"}, {"conversation_id": "conv_d17a6d143f4140b3", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T15:26:17", "last_message_at": "2025-08-01T15:26:18", "message_count": 2, "last_user_message": "��ã������˽�һ���㷨ѵ������"}], "total": 5}, "timestamp": "2025-08-04T09:58:37.946956", "session_id": "078106d56b5746e19ff09caed4de26ed", "status": "success"}
2025-08-04 09:58:37,947 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: conversations_list, data: Object, timestamp: 2025-08-04T09:58:37.946956, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:38,057 - __main__ - INFO - WebSocket����: {"type": "conversations_list", "data": {"conversations": [{"conversation_id": "conv_1edb4503dcf84244", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T16:08:43", "last_message_at": "2025-08-01T16:08:44", "message_count": 2, "last_user_message": "��ã�����ѵ��һ��LSTM�㷨����Ԥ�����"}, {"conversation_id": "conv_e9e23f1a266c4558", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T16:00:51", "last_message_at": "2025-08-01T16:00:51", "message_count": 2, "last_user_message": "���"}, {"conversation_id": "conv_94fa1a6c33c142a4", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T15:52:07", "last_message_at": "2025-08-01T15:52:08", "message_count": 2, "last_user_message": "����v2��ʽ����Ϣ����"}, {"conversation_id": "conv_76a7e22b2b1c4359", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T15:51:41", "last_message_at": "2025-08-01T15:51:42", "message_count": 2, "last_user_message": "��ã������˽�LSTM�㷨"}, {"conversation_id": "conv_d17a6d143f4140b3", "title": "�½��㷨ѵ������", "status": "active", "created_at": "2025-08-01T15:26:17", "last_message_at": "2025-08-01T15:26:18", "message_count": 2, "last_user_message": "��ã������˽�һ���㷨ѵ������"}], "total": 5}, "timestamp": "2025-08-04T09:58:38.055980", "session_id": "078106d56b5746e19ff09caed4de26ed", "status": "success"}
2025-08-04 09:58:38,057 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: conversations_list, data: Object, timestamp: 2025-08-04T09:58:38.055980, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:38,099 - __main__ - INFO - ��Ŀѡ��ɹ���8��API��ť������
2025-08-04 09:58:38,099 - __main__ - INFO - ��ʼ��������Ի�����...
2025-08-04 09:58:38,100 - __main__ - INFO - ���Ͳ�����Ϣ 1: ��ã������˽��㷨������Ĺ���
2025-08-04 09:58:38,134 - __main__ - INFO - WebSocket����: {"type":"chat","data":{"message":"��ã������˽��㷨������Ĺ���"},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:58:38,657 - __main__ - INFO - WebSocket����: {"type": "thinking", "data": {"message": "���ڷ�����������..."}, "timestamp": "2025-08-04T09:58:38.655978", "session_id": "078106d56b5746e19ff09caed4de26ed", "status": "success"}
2025-08-04 09:58:38,657 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: thinking, data: Object, timestamp: 2025-08-04T09:58:38.655978, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:38,658 - __main__ - INFO - ���������̨: AI����˼��...
2025-08-04 09:58:39,421 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: chat_response, data: Object, timestamp: 2025-08-04T09:58:39.403247, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:41,158 - __main__ - INFO - ��Ϣ 1 ���ͳɹ����յ��ظ�
2025-08-04 09:58:43,160 - __main__ - INFO - ���Ͳ�����Ϣ 2: ����Ҫ��һ��Ԥ����ά�����㷨
2025-08-04 09:58:43,195 - __main__ - INFO - WebSocket����: {"type":"chat","data":{"message":"����Ҫ��һ��Ԥ����ά�����㷨"},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:58:43,498 - __main__ - INFO - WebSocket����: {"type": "thinking", "data": {"message": "���ڷ�����������..."}, "timestamp": "2025-08-04T09:58:43.496519", "session_id": "078106d56b5746e19ff09caed4de26ed", "status": "success"}
2025-08-04 09:58:43,498 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: thinking, data: Object, timestamp: 2025-08-04T09:58:43.496519, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:43,499 - __main__ - INFO - ���������̨: AI����˼��...
2025-08-04 09:58:44,246 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: chat_response, data: Object, timestamp: 2025-08-04T09:58:44.240148, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:46,217 - __main__ - INFO - ��Ϣ 2 ���ͳɹ����յ��ظ�
2025-08-04 09:58:48,232 - __main__ - INFO - ���Ͳ�����Ϣ 3: ���Ƽ����ʵ�LSTM����
2025-08-04 09:58:48,265 - __main__ - INFO - WebSocket����: {"type":"chat","data":{"message":"���Ƽ����ʵ�LSTM����"},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:58:48,575 - __main__ - INFO - WebSocket����: {"type": "thinking", "data": {"message": "���ڷ�����������..."}, "timestamp": "2025-08-04T09:58:48.575424", "session_id": "078106d56b5746e19ff09caed4de26ed", "status": "success"}
2025-08-04 09:58:48,576 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: thinking, data: Object, timestamp: 2025-08-04T09:58:48.575424, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:48,576 - __main__ - INFO - ���������̨: AI����˼��...
2025-08-04 09:58:49,405 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: chat_response, data: Object, timestamp: 2025-08-04T09:58:49.396799, session_id: 078106d56b5746e19ff09caed4de26ed, status: success}
2025-08-04 09:58:51,283 - __main__ - INFO - ��Ϣ 3 ���ͳɹ����յ��ظ�
2025-08-04 09:58:53,293 - __main__ - INFO - ����Ի����ܲ�����ȫͨ��
2025-08-04 09:58:53,294 - __main__ - INFO - ��ʼ����API���ù���...
2025-08-04 09:58:53,294 - __main__ - INFO - ����API: ��ѯȫ���㷨����
2025-08-04 09:58:53,318 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:58:53,319 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_algorithms","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:58:56,334 - __main__ - ERROR - API����ʧ��: ��ѯȫ���㷨����
2025-08-04 09:58:58,334 - __main__ - INFO - ����API: ��ѯȫ�����ݼ�����
2025-08-04 09:58:58,351 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:58:58,352 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_datasets","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:01,369 - __main__ - ERROR - API����ʧ��: ��ѯȫ�����ݼ�����
2025-08-04 09:59:03,375 - __main__ - INFO - ����API: ��ѯȫ��IOT����
2025-08-04 09:59:03,397 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:59:03,398 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_iot","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:06,405 - __main__ - ERROR - API����ʧ��: ��ѯȫ��IOT����
2025-08-04 09:59:08,417 - __main__ - INFO - ����API: ��ѯȫ����������
2025-08-04 09:59:08,435 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:59:08,435 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_services","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:11,441 - __main__ - ERROR - API����ʧ��: ��ѯȫ����������
2025-08-04 09:59:13,448 - __main__ - WARNING - API���ù��ܲ��Բ���ͨ��: 0/4
2025-08-04 09:59:13,448 - __main__ - INFO - ��ʼ���Ի�ϳ���...
2025-08-04 09:59:13,448 - __main__ - INFO - ����1: ���� -> API���� -> ����
2025-08-04 09:59:13,474 - __main__ - INFO - WebSocket����: {"type":"chat","data":{"message":"�����һ�¿��õ��㷨"},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:16,509 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:59:16,509 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_algorithms","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:19,537 - __main__ - INFO - WebSocket����: {"type":"chat","data":{"message":"лл����Щ��Ϣ������"},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:22,554 - __main__ - INFO - ��ϳ���1���Գɹ�
2025-08-04 09:59:22,555 - __main__ - INFO - ����2: API���� -> ���� -> API����
2025-08-04 09:59:22,565 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:59:22,565 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_datasets","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:25,421 - __main__ - INFO - WebSocket����: {"type": "api_response", "data": {"action": "get_all_algorithms", "status": "error", "error": {"code": "API_CALL_ERROR", "message": "�޷���ȡ�㷨�б�: ��������ʧ��: [WinError 64] ָ�������������ٿ��á�", "details": "API����ִ��ʧ��: �޷���ȡ�㷨�б�: ��������ʧ��: [WinError 64] ָ�������������ٿ��á�"}, "timestamp": "2025-08-04T09:59:25.420398"}}
2025-08-04 09:59:25,422 - __main__ - INFO - ���������̨: �յ���Ϣ: {type: api_response, data: Object}
2025-08-04 09:59:25,595 - __main__ - INFO - WebSocket����: {"type":"chat","data":{"message":"��Щ���ݼ����ĸ��ʺ�Ԥ��ά����"},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:28,621 - __main__ - INFO - ���������̨: ����API����: {type: api_call, data: Object, session_id: 078106d56b5746e19ff09caed4de26ed, user_id: test_user_001}
2025-08-04 09:59:28,621 - __main__ - INFO - WebSocket����: {"type":"api_call","data":{"action":"get_all_iot","project_id":"285","parameters":{}},"session_id":"078106d56b5746e19ff09caed4de26ed","user_id":"test_user_001"}
2025-08-04 09:59:31,644 - __main__ - INFO - ��ϳ���2���Գɹ�
2025-08-04 09:59:31,644 - __main__ - INFO - ��ϳ���������ȫͨ��
2025-08-04 09:59:31,645 - __main__ - INFO - ��ʼ���Դ�����...
2025-08-04 09:59:31,645 - __main__ - INFO - ���ԶϿ����Ӻ����Ϊ
2025-08-04 09:59:31,659 - __main__ - INFO - ���������̨: WebSocket�����ѹر�
2025-08-04 10:00:03,681 - __main__ - ERROR - ���������ʧ��: Page.fill: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("#messageInput")
    - locator resolved to <textarea rows="1" disabled id="messageInput" class="message-input" placeholder="������Ϣ������Ȼ���Խ�������ʹ�����API���ܽ���ֱ�ӵ���"></textarea>
    - fill("������ϢӦ�÷���ʧ��")
  - attempting fill action
    2 �� waiting for element to be visible, enabled and editable
      - element is not enabled
    - retrying fill action
    - waiting 20ms
    2 �� waiting for element to be visible, enabled and editable
      - element is not enabled
    - retrying fill action
      - waiting 100ms
    58 �� waiting for element to be visible, enabled and editable
       - element is not enabled
     - retrying fill action
       - waiting 500ms

2025-08-04 10:00:03,682 - __main__ - INFO - ���Ա����ѱ��浽: D:\project\jiaoda\twinbuilder-agents\agents\predict_algorith_agent\tests\compatibility_test_report.json
2025-08-04 10:00:03,683 - __main__ - INFO - ���������Բ������
2025-08-04 10:00:04,012 - __main__ - INFO - ��Դ�������
