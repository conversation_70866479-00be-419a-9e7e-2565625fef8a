[tool:pytest]
# pytest配置文件
addopts =
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=agents/predict_algorith_agent
    --cov-report=xml
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=60
    -v

# 异步测试支持
asyncio_mode = auto

# 测试发现
testpaths =
    agents/predict_algorith_agent/tests

python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 标记定义
markers =
    asyncio: 异步测试标记
    slow: 慢速测试标记
    integration: 集成测试标记
    unit: 单元测试标记
    fixed: 修复后的测试标记
    mock: 使用Mock的测试标记

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PytestUnknownMarkWarning
    ignore::PytestRemovedIn9Warning
    ignore::RuntimeWarning
    ignore::UserWarning
    ignore:.*aiohttp.*:DeprecationWarning
    ignore:.*pydantic.*:DeprecationWarning

# 测试超时设置
timeout = 300

# 并行测试设置（如果安装了pytest-xdist）
# addopts = -n auto
