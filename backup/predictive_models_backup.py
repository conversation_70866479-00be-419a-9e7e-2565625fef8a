from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum

# 任务类型枚举
class PredictiveTaskType(str, Enum):
    INITIATE = "发起算法任务"
    QUERY = "查询任务进度"
    REPORT = "生成分析报告"
    UNSUPPORTED = "不支持的操作"

# 交互类型枚举
class InteractionType(str, Enum):
    CONFIRM = "确认"
    ADJUST = "调整"
    CANCEL = "取消"
    QUERY_STATUS = "查询进度"
    QUERY_RESULT = "查询结果"
    GENERATE_REPORT = "生成报告"
    UNKNOWN = "未知"

class InteractionClassification(BaseModel):
    interaction_type: InteractionType
    confidence: float
    reason: str

# LSTM参数模型
class LSTMParams(BaseModel):
    input_dim: int = Field(..., description="输入特征维度")
    hidden_dim: int = Field(..., description="隐藏层维度")
    num_layers: int = Field(..., description="LSTM层数")
    output_dim: int = Field(..., description="输出维度")
    batch_size: int = Field(..., description="批次大小")
    learning_rate: float = Field(..., description="学习率")
    epochs: int = Field(..., description="训练轮数")

# CNN参数模型
class CNNParams(BaseModel):
    input_shape: List[int] = Field(..., description="输入形状")
    num_filters: int = Field(..., description="卷积核数量")
    kernel_size: int = Field(..., description="卷积核大小")
    pool_size: int = Field(..., description="池化大小")
    dense_units: int = Field(..., description="全连接层单元数")
    batch_size: int = Field(..., description="批次大小")
    learning_rate: float = Field(..., description="学习率")
    epochs: int = Field(..., description="训练轮数")

AlgorithmParams = Union[LSTMParams, CNNParams, Dict[str, Any]]  # 可扩展

# 预测性算法Agent状态
class PredictiveAgentState(BaseModel):
    history: List[Dict[str, Any]] = Field(default_factory=list, description="多轮对话历史")
    current_params: Dict[str, Any] = Field(default_factory=dict, description="当前参数")
    missing_params: List[str] = Field(default_factory=list, description="缺失参数")
    confirmed: bool = False
    task_type: Optional[PredictiveTaskType] = None
    task_id: Optional[str] = None
    task_status: Optional[str] = None  # 任务进度
    report: Optional[str] = None
    awaiting_confirmation: bool = False
    last_interaction: Optional[str] = None
    last_api_result: Optional[Dict[str, Any]] = None

# 任务分类结果
class PredictiveTaskClassification(BaseModel):
    task_type: PredictiveTaskType = Field(description="任务类型")
    confidence: float = Field(description="分类置信度", ge=0.0, le=1.0)
    reason: str = Field(description="分类理由")

# 参数提取结果
class PredictiveParameterExtraction(BaseModel):
    extracted_params: Dict[str, Any] = Field(default_factory=dict, description="提取的参数")
    missing_params: List[str] = Field(default_factory=list, description="缺失的参数")
    confidence: float = Field(description="参数提取置信度", ge=0.0, le=1.0)
    notes: str = Field(default="", description="参数提取说明")

# 交互分类结果
class PredictiveInteractionClassification(BaseModel):
    interaction_type: InteractionType = Field(description="交互类型")
    confidence: float = Field(description="分类置信度", ge=0.0, le=1.0)
    reason: str = Field(description="分类理由")

# 参数确认请求
class PredictiveConfirmationRequest(BaseModel):
    current_params: Dict[str, Any] = Field(description="当前参数")
    formatted_params: str = Field(description="格式化参数显示")
    confirmation_message: str = Field(description="确认消息") 