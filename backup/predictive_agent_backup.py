from pydantic_ai import Agent as PydanticAIAgent, Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from .predictive_models import (
    PredictiveTaskType, PredictiveTaskClassification, PredictiveParameterExtraction,
    PredictiveInteractionClassification, PredictiveConfirmationRequest,
    PredictiveAgentState, InteractionType, AlgorithmParams
)
from agents.config import SHOW_DEEPSEEK_THOUGHT, ALGORITHM_API_BASE, WKHTMLTOPDF_PATH, REPORT_PDF_DIR
import pprint
from fastapi import APIRouter
from core.response import Response
import requests
from agents.predict_algorith_agent.predict_memory_manager import AgentMemoryManager
import os
from jinja2 import Template
import pdfkit
from typing import Tuple
import platform

router = APIRouter()

# 配置DeepSeek大模型对象
llm_instance = OpenAIModel(
    # 'deepseek-chat',
    # provider=OpenAIProvider(
    #     base_url='https://api.deepseek.com',
    #     api_key='***********************************'
    # )
    'qwen-turbo-latest',
    provider=OpenAIProvider(
        base_url='https://dashscope.aliyuncs.com/compatible-mode/v1',
        api_key='sk-b43e1b0d51e24f359b1d3c6342149577'
    )
)

# 通用带思考过程的输出模型
def make_thought_output_model(output_type):
    class ThoughtOutputModel(BaseModel):
        thought: str = Field(description="模型推理和思考过程")
        output: output_type = Field(description="结构化输出结果")
    return ThoughtOutputModel

# 任务分类Prompt
task_classify_prompt = """
你是一个工业预测性算法任务分类专家。请分两步输出：
1. 先详细说明你的推理和思考过程（字段名：thought）。
2. 再输出结构化分类结果（字段名：output，类型为PredictiveTaskClassification）。
输出格式为JSON对象，包含thought和output两个字段。
"""
# 参数提取Prompt
param_extract_prompt = """
你是一个算法参数提取专家。请分两步输出：
1. 先详细说明你的推理和思考过程（字段名：thought）。
2. 再输出结构化参数提取结果（字段名：output，类型为PredictiveParameterExtraction）。
输出格式为JSON对象，包含thought和output两个字段。
"""
# 交互分类Prompt
interaction_classify_prompt = """
你是一个用户交互意图分类专家。请分两步输出：
1. 先详细说明你的推理和思考过程（字段名：thought）。
2. 再输出结构化交互分类结果（字段名：output，类型为PredictiveInteractionClassification）。
输出格式为JSON对象，包含thought和output两个字段。
"""
# 参数调整Prompt
param_adjust_prompt = """
你是一个参数调整专家。请分两步输出：
1. 先详细说明你的推理和思考过程（字段名：thought）。
2. 再输出结构化参数提取结果（字段名：output，类型为PredictiveParameterExtraction）。
输出格式为JSON对象，包含thought和output两个字段。
"""

REPORT_HTML_TEMPLATE = """
<html>
<head>
  <meta charset="utf-8">
  <title>任务{{ task_id }}分析报告</title>
  <style>
    body {
      font-family: "Microsoft YaHei", "SimSun", "SimHei", "Arial Unicode MS", Arial, sans-serif;
    }
  </style>
</head>
<body>
  <h1>任务 {{ task_id }} 分析报告</h1>
  <h2>算法类型：{{ algorithm_type }}</h2>
  <h3>参数配置</h3>
  <ul>
    <li>输入维度：{{ input_dim }}</li>
    <li>隐藏层：{{ hidden_dim }}</li>
    <li>层数：{{ num_layers }}</li>
    <li>输出维度：{{ output_dim }}</li>
    <li>Batch Size：{{ batch_size }}</li>
    <li>学习率：{{ learning_rate }}</li>
    <li>训练轮数：{{ epochs }}</li>
  </ul>
  <h3>分析结果</h3>
  <div>{{ analysis_result }}</div>
  <h3>优化建议</h3>
  <div>{{ suggestions }}</div>
</body>
</html>
"""

SYSTEM_PROMPT_TEMPLATE = """
你是一个工业预测性算法分析专家，请根据以下参数和结果，生成一份结构化的分析报告和优化建议：
- 任务ID: {{ task_id }}
- 算法类型: {{ algorithm_type }}
- 输入维度: {{ input_dim }}
- 隐藏层: {{ hidden_dim }}
- 层数: {{ num_layers }}
- 输出维度: {{ output_dim }}
- Batch Size: {{ batch_size }}
- 学习率: {{ learning_rate }}
- 训练轮数: {{ epochs }}
- 结果摘要: {{ analysis_result }}
请输出详细的分析和针对参数的优化建议。
"""

class PredictiveAlgorithmAssistant:
    """
    预测性算法智能体主类，封装了任务分类、参数提取、交互分类、参数调整等子Agent。
    """
    def __init__(self):
        """
        初始化各类子Agent，包括任务分类、参数提取、交互分类和参数调整。
        """
        # 任务分类Agent
        self.task_classifier = Agent(
            model=llm_instance,
            output_type=make_thought_output_model(PredictiveTaskClassification),
            system_prompt=task_classify_prompt
        )
        # 参数提取Agent
        self.parameter_extractor = Agent(
            model=llm_instance,
            output_type=make_thought_output_model(PredictiveParameterExtraction),
            system_prompt=param_extract_prompt
        )
        # 交互分类Agent
        self.interaction_classifier = Agent(
            model=llm_instance,
            output_type=make_thought_output_model(PredictiveInteractionClassification),
            system_prompt=interaction_classify_prompt
        )
        # 参数调整Agent（可扩展，暂留）
        self.parameter_adjuster = Agent(
            model=llm_instance,
            output_type=make_thought_output_model(PredictiveParameterExtraction),
            system_prompt=param_adjust_prompt
        )
        self.state = PredictiveAgentState()

    def classify_task(self, user_input: str) -> Dict[str, Any]:
        """
        调用任务分类Agent，对用户输入进行任务类型分类。
        返回包含deepseek思考过程和最终结果的字典。
        """
        result = self.task_classifier.run_sync(user_input)
        if SHOW_DEEPSEEK_THOUGHT:
            print("[DeepSeek][任务分类] 思考过程:")
            pprint.pprint(result.output.thought)
        print("[任务分类] 输出结果:")
        pprint.pprint(result.output.output)
        return {
            "thought": result.output.thought if SHOW_DEEPSEEK_THOUGHT else None,
            "output": result.output.output
        }

    def extract_parameters(self, user_input: str) -> Dict[str, Any]:
        """
        调用参数提取Agent，从用户输入中提取算法任务所需的参数。
        返回包含deepseek思考过程和最终结果的字典。
        """
        result = self.parameter_extractor.run_sync(user_input)
        if SHOW_DEEPSEEK_THOUGHT:
            print("[DeepSeek][参数提取] 思考过程:")
            pprint.pprint(result.output.thought)
        print("[参数提取] 输出结果:")
        pprint.pprint(result.output.output)
        return {
            "thought": result.output.thought if SHOW_DEEPSEEK_THOUGHT else None,
            "output": result.output.output
        }

    def classify_interaction(self, user_input: str) -> Dict[str, Any]:
        """
        调用交互分类Agent，判断用户输入的交互意图。
        返回包含deepseek思考过程和最终结果的字典。
        """
        result = self.interaction_classifier.run_sync(user_input)
        if SHOW_DEEPSEEK_THOUGHT:
            print("[DeepSeek][交互分类] 思考过程:")
            pprint.pprint(result.output.thought)
        print("[交互分类] 输出结果:")
        pprint.pprint(result.output.output)
        return {
            "thought": result.output.thought if SHOW_DEEPSEEK_THOUGHT else None,
            "output": result.output.output
        }

    def adjust_parameters(self, user_input: str, current_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用参数调整Agent，根据用户的调整指令和当前参数，生成调整后的参数。
        返回包含deepseek思考过程和最终结果的字典。
        """
        prompt = f"用户调整指令：{user_input}\n当前参数：{current_params}"
        result = self.parameter_adjuster.run_sync(prompt)
        if SHOW_DEEPSEEK_THOUGHT:
            print("[DeepSeek][参数调整] 思考过程:")
            pprint.pprint(result.output.thought)
        print("[参数调整] 输出结果:")
        pprint.pprint(result.output.output)
        return {
            "thought": result.output.thought if SHOW_DEEPSEEK_THOUGHT else None,
            "output": result.output.output
        }

    def confirm_parameters(self, params: Dict[str, Any]) -> PredictiveConfirmationRequest:
        """
        生成参数确认请求，格式化参数摘要并生成确认消息。
        参数：
            params (Dict[str, Any]): 当前参数字典。
        返回：
            PredictiveConfirmationRequest: 参数确认请求对象。
        """
        formatted_params = "\n".join(f"{k}: {v}" for k, v in params.items())
        confirmation_message = f"请确认以下参数设置：\n{formatted_params}\n如需修改请直接回复，否则回复'确认'继续。"
        return PredictiveConfirmationRequest(
            current_params=params,
            formatted_params=formatted_params,
            confirmation_message=confirmation_message
        )

    def create_algorithm_task(self, params: dict) -> dict:
        """
        MOCK: 向三方算法生成器提交任务，返回任务ID等信息。
        """
        # url = f"{ALGORITHM_API_BASE}/create_task"
        # ...真实请求代码注释...
        # return {"error": f"算法任务创建失败: {str(e)}"}
        return {
            "mock": True,
            "task_id": "mock_task_123456",
            "params": params,
            "message": "已模拟创建算法任务，等待三方API对接。"
        }

    def query_task_status(self, task_id: str) -> dict:
        """
        MOCK: 查询三方算法任务进度。
        """
        # url = f"{ALGORITHM_API_BASE}/task_status/{task_id}"
        # ...真实请求代码注释...
        return {
            "mock": True,
            "task_id": task_id,
            "status": "running",
            "progress": 42,
            "message": "已模拟查询任务进度，等待三方API对接。"
        }

    def get_task_result(self, task_id: str) -> dict:
        """
        MOCK: 获取三方算法任务结果或报告。
        """
        # url = f"{ALGORITHM_API_BASE}/task_result/{task_id}"
        # ...真实请求代码注释...
        return {
            "mock": True,
            "task_id": task_id,
            "result": {"accuracy": 0.95, "report": "预测性维护算法效果良好。"},
            "message": "已模拟获取任务结果，等待三方API对接。"
        }

    def process_user_input(self, user_input: str, state_dict: dict = None) -> Tuple[dict, dict, str]:
        """
        主流程：支持多轮对话、参数链、任务链、交互类型等，流程与时序图一致。
        """
        # 恢复或初始化状态
        if state_dict:
            state = PredictiveAgentState(**state_dict)
        else:
            state = PredictiveAgentState()
        # 1. 等待参数确认
        if state.awaiting_confirmation:
            interaction = self.classify_interaction(user_input)
            if interaction['output'].interaction_type == InteractionType.CONFIRM:
                # 调用算法生成器API创建任务
                api_result = self.create_algorithm_task(state.current_params)
                state.task_id = api_result.get("task_id")
                state.task_status = "created"
                state.awaiting_confirmation = False
                state.last_api_result = api_result
                return {"msg": "任务已创建", "task_id": state.task_id}, state.dict(), "created"
            elif interaction['output'].interaction_type == InteractionType.ADJUST:
                # 进入参数补全/调整流程
                # 这里可以调用参数提取Agent进行补全
                param_result = self.extract_parameters(user_input)
                state.current_params.update(param_result['output'].extracted_params)
                state.missing_params = param_result['output'].missing_params
                state.awaiting_confirmation = True
                return {"msg": "请补充或调整参数", "missing": state.missing_params}, state.dict(), "adjust"
            elif interaction['output'].interaction_type == InteractionType.CANCEL:
                # 取消任务，重置状态
                state = PredictiveAgentState()
                return {"msg": "任务已取消"}, state.dict(), "cancel"
            else:
                return {"msg": "请确认参数或进行调整"}, state.dict(), "wait"
        # 2. 查询进度/结果/报告
        elif state.task_id:
            interaction = self.classify_interaction(user_input)
            if interaction['output'].interaction_type == InteractionType.QUERY_STATUS:
                status = self.query_task_status(state.task_id)
                state.task_status = status.get("status")
                return {"msg": "任务进度", "status": state.task_status}, state.dict(), "status"
            elif interaction['output'].interaction_type == InteractionType.QUERY_RESULT:
                result = self.get_task_result(state.task_id)
                return {"msg": "任务结果", "result": result}, state.dict(), "result"
            elif interaction['output'].interaction_type == InteractionType.GENERATE_REPORT:
                report = self.generate_report(state.task_id)
                state.report = report
                return {"msg": "报告已生成", "report": report}, state.dict(), "report"
            else:
                return {"msg": "请说明您的需求"}, state.dict(), "wait"
        # 3. 首次任务描述/参数提取
        else:
            # 任务分类、参数提取
            task_result = self.classify_task(user_input)
            param_result = self.extract_parameters(user_input)
            state.task_type = task_result['output'].task_type
            state.current_params = param_result['output'].extracted_params
            state.missing_params = param_result['output'].missing_params
            if state.missing_params:
                state.awaiting_confirmation = True
                return {"msg": "请补充参数", "missing": state.missing_params}, state.dict(), "need_params"
            else:
                state.awaiting_confirmation = True
                return {"msg": "请确认参数", "params": state.current_params}, state.dict(), "need_confirm"

    def generate_report(self, task_id: str, params: dict = None, analysis_result: str = None, suggestions: str = None) -> str:
        # 数据先写死，参数保留占位符
        context = {
            "task_id": task_id,
            "algorithm_type": params.get("algorithm_type", "LSTM") if params else "LSTM",
            "input_dim": params.get("input_dim", 10) if params else 10,
            "hidden_dim": params.get("hidden_dim", 128) if params else 128,
            "num_layers": params.get("num_layers", 2) if params else 2,
            "output_dim": params.get("output_dim", 1) if params else 1,
            "batch_size": params.get("batch_size", 32) if params else 32,
            "learning_rate": params.get("learning_rate", 0.001) if params else 0.001,
            "epochs": params.get("epochs", 100) if params else 100,
            "analysis_result": analysis_result or "模型在验证集上准确率达到92%，表现良好。",
            "suggestions": suggestions or "建议尝试增加层数或调整学习率以进一步提升模型表现。"
        }
        template = Template(REPORT_HTML_TEMPLATE)
        html = template.render(**context)
        return html

    def get_report_pdf(self, task_id: str, params: dict = None, analysis_result: str = None, suggestions: str = None) -> str:
        html = self.generate_report(task_id, params, analysis_result, suggestions)
        # 确保输出目录存在
        os.makedirs(REPORT_PDF_DIR, exist_ok=True)
        output_path = os.path.join(REPORT_PDF_DIR, f"report_{task_id}.pdf")
        try:
            config = None
            # 统一用配置项，跨平台部署时只需改 config.py
            wkhtmltopdf_path = WKHTMLTOPDF_PATH
            if wkhtmltopdf_path:
                config = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)
            pdfkit.from_string(html, output_path, configuration=config)
        except OSError as e:
            raise RuntimeError(f"PDF生成失败，请检查wkhtmltopdf是否正确安装并配置到环境变量。详细信息: {e}")
        return output_path

    def get_system_prompt(self, task_id: str, params: dict, analysis_result: str = "", suggestions: str = "") -> str:
        context = {
            "task_id": task_id,
            "algorithm_type": params.get("algorithm_type", "LSTM"),
            "input_dim": params.get("input_dim", 10),
            "hidden_dim": params.get("hidden_dim", 128),
            "num_layers": params.get("num_layers", 2),
            "output_dim": params.get("output_dim", 1),
            "batch_size": params.get("batch_size", 32),
            "learning_rate": params.get("learning_rate", 0.001),
            "epochs": params.get("epochs", 100),
            "analysis_result": analysis_result,
            "suggestions": suggestions
        }
        template = Template(SYSTEM_PROMPT_TEMPLATE)
        return template.render(**context)

    # 预留：调用大模型生成分析内容
    def call_llm_for_report(self, system_prompt: str) -> dict:
        # TODO: 调用大模型API，返回分析结果和建议
        return {
            "analysis_result": "模型在验证集上准确率达到92%，表现良好。",
            "suggestions": "建议尝试增加层数或调整学习率以进一步提升模型表现。"
        }

# 预测性算法Agent全局实例
predict_algorith_agent = PredictiveAlgorithmAssistant()

memory_manager = AgentMemoryManager()

@router.post("/predict_algorith", response_model=Response[dict])
def predict_algorith_handler(request: dict):
    """
    预测性算法Agent主接口，接收用户输入，返回结构化结果。
    """
    user_input = request["question"]
    user_id = request.get("user_id")
    session_id = request.get("session_id")
    state = memory_manager.get_state(user_id, session_id) if user_id and session_id else None
    result, new_state, task_id = predict_algorith_agent.process_user_input(user_input, state)
    if user_id and session_id:
        memory_manager.save_state(user_id, session_id, task_id, new_state)
    return Response(data=result) 